targets:
  $default:
    builders:
      json_serializable:
        options:
          # Creates `to<PERSON><PERSON>()` and `from<PERSON><PERSON>()` methods
          explicit_to_json: true
          # Creates checked methods
          checked: true
          # Creates constructor with required parameters
          create_factory: true
          # Creates per field to json methods
          create_per_field_to_json: true
          # Disallow extra keys
          disallow_unrecognized_keys: false
          # Include if null
          include_if_null: true
