<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>巅峰表现词汇库 - 互动版</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- 
    Chosen Palette: Zen Scholar Palette
    Application Structure Plan: The application is structured as a single-page dashboard. The core design prioritizes user task-flow over mirroring the report's linear structure. It starts with a brief introduction to the "why" (Embodied Cognition), followed by the new "Word Decomposer" tool for direct application. After that, the "Action Explorer" allows for browsing the full libraries. This tiered structure—concept, direct application, full exploration—guides the user through a logical journey. A data visualization section provides a comparative overview, and a "5-Minute Reset" generator offers immediate, practical value. This structure facilitates rapid exploration and direct application, making the content highly usable for a time-constrained student.
    Visualization & Content Choices: 
    - Report Info: Distribution of "Simple" vs. "Concentrated" actions per category. -> Goal: Compare. -> Viz Method: Horizontal Bar Chart (Chart.js/Canvas). -> Interaction: Clicking a bar filters the main explorer. -> Justification: The bar chart provides an at-a-glance comparison of which hobbies are better suited for quick breaks versus dedicated workouts, guiding user choice.
    - Report Info: Detailed A-Z action lists. -> Goal: Organize/Inform. -> Presentation Method: Interactive Cards (HTML/CSS) with Modal pop-ups. -> Interaction: Click-to-reveal details. -> Justification: A card grid is scannable. Modals for details keep the main interface clean.
    - New Feature: Word Decomposer. -> Goal: Apply. -> Presentation Method: Input field with dynamic result display area (HTML/JS). -> Interaction: User types a word, clicks a button, and sees a list of corresponding, clickable actions. -> Justification: This feature directly implements the core "word-to-action" concept from the user request, making the tool highly practical and engaging. It uses a simulated dictionary for definitions to work offline.
    CONFIRMATION: NO SVG graphics used. NO Mermaid JS used.
    -->
    <style>
        body { font-family: 'Inter', 'Noto Sans SC', sans-serif; background-color: #FDFBF8; }
        .bg-warm-beige { background-color: #F5F1EB; }
        .bg-stone-olive { background-color: #EAE8E0; }
        .text-deep-charcoal { color: #333333; }
        .text-muted-olive { color: #5E5E4B; }
        .accent-burnt-sienna { background-color: #A0522D; }
        .accent-burnt-sienna-text { color: #A0522D; }
        .accent-burnt-sienna-border { border-color: #A0522D; }
        .accent-sky-blue { background-color: #87CEEB; }
        .accent-sky-blue-text { color: #4682B4; }
        .accent-sky-blue-border { border-color: #87CEEB; }
        .chart-container { position: relative; width: 100%; max-width: 800px; margin-left: auto; margin-right: auto; height: 350px; max-height: 50vh; }
        @media (min-width: 768px) { .chart-container { height: 450px; } }
        .smooth-scroll { scroll-behavior: smooth; }
        ::-webkit-scrollbar { width: 8px; }
        ::-webkit-scrollbar-track { background: #F5F1EB; }
        ::-webkit-scrollbar-thumb { background: #D1C7B7; border-radius: 10px; }
        ::-webkit-scrollbar-thumb:hover { background: #BFB3A2; }
        .card-enter { animation: card-enter 0.5s ease-out forwards; }
        @keyframes card-enter {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .result-item-enter { animation: result-item-enter 0.4s ease-out forwards; }
        @keyframes result-item-enter {
             from { opacity: 0; transform: translateX(-20px); }
             to { opacity: 1; transform: translateX(0); }
        }
    </style>
</head>
<body class="text-deep-charcoal smooth-scroll">

    <!-- Header Section -->
    <header class="bg-warm-beige shadow-sm sticky top-0 z-40">
        <div class="container mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <h1 class="text-xl sm:text-2xl font-bold text-deep-charcoal">巅峰表现词汇库</h1>
                <nav class="hidden md:flex items-center space-x-6 text-base font-medium">
                    <a href="#introduction" class="text-muted-olive hover:accent-burnt-sienna-text transition">核心理念</a>
                    <a href="#decomposer" class="text-muted-olive hover:accent-burnt-sienna-text transition">单词分解器</a>
                    <a href="#explorer" class="text-muted-olive hover:accent-burnt-sienna-text transition">动作浏览器</a>
                    <a href="#analysis" class="text-muted-olive hover:accent-burnt-sienna-text transition">数据洞察</a>
                    <a href="#reset" class="text-muted-olive hover:accent-burnt-sienna-text transition">5分钟重置</a>
                </nav>
                <button id="mobile-menu-button" class="md:hidden p-2 rounded-md text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive focus:outline-none focus:ring-2 focus:ring-inset focus:ring-burnt-sienna">
                    <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
                    </svg>
                </button>
            </div>
        </div>
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#introduction" class="block px-3 py-2 rounded-md text-base font-medium text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive transition">核心理念</a>
                <a href="#decomposer" class="block px-3 py-2 rounded-md text-base font-medium text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive transition">单词分解器</a>
                <a href="#explorer" class="block px-3 py-2 rounded-md text-base font-medium text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive transition">动作浏览器</a>
                <a href="#analysis" class="block px-3 py-2 rounded-md text-base font-medium text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive transition">数据洞察</a>
                <a href="#reset" class="block px-3 py-2 rounded-md text-base font-medium text-muted-olive hover:accent-burnt-sienna-text hover:bg-stone-olive transition">5分钟重置</a>
            </div>
        </div>
    </header>

    <main class="container mx-auto p-4 sm:p-6 lg:p-8">

        <!-- Introduction Section -->
        <section id="introduction" class="my-8 scroll-mt-20">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl accent-burnt-sienna-text">超越死记硬背，拥抱“具身认知”</h2>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-muted-olive">将身体活动融入学习，不仅能缓解备考压力，更能通过多感官刺激，构建强大而持久的词汇记忆。</p>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-lg border border-stone-200">
                <p class="text-base text-deep-charcoal leading-relaxed">本指南的核心理念基于“具身认知”（Embodied Cognition）理论，即人的认知过程深深植根于身体的感知和运动体验中。通过将一个具体的身体动作与一个需要记忆的单词相关联，我们能够创造一种多感官的学习体验。这种“动作-单词”联想记忆法，相比于单纯的视觉或听觉重复，能够构建更强大、更持久的神经连接。这不仅能显著提高记忆的深度和持久性，还能帮助考生在压力巨大的考试环境中更迅速、准确地提取信息。本工具旨在将这一理念付诸实践，让每一次短暂的休息都成为一次高效的学习充电。</p>
            </div>
        </section>

        <!-- Word Decomposer Section -->
        <section id="decomposer" class="my-16 scroll-mt-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl accent-burnt-sienna-text">单词-动作分解器</h2>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-muted-olive">输入一个单词，让它成为您专属的训练计划。</p>
            </div>
            <div class="max-w-4xl mx-auto grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Input and How-to Section -->
                <div class="bg-white p-6 rounded-xl shadow-lg border border-stone-200">
                    <h3 class="font-bold text-xl mb-4 text-deep-charcoal">如何使用</h3>
                    <ol class="list-decimal list-inside space-y-2 text-muted-olive">
                        <li>从下面的下拉菜单中选择一个您喜欢的<b class="text-deep-charcoal">动作库</b> (如：健身力量)。</li>
                        <li>在输入框中键入一个您想记忆的<b class="text-deep-charcoal">英文单词</b>。</li>
                        <li>点击“分解单词”按钮。</li>
                        <li>右侧将展示单词释义，以及根据单词字母<b class="text-deep-charcoal">逐一匹配</b>的动作序列。</li>
                        <li>跟着这个序列动起来，一边做动作，一边加深单词记忆！</li>
                    </ol>
                    <div class="mt-6 space-y-4">
                        <div>
                            <label for="decomposer-category-select" class="block text-sm font-medium text-deep-charcoal mb-1">1. 选择动作库</label>
                            <select id="decomposer-category-select" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-accent-burnt-sienna focus:border-accent-burnt-sienna"></select>
                        </div>
                        <div>
                            <label for="word-input" class="block text-sm font-medium text-deep-charcoal mb-1">2. 输入单词</label>
                            <input type="text" id="word-input" placeholder="例如: ACHIEVE" class="w-full p-2 border border-gray-300 rounded-md shadow-sm focus:ring-accent-burnt-sienna focus:border-accent-burnt-sienna">
                        </div>
                        <button id="decompose-word-btn" class="w-full accent-burnt-sienna text-white font-bold py-3 px-8 rounded-lg shadow-md hover:opacity-90 transition transform hover:scale-105">
                            分解单词
                        </button>
                    </div>
                </div>
                <!-- Results Section -->
                <div id="decomposer-results-container" class="bg-warm-beige p-6 rounded-xl">
                    <div id="decomposer-placeholder" class="h-full flex flex-col items-center justify-center text-center text-muted-olive">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/></svg>
                        <p class="font-semibold text-lg">结果将在此处显示</p>
                        <p class="text-sm">准备好将词汇与动作连接起来了吗？</p>
                    </div>
                    <div id="decomposer-results" class="hidden"></div>
                </div>
            </div>
        </section>


        <!-- Action Explorer Section -->
        <section id="explorer" class="my-16 scroll-mt-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl accent-burnt-sienna-text">动作浏览器</h2>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-muted-olive">根据您的兴趣和时间，探索海量动作库，为您的词汇注入活力。</p>
            </div>

            <!-- Category Filters -->
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4 text-center text-deep-charcoal">第一步：选择您的兴趣领域</h3>
                <div id="category-filters" class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-7 gap-3 sm:gap-4 text-center">
                </div>
            </div>

            <!-- Scene Filters -->
            <div class="mb-10">
                <h3 class="text-xl font-semibold mb-4 text-center text-deep-charcoal">第二步：选择您的可用场景</h3>
                <div id="scene-filters" class="flex justify-center items-center space-x-4 rounded-lg bg-warm-beige p-2 max-w-md mx-auto">
                    <button data-scene="all" class="scene-filter-btn flex-1 py-2 px-4 text-sm font-semibold rounded-md transition duration-200">全部动作</button>
                    <button data-scene="简单活动" class="scene-filter-btn flex-1 py-2 px-4 text-sm font-semibold rounded-md transition duration-200">简单活动 (5-10分钟)</button>
                    <button data-scene="集中训练" class="scene-filter-btn flex-1 py-2 px-4 text-sm font-semibold rounded-md transition duration-200">集中训练 (30-60分钟)</button>
                </div>
            </div>
            
            <!-- Action Cards Grid -->
            <div id="action-grid" class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 xl:grid-cols-8 gap-4">
            </div>
             <p id="no-results" class="text-center text-muted-olive text-lg hidden py-8">在此分类下没有找到符合条件的动作。</p>
        </section>

        <!-- Data Analysis Section -->
        <section id="analysis" class="my-16 scroll-mt-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl accent-burnt-sienna-text">数据洞察</h2>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-muted-olive">快速了解不同动作库的场景分布，助您规划学习与锻炼。</p>
            </div>
            <div class="bg-white p-4 sm:p-6 rounded-xl shadow-lg border border-stone-200">
                <p class="text-center text-muted-olive mb-4">该图表展示了每个兴趣领域中，“简单活动”与“集中训练”两类动作的数量分布。您可以点击图例切换显示，或点击图表条目直接筛选上方的动作库。</p>
                <div class="chart-container">
                    <canvas id="categoryChart"></canvas>
                </div>
            </div>
        </section>

        <!-- 5-Minute Reset Section -->
        <section id="reset" class="my-16 scroll-mt-20">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold tracking-tight sm:text-4xl accent-burnt-sienna-text">5分钟认知重置</h2>
                <p class="mt-4 max-w-3xl mx-auto text-lg text-muted-olive">学习累了？一键生成一套科学的5分钟放松流程，快速恢复身心状态。</p>
            </div>
            <div class="bg-white p-6 rounded-xl shadow-lg border border-stone-200 text-center">
                <button id="generate-reset-plan" class="accent-burnt-sienna text-white font-bold py-3 px-8 rounded-lg shadow-md hover:opacity-90 transition transform hover:scale-105">
                    一键生成我的5分钟计划
                </button>
                <div id="reset-plan-container" class="mt-8 text-left hidden">
                </div>
            </div>
        </section>

    </main>
    
    <!-- Footer -->
    <footer class="bg-warm-beige mt-16 py-8">
        <div class="container mx-auto text-center text-muted-olive">
            <p>&copy; 2025 巅峰表现词汇库. 专为奋斗中的备考者设计。</p>
            <p class="text-sm mt-2">将科学融入汗水，让记忆刻入肌理。</p>
        </div>
    </footer>

    <!-- Action Detail Modal -->
    <div id="action-modal" class="fixed inset-0 bg-black bg-opacity-60 z-50 hidden flex items-center justify-center p-4">
        <div id="modal-content" class="bg-white rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-y-auto transform transition-all duration-300 scale-95 opacity-0">
        </div>
    </div>
    
<script>
const APP_DATA = {
  "dictionary": {
    "ACHIEVE": "v. 实现; 取得; 成功",
    "SUCCESS": "n. 成功, 成就; 胜利",
    "CAT": "n. 猫科动物",
    "DOG": "n. 狗",
    "LEARN": "v. 学习; 得知",
    "POWER": "n. 力量; 权力; 能量",
    "FOCUS": "v. 集中; n. 焦点",
    "STRENGTH": "n. 力量; 实力; 强度"
  },
  "办公室拉伸": {
    "icon": "💼",
    "actions": {
      "A": { name: "Ankle Alphabet", cn: "脚踝写字母", scene: "简单活动", desc: "脱鞋后，抬起一只脚，用大脚趾在空中“书写”从A到Z的全部字母。这个动作能有效提升脚踝的灵活性和活动范围。", words: "Ankle, Alphabet, Articulate" },
      "B": { name: "Back Stretch", cn: "背部拉伸", scene: "简单活动", desc: "坐在椅子上或站立，面向书桌，双手扶住桌面，身体后退，下压背部，拉伸整个脊柱和背部肌群。", words: "Back, Bend, Beneficial" },
      "C": { name: "Chest Stretch", cn: "胸部拉伸", scene: "简单活动", desc: "坐直，双手在背后十指相扣，向前挺胸，打开胸腔。此动作旨在对抗因长期伏案导致的“含胸驼背”。", words: "Chest, Correct, Confidence" },
      "D": { name: "Desk Push-up", cn: "书桌俯卧撑", scene: "简单活动", desc: "双手扶住稳固的书桌边缘，身体后退呈倾斜直线，进行俯卧撑动作。激活胸部、肩部和手臂肌肉。", words: "Desk, Dip, Develop" },
      "E": { name: "Ear Massage", cn: "耳朵按摩", scene: "简单活动", desc: "用拇指和食指从上到下轻轻揉捏耳廓，可以刺激与全身相关的穴位，提神醒脑。", words: "Ear, Energy, Ease" },
      "F": { name: "Finger Stretch", cn: "手指拉伸", scene: "简单活动", desc: "将一只手的手指向上或向下掰，用另一只手辅助，拉伸手腕和前臂，缓解长时间握笔或打字带来的疲劳。", words: "Finger, Flexible, Free" },
      "G": { name: "Glute Squeeze", cn: "臀部收缩", scene: "简单活动", desc: "坐在椅子上，用力收紧臀部肌肉，保持5-10秒后放松。此动作可以激活因久坐而“休眠”的臀肌。", words: "Glute, Grip, Generate" },
      "H": { name: "Hip Marching", cn: "坐姿踏步", scene: "简单活动", desc: "坐直，双手扶住椅子，像原地踏步一样交替抬起膝盖，锻炼髋屈肌和下腹部。", words: "Hip, High, Hinge" },
      "I": { name: "Isometric Contraction", cn: "等长收缩", scene: "简单活动", desc: "双手在胸前合十，用力互推，保持肌肉紧张但关节不动，可快速唤醒肌肉力量。", words: "Isometric, Intense, Ignite" },
      "J": { name: "Jaw Release", cn: "下颌放松", scene: "简单活动", desc: "张大嘴巴，然后缓慢闭合，左右移动下颌，放松因精神紧张而紧绷的面部肌肉。", words: "Jaw, Joint, Joy" },
      "K": { name: "Knee Lift", cn: "屈膝上提", scene: "简单活动", desc: "坐姿，双手扶椅，吐气时将一侧膝盖抬向胸口，保持5秒后放下，交替进行，可锻炼下腹部。", words: "Knee, Keep, Kinetic" },
      "L": { name: "Leg Extension", cn: "坐姿伸腿", scene: "简单活动", desc: "坐直，将一条腿向前伸直，与地面平行，保持几秒后放下，锻炼股四头肌。", words: "Leg, Lift, Long" },
      "M": { name: "March in Place", cn: "原地踏步", scene: "简单活动", desc: "利用去接水或上厕所的间隙，进行高抬腿原地踏步，促进全身血液循环。", words: "March, Move, Momentum" },
      "N": { name: "Neck Rotation", cn: "颈部转动", scene: "简单活动", desc: "缓慢地将头部向左、向右转动，然后向前、向后轻仰，拉伸颈部肌肉，缓解颈椎压力。", words: "Neck, Nod, Navigate" },
      "O": { name: "Overhead Stretch", cn: "过顶拉伸", scene: "简单活动", desc: "坐姿或站姿，双手十指相扣，掌心向上举过头顶，身体向上延伸，拉长整个躯干。", words: "Overhead, Open, Optimize" },
      "P": { name: "Palm Press", cn: "掌心按压", scene: "简单活动", desc: "双手合十于胸前，用力互压，保持10秒，可以锻炼胸肌和手臂。", words: "Palm, Press, Power" },
      "Q": { name: "Quad Stretch", cn: "股四头肌拉伸", scene: "简单活动", desc: "站立，扶住椅子或墙壁，将一侧脚跟拉向臀部，感受大腿前侧的拉伸。", words: "Quad, Quiet, Quality" },
      "R": { name: "Reverse Shoulder Fly", cn: "坐姿反向飞鸟", scene: "简单活动", desc: "坐在椅子边缘，上身前倾，背部挺直，双臂在身体两侧抬起，锻炼上背部肌群。", words: "Reverse, Rotate, Release" },
      "S": { name: "Seated March", cn: "坐姿行军", scene: "简单活动", desc: "见 H - Hip Marching。坐直，双手扶住椅子，像原地踏步一样交替抬起膝盖。", words: "Seated, Strong, Steady" },
      "T": { name: "Torso Twist", cn: "躯干扭转", scene: "简单活动", desc: "坐姿，双脚平放，转动上半身向一侧，手可以扶住椅子靠背以加深扭转。", words: "Torso, Twist, Turn" },
      "U": { name: "Upper Back Stretch", cn: "上背部拉伸", scene: "简单活动", desc: "双臂前伸，十指交叉，掌心向外，含胸弓背，感受上背部的拉伸。", words: "Upper, Unfold, Unwind" },
      "V": { name: "Victory Pose", cn: "胜利姿势", scene: "简单活动", desc: "站立，双臂举过头顶呈V字形，同时挺胸抬头，这是一个能提升自信和能量的姿势。", words: "Victory, Value, Vibrant" },
      "W": { name: "Wrist Stretch", cn: "手腕拉伸", scene: "简单活动", desc: "将一只手的手指向上或向下掰，用另一只手辅助，拉伸手腕和前臂。", words: "Wrist, Wave, Widen" },
      "X": { name: "X Stretch", cn: "交叉拉伸", scene: "简单活动", desc: "坐姿，将右脚踝放到左膝上，轻轻下压右膝；或站姿，双腿交叉，身体前屈。", words: "eXtend, eXhale, eXamine" },
      "Y": { name: "Yawn and Stretch", cn: "哈欠伸展", scene: "简单活动", desc: "像刚睡醒一样，打一个大大的哈欠，同时将身体和四肢向外伸展。", words: "Yawn, Yield, Youth" },
      "Z": { name: "Zen Breathing", cn: "禅定呼吸", scene: "简单活动", desc: "闭上眼睛，坐直，将注意力集中在呼吸上，深吸慢呼，进行1-2分钟的腹式呼吸。", words: "Zen, Zone, Zeal" }
    }
  },
  "传统养生": {
    "icon": "☯️",
    "actions": {
      "A": { name: "Acupressure Massage", cn: "穴位按压", scene: "简单活动", desc: "按揉合谷穴（虎口）或足三里穴（膝下），每个穴位1-2分钟，可提神、缓解疲劳。", words: "Acupressure, Alleviate, Accurate" },
      "B": { name: "Bouncing on the Toes", cn: "颠趾", scene: "简单活动", desc: "八段锦第八式。双脚并拢，脚跟提起，身体上提，然后轻轻下落，温和地震动全身。", words: "Bounce, Boost, Balance" },
      "C": { name: "Cloud Hands", cn: "云手", scene: "简单活动", desc: "太极拳核心动作。身体重心左右移动，双手如行云流水般在身前划圆，锻炼协调性。", words: "Cloud, Calm, Coordinate" },
      "D": { name: "Drawing the Bow", cn: "左右开弓", scene: "简单活动", desc: "八段锦第二式。马步站立，向两侧做出拉弓射箭的姿势，能开阔胸襟。", words: "Draw, Develop, Determine" },
      "E": { name: "Embracing the Tree", cn: "站桩", scene: "简单/集中", desc: "双脚与肩同宽，膝盖微屈，双手在胸前环抱，如抱大树。可静心养气。", words: "Embrace, Endurance, Energy" },
      "F": { name: "Fist Clenching", cn: "攒拳怒目", scene: "简单活动", desc: "八段锦第七式。马步站立，一拳缓慢有力地击出，同时眼睛注视前方，可增强气力。", words: "Fist, Focus, Force" },
      "G": { name: "Grasping the Bird's Tail", cn: "揽雀尾", scene: "集中训练", desc: "太极拳核心招式。包含“掤、捋、挤、按”四个动作，完整练习需要较大空间和时间。", words: "Grasp, Graceful, Gentle" },
      "H": { name: "Holding up the Heavens", cn: "双手托天", scene: "简单活动", desc: "八段锦第一式。双手上托，拉伸全身，调理三焦。", words: "Hold, Heaven, Harmony" },
      "I": { name: "Internal Organs Rub", cn: "摩腹", scene: "简单活动", desc: "将手掌置于腹部，围绕肚脐顺时针和逆时针缓慢画圈按摩，有助于消化。", words: "Internal, Improve, Inner" },
      "J": { name: "Joint Rolling", cn: "摇关节", scene: "简单活动", desc: "依次缓慢、轻柔地转动脚踝、膝盖、髋部、手腕、肘部和肩部关节。", words: "Joint, Joyful, Jubilant" },
      "K": { name: "Knocking the Teeth", cn: "叩齿", scene: "简单活动", desc: "上下牙齿轻轻叩击36次，可固齿健肾，是古代养生术之一。", words: "Knock, Keep, Knowledge" },
      "L": { name: "Lower Back Rub", cn: "摩肾俞", scene: "简单活动", desc: "双手搓热，用手掌在后腰上下摩擦，直至发热，可补肾气，缓解腰酸。", words: "Lower, Lumbar, Longevity" },
      "M": { name: "Monkey, Repulse the", cn: "倒卷肱", scene: "集中训练", desc: "太极拳招式。边后退边出手，形如驱猴。锻炼身体的协调与退守能力。", words: "Monkey, Movement, Mindful" },
      "N": { name: "Needle at Sea Bottom", cn: "海底针", scene: "集中训练", desc: "太极拳招式。身体下蹲，一指向下点出，如同探海底之针。", words: "Needle, Navigate, Nimble" },
      "O": { name: "Opening the Chest", cn: "开阔胸襟", scene: "简单活动", desc: "气功动作。双臂在胸前交叉，吸气时向两侧打开，挺胸抬头，呼气时收回。", words: "Open, Optimistic, Opportunity" },
      "P": { name: "Parting the Wild Horse's Mane", cn: "野马分鬃", scene: "集中训练", desc: "太极拳招式。弓步，手臂一上一下分开，姿态开阔。", words: "Part, Power, Persevere" },
      "Q": { name: "Qi Gathering", cn: "收气", scene: "简单活动", desc: "动作结束时，双手从两侧向上画圆，在头顶汇合，然后缓慢下按至腹部丹田处。", words: "Qi, Quiet, Quality" },
      "R": { name: "Rooster Stands on One Leg", cn: "金鸡独立", scene: "简单/集中", desc: "太极拳招式。一腿站立，另一腿膝盖抬起。可作为简单的平衡练习。", words: "Rooster, Resilience, Reliable" },
      "S": { name: "Separating Heaven and Earth", cn: "调理脾胃", scene: "简单活动", desc: "八段锦第三式。一手向上托，一手向下按，交替进行，拉伸躯干两侧。", words: "Separate, Stability, Strengthen" },
      "T": { name: "Touching the Toes", cn: "攀足固肾", scene: "简单活动", desc: "八段锦第六式。身体前屈，双手尽量触摸脚尖，可拉伸背部和腿部。", words: "Touch, Toughness, Target" },
      "U": { name: "Upholding the Sun", cn: "托日", scene: "简单活动", desc: "气功动作。双手如捧物，从下方缓慢上托至头顶，仿佛托起太阳。", words: "Uphold, Uplift, Unify" },
      "V": { name: "Vibrating the Body", cn: "抖动", scene: "简单活动", desc: "全身放松站立，带动全身进行快速而小幅度的抖动，有助于放松。", words: "Vibrate, Vitality, Vigorous" },
      "W": { name: "Wise Owl Gazes Backwards", cn: "五劳七伤往后瞧", scene: "简单活动", desc: "八段锦第四式。头部缓慢向后转动，注视后方，可缓解颈肩疲劳。", words: "Wise, Wellness, Wisdom" },
      "X": { name: "X Crossing Arms", cn: "十字手", scene: "简单活动", desc: "太极拳收式。双臂在胸前交叉，然后缓缓下落，有平衡气血的作用。", words: "eXcellent, eXamine, eXchange" },
      "Y": { name: "Yin Yang Harmony", cn: "阴阳调和", scene: "简单活动", desc: "想象身体左右两侧、上下内外达到平衡。可配合“云手”等动作进行冥想。", words: "Yin, Yang, Yearning" },
      "Z": { name: "Zigzag Palm Pushing", cn: "左右推掌", scene: "简单活动", desc: "气功动作。身体重心左右移动，交替向斜前方推出手掌。", words: "Zigzag, Zest, Zenith" }
    }
  },
  "健身力量": {
    "icon": "💪",
    "actions": {
        "A": { name: "Abdominal Crunch", cn: "卷腹", scene: "简单活动", desc: "仰卧，双腿弯曲，双手置于耳旁或胸前，上背部抬离地面。", words: "Abdominal, Achieve, Ability" },
        "B": { name: "Burpee", cn: "波比跳", scene: "集中训练", desc: "一个高强度复合动作：下蹲、后踢腿成俯卧撑姿势、俯卧撑、收腿、跳跃。", words: "Burpee, Breakthrough, Build" },
        "C": { name: "Calf Raise", cn: "提踵", scene: "简单活动", desc: "站立，双脚缓慢抬起脚跟，用脚尖支撑，在最高点停留后缓慢下落。", words: "Calf, Challenge, Concentrate" },
        "D": { name: "Deadlift", cn: "硬拉", scene: "集中训练", desc: "使用杠铃或哑铃，从地面将重量提起至身体直立。", words: "Deadlift, Dedication, Durable" },
        "E": { name: "Elbow Plank", cn: "肘部平板支撑", scene: "简单活动", desc: "以肘部和脚尖支撑身体，保持身体呈一条直线。", words: "Elbow, Effective, Elevate" },
        "F": { name: "Front Raise", cn: "前平举", scene: "集中训练", desc: "手持哑铃或弹力带，手臂伸直从身前抬起至与肩同高。", words: "Front, Foundation, Future" },
        "G": { name: "Glute Bridge", cn: "臀桥", scene: "简单活动", desc: "仰卧，双脚踩地，将臀部向上抬起，使身体从肩膀到膝盖呈一条直线。", words: "Glute, Goal, Growth" },
        "H": { name: "High Knees", cn: "高抬腿", scene: "简单活动", desc: "原地快速交替抬高膝盖，尽可能抬至髋部高度。", words: "High, Honor, Hustle" },
        "I": { name: "Inchworm", cn: "英寸虫", scene: "简单活动", desc: "站立，身体前屈，双手触地，然后双手向前爬行至平板支撑位，再走回。", words: "Inchworm, Initiative, Integrate" },
        "J": { name: "Jumping Jacks", cn: "开合跳", scene: "简单活动", desc: "跳跃的同时双脚向外打开，双臂举过头顶，再跳回起始位置。", words: "Jumping, Join, Justify" },
        "K": { name: "Kettlebell Swing", cn: "壶铃摇摆", scene: "集中训练", desc: "使用壶铃，通过髋部爆发力将壶铃向前甩起至胸部高度。", words: "Kettlebell, Key, Kinetic" },
        "L": { name: "Lunge", cn: "弓步", scene: "简单/集中", desc: "向前迈出一大步，身体下蹲，双膝呈90度。可自重进行，也可负重。", words: "Lunge, Leap, Leadership" },
        "M": { name: "Mountain Climber", cn: "登山者", scene: "简单活动", desc: "在平板支撑姿势下，交替将膝盖提向胸口。", words: "Mountain, Momentum, Motivate" },
        "N": { name: "Negative Push-up", cn: "离心俯卧撑", scene: "简单活动", desc: "从平板支撑位，用3-5秒时间缓慢将身体下降至地面。", words: "Negative, Negate, Nurture" },
        "O": { name: "Overhead Press", cn: "过顶推举", scene: "集中训练", desc: "将杠铃或哑铃从肩部位置向上推举至手臂完全伸直。", words: "Overhead, Overcome, Opportunity" },
        "P": { name: "Push-up", cn: "俯卧撑", scene: "简单活动", desc: "经典的自重训练动作，保持身体成直线，屈肘使身体下降再推起。", words: "Push-up, Potential, Progress" },
        "Q": { name: "Quick Feet", cn: "快速小碎步", scene: "简单活动", desc: "原地快速地进行小碎步跑，保持低重心，提高反应速度。", words: "Quick, Qualify, Question" },
        "R": { name: "Rowing", cn: "划船", scene: "集中训练", desc: "使用划船机或杠铃/哑铃进行划船动作，锻炼整个背部肌群。", words: "Rowing, Reinforce, Resolve" },
        "S": { name: "Squat", cn: "深蹲", scene: "简单/集中", desc: "身体下蹲至大腿与地面平行或更低，然后站起。可自重或负重。", words: "Squat, Strength, Success" },
        "T": { name: "Triceps Dip", cn: "臂屈伸", scene: "简单/集中", desc: "利用椅子或双杠，背对支撑物，双手支撑，身体下降再撑起。", words: "Triceps, Triumph, Transform" },
        "U": { name: "Upright Row", cn: "直立划船", scene: "集中训练", desc: "手持杠铃或哑铃，沿身体向上提拉至下巴高度。", words: "Upright, Unique, Ultimate" },
        "V": { name: "V-up", cn: "V字起身", scene: "简单活动", desc: "仰卧，同时抬起上身和双腿，身体呈V字形，用手触摸脚尖。", words: "V-up, Value, Victory" },
        "W": { name: "Wall Sit", cn: "靠墙静蹲", scene: "简单活动", desc: "背靠墙壁，身体下蹲至膝盖呈90度，保持姿势。", words: "Wall, Willpower, Worthy" },
        "X": { name: "X-Jumps", cn: "X型跳", scene: "简单活动", desc: "从深蹲姿势起跳，空中身体和四肢展开呈X形。", words: "X-Jumps, eXplode, eXcel" },
        "Y": { name: "Y-Raise", cn: "Y字上举", scene: "简单活动", desc: "俯身，手臂向前上方伸展呈Y字形，并向上抬起。", words: "Y-Raise, Yearn, Youth" },
        "Z": { name: "Zigzag Hops", cn: "Z字跳", scene: "简单活动", desc: "双脚并拢，以Z字形路线向前或向后连续跳跃。", words: "Zigzag, Zeal, Zest" }
    }
  },
  "篮球技巧": {
    "icon": "�",
    "actions": {
      "A": { name: "Arm Fake", cn: "手臂假动作", scene: "简单/集中", desc: "不移动脚步，用持球手臂做出投篮或传球的假动作，迷惑防守者。", words: "Arm, Artful, Adapt" },
      "B": { name: "Box Out", cn: "卡位抢篮板", scene: "集中训练", desc: "投篮后，转身用背部和臀部将对手挡在身后，占据有利的抢篮板位置。", words: "Box, Battle, Boundary" },
      "C": { name: "Crossover Dribble", cn: "交叉步运球", scene: "简单/集中", desc: "快速将球从一只手运到另一只手，通常伴随身体重心的变化，是突破防守的基础动作。", words: "Crossover, Change, Clever" },
      "D": { name: "Defensive Stance", cn: "防守姿势", scene: "简单活动", desc: "屈膝，降低重心，背部挺直，张开双臂，随时准备滑步移动。", words: "Defensive, Defend, Discipline" },
      "E": { name: "Euro Step", cn: "欧洲步", scene: "集中训练", desc: "上篮时的一种步法，向一个方向迈出第一步，然后迅速向另一个方向迈出第二步上篮。", words: "Euro, Evasive, Execute" },
      "F": { name: "Free Throw Routine", cn: "罚球流程", scene: "集中训练", desc: "练习一套固定的罚球准备动作，如深呼吸、运球三次、瞄准篮筐，培养肌肉记忆。", words: "Free, Focus, Flawless" },
      "G": { name: "Give and Go", cn: "传切配合", scene: "集中训练", desc: "将球传给队友后，立即空手切入篮下，准备接回传球上篮。", words: "Give, Go, Generate" },
      "H": { name: "Hesitation Dribble", cn: "迟疑步运球", scene: "简单/集中", desc: "运球时突然减速并直起身体，做出要停球或投篮的假象，然后突然加速突破。", words: "Hesitate, Hustle, Handle" },
      "I": { name: "In-and-Out Dribble", cn: "内外运球", scene: "简单/集中", desc: "运球手做一个向内再向外的快速晃动，迷惑防守者以为你要交叉步。", words: "In, Out, Illusion" },
      "J": { name: "Jump Shot", cn: "跳投", scene: "集中训练", desc: "跳起在空中最高点出手投篮，是主要的得分方式。", words: "Jump, Judgment, Justify" },
      "K": { name: "Kyrie Irving Mikan Drill", cn: "欧文式麦肯训练", scene: "集中训练", desc: "在篮下左右交替进行反手或各种手法的上篮练习，提升终结能力。", words: "Kyrie, Keen, Kinetic" },
      "L": { name: "Layup", cn: "上篮", scene: "集中训练", desc: "跑向篮筐，用单手将球送入篮筐的最基本得分方式。", words: "Layup, Launch, Lift" },
      "M": { name: "Mikan Drill", cn: "麦肯训练", scene: "集中训练", desc: "经典的篮下训练，左右交替连续上篮，锻炼手感和篮下脚步。", words: "Mikan, Master, Methodical" },
      "N": { name: "No-Look Pass", cn: "不看人传球", scene: "集中训练", desc: "眼睛看着一个方向，却将球传向另一个方向的队友，极具创造性。", words: "No-Look, Notable, Novel" },
      "O": { name: "Outlet Pass", cn: "长传快攻", scene: "集中训练", desc: "抢到后场篮板后，迅速将球长传给已跑向前场的队友，发动快速反击。", words: "Outlet, Open, Outstanding" },
      "P": { name: "Pivot", cn: "中枢脚", scene: "简单/集中", desc: "以一只脚为轴心脚保持不动，另一只脚可以向任意方向移动。", words: "Pivot, Position, Patient" },
      "Q": { name: "Quick Release", cn: "快速出手", scene: "集中训练", desc: "练习接球后迅速调整并完成投篮动作，减少被封盖的几率。", words: "Quick, Quiet, Quality" },
      "R": { name: "Rebound", cn: "篮板球", scene: "集中训练", desc: "在投篮不中后，抢夺球权的动作。", words: "Rebound, Recover, React" },
      "S": { name: "Screen/Pick", cn: "掩护/挡拆", scene: "集中训练", desc: "为持球队友设立一道“墙”，阻挡防守者，创造进攻机会。", words: "Screen, Strategy, Support" },
      "T": { name: "Triple Threat Position", cn: "三威胁姿势", scene: "简单活动", desc: "接球后，身体侧对篮筐，双膝微屈，此时可以投篮、传球或突破。", words: "Triple, Threat, Tactical" },
      "U": { name: "Up-and-Under Move", cn: "高低手上篮", scene: "集中训练", desc: "持球向篮下做投篮假动作，骗取防守者跳起后，再从其下方上篮。", words: "Up, Under, Unpredictable" },
      "V": { name: "V-Cut", cn: "V字形切入", scene: "集中训练", desc: "为了摆脱防守，先向篮筐方向移动，再突然反向切出接球，跑动路线呈V字形。", words: "V-Cut, Vacant, Versatile" },
      "W": { name: "Wing to Wing Pass", cn: "两侧翼传球", scene: "集中训练", desc: "在进攻阵型中，将球从一侧的45度角（侧翼）快速转移到另一侧侧翼。", words: "Wing, Wide, Weave" },
      "X": { name: "X-Out Layup", cn: "X型上篮训练", scene: "集中训练", desc: "从一侧底角运球至另一侧罚球线肘区，然后切入上篮，路线呈X型。", words: "X-Out, eXplosive, eXecution" },
      "Y": { name: "'Y' Passing Drill", cn: "Y字传球训练", scene: "集中训练", desc: "三名球员呈Y字形站位，进行连续的传球和跑位练习。", words: "Yield, Youthful, Yearn" },
      "Z": { name: "Zig-Zag Dribble", cn: "Z字形运球训练", scene: "集中训练", desc: "在球场上以Z字形路线进行运球，同时练习变向、变速等技巧。", words: "Zig-Zag, Zone, Zest" }
    }
  },
  "足球技巧": {
    "icon": "⚽",
    "actions": {
        "A": { name: "Altering the Angle Pass", cn: "变向传球", scene: "集中训练", desc: "接球后不停球，顺势将球向侧方拨动一步再传出，改变进攻角度。", words: "Alter, Agile, Attack" },
        "B": { name: "Bicycle Kick", cn: "倒挂金钩", scene: "集中训练", desc: "背对球门，身体腾空后仰，用脚将过顶的球踢向球门。", words: "Bicycle, Bold, Brilliant" },
        "C": { name: "Chip Shot", cn: "挑射", scene: "集中训练", desc: "用脚尖轻巧地将球挑起，使其越过守门员头顶落入球门。", words: "Chip, Clever, Calculate" },
        "D": { name: "Dribbling", cn: "盘带", scene: "简单/集中", desc: "用脚控制球向前移动，是足球最基本的技能之一。", words: "Dribble, Drive, Dynamic" },
        "E": { name: "Elastic/Flip-Flap", cn: "牛尾巴过人", scene: "集中训练", desc: "用脚外侧将球拨向一侧，再迅速用脚内侧扣回，完成变向过人。", words: "Elastic, Elegant, Elude" },
        "F": { name: "Flick-on Header", cn: "头球后蹭", scene: "集中训练", desc: "不正对来球方向，用头部侧面轻轻一蹭，改变球路传给队友。", words: "Flick, Finesse, Forward" },
        "G": { name: "Give and Go/One-Two", cn: "二过一配合", scene: "集中训练", desc: "将球传给队友后，自己迅速前插，再接队友的回传球。", words: "Give, Go, Generate" },
        "H": { name: "Half Volley", cn: "半凌空抽射", scene: "集中训练", desc: "在球落地反弹的瞬间，用脚抽射，时机把握要求极高。", words: "Half, Hasty, Hit" },
        "I": { name: "Inside-of-the-foot Pass", cn: "脚内侧传球", scene: "简单/集中", desc: "使用脚弓部位进行传球，是最常用、最精准的短传方式。", words: "Inside, Intent, Initiate" },
        "J": { name: "Juggling/Keepie-uppie", cn: "颠球", scene: "简单/集中", desc: "用脚、大腿、头等部位连续颠球，使其不落地，是锻炼球感的基础。", words: "Juggle, Joyful, Judgment" },
        "K": { name: "Knuckleball Shot", cn: "电梯球射门", scene: "集中训练", desc: "射门时击打球的中心偏下位置，使球在空中不旋转，产生飘忽不定的轨迹。", words: "Knuckleball, Knock, Kinetic" },
        "L": { name: "Laces Shot", cn: "正脚背抽射", scene: "集中训练", desc: "使用鞋带区域（正脚背）大力射门，是力量最大的射门方式。", words: "Laces, Launch, Lethal" },
        "M": { name: "Marseille Turn/Roulette", cn: "马赛回旋", scene: "集中训练", desc: "一脚踩球向后拉，同时转身，再用另一只脚将球向前拨，完成360度人球分过。", words: "Marseille, Magical, Maneuver" },
        "N": { name: "Nutmeg/Panna", cn: "穿裆过人", scene: "集中训练", desc: "将球从防守队员张开的两腿之间踢过，然后人球分过。", words: "Nutmeg, Naughty, Narrow" },
        "O": { name: "Outside-of-the-foot Pass", cn: "脚外侧传球", scene: "集中训练", desc: "使用脚的外侧进行传球或射门，可以产生意想不到的弧线。", words: "Outside, Original, Opportunity" },
        "P": { name: "Penalty Kick", cn: "点球", scene: "集中训练", desc: "在十二码点进行的射门，考验射手的技术和心理素质。", words: "Penalty, Pressure, Precision" },
        "Q": { name: "Quick Throw-in", cn: "快速掷界外球", scene: "集中训练", desc: "快速捡起出界的球并掷给队友，抓住对方防守未稳的机会。", words: "Quick, Quietly, Quest" },
        "R": { name: "Rabona", cn: "插花脚", scene: "集中训练", desc: "支撑脚站稳，另一只脚从支撑脚后方绕过，用脚外侧传球或射门。", words: "Rabona, Rare, Radiant" },
        "S": { name: "Step Over", cn: "踩单车", scene: "简单/集中", desc: "一只脚从球上方跨过，做出要向一侧突破的假象，再用另一只脚向反方向拨球。", words: "Step, Subtle, Strategy" },
        "T": { name: "Tackle", cn: "铲球/抢断", scene: "集中训练", desc: "防守时用脚将对方脚下的球断下，分为站立抢断和滑铲。", words: "Tackle, Timing, Tough" },
        "U": { name: "Upper 90 Shot", cn: "射上角", scene: "集中训练", desc: "将球射入球门两个上方的死角，是最难扑救的射门。", words: "Upper, Unstoppable, Ultimate" },
        "V": { name: "Volley", cn: "凌空射门", scene: "集中训练", desc: "在球未落地之前，直接起脚射门。", words: "Volley, Victorious, Volatile" },
        "W": { name: "Wing Play", cn: "边路进攻", scene: "集中训练", desc: "利用球场两侧的宽度进行突破和传中。", words: "Wing, Width, Weapon" },
        "X": { name: "'X' Pattern Passing Drill", cn: "X型传球训练", scene: "集中训练", desc: "四名球员呈正方形站位，沿对角线进行X型传球练习。", words: "eXchange, eXpertise, eXecute" },
        "Y": { name: "'Y' Dribbling Drill", cn: "Y型盘带训练", scene: "集中训练", desc: "摆放三个标志物呈Y字形，练习向不同方向的变向盘带。", words: "Yield, Yard, Youthful" },
        "Z": { name: "Zonal Marking", cn: "区域防守", scene: "集中训练", desc: "防守时每名球员负责自己的一块区域，而不是紧盯某一个特定对手。", words: "Zonal, Zealous, Zone" }
    }
  },
  "瑜伽柔韧": {
    "icon": "🧘‍♀️",
    "actions": {
        "A": { name: "Ankle Stretch", cn: "脚踝拉伸", scene: "简单活动", desc: "坐姿或跪姿，拉伸脚背和脚踝，促进下肢循环。", words: "Ankle, Awaken, Align" },
        "B": { name: "Boat Pose", cn: "船式", scene: "简单/集中", desc: "坐姿，抬起双腿和上身，身体呈V形。可简化（屈膝）或加强（伸直腿）。", words: "Boat, Balance, Build" },
        "C": { name: "Cat-Cow Pose", cn: "猫牛式", scene: "简单/集中", desc: "吸气时抬头塌腰（牛式），呼气时含胸弓背（猫式）。可在垫上或椅子上做。", words: "Cat, Cow, Cycle" },
        "D": { name: "Downward-Facing Dog", cn: "下犬式", scene: "集中训练", desc: "身体呈倒V形，是经典的全身拉伸和力量体式。", words: "Downward, Dog, Dynamic" },
        "E": { name: "Eagle Pose", cn: "鸟王式", scene: "简单/集中", desc: "站立，双腿和双臂相互缠绕。可简化为坐姿，只做手臂缠绕。", words: "Eagle, Equilibrium, Embrace" },
        "F": { name: "Forward Bend", cn: "前屈式", scene: "简单/集中", desc: "站立或坐姿，身体前屈，拉伸整个背部和腿后侧。", words: "Forward, Fold, Flexibility" },
        "G": { name: "Garland Pose", cn: "花环式", scene: "简单/集中", desc: "瑜伽深蹲，双脚外开，手肘抵住膝盖内侧。可借助椅子辅助。", words: "Garland, Ground, Gratitude" },
        "H": { name: "Happy Baby Pose", cn: "快乐婴儿式", scene: "集中训练", desc: "仰卧，抓住脚掌外侧，将膝盖拉向腋窝，放松髋部和下背部。", words: "Happy, Healing, Hold" },
        "I": { name: "Inversion (Legs-Up-the-Wall)", cn: "倒箭式", scene: "简单/集中", desc: "将双腿靠在墙上，身体仰卧。极佳的放松和恢复体式。", words: "Inversion, Inner, Inhale" },
        "J": { name: "Janu Sirsasana", cn: "头碰膝前屈式", scene: "集中训练", desc: "坐姿，一腿伸直，另一腿弯曲，身体向伸直腿前屈。", words: "Journey, Join, Joy" },
        "K": { name: "Kneeling Pose", cn: "跪姿", scene: "简单活动", desc: "跪坐姿势，臀部坐在脚跟上，有助于消化和静心。", words: "Kneel, Kindness, Keep" },
        "L": { name: "Low Lunge", cn: "低弓步", scene: "简单/集中", desc: "低弓步，拉伸髋屈肌和大腿前侧。", words: "Low, Lunge, Length" },
        "M": { name: "Mountain Pose", cn: "山式", scene: "简单活动", desc: "站姿基础，身体挺拔，感受稳定和力量。", words: "Mountain, Majesty, Mindful" },
        "N": { name: "Needle and Thread Pose", cn: "穿针引线式", scene: "集中训练", desc: "从四足跪姿开始，将一侧手臂从另一侧腋下穿过，扭转上背部。", words: "Needle, Nourish, Nurture" },
        "O": { name: "One-Legged King Pigeon Pose", cn: "单腿鸽王式", scene: "集中训练", desc: "鸽子式，深度打开髋部，对柔韧性要求高。", words: "One, Opening, Optimal" },
        "P": { name: "Plank Pose", cn: "平板式", scene: "简单/集中", desc: "平板支撑，锻炼全身核心力量。", words: "Plank, Power, Patience" },
        "Q": { name: "Queen Pose", cn: "女王式", scene: "简单活动", desc: "坐姿，双腿交叉，一手置于膝上，一手向上伸展，模拟女王的优雅姿态。", words: "Queen, Quietude, Quality" },
        "R": { name: "Reverse Warrior", cn: "反战士式", scene: "集中训练", desc: "战士二式的变体，身体向后腿侧弯，拉伸侧腰。", words: "Reverse, Radiate, Release" },
        "S": { name: "Seated Spinal Twist", cn: "坐姿扭转", scene: "简单/集中", desc: "坐姿扭转，可有效灵活脊柱，按摩腹部器官。", words: "Spinal, Serenity, Soothe" },
        "T": { name: "Tree Pose", cn: "树式", scene: "简单/集中", desc: "经典的站立平衡体式，可扶墙或椅子辅助。", words: "Tree, Tranquility, Trust" },
        "U": { name: "Upward-Facing Dog", cn: "上犬式", scene: "集中训练", desc: "俯卧，用手臂撑起上身，打开胸腔，是拜日式中的一环。", words: "Upward, Unfold, Uplift" },
        "V": { name: "Warrior I/II/III Pose", cn: "战士一/二/三式", scene: "集中训练", desc: "建立力量、稳定和专注的系列体式。", words: "Warrior, Victory, Valor" },
        "W": { name: "Wheel Pose", cn: "轮式", scene: "集中训练", desc: "深度后弯，需要强大的背部和手臂力量。", words: "Wheel, Whole, Wonder" },
        "X": { name: "X Pose (Supine)", cn: "大字伸展式", scene: "简单活动", desc: "仰卧，身体和四肢向外伸展成大X形，进行全身的放松伸展。", words: "eXpand, eXhale, eXplore" },
        "Y": { name: "Yogi Squat", cn: "瑜伽蹲", scene: "简单/集中", desc: "见 G - Garland Pose。双脚外开，手肘抵住膝盖内侧。", words: "Yogi, Yield, Yearn" },
        "Z": { name: "Z-Pose", cn: "Z字坐", scene: "简单活动", desc: "坐姿，一腿内旋一腿外旋，膝盖弯曲，身体呈Z字形，可进行多种拉伸和扭转。", words: "Zen, Zest, Zone" }
    }
  },
  "眼部保健": {
    "icon": "👁️",
    "actions": {
      "第1节": { name: "Massaging Zǎnzhú Point", cn: "按揉攒竹穴", scene: "简单活动", desc: "定位：眉毛内侧边缘的凹陷处。手法：用双手大拇指的螺纹面按揉，每拍一圈，共四个八拍。", words: "Zone, Zenith, Zeal" },
      "第2节": { name: "Pressing Jīngmíng Point", cn: "按压睛明穴", scene: "简单活动", desc: "定位：内眼角稍上方，鼻梁两侧的凹陷处。手法：用双手食指的螺纹面进行有节奏的上下按压，共四个八拍。", words: "Point, Press, Purity" },
      "第3节": { name: "Massaging Sìbái Point", cn: "按揉四白穴", scene: "简单活动", desc: "定位：瞳孔正下方，颧骨的凹陷处。手法：用食指螺纹面按揉穴位，每拍一圈，共四个八拍。", words: "Sight, Soothe, Shine" },
      "第4节": { name: "Massaging Tàiyáng & Scraping", cn: "按揉太阳穴与刮上眼眶", scene: "简单活动", desc: "先用大拇指按揉太阳穴，再用食指关节内侧沿眉骨下方刮至眉梢。交替进行，共四个八拍。", words: "Temple, Touch, Tranquil" },
      "第5节": { name: "Dynamic Eye Rolling", cn: "眼球动态转动", scene: "简单活动", desc: "保持头部不动，闭眼，眼球先顺时针缓慢转动10圈，再逆时针转动10圈。", words: "Dynamic, Direction, Diligence" },
      "第6节": { name: "Palming", cn: "掌心热敷法", scene: "简单活动", desc: "双手搓热，用温热的掌心轻轻覆盖在闭合的眼睛上1-2分钟，深度放松视神经。", words: "Palm, Peace, Patient" }
    }
  }
};

document.addEventListener('DOMContentLoaded', () => {
    // Element selections
    const categoryFiltersContainer = document.getElementById('category-filters');
    const sceneFiltersContainer = document.getElementById('scene-filters');
    const actionGrid = document.getElementById('action-grid');
    const noResults = document.getElementById('no-results');
    const modal = document.getElementById('action-modal');
    const modalContent = document.getElementById('modal-content');
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');
    const generateResetBtn = document.getElementById('generate-reset-plan');
    const resetPlanContainer = document.getElementById('reset-plan-container');
    const decomposerCategorySelect = document.getElementById('decomposer-category-select');
    const wordInput = document.getElementById('word-input');
    const decomposeBtn = document.getElementById('decompose-word-btn');
    const decomposerResultsContainer = document.getElementById('decomposer-results-container');
    const decomposerPlaceholder = document.getElementById('decomposer-placeholder');
    const decomposerResults = document.getElementById('decomposer-results');

    // State
    let currentCategory = Object.keys(APP_DATA).filter(k => k !== 'dictionary')[0];
    let currentScene = 'all';
    let myChart;

    function init() {
        populateCategorySelect();
        renderCategoryFilters();
        renderActions();
        renderChart();
        setupEventListeners();
        setActiveFilters();
    }

    function populateCategorySelect() {
        let optionsHTML = '';
        for (const category in APP_DATA) {
            if (category === 'dictionary') continue;
            optionsHTML += `<option value="${category}">${APP_DATA[category].icon} ${category}</option>`;
        }
        decomposerCategorySelect.innerHTML = optionsHTML;
    }

    function renderCategoryFilters() {
        let buttonsHTML = '';
        for (const category in APP_DATA) {
            if (category === 'dictionary') continue;
            buttonsHTML += `
                <button data-category="${category}" class="category-filter-btn flex flex-col items-center justify-center p-3 rounded-lg shadow-sm text-sm font-medium transition duration-200 border-2 border-transparent">
                    <span class="text-3xl mb-1">${APP_DATA[category].icon}</span>
                    <span class="text-deep-charcoal">${category}</span>
                </button>
            `;
        }
        categoryFiltersContainer.innerHTML = buttonsHTML;
    }

    function renderActions() {
        actionGrid.innerHTML = '';
        const categoryData = APP_DATA[currentCategory].actions;
        let hasResults = false;
        let delay = 0;

        for (const letter in categoryData) {
            const action = categoryData[letter];
            const sceneMatch = currentScene === 'all' || action.scene.includes(currentScene);

            if (sceneMatch) {
                hasResults = true;
                const card = document.createElement('div');
                card.className = 'bg-white p-3 rounded-xl shadow-md hover:shadow-xl hover:-translate-y-1 transition transform duration-300 cursor-pointer flex flex-col items-center justify-center text-center border border-gray-200 card-enter';
                card.style.animationDelay = `${delay}ms`;
                delay += 25;
                card.dataset.category = currentCategory;
                card.dataset.letter = letter;
                card.innerHTML = `
                    <div class="w-10 h-10 flex items-center justify-center bg-warm-beige rounded-full text-lg font-bold accent-burnt-sienna-text mb-2">${letter.startsWith('第') ? letter.slice(1,2) : letter}</div>
                    <p class="font-semibold text-sm text-deep-charcoal leading-tight">${action.cn}</p>
                    <p class="text-xs text-muted-olive">${action.name}</p>
                `;
                actionGrid.appendChild(card);
            }
        }
        noResults.classList.toggle('hidden', hasResults);
    }
    
    function setActiveFilters() {
        document.querySelectorAll('.category-filter-btn').forEach(btn => {
            if (btn.dataset.category === currentCategory) {
                btn.classList.add('accent-burnt-sienna-border', 'bg-warm-beige', 'shadow-inner');
                btn.classList.remove('bg-white');
            } else {
                btn.classList.remove('accent-burnt-sienna-border', 'bg-warm-beige', 'shadow-inner');
                 btn.classList.add('bg-white');
            }
        });

        document.querySelectorAll('.scene-filter-btn').forEach(btn => {
            if (btn.dataset.scene === currentScene) {
                btn.classList.add('accent-burnt-sienna', 'text-white', 'shadow-md');
                btn.classList.remove('text-muted-olive');
            } else {
                btn.classList.remove('accent-burnt-sienna', 'text-white', 'shadow-md');
                btn.classList.add('text-muted-olive');
            }
        });
    }

    function openModal(category, letter) {
        const action = APP_DATA[category].actions[letter];
        let visualHTML = '';
        if (category === '眼部保健') {
             visualHTML = getEyeExerciseVisual(letter);
        }

        modalContent.innerHTML = `
            <div class="p-6 sm:p-8">
                <div class="flex justify-between items-start mb-4">
                    <div>
                        <p class="text-xs font-semibold uppercase tracking-wider accent-burnt-sienna-text">${category} / ${letter}</p>
                        <h3 class="text-2xl sm:text-3xl font-bold text-deep-charcoal">${action.cn}</h3>
                        <p class="text-base text-muted-olive">${action.name}</p>
                    </div>
                    <button id="close-modal-btn" class="p-2 -mr-2 -mt-2 text-gray-400 hover:text-gray-700 transition rounded-full">&times;</button>
                </div>
                <div class="space-y-6">
                    <div>
                        <h4 class="font-semibold mb-2 text-deep-charcoal">场景分类</h4>
                        <span class="inline-block bg-stone-olive text-muted-olive text-sm font-medium mr-2 px-3 py-1 rounded-full">${action.scene}</span>
                    </div>
                    ${visualHTML ? `<div><h4 class="font-semibold mb-2 text-deep-charcoal">穴位示意</h4>${visualHTML}</div>` : ''}
                    <div>
                        <h4 class="font-semibold mb-2 text-deep-charcoal">动作描述</h4>
                        <p class="text-base text-deep-charcoal leading-relaxed">${action.desc}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2 text-deep-charcoal">示例单词联想</h4>
                        <div class="flex flex-wrap gap-2">
                            ${action.words.split(', ').map(word => `<span class="bg-warm-beige text-deep-charcoal text-sm font-medium px-3 py-1 rounded-full">${word}</span>`).join('')}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        modal.classList.remove('hidden');
        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);

        document.getElementById('close-modal-btn').onclick = closeModal;
    }

    function getEyeExerciseVisual(letter) {
        const baseFace = `<div class="w-40 h-48 mx-auto border-2 border-gray-300 rounded-full relative bg-gray-50 flex items-center justify-center">
                            <div class="absolute w-8 h-2 top-12 left-1/2 -translate-x-1/2 border-t-2 border-gray-400 rounded-full"></div>
                            <div class="absolute w-8 h-2 top-12 left-1/2 -translate-x-1/2 border-t-2 border-gray-400 rounded-full transform scale-x-[-1]"></div>
                            <div class="absolute w-3 h-3 bg-gray-400 rounded-full top-20 left-12"></div>
                            <div class="absolute w-3 h-3 bg-gray-400 rounded-full top-20 right-12"></div>
                            <div class="absolute w-12 h-1 border-b-2 border-gray-400 rounded-full top-28 left-1/2 -translate-x-1/2"></div>`;
        
        let points = '';
        switch(letter) {
            case '第1节': points = `<div class="absolute w-3 h-3 bg-red-500 rounded-full top-16 left-16 animate-pulse"></div><div class="absolute w-3 h-3 bg-red-500 rounded-full top-16 right-16 animate-pulse"></div>`; break;
            case '第2节': points = `<div class="absolute w-3 h-3 bg-red-500 rounded-full top-20 left-[4.5rem] animate-pulse"></div><div class="absolute w-3 h-3 bg-red-500 rounded-full top-20 right-[4.5rem] animate-pulse"></div>`; break;
            case '第3节': points = `<div class="absolute w-3 h-3 bg-red-500 rounded-full top-24 left-16 animate-pulse"></div><div class="absolute w-3 h-3 bg-red-500 rounded-full top-24 right-16 animate-pulse"></div>`; break;
            case '第4节': points = `<div class="absolute w-3 h-3 bg-red-500 rounded-full top-20 left-6 animate-pulse"></div><div class="absolute w-3 h-3 bg-red-500 rounded-full top-20 right-6 animate-pulse"></div><div class="absolute w-12 h-1 border-t-2 border-red-500 rounded-full top-16 left-1/2 -translate-x-1/2"></div>`; break;
        }
        return `${baseFace}${points}</div>`;
    }

    function closeModal() {
        modalContent.classList.add('scale-95', 'opacity-0');
        modalContent.classList.remove('scale-100', 'opacity-100');
        setTimeout(() => modal.classList.add('hidden'), 300);
    }

    function renderChart() {
        const ctx = document.getElementById('categoryChart').getContext('2d');
        const labels = Object.keys(APP_DATA).filter(k => k !== 'dictionary');
        const simpleData = [], concentratedData = [];

        labels.forEach(category => {
            let simpleCount = 0, concentratedCount = 0;
            const actions = APP_DATA[category].actions;
            for (const key in actions) {
                if (actions[key].scene.includes('简单活动')) simpleCount++;
                if (actions[key].scene.includes('集中训练')) concentratedCount++;
            }
            simpleData.push(simpleCount);
            concentratedData.push(concentratedCount);
        });

        myChart = new Chart(ctx, {
            type: 'bar',
            data: { labels: labels, datasets: [
                    { label: '简单活动', data: simpleData, backgroundColor: 'rgba(135, 206, 235, 0.7)', borderColor: 'rgba(70, 130, 180, 1)', borderWidth: 1 },
                    { label: '集中训练', data: concentratedData, backgroundColor: 'rgba(160, 82, 45, 0.7)', borderColor: 'rgba(139, 69, 19, 1)', borderWidth: 1 }
                ]
            },
            options: {
                indexAxis: 'y', responsive: true, maintainAspectRatio: false,
                scales: { x: { stacked: true, ticks: { font: { family: "'Inter', 'Noto Sans SC', sans-serif" }}}, y: { stacked: true, ticks: { font: { family: "'Inter', 'Noto Sans SC', sans-serif" }}}},
                plugins: { legend: { position: 'top', labels: { font: { family: "'Inter', 'Noto Sans SC', sans-serif" }}}, tooltip: { mode: 'index', intersect: false, titleFont: { family: "'Inter', 'Noto Sans SC', sans-serif" }, bodyFont: { family: "'Inter', 'Noto Sans SC', sans-serif" }}},
                onClick: (event, elements) => {
                    if (elements.length > 0) {
                        const category = labels[elements[0].index];
                        currentCategory = category;
                        currentScene = 'all';
                        setActiveFilters();
                        renderActions();
                        document.getElementById('explorer').scrollIntoView({ behavior: 'smooth' });
                    }
                }
            }
        });
    }
    
    function generateResetPlan() {
        const simpleActions = [];
        for (const category in APP_DATA) {
            if (category === 'dictionary') continue;
            for (const letter in APP_DATA[category].actions) {
                const action = APP_DATA[category].actions[letter];
                if (action.scene.includes('简单活动')) {
                    simpleActions.push({ ...action, letter, category });
                }
            }
        }
        
        const selectedActions = simpleActions.sort(() => 0.5 - Math.random()).slice(0, 4);
        
        const planHTML = `
            <h3 class="text-xl font-bold mb-4 text-center accent-sky-blue-text">您的专属5分钟计划</h3>
            <ol class="list-decimal list-inside space-y-4">
                ${selectedActions.map((action, index) => {
                    const times = ["0:00 - 1:00", "1:00 - 2:30", "2:30 - 4:00", "4:00 - 5:00"];
                    return `<li class="p-4 bg-warm-beige rounded-lg">
                                <p class="font-semibold text-deep-charcoal">${times[index]} &nbsp; ${action.cn} (${action.category})</p>
                                <p class="text-sm text-muted-olive mt-1">${action.desc}</p>
                            </li>`;
                }).join('')}
            </ol>
        `;
        
        resetPlanContainer.innerHTML = planHTML;
        resetPlanContainer.classList.remove('hidden');
    }

    function decomposeWord() {
        const word = wordInput.value.trim().toUpperCase();
        if (!word) return;

        const selectedCategory = decomposerCategorySelect.value;
        const actions = APP_DATA[selectedCategory].actions;
        const definition = APP_DATA.dictionary[word] || "抱歉，内置词典中未找到该词的释义。";
        
        let resultsHTML = `<div class="text-center mb-6">
                               <h3 class="text-2xl font-bold tracking-wider text-deep-charcoal">${word}</h3>
                               <p class="text-muted-olive">${definition}</p>
                           </div>
                           <div class="space-y-3">`;

        let delay = 0;
        for (const letter of word) {
            if (actions[letter]) {
                const action = actions[letter];
                resultsHTML += `
                    <div class="result-item-enter bg-white p-3 rounded-lg shadow-sm flex items-center space-x-4 cursor-pointer hover:bg-stone-100 transition decomposer-action-item" 
                         data-category="${selectedCategory}" data-letter="${letter}" style="animation-delay: ${delay}ms;">
                        <div class="w-8 h-8 flex-shrink-0 flex items-center justify-center bg-stone-olive rounded-full text-base font-bold accent-burnt-sienna-text">${letter}</div>
                        <div>
                            <p class="font-semibold text-deep-charcoal">${action.cn}</p>
                            <p class="text-xs text-muted-olive">${action.name}</p>
                        </div>
                    </div>
                `;
                delay += 50;
            }
        }
        resultsHTML += `</div>`;

        decomposerPlaceholder.classList.add('hidden');
        decomposerResults.innerHTML = resultsHTML;
        decomposerResults.classList.remove('hidden');

        document.querySelectorAll('.decomposer-action-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const el = e.currentTarget;
                openModal(el.dataset.category, el.dataset.letter);
            });
        });
    }

    function setupEventListeners() {
        categoryFiltersContainer.addEventListener('click', e => {
            const button = e.target.closest('.category-filter-btn');
            if (button) {
                currentCategory = button.dataset.category;
                setActiveFilters();
                renderActions();
            }
        });

        sceneFiltersContainer.addEventListener('click', e => {
            const button = e.target.closest('.scene-filter-btn');
            if (button) {
                currentScene = button.dataset.scene;
                setActiveFilters();
                renderActions();
            }
        });

        actionGrid.addEventListener('click', e => {
            const card = e.target.closest('.cursor-pointer');
            if (card) {
                openModal(card.dataset.category, card.dataset.letter);
            }
        });

        modal.addEventListener('click', e => { if (e.target === modal) closeModal(); });
        
        mobileMenuButton.addEventListener('click', () => { mobileMenu.classList.toggle('hidden'); });
        document.querySelectorAll('#mobile-menu a').forEach(link => {
            link.addEventListener('click', () => mobileMenu.classList.add('hidden'));
        });

        generateResetBtn.addEventListener('click', generateResetPlan);

        // New feature listeners
        decomposeBtn.addEventListener('click', decomposeWord);
        wordInput.addEventListener('keyup', (e) => {
            if (e.key === 'Enter') decomposeWord();
        });
    }

    init();
});
</script>
</body>
</html>
�