import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/time_box/timebox_list_page.dart';
import 'package:oneday/features/home/<USER>';

void main() {
  group('任务同步测试', () {
    setUp(() async {
      // 清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('从首页创建任务应该同步到TimeBox列表', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const HomePage(),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 查找并点击创建任务按钮
      final createButton = find.byType(FloatingActionButton);
      expect(createButton, findsOneWidget);
      
      await tester.tap(createButton);
      await tester.pumpAndSettle();

      // 验证对话框打开
      expect(find.text('创建任务'), findsOneWidget);
      
      // 输入任务信息
      await tester.enterText(find.byType(TextFormField).first, '测试任务');
      await tester.pumpAndSettle();

      // 点击创建按钮
      await tester.tap(find.text('创建'));
      await tester.pumpAndSettle();

      // 验证成功提示
      expect(find.text('任务时间盒子创建成功！'), findsOneWidget);
    });

    testWidgets('TimeBox页面应该正确显示新创建的任务', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      // 创建一个测试任务
      final testTask = TimeBoxTask(
        id: '1',
        title: '测试任务',
        description: '这是一个测试任务',
        plannedMinutes: 60,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '计算机科学',
        createdAt: DateTime.now(),
      );

      // 添加任务到provider
      await container.read(timeBoxProvider.notifier).addTask(testTask);

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: const TimeBoxListPage(),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证任务显示在列表中
      expect(find.text('测试任务'), findsOneWidget);
      expect(find.text('这是一个测试任务'), findsOneWidget);
      
      container.dispose();
    });

    testWidgets('任务状态更新应该实时反映在UI中', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      // 创建一个测试任务
      final testTask = TimeBoxTask(
        id: '1',
        title: '测试任务',
        description: '这是一个测试任务',
        plannedMinutes: 60,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '计算机科学',
        createdAt: DateTime.now(),
      );

      // 添加任务到provider
      await container.read(timeBoxProvider.notifier).addTask(testTask);

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: const TimeBoxListPage(),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证初始状态
      expect(find.text('待开始'), findsOneWidget);

      // 更新任务状态
      final updatedTask = testTask.copyWith(status: TaskStatus.inProgress);
      await container.read(timeBoxProvider.notifier).updateTask(updatedTask);
      await tester.pumpAndSettle();

      // 验证状态更新
      expect(find.text('进行中'), findsOneWidget);
      expect(find.text('待开始'), findsNothing);
      
      container.dispose();
    });

    testWidgets('任务删除应该从列表中移除', (WidgetTester tester) async {
      final container = ProviderContainer();
      
      // 创建一个测试任务
      final testTask = TimeBoxTask(
        id: '1',
        title: '测试任务',
        description: '这是一个测试任务',
        plannedMinutes: 60,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '计算机科学',
        createdAt: DateTime.now(),
      );

      // 添加任务到provider
      await container.read(timeBoxProvider.notifier).addTask(testTask);

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: const TimeBoxListPage(),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证任务存在
      expect(find.text('测试任务'), findsOneWidget);

      // 删除任务
      await container.read(timeBoxProvider.notifier).deleteTask(testTask.id);
      await tester.pumpAndSettle();

      // 验证任务已被删除
      expect(find.text('测试任务'), findsNothing);
      
      container.dispose();
    });

    testWidgets('Provider状态变化应该触发UI重建', (WidgetTester tester) async {
      final container = ProviderContainer();

      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                final timeBoxState = ref.watch(timeBoxProvider);
                return Scaffold(
                  body: Column(
                    children: [
                      Text('任务数量: ${timeBoxState.tasks.length}'),
                      ElevatedButton(
                        onPressed: () async {
                          final task = TimeBoxTask(
                            id: DateTime.now().millisecondsSinceEpoch.toString(),
                            title: '新任务',
                            description: '描述',
                            plannedMinutes: 30,
                            status: TaskStatus.pending,
                            priority: TaskPriority.medium,
                            category: '测试',
                            createdAt: DateTime.now(),
                          );
                          await ref.read(timeBoxProvider.notifier).addTask(task);
                        },
                        child: const Text('添加任务'),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证初始状态（可能有默认任务）
      final initialText = find.textContaining('任务数量:');
      expect(initialText, findsOneWidget);

      // 点击添加任务按钮
      await tester.tap(find.text('添加任务'));
      await tester.pumpAndSettle();

      // 验证任务数量增加
      expect(find.textContaining('任务数量:'), findsOneWidget);
      
      container.dispose();
    });
  });
}
