import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/onboarding/notion_style_onboarding_page.dart';

void main() {
  group('引导页 Web 平台点击导航测试', () {
    testWidgets('Web 平台应该显示可点击的页面指示器', (WidgetTester tester) async {
      // 模拟 Web 平台
      debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia; // 用于模拟 Web
      
      await tester.pumpWidget(
        const MaterialApp(
          home: NotionStyleOnboardingPage(),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 查找页面指示器
      expect(find.byType(GestureDetector), findsWidgets);
      
      // 重置平台设置
      debugDefaultTargetPlatformOverride = null;
    });

    testWidgets('移动端不应该有点击功能', (WidgetTester tester) async {
      // 模拟移动平台
      debugDefaultTargetPlatformOverride = TargetPlatform.android;
      
      await tester.pumpWidget(
        const MaterialApp(
          home: NotionStyleOnboardingPage(),
        ),
      );

      // 等待页面加载
      await tester.pumpAndSettle();

      // 在移动端，SmoothPageIndicator 的 onDotClicked 应该为 null
      // 这个测试主要验证代码结构正确性
      expect(find.byType(NotionStyleOnboardingPage), findsOneWidget);
      
      // 重置平台设置
      debugDefaultTargetPlatformOverride = null;
    });
  });
}
