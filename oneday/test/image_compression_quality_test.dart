import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/shared/config/image_compression_config.dart';
import 'package:oneday/shared/utils/image_compression_utils.dart';

void main() {
  group('图片压缩质量测试', () {
    test('默认压缩配置应该保持高质量', () {
      const config = ImageCompressionConfig.defaultConfig;
      
      // 验证新的默认配置
      expect(config.maxDimension, 1000); // 提高到1000px
      expect(config.jpegQuality, 92); // 提高到92%
      expect(config.enableCompression, true);
    });

    test('高质量配置应该适合文字内容', () {
      const config = ImageCompressionConfig.highQuality;
      
      expect(config.maxDimension, 1200);
      expect(config.jpegQuality, 95);
    });

    test('压缩配置描述应该正确', () {
      const config = ImageCompressionConfig.defaultConfig;
      final description = config.description;
      
      expect(description, contains('MaxSize: 1000px'));
      expect(description, contains('Quality: 92%'));
      expect(description, contains('Enabled: true'));
    });

    test('根据使用场景选择合适的配置', () {
      // 记忆宫殿场景应该使用默认配置
      final memoryConfig = ImageCompressionConfig.forUseCase(
        ImageCompressionUseCase.memoryPalace,
      );
      expect(memoryConfig.maxDimension, 1000);
      expect(memoryConfig.jpegQuality, 92);

      // 分享场景应该使用低质量配置
      final shareConfig = ImageCompressionConfig.forUseCase(
        ImageCompressionUseCase.sharing,
      );
      expect(shareConfig.maxDimension, 600);
      expect(shareConfig.jpegQuality, 70);

      // 存档场景应该使用高质量配置
      final archiveConfig = ImageCompressionConfig.forUseCase(
        ImageCompressionUseCase.archiving,
      );
      expect(archiveConfig.maxDimension, 1200);
      expect(archiveConfig.jpegQuality, 95);
    });

    test('配置验证应该正确工作', () {
      // 有效配置
      const validConfig = ImageCompressionConfig(
        maxDimension: 800,
        jpegQuality: 85,
      );
      expect(validConfig.isValid, true);

      // 无效配置 - 质量超出范围
      const invalidConfig = ImageCompressionConfig(
        maxDimension: 800,
        jpegQuality: 101,
      );
      expect(invalidConfig.isValid, false);

      // 无效配置 - 尺寸为0
      const invalidSizeConfig = ImageCompressionConfig(
        maxDimension: 0,
        jpegQuality: 85,
      );
      expect(invalidSizeConfig.isValid, false);
    });

    test('copyWith方法应该正确创建新配置', () {
      const originalConfig = ImageCompressionConfig.defaultConfig;
      
      final modifiedConfig = originalConfig.copyWith(
        maxDimension: 1500,
        jpegQuality: 98,
      );

      expect(modifiedConfig.maxDimension, 1500);
      expect(modifiedConfig.jpegQuality, 98);
      expect(modifiedConfig.enableCompression, originalConfig.enableCompression);
      expect(modifiedConfig.keepOriginal, originalConfig.keepOriginal);
    });
  });

  group('图片压缩工具测试', () {
    test('获取图片信息应该正确工作', () async {
      // 这个测试需要实际的图片文件，在实际环境中运行
      // 这里只测试方法存在性
      expect(ImageCompressionUtils.getImageInfo, isA<Function>());
    });

    test('压缩方法应该存在', () {
      expect(ImageCompressionUtils.compressImage, isA<Function>());
      expect(ImageCompressionUtils.compressImages, isA<Function>());
    });
  });

  group('压缩质量验证', () {
    test('文字内容检测逻辑应该正确', () {
      // 模拟不同宽高比的图片
      
      // 横向图片（可能包含文字）
      const landscapeRatio = 1.5; // 宽 > 高
      expect(landscapeRatio > 1.2, true);
      
      // 纵向图片（可能包含文字）
      const portraitRatio = 0.7; // 高 > 宽
      expect(portraitRatio < 0.8, true);
      
      // 正方形图片（不太可能包含大量文字）
      const squareRatio = 1.0;
      expect(squareRatio > 1.2 || squareRatio < 0.8, false);
    });

    test('自适应质量逻辑应该合理', () {
      const baseQuality = 92;
      const textContentQuality = 95;
      
      // 对于可能包含文字的图片，质量应该提升
      final adaptiveQuality = baseQuality > textContentQuality 
          ? baseQuality 
          : textContentQuality;
      
      expect(adaptiveQuality, textContentQuality);
    });
  });
}
