import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:oneday/features/reflection/reflection_log_page.dart';

void main() {
  group('优化日志页面集成测试', () {
    setUpAll(() async {
      // 初始化本地化数据
      await initializeDateFormatting('zh_CN', null);
    });

    testWidgets('优化日志页面应该正常加载，不出现LocaleDataException', (WidgetTester tester) async {
      // 这个测试的主要目的是验证页面能够正常加载，不抛出LocaleDataException
      bool hasException = false;
      String? exceptionMessage;

      try {
        // 构建优化日志页面
        await tester.pumpWidget(
          MaterialApp(
            home: const ReflectionLogPage(),
            theme: ThemeData(
              useMaterial3: true,
              primaryColor: const Color(0xFF2E7EED),
            ),
          ),
        );

        // 等待页面完全加载
        await tester.pumpAndSettle();

        // 验证页面标题存在
        expect(find.text('优化日志'), findsOneWidget);

        // 验证浮动操作按钮存在
        expect(find.byType(FloatingActionButton), findsOneWidget);

      } catch (e) {
        hasException = true;
        exceptionMessage = e.toString();
      }

      // 验证没有LocaleDataException
      expect(hasException, false, reason: 'Page should load without exceptions: $exceptionMessage');

      print('✅ 优化日志页面加载成功，没有LocaleDataException错误');
    });

    testWidgets('DateFormat功能应该正常工作，不抛出LocaleDataException', (WidgetTester tester) async {
      bool hasException = false;
      String? exceptionMessage;

      try {
        await tester.pumpWidget(
          MaterialApp(
            home: const ReflectionLogPage(),
            theme: ThemeData(
              useMaterial3: true,
              primaryColor: const Color(0xFF2E7EED),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // 点击浮动操作按钮打开编辑器
        await tester.tap(find.byType(FloatingActionButton));
        await tester.pumpAndSettle();

        // 验证对话框打开（这会触发DateFormat的使用）
        expect(find.byType(Dialog), findsOneWidget);

        print('✅ DateFormat功能正常工作，编辑器对话框成功打开');

      } catch (e) {
        hasException = true;
        exceptionMessage = e.toString();

        // 特别检查是否是LocaleDataException
        if (e.toString().contains('LocaleDataException')) {
          fail('发现LocaleDataException错误: $exceptionMessage');
        }
      }

      // 验证没有异常
      expect(hasException, false, reason: 'DateFormat should work without exceptions: $exceptionMessage');
    });
  });
}
