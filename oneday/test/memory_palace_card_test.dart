import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('MemoryPalaceCard 封面显示测试', () {
    testWidgets('应该正确显示网络图片封面', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '1',
        title: '测试宫殿',
        imagePaths: ['https://example.com/image.jpg'],
        anchorCount: 5,
        tags: ['测试'],
        category: '测试分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () {},
              onLongPress: () {},
            ),
          ),
        ),
      );

      // 验证卡片存在
      expect(find.byType(MemoryPalaceCard), findsOneWidget);
      expect(find.text('测试宫殿'), findsOneWidget);
      expect(find.text('5个知识点'), findsOneWidget);
    });

    testWidgets('应该正确显示本地文件封面', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '2',
        title: '本地图片宫殿',
        imagePaths: ['/path/to/local/image.jpg'],
        anchorCount: 3,
        tags: ['本地'],
        category: '本地分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () {},
              onLongPress: () {},
            ),
          ),
        ),
      );

      // 验证卡片存在
      expect(find.byType(MemoryPalaceCard), findsOneWidget);
      expect(find.text('本地图片宫殿'), findsOneWidget);
      expect(find.text('3个知识点'), findsOneWidget);
    });

    testWidgets('应该正确处理空图片路径', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '3',
        title: '无图片宫殿',
        imagePaths: [],
        anchorCount: 0,
        tags: [],
        category: '空分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () {},
              onLongPress: () {},
            ),
          ),
        ),
      );

      // 验证卡片存在
      expect(find.byType(MemoryPalaceCard), findsOneWidget);
      expect(find.text('无图片宫殿'), findsOneWidget);
      expect(find.text('0个知识点'), findsOneWidget);
      
      // 验证显示默认图标
      expect(find.byIcon(Icons.image), findsOneWidget);
    });

    testWidgets('应该正确处理空字符串图片路径', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '4',
        title: '空字符串图片宫殿',
        imagePaths: [''],
        anchorCount: 1,
        tags: ['空'],
        category: '空分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () {},
              onLongPress: () {},
            ),
          ),
        ),
      );

      // 验证卡片存在
      expect(find.byType(MemoryPalaceCard), findsOneWidget);
      expect(find.text('空字符串图片宫殿'), findsOneWidget);
      expect(find.text('1个知识点'), findsOneWidget);
      
      // 验证显示默认图标
      expect(find.byIcon(Icons.image), findsOneWidget);
    });

    testWidgets('imagePath getter应该返回第一张图片路径', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '5',
        title: '多图片宫殿',
        imagePaths: [
          '/path/to/first.jpg',
          '/path/to/second.jpg',
          '/path/to/third.jpg',
        ],
        anchorCount: 10,
        tags: ['多图'],
        category: '多图分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // 验证imagePath返回第一张图片
      expect(palace.imagePath, '/path/to/first.jpg');
    });

    testWidgets('imagePath getter应该处理空列表', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '6',
        title: '空列表宫殿',
        imagePaths: [],
        anchorCount: 0,
        tags: [],
        category: '空分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      // 验证imagePath返回空字符串
      expect(palace.imagePath, '');
    });

    testWidgets('卡片应该响应点击事件', (WidgetTester tester) async {
      bool tapped = false;
      bool longPressed = false;

      final palace = MemoryPalace(
        id: '7',
        title: '交互测试宫殿',
        imagePaths: ['https://example.com/test.jpg'],
        anchorCount: 2,
        tags: ['交互'],
        category: '交互分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () => tapped = true,
              onLongPress: () => longPressed = true,
            ),
          ),
        ),
      );

      // 测试点击
      await tester.tap(find.byType(MemoryPalaceCard));
      expect(tapped, true);

      // 测试长按
      await tester.longPress(find.byType(MemoryPalaceCard));
      expect(longPressed, true);
    });

    testWidgets('卡片应该有正确的样式', (WidgetTester tester) async {
      final palace = MemoryPalace(
        id: '8',
        title: '样式测试宫殿',
        imagePaths: ['https://example.com/style.jpg'],
        anchorCount: 7,
        tags: ['样式'],
        category: '样式分类',
        createdAt: DateTime.now(),
        lastUsed: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: MemoryPalaceCard(
              palace: palace,
              onTap: () {},
              onLongPress: () {},
            ),
          ),
        ),
      );

      // 验证Card组件存在
      expect(find.byType(Card), findsOneWidget);
      
      // 验证InkWell组件存在（用于点击效果）
      expect(find.byType(InkWell), findsOneWidget);
      
      // 验证文本样式
      final titleText = tester.widget<Text>(find.text('样式测试宫殿'));
      expect(titleText.style?.fontWeight, FontWeight.bold);
      expect(titleText.style?.fontSize, 15);
    });
  });
}
