import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/utils/anchor_data_migration.dart';
import 'package:oneday/features/memory_palace/utils/image_coordinate_system.dart'
    as coord;

void main() {
  group('标准化坐标系统测试', () {
    group('ImageSizeInfo 测试', () {
      test('应该正确计算缩放比例', () {
        final sizeInfo = coord.ImageSizeInfo(
          imagePath: '/test/image.jpg',
          currentSize: const Size(800, 600),
          originalSize: const Size(1600, 1200),
          isCompressed: true,
        );

        expect(sizeInfo.scaleFactorX, 0.5);
        expect(sizeInfo.scaleFactorY, 0.5);
        expect(sizeInfo.needsCoordinateTransform, true);
      });

      test('未压缩图片不需要坐标转换', () {
        final sizeInfo = coord.ImageSizeInfo(
          imagePath: '/test/image.jpg',
          currentSize: const Size(800, 600),
          originalSize: const Size(800, 600),
          isCompressed: false,
        );

        expect(sizeInfo.scaleFactorX, 1.0);
        expect(sizeInfo.scaleFactorY, 1.0);
        expect(sizeInfo.needsCoordinateTransform, false);
      });
    });

    group('StandardizedCoordinate 测试', () {
      test('应该正确计算比例坐标', () {
        final coordinate = coord.StandardizedCoordinate(
          x: 400,
          y: 300,
          originalImageSize: const Size(800, 600),
        );

        expect(coordinate.ratioCoordinate.dx, 0.5);
        expect(coordinate.ratioCoordinate.dy, 0.5);
      });

      test('toString 应该返回正确格式', () {
        final coordinate = coord.StandardizedCoordinate(
          x: 400,
          y: 300,
          originalImageSize: const Size(800, 600),
        );

        final str = coordinate.toString();
        expect(str, contains('x: 400.0'));
        expect(str, contains('y: 300.0'));
        expect(str, contains('ratio: 0.500'));
      });
    });

    group('坐标转换测试', () {
      late coord.ImageSizeInfo sizeInfo;

      setUp(() {
        sizeInfo = coord.ImageSizeInfo(
          imagePath: '/test/compressed_image.jpg',
          currentSize: const Size(800, 600), // 压缩后尺寸
          originalSize: const Size(1600, 1200), // 原始尺寸
          isCompressed: true,
        );
      });

      test('屏幕坐标到标准化坐标转换', () {
        // 模拟变换矩阵：缩放0.5，平移(100, 50)
        final matrix = Matrix4.identity()
          ..translate(100.0, 50.0)
          ..scale(0.5);

        // 屏幕点击位置
        const screenPoint = Offset(300, 200);

        final standardizedCoord =
            coord.ImageCoordinateSystem.screenToStandardized(
              screenPoint,
              matrix,
              sizeInfo,
            );

        // 验证转换结果
        // 屏幕坐标(300, 200) -> 当前图片坐标(400, 300) -> 标准化坐标(800, 600)
        expect(standardizedCoord.x, closeTo(800, 0.1));
        expect(standardizedCoord.y, closeTo(600, 0.1));
      });

      test('标准化坐标到当前图片坐标转换', () {
        final standardizedCoord = coord.StandardizedCoordinate(
          x: 800,
          y: 600,
          originalImageSize: sizeInfo.originalSize,
        );

        final currentImageCoord =
            coord.ImageCoordinateSystem.standardizedToCurrentImage(
              standardizedCoord,
              sizeInfo,
            );

        // 验证转换结果
        // 标准化坐标(800, 600) -> 当前图片坐标(400, 300)
        expect(currentImageCoord.dx, closeTo(400, 0.1));
        expect(currentImageCoord.dy, closeTo(300, 0.1));
      });

      test('往返转换应该保持一致性', () {
        // 原始屏幕坐标
        const originalScreenPoint = Offset(300, 200);

        // 变换矩阵
        final matrix = Matrix4.identity()
          ..translate(100.0, 50.0)
          ..scale(0.5);

        // 屏幕坐标 -> 标准化坐标
        final standardizedCoord =
            coord.ImageCoordinateSystem.screenToStandardized(
              originalScreenPoint,
              matrix,
              sizeInfo,
            );

        // 标准化坐标 -> 当前图片坐标
        final currentImageCoord =
            coord.ImageCoordinateSystem.standardizedToCurrentImage(
              standardizedCoord,
              sizeInfo,
            );

        // 当前图片坐标 -> 屏幕坐标（反向验证）
        final scale = matrix.getMaxScaleOnAxis();
        final translation = matrix.getTranslation();
        final verifyScreenX = currentImageCoord.dx * scale + translation.x;
        final verifyScreenY = currentImageCoord.dy * scale + translation.y;

        // 验证往返转换的一致性
        expect(verifyScreenX, closeTo(originalScreenPoint.dx, 0.1));
        expect(verifyScreenY, closeTo(originalScreenPoint.dy, 0.1));
      });
    });

    group('不同压缩配置下的一致性测试', () {
      test('相同锚点在不同压缩配置下应该显示在相同位置', () {
        // 模拟原始图片尺寸
        const originalSize = Size(2000, 1500);

        // 模拟800px压缩配置
        final sizeInfo800 = coord.ImageSizeInfo(
          imagePath: '/test/image_800.jpg',
          currentSize: const Size(800, 600),
          originalSize: originalSize,
          isCompressed: true,
        );

        // 模拟1000px压缩配置
        final sizeInfo1000 = coord.ImageSizeInfo(
          imagePath: '/test/image_1000.jpg',
          currentSize: const Size(1000, 750),
          originalSize: originalSize,
          isCompressed: true,
        );

        // 锚点比例坐标（基于原始图片尺寸）
        const anchorXRatio = 0.5;
        const anchorYRatio = 0.4;

        // 创建标准化坐标
        final standardizedCoord = coord.StandardizedCoordinate(
          x: anchorXRatio * originalSize.width,
          y: anchorYRatio * originalSize.height,
          originalImageSize: originalSize,
        );

        // 转换为不同压缩配置下的当前图片坐标
        final coord800 = coord.ImageCoordinateSystem.standardizedToCurrentImage(
          standardizedCoord,
          sizeInfo800,
        );

        final coord1000 =
            coord.ImageCoordinateSystem.standardizedToCurrentImage(
              standardizedCoord,
              sizeInfo1000,
            );

        // 验证比例位置一致性
        expect(
          coord800.dx / sizeInfo800.currentSize.width,
          closeTo(anchorXRatio, 0.001),
        );
        expect(
          coord800.dy / sizeInfo800.currentSize.height,
          closeTo(anchorYRatio, 0.001),
        );
        expect(
          coord1000.dx / sizeInfo1000.currentSize.width,
          closeTo(anchorXRatio, 0.001),
        );
        expect(
          coord1000.dy / sizeInfo1000.currentSize.height,
          closeTo(anchorYRatio, 0.001),
        );
      });
    });
  });

  group('锚点数据迁移测试', () {
    group('MigrationStats 测试', () {
      test('应该正确计算迁移进度', () {
        const stats = MigrationStats(
          totalScenes: 10,
          migratedScenes: 7,
          totalAnchors: 50,
          migratedAnchors: 35,
        );

        expect(stats.progress, closeTo(0.7, 0.01));
        expect(stats.isComplete, false);
      });

      test('完成状态应该正确', () {
        const stats = MigrationStats(
          totalScenes: 5,
          migratedScenes: 5,
          totalAnchors: 25,
          migratedAnchors: 25,
        );

        expect(stats.isComplete, true);
        expect(stats.progress, 1.0);
      });

      test('toString 应该返回正确格式', () {
        const stats = MigrationStats(
          totalScenes: 10,
          migratedScenes: 7,
          totalAnchors: 50,
          migratedAnchors: 35,
        );

        final str = stats.toString();
        expect(str, contains('scenes: 7/10'));
        expect(str, contains('anchors: 35/50'));
        expect(str, contains('progress: 70.0%'));
      });
    });

    group('坐标校准测试', () {
      test('应该正确校准基于旧压缩尺寸的坐标', () {
        // 模拟图片尺寸信息
        final sizeInfo = coord.ImageSizeInfo(
          imagePath: '/test/image.jpg',
          currentSize: const Size(1000, 750), // 新压缩尺寸
          originalSize: const Size(2000, 1500), // 原始尺寸
          isCompressed: true,
        );

        // 模拟基于旧压缩尺寸(800x600)的锚点坐标
        const oldXRatio = 0.5; // 在800px图片中的中心位置
        const oldYRatio = 0.5; // 在600px图片中的中心位置

        // 使用私有方法进行测试（这里模拟其逻辑）
        // 1. 从旧比例坐标恢复为旧压缩图片的像素坐标
        const oldImageSize = Size(800, 600);
        final oldImageX = oldXRatio * oldImageSize.width; // 400
        final oldImageY = oldYRatio * oldImageSize.height; // 300

        // 2. 转换为原始图片的像素坐标
        final scaleFactorX =
            sizeInfo.originalSize.width / oldImageSize.width; // 2.5
        final scaleFactorY =
            sizeInfo.originalSize.height / oldImageSize.height; // 2.5

        final originalImageX = oldImageX * scaleFactorX; // 1000
        final originalImageY = oldImageY * scaleFactorY; // 750

        // 3. 转换为基于原始图片尺寸的新比例坐标
        final newXRatio = originalImageX / sizeInfo.originalSize.width; // 0.5
        final newYRatio = originalImageY / sizeInfo.originalSize.height; // 0.5

        // 验证校准结果
        expect(newXRatio, closeTo(0.5, 0.001));
        expect(newYRatio, closeTo(0.5, 0.001));
      });
    });
  });

  group('边界情况测试', () {
    test('应该正确处理边界坐标', () {
      final sizeInfo = coord.ImageSizeInfo(
        imagePath: '/test/image.jpg',
        currentSize: const Size(800, 600),
        originalSize: const Size(1600, 1200),
        isCompressed: true,
      );

      // 测试左上角
      final topLeft = coord.StandardizedCoordinate(
        x: 0,
        y: 0,
        originalImageSize: sizeInfo.originalSize,
      );
      final topLeftCurrent = coord
          .ImageCoordinateSystem.standardizedToCurrentImage(topLeft, sizeInfo);
      expect(topLeftCurrent.dx, 0);
      expect(topLeftCurrent.dy, 0);

      // 测试右下角
      final bottomRight = coord.StandardizedCoordinate(
        x: sizeInfo.originalSize.width,
        y: sizeInfo.originalSize.height,
        originalImageSize: sizeInfo.originalSize,
      );
      final bottomRightCurrent =
          coord.ImageCoordinateSystem.standardizedToCurrentImage(
            bottomRight,
            sizeInfo,
          );
      expect(bottomRightCurrent.dx, sizeInfo.currentSize.width);
      expect(bottomRightCurrent.dy, sizeInfo.currentSize.height);
    });

    test('应该正确处理零尺寸图片', () {
      final sizeInfo = coord.ImageSizeInfo(
        imagePath: '/test/empty.jpg',
        currentSize: const Size(0, 0),
        originalSize: const Size(0, 0),
        isCompressed: false,
      );

      expect(sizeInfo.scaleFactorX, isNaN);
      expect(sizeInfo.scaleFactorY, isNaN);
      expect(sizeInfo.needsCoordinateTransform, false);
    });
  });
}
