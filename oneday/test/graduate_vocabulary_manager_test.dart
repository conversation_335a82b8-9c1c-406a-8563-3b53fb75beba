import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/vocabulary/vocabulary_service.dart';
import 'package:oneday/features/vocabulary/word_model.dart';

void main() {
  group('考研词库管理功能测试', () {
    late VocabularyService vocabularyService;

    setUpAll(() {
      TestWidgetsFlutterBinding.ensureInitialized();
    });

    setUp(() {
      vocabularyService = VocabularyService();
    });

    test('应该能够获取选择状态统计', () async {
      // 这个测试需要模拟数据，在实际环境中会从SharedPreferences读取
      final stats = await vocabularyService.getSelectionStatistics();
      
      expect(stats, isA<Map<String, int>>());
      expect(stats.containsKey('selected'), true);
      expect(stats.containsKey('unselected'), true);
      expect(stats.containsKey('total'), true);
    });

    test('应该能够批量选择单词', () async {
      final testWords = ['test', 'example', 'word'];
      
      // 批量选择单词
      await vocabularyService.batchSelectWords(testWords);
      
      // 验证单词已被选择
      final selectedWords = await vocabularyService.getSelectedWordsFromProgress();
      
      for (final word in testWords) {
        expect(selectedWords.contains(word), true);
      }
    });

    test('应该能够批量取消选择单词', () async {
      final testWords = ['test', 'example', 'word'];
      
      // 先选择单词
      await vocabularyService.batchSelectWords(testWords);
      
      // 然后取消选择
      await vocabularyService.batchUnselectWords(testWords);
      
      // 验证单词已被取消选择
      final selectedWords = await vocabularyService.getSelectedWordsFromProgress();
      
      for (final word in testWords) {
        expect(selectedWords.contains(word), false);
      }
    });

    test('应该能够获取学习进度', () async {
      final progress = await vocabularyService.getLearningProgress();
      
      expect(progress, isA<Map<String, WordLearningProgress>>());
    });

    test('WordLearningProgress模型应该正确序列化', () {
      final progress = WordLearningProgress(
        wordId: 'test',
        isSelected: true,
        isLearned: false,
        reviewCount: 1,
        masteryLevel: 0.5,
      );

      final json = progress.toJson();
      final restored = WordLearningProgress.fromJson(json);

      expect(restored.wordId, equals('test'));
      expect(restored.isSelected, equals(true));
      expect(restored.isLearned, equals(false));
      expect(restored.reviewCount, equals(1));
      expect(restored.masteryLevel, equals(0.5));
    });
  });
}
