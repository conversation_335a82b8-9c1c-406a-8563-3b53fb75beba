import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/community/profanity_filter_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();
  
  group('简单敏感词过滤测试', () {
    late ProfanityFilterService filterService;

    setUp(() async {
      filterService = ProfanityFilterService();
      await filterService.initialize();
    });

    test('应该正确检测和过滤敏感词', () async {
      const testText = '这是一个包含傻逼的测试文本';
      
      // 测试过滤服务
      final filterResult = await filterService.filterText(testText);
      
      print('原始文本: "$testText"');
      print('过滤后文本: "${filterResult.filteredText}"');
      print('是否有违禁词: ${filterResult.hasViolations}');
      print('检测到的词汇数量: ${filterResult.detectedWords.length}');
      
      expect(filterResult.hasViolations, true);
      expect(filterResult.detectedWords.length, greaterThan(0));
      expect(filterResult.filteredText.contains('***'), true);
      expect(filterResult.filteredText.contains('傻逼'), false);
    });

    test('应该正确处理干净的文本', () async {
      const testText = '这是一个干净的测试文本';
      
      final filterResult = await filterService.filterText(testText);
      
      print('原始文本: "$testText"');
      print('过滤后文本: "${filterResult.filteredText}"');
      print('是否有违禁词: ${filterResult.hasViolations}');
      
      expect(filterResult.hasViolations, false);
      expect(filterResult.detectedWords.length, 0);
      expect(filterResult.filteredText, testText);
    });

    test('应该正确处理多个敏感词', () async {
      const testText = '这个傻逼真是个白痴';
      
      final filterResult = await filterService.filterText(testText);
      
      print('原始文本: "$testText"');
      print('过滤后文本: "${filterResult.filteredText}"');
      print('是否有违禁词: ${filterResult.hasViolations}');
      print('检测到的词汇数量: ${filterResult.detectedWords.length}');
      
      expect(filterResult.hasViolations, true);
      expect(filterResult.detectedWords.length, greaterThan(1));
      expect(filterResult.filteredText.contains('***'), true);
      expect(filterResult.filteredText.contains('傻逼'), false);
      expect(filterResult.filteredText.contains('白痴'), false);
    });
  });
}
