import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('DropdownButton背景色测试', () {
    testWidgets('DropdownButton应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE3E2E0)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: null,
                    hint: const Text(
                      '请选择分类',
                      style: TextStyle(color: Color(0xFF6E6E6E)),
                    ),
                    isExpanded: true,
                    dropdownColor: Colors.white, // 设置下拉菜单背景为白色
                    items: const [
                      DropdownMenuItem<String>(
                        value: '默认分类',
                        child: Text('默认分类'),
                      ),
                      DropdownMenuItem<String>(
                        value: '景点',
                        child: Text('景点'),
                      ),
                      DropdownMenuItem<String>(
                        value: '人文',
                        child: Text('人文'),
                      ),
                      DropdownMenuItem<String>(
                        value: '自然',
                        child: Text('自然'),
                      ),
                    ],
                    onChanged: (String? newValue) {
                      // 处理选择变化
                    },
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      // 查找DropdownButton
      final dropdownButton = find.byType(DropdownButton<String>);
      expect(dropdownButton, findsOneWidget);

      // 验证DropdownButton的dropdownColor属性
      final dropdownWidget = tester.widget<DropdownButton<String>>(dropdownButton);
      expect(dropdownWidget.dropdownColor, Colors.white);

      // 点击DropdownButton展开菜单
      await tester.tap(dropdownButton);
      await tester.pumpAndSettle();

      // 验证菜单项显示
      expect(find.text('默认分类'), findsAtLeastNWidgets(1));
      expect(find.text('景点'), findsOneWidget);
      expect(find.text('人文'), findsOneWidget);
      expect(find.text('自然'), findsOneWidget);
    });

    testWidgets('DropdownButtonFormField应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: DropdownButtonFormField<String>(
                value: null,
                decoration: const InputDecoration(
                  labelText: '学科分类',
                  border: OutlineInputBorder(),
                ),
                dropdownColor: Colors.white,
                items: ['计算机科学', '数学', '英语', '政治', '休息', '其他'].map((category) {
                  return DropdownMenuItem(
                    value: category,
                    child: Text(category),
                  );
                }).toList(),
                onChanged: (value) {
                  // 处理选择变化
                },
              ),
            ),
          ),
        ),
      );

      // 查找DropdownButtonFormField
      final dropdownFormField = find.byType(DropdownButtonFormField<String>);
      expect(dropdownFormField, findsOneWidget);

      // 验证DropdownButtonFormField存在
      expect(dropdownFormField, findsOneWidget);

      // 点击DropdownButtonFormField展开菜单
      await tester.tap(dropdownFormField);
      await tester.pumpAndSettle();

      // 验证菜单项显示
      expect(find.text('计算机科学'), findsOneWidget);
      expect(find.text('数学'), findsOneWidget);
      expect(find.text('英语'), findsOneWidget);
      expect(find.text('政治'), findsOneWidget);
      expect(find.text('休息'), findsOneWidget);
      expect(find.text('其他'), findsOneWidget);
    });

    testWidgets('知忆相册分类选择DropdownButton应该使用白色背景', (WidgetTester tester) async {
      // 模拟知忆相册分类选择的DropdownButton
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '分类',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: null,
                        hint: const Text(
                          '请选择分类',
                          style: TextStyle(color: Color(0xFF6E6E6E)),
                        ),
                        isExpanded: true,
                        dropdownColor: Colors.white, // 设置下拉菜单背景为白色
                        items: const [
                          DropdownMenuItem<String>(
                            value: null,
                            child: Text('默认分类'),
                          ),
                          DropdownMenuItem<String>(
                            value: '景点',
                            child: Padding(
                              padding: EdgeInsets.only(left: 0),
                              child: Text('景点'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '人文',
                            child: Padding(
                              padding: EdgeInsets.only(left: 0),
                              child: Text('人文'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '自然',
                            child: Padding(
                              padding: EdgeInsets.only(left: 0),
                              child: Text('自然'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '新分类',
                            child: Padding(
                              padding: EdgeInsets.only(left: 0),
                              child: Text('新分类'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '学校',
                            child: Padding(
                              padding: EdgeInsets.only(left: 0),
                              child: Text('学校'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '学前',
                            child: Padding(
                              padding: EdgeInsets.only(left: 16.0),
                              child: Text('学前'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '小学',
                            child: Padding(
                              padding: EdgeInsets.only(left: 16.0),
                              child: Text('小学'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '中学',
                            child: Padding(
                              padding: EdgeInsets.only(left: 16.0),
                              child: Text('中学'),
                            ),
                          ),
                          DropdownMenuItem<String>(
                            value: '高中',
                            child: Padding(
                              padding: EdgeInsets.only(left: 16.0),
                              child: Text('高中'),
                            ),
                          ),
                        ],
                        onChanged: (String? newValue) {
                          // 处理选择变化
                        },
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 查找DropdownButton
      final dropdownButton = find.byType(DropdownButton<String>);
      expect(dropdownButton, findsOneWidget);

      // 验证DropdownButton的dropdownColor属性
      final dropdownWidget = tester.widget<DropdownButton<String>>(dropdownButton);
      expect(dropdownWidget.dropdownColor, Colors.white);

      // 点击DropdownButton展开菜单
      await tester.tap(dropdownButton);
      await tester.pumpAndSettle();

      // 验证菜单项显示（这些是知忆相册中的分类选项）
      expect(find.text('默认分类'), findsAtLeastNWidgets(1));
      expect(find.text('景点'), findsOneWidget);
      expect(find.text('人文'), findsOneWidget);
      expect(find.text('自然'), findsOneWidget);
      expect(find.text('新分类'), findsOneWidget);
      expect(find.text('学校'), findsOneWidget);
      expect(find.text('学前'), findsOneWidget);
      expect(find.text('小学'), findsOneWidget);
      expect(find.text('中学'), findsOneWidget);
      expect(find.text('高中'), findsOneWidget);
    });

    testWidgets('知忆相册分类选择DropdownButton应该使用纯白色背景', (WidgetTester tester) async {
      // 模拟知忆相册分类选择的DropdownButton
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true,
            popupMenuTheme: const PopupMenuThemeData(
              color: Colors.white,
              surfaceTintColor: Colors.transparent,
            ),
          ),
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    '选择分类',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 200,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                                        child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: null,
                        hint: const Text(
                          '请选择分类',
                          style: TextStyle(color: Color(0xFF6E6E6E)),
                        ),
                        isExpanded: true,
                        dropdownColor: const Color(0xFFFFFFFF),
                        items: const [
                          DropdownMenuItem(value: null, child: Text('默认分类')),
                          DropdownMenuItem(value: '景点', child: Text('景点')),
                          DropdownMenuItem(value: '人文', child: Text('人文')),
                          DropdownMenuItem(value: '自然', child: Text('自然')),
                          DropdownMenuItem(value: '学校', child: Text('学校')),
                        ],
                        onChanged: (String? newValue) {},
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 找到DropdownButton
      final dropdownButton = find.byType(DropdownButton<String>);
      expect(dropdownButton, findsOneWidget);

      // 验证dropdownColor设置
      final dropdownWidget = tester.widget<DropdownButton<String>>(dropdownButton);
      expect(dropdownWidget.dropdownColor, const Color(0xFFFFFFFF));

      // 点击下拉按钮
      await tester.tap(dropdownButton);
      await tester.pumpAndSettle();

      // 验证菜单项显示（使用更具体的查找方式）
      expect(find.byType(DropdownMenuItem<String>), findsNWidgets(5));
      expect(find.text('景点'), findsOneWidget);
      expect(find.text('人文'), findsOneWidget);
      expect(find.text('自然'), findsOneWidget);
      expect(find.text('学校'), findsOneWidget);
    });

    testWidgets('验证Notion风格设计一致性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            // Notion风格主题配置
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          home: Scaffold(
            body: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  border: Border.all(color: const Color(0xFFE3E2E0)),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: null,
                    hint: const Text(
                      '请选择分类',
                      style: TextStyle(color: Color(0xFF6E6E6E)),
                    ),
                    isExpanded: true,
                    dropdownColor: Colors.white, // 白色背景
                    items: const [
                      DropdownMenuItem<String>(
                        value: '测试分类',
                        child: Text('测试分类'),
                      ),
                    ],
                    onChanged: (String? newValue) {},
                  ),
                ),
              ),
            ),
          ),
        ),
      );

      // 验证主题配置
      final theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.dialogTheme.backgroundColor, Colors.white);

      // 验证DropdownButton的白色背景
      final dropdownButton = find.byType(DropdownButton<String>);
      final dropdownWidget = tester.widget<DropdownButton<String>>(dropdownButton);
      expect(dropdownWidget.dropdownColor, Colors.white);

      // 验证边框颜色符合Notion风格
      final container = find.byType(Container).first;
      final containerWidget = tester.widget<Container>(container);
      final decoration = containerWidget.decoration as BoxDecoration;
      expect(decoration.border, isA<Border>());
    });
  });
}
