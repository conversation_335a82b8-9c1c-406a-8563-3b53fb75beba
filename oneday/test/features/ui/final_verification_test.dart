import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('最终验证测试 - 所有弹窗背景色统一为白色', () {
    testWidgets('验证所有弹窗组件都使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            // 全局Dialog主题 - 白色背景
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          home: Scaffold(
            body: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: <PERSON>umn(
                children: [
                  // 1. AlertDialog测试按钮
                  ElevatedButton(
                    onPressed: () {
                      showDialog(
                        context: tester.element(find.byType(Scaffold)),
                        builder: (context) => const AlertDialog(
                          title: Text('AlertDialog测试'),
                          content: Text('这是AlertDialog，应该使用白色背景'),
                        ),
                      );
                    },
                    child: const Text('测试AlertDialog'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 2. ModalBottomSheet测试按钮
                  ElevatedButton(
                    onPressed: () {
                      showModalBottomSheet(
                        context: tester.element(find.byType(Scaffold)),
                        backgroundColor: Colors.white,
                        shape: const RoundedRectangleBorder(
                          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                        ),
                        builder: (context) => Container(
                          height: 200,
                          padding: const EdgeInsets.all(16),
                          child: const Text('ModalBottomSheet测试'),
                        ),
                      );
                    },
                    child: const Text('测试ModalBottomSheet'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 3. FilterChip - 白色背景
                  FilterChip(
                    selected: false,
                    label: const Text('FilterChip测试'),
                    backgroundColor: Colors.white,
                    side: const BorderSide(
                      color: Color(0xFFE3E2E0),
                      width: 1,
                    ),
                    onSelected: (bool value) {},
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 4. DropdownButton - 白色背景
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                    decoration: BoxDecoration(
                      border: Border.all(color: const Color(0xFFE3E2E0)),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: null,
                        hint: const Text(
                          'DropdownButton测试',
                          style: TextStyle(color: Color(0xFF6E6E6E)),
                        ),
                        isExpanded: true,
                        dropdownColor: Colors.white, // 白色背景
                        items: const [
                          DropdownMenuItem<String>(
                            value: '选项1',
                            child: Text('选项1'),
                          ),
                          DropdownMenuItem<String>(
                            value: '选项2',
                            child: Text('选项2'),
                          ),
                        ],
                        onChanged: (String? newValue) {},
                      ),
                    ),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 5. 自定义Dialog测试按钮
                  ElevatedButton(
                    onPressed: () {
                      showDialog(
                        context: tester.element(find.byType(Scaffold)),
                        builder: (context) => Dialog(
                          backgroundColor: Colors.transparent,
                          child: Container(
                            width: 300,
                            height: 200,
                            decoration: BoxDecoration(
                              color: Colors.white, // 白色背景
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                            ),
                            child: const Center(
                              child: Text('自定义Dialog测试'),
                            ),
                          ),
                        ),
                      );
                    },
                    child: const Text('测试自定义Dialog'),
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // 6. PopupMenuButton - 白色背景
                  PopupMenuButton<String>(
                    color: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                      side: const BorderSide(color: Color(0xFFE3E2E0), width: 1),
                    ),
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'option1',
                        child: Text('PopupMenu选项1'),
                      ),
                      const PopupMenuItem(
                        value: 'option2',
                        child: Text('PopupMenu选项2'),
                      ),
                    ],
                    child: const Text('测试PopupMenuButton'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 验证所有组件都正确显示
      expect(find.text('测试AlertDialog'), findsOneWidget);
      expect(find.text('测试ModalBottomSheet'), findsOneWidget);
      expect(find.text('FilterChip测试'), findsOneWidget);
      expect(find.text('DropdownButton测试'), findsOneWidget);
      expect(find.text('测试自定义Dialog'), findsOneWidget);
      expect(find.text('测试PopupMenuButton'), findsOneWidget);

      // 验证FilterChip的白色背景
      final filterChip = tester.widget<FilterChip>(find.byType(FilterChip));
      expect(filterChip.backgroundColor, Colors.white);

      // 验证DropdownButton的白色背景
      final dropdownButton = tester.widget<DropdownButton<String>>(find.byType(DropdownButton<String>));
      expect(dropdownButton.dropdownColor, Colors.white);

      // 验证PopupMenuButton的白色背景
      final popupMenuButton = tester.widget<PopupMenuButton<String>>(find.byType(PopupMenuButton<String>));
      expect(popupMenuButton.color, Colors.white);

      // 验证全局主题的白色背景
      final theme = Theme.of(tester.element(find.byType(Scaffold)));
      expect(theme.dialogTheme.backgroundColor, Colors.white);
    });

    testWidgets('验证Notion风格设计一致性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            // Notion风格主题配置
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              titleTextStyle: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              contentTextStyle: TextStyle(
                color: Color(0xFF787774),
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          home: const Scaffold(
            body: Center(
              child: Text('Notion风格验证'),
            ),
          ),
        ),
      );

      // 验证主题配置符合Notion风格
      final theme = Theme.of(tester.element(find.text('Notion风格验证')));
      
      // 验证背景色
      expect(theme.dialogTheme.backgroundColor, Colors.white);
      
      // 验证圆角
      expect(theme.dialogTheme.shape, isA<RoundedRectangleBorder>());
      
      // 验证标题样式
      expect(theme.dialogTheme.titleTextStyle?.color, const Color(0xFF37352F));
      expect(theme.dialogTheme.titleTextStyle?.fontWeight, FontWeight.w600);
      expect(theme.dialogTheme.titleTextStyle?.fontSize, 20);
      
      // 验证内容样式
      expect(theme.dialogTheme.contentTextStyle?.color, const Color(0xFF787774));
      expect(theme.dialogTheme.contentTextStyle?.fontSize, 16);
      expect(theme.dialogTheme.contentTextStyle?.height, 1.5);
    });

    testWidgets('验证颜色方案一致性', (WidgetTester tester) async {
      // 定义OneDay应用的标准颜色
      const primaryBlue = Color(0xFF2E7EED);
      const darkText = Color(0xFF37352F);
      const lightBorder = Color(0xFFE3E2E0);
      const hintText = Color(0xFF6E6E6E);

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 验证主色调使用
                Container(
                  color: primaryBlue,
                  child: const Text('主色调测试'),
                ),
                
                // 验证深色文字
                Text(
                  '深色文字测试',
                  style: TextStyle(color: darkText),
                ),
                
                // 验证边框颜色
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: lightBorder),
                  ),
                  child: const Text('边框颜色测试'),
                ),
                
                // 验证提示文字颜色
                Text(
                  '提示文字测试',
                  style: TextStyle(color: hintText),
                ),
              ],
            ),
          ),
        ),
      );

      // 验证所有颜色都正确显示
      expect(find.text('主色调测试'), findsOneWidget);
      expect(find.text('深色文字测试'), findsOneWidget);
      expect(find.text('边框颜色测试'), findsOneWidget);
      expect(find.text('提示文字测试'), findsOneWidget);
    });
  });
}
