import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('弹窗背景色统一性简单测试', () {
    testWidgets('AlertDialog应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => const AlertDialog(
                      title: Text('测试对话框'),
                      content: Text('这是一个测试对话框'),
                    ),
                  );
                },
                child: const Text('显示对话框'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示对话框
      await tester.tap(find.text('显示对话框'));
      await tester.pumpAndSettle();

      // 验证对话框显示
      expect(find.text('测试对话框'), findsOneWidget);
      expect(find.text('这是一个测试对话框'), findsOneWidget);
    });

    testWidgets('ModalBottomSheet应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showModalBottomSheet(
                    context: context,
                    backgroundColor: Colors.white,
                    shape: const RoundedRectangleBorder(
                      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                    ),
                    builder: (context) => Container(
                      height: 200,
                      padding: const EdgeInsets.all(16),
                      child: const Text('底部弹窗内容'),
                    ),
                  );
                },
                child: const Text('显示底部弹窗'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示底部弹窗
      await tester.tap(find.text('显示底部弹窗'));
      await tester.pumpAndSettle();

      // 验证底部弹窗显示
      expect(find.text('底部弹窗内容'), findsOneWidget);
    });

    testWidgets('FilterChip应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: FilterChip(
                selected: false,
                label: const Text('测试筛选'),
                backgroundColor: Colors.white,
                side: const BorderSide(
                  color: Color(0xFFE3E2E0),
                  width: 1,
                ),
                onSelected: (bool value) {},
              ),
            ),
          ),
        ),
      );

      // 验证FilterChip显示
      expect(find.text('测试筛选'), findsOneWidget);
      
      // 验证FilterChip的背景色
      final filterChip = tester.widget<FilterChip>(find.byType(FilterChip));
      expect(filterChip.backgroundColor, Colors.white);
    });

    testWidgets('自定义Dialog应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      backgroundColor: Colors.transparent,
                      child: Container(
                        width: 300,
                        height: 200,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(color: const Color(0xFFE3E2E0), width: 1),
                        ),
                        child: const Center(
                          child: Text('自定义对话框'),
                        ),
                      ),
                    ),
                  );
                },
                child: const Text('显示自定义对话框'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示自定义对话框
      await tester.tap(find.text('显示自定义对话框'));
      await tester.pumpAndSettle();

      // 验证自定义对话框显示
      expect(find.text('自定义对话框'), findsOneWidget);
    });

    testWidgets('PopupMenuButton应该使用白色背景', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            appBar: AppBar(
              actions: [
                PopupMenuButton<String>(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                    side: const BorderSide(color: Color(0xFFE3E2E0), width: 1),
                  ),
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'option1',
                      child: Text('选项1'),
                    ),
                    const PopupMenuItem(
                      value: 'option2',
                      child: Text('选项2'),
                    ),
                  ],
                ),
              ],
            ),
            body: const Center(child: Text('测试页面')),
          ),
        ),
      );

      // 查找PopupMenuButton
      final popupButton = find.byType(PopupMenuButton<String>);
      expect(popupButton, findsOneWidget);

      // 点击PopupMenuButton
      await tester.tap(popupButton);
      await tester.pumpAndSettle();

      // 验证菜单项显示
      expect(find.text('选项1'), findsOneWidget);
      expect(find.text('选项2'), findsOneWidget);
    });

    testWidgets('全局主题配置验证', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            // Notion风格主题配置
            dialogTheme: const DialogThemeData(
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              titleTextStyle: TextStyle(
                color: Color(0xFF37352F),
                fontSize: 20,
                fontWeight: FontWeight.w600,
              ),
              contentTextStyle: TextStyle(
                color: Color(0xFF787774),
                fontSize: 16,
                height: 1.5,
              ),
            ),
          ),
          home: const Scaffold(
            body: Center(child: Text('Notion风格测试')),
          ),
        ),
      );

      // 验证主题配置
      final theme = Theme.of(tester.element(find.text('Notion风格测试')));
      
      // 验证对话框背景色
      expect(theme.dialogTheme.backgroundColor, Colors.white);
      
      // 验证圆角设计
      expect(theme.dialogTheme.shape, isA<RoundedRectangleBorder>());
      
      // 验证标题样式
      expect(theme.dialogTheme.titleTextStyle?.color, const Color(0xFF37352F));
      expect(theme.dialogTheme.titleTextStyle?.fontWeight, FontWeight.w600);
      
      // 验证内容样式
      expect(theme.dialogTheme.contentTextStyle?.color, const Color(0xFF787774));
    });
  });
}
