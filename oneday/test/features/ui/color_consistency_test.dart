import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('白色背景色一致性测试', () {
    testWidgets('验证Colors.white和Color(0xFFFFFFFF)的一致性', (
      WidgetTester tester,
    ) async {
      // 验证两种白色定义是否相同
      expect(
        Colors.white.toARGB32(),
        equals(const Color(0xFFFFFFFF).toARGB32()),
      );
      expect(Colors.white, equals(const Color(0xFFFFFFFF)));
    });

    testWidgets('验证不同组件的白色背景渲染一致性', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // AlertDialog样式的白色背景
                Container(
                  width: 200,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: const Center(child: Text('AlertDialog风格')),
                ),

                const SizedBox(height: 20),

                // Dialog + Container样式的白色背景
                Container(
                  width: 200,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Center(child: Text('Dialog风格')),
                ),

                const SizedBox(height: 20),

                // DropdownButton样式的白色背景
                Container(
                  width: 200,
                  height: 50,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: const Color(0xFFE3E2E0)),
                  ),
                  child: const Center(child: Text('DropdownButton风格')),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证所有容器都正确显示
      expect(find.text('AlertDialog风格'), findsOneWidget);
      expect(find.text('Dialog风格'), findsOneWidget);
      expect(find.text('DropdownButton风格'), findsOneWidget);
    });

    testWidgets('验证时间盒子和知忆相册使用相同的白色色值', (WidgetTester tester) async {
      // 模拟时间盒子的AlertDialog背景
      const timeboxBackground = Colors.white;

      // 模拟知忆相册的Dialog Container背景
      const memoryPalaceBackground = Colors.white;

      // 模拟知忆相册的DropdownButton背景
      const dropdownBackground = Colors.white;

      // 验证所有背景色都相同
      expect(
        timeboxBackground.toARGB32(),
        equals(memoryPalaceBackground.toARGB32()),
      );
      expect(
        timeboxBackground.toARGB32(),
        equals(dropdownBackground.toARGB32()),
      );
      expect(
        memoryPalaceBackground.toARGB32(),
        equals(dropdownBackground.toARGB32()),
      );

      // 验证都是纯白色
      expect(timeboxBackground.toARGB32(), equals(0xFFFFFFFF));
      expect(memoryPalaceBackground.toARGB32(), equals(0xFFFFFFFF));
      expect(dropdownBackground.toARGB32(), equals(0xFFFFFFFF));
    });

    testWidgets('验证Material Design elevation对白色背景的影响', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                // 无elevation的白色背景
                Material(
                  color: Colors.white,
                  elevation: 0,
                  child: SizedBox(
                    width: 200,
                    height: 100,
                    child: const Center(child: Text('无elevation')),
                  ),
                ),

                const SizedBox(height: 20),

                // 有elevation的白色背景
                Material(
                  color: Colors.white,
                  elevation: 4,
                  child: SizedBox(
                    width: 200,
                    height: 100,
                    child: const Center(child: Text('有elevation')),
                  ),
                ),

                const SizedBox(height: 20),

                // 高elevation的白色背景
                Material(
                  color: Colors.white,
                  elevation: 8,
                  child: SizedBox(
                    width: 200,
                    height: 100,
                    child: const Center(child: Text('高elevation')),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证所有Material组件都正确显示
      expect(find.text('无elevation'), findsOneWidget);
      expect(find.text('有elevation'), findsOneWidget);
      expect(find.text('高elevation'), findsOneWidget);
    });

    testWidgets('验证Material 3.0 surface tinting禁用效果', (
      WidgetTester tester,
    ) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            useMaterial3: true, // 启用Material 3.0
            brightness: Brightness.light,
          ),
          home: Scaffold(
            body: Column(
              children: [
                // 未禁用surface tinting的DropdownButton
                DropdownButton<String>(
                  value: null,
                  hint: const Text('未禁用surface tinting'),
                  dropdownColor: Colors.white,
                  items: const [
                    DropdownMenuItem(value: '选项1', child: Text('选项1')),
                  ],
                  onChanged: (value) {},
                ),

                const SizedBox(height: 20),

                // 禁用surface tinting的DropdownButton
                Theme(
                  data: ThemeData(
                    useMaterial3: true,
                    popupMenuTheme: const PopupMenuThemeData(
                      surfaceTintColor: Colors.transparent, // 禁用surface tinting
                    ),
                  ),
                  child: DropdownButton<String>(
                    value: null,
                    hint: const Text('已禁用surface tinting'),
                    dropdownColor: const Color(0xFFFFFFFF), // 明确的纯白色
                    items: const [
                      DropdownMenuItem(value: '选项1', child: Text('选项1')),
                    ],
                    onChanged: (value) {},
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证两个DropdownButton都正确显示
      expect(find.text('未禁用surface tinting'), findsOneWidget);
      expect(find.text('已禁用surface tinting'), findsOneWidget);
    });

    testWidgets('验证OneDay应用标准白色背景配置', (WidgetTester tester) async {
      // OneDay应用的标准配色
      const oneDayWhite = Colors.white; // #FFFFFF
      const oneDayBorder = Color(0xFFE3E2E0); // 浅灰边框
      const oneDayText = Color(0xFF37352F); // 深灰文字

      await tester.pumpWidget(
        MaterialApp(
          theme: ThemeData(
            dialogTheme: const DialogThemeData(
              backgroundColor: oneDayWhite,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
            ),
          ),
          home: Scaffold(
            backgroundColor: oneDayWhite,
            body: Container(
              decoration: BoxDecoration(
                color: oneDayWhite,
                border: Border.all(color: oneDayBorder),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text('OneDay标准配色测试', style: TextStyle(color: oneDayText)),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();
      expect(find.text('OneDay标准配色测试'), findsOneWidget);

      // 验证主题配置
      final theme = Theme.of(tester.element(find.text('OneDay标准配色测试')));
      expect(theme.dialogTheme.backgroundColor, equals(oneDayWhite));
    });
  });
}
