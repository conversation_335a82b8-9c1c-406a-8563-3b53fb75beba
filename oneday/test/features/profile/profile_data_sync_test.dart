import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/profile/profile_page.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/study_time/providers/study_time_providers.dart';
import 'package:oneday/features/memory_palace/providers/memory_palace_provider.dart';

void main() {
  group('ProfilePage 数据实时同步测试', () {
    setUp(() {
      // 每个测试前清理SharedPreferences
      SharedPreferences.setMockInitialValues({});
    });

    testWidgets('应该显示实时的学习统计数据', (WidgetTester tester) async {
      // 构建带有Provider的页面
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ProfilePage(),
          ),
        ),
      );

      // 等待页面加载完成
      await tester.pumpAndSettle();

      // 验证页面标题
      expect(find.text('我的'), findsOneWidget);

      // 验证学习概览部分存在
      expect(find.text('学习概览'), findsOneWidget);

      // 验证统计卡片存在
      expect(find.text('今日学习'), findsOneWidget);
      expect(find.text('累计工资'), findsOneWidget);
      expect(find.text('连续天数'), findsOneWidget);
      expect(find.text('记忆宫殿'), findsOneWidget);

      // 验证刷新按钮存在
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });

    testWidgets('点击刷新按钮应该更新数据', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ProfilePage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击刷新按钮
      await tester.tap(find.byIcon(Icons.refresh));
      await tester.pumpAndSettle();

      // 验证刷新成功提示
      expect(find.text('数据已刷新'), findsOneWidget);
    });

    testWidgets('应该响应时间盒子任务状态变化', (WidgetTester tester) async {
      late WidgetRef testRef;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                testRef = ref;
                return const ProfilePage();
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 模拟添加一个已完成的任务
      final newTask = TimeBoxTask(
        id: 'test_task_1',
        title: '测试任务',
        description: '这是一个测试任务',
        plannedMinutes: 30,
        status: TaskStatus.completed,
        priority: TaskPriority.medium,
        category: '学习',
        createdAt: DateTime.now(),
        startTime: DateTime.now().subtract(const Duration(minutes: 30)),
        endTime: DateTime.now(),
      );

      // 添加任务到时间盒子
      await testRef.read(timeBoxProvider.notifier).addTask(newTask);
      await tester.pumpAndSettle();

      // 验证页面没有报错
      expect(tester.takeException(), isNull);
    });

    testWidgets('应该显示正确的数据格式', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ProfilePage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找统计数据的文本，验证格式
      // 注意：由于是异步数据，可能显示默认值
      final studyTimeText = find.textContaining('m').first;
      final wageText = find.textContaining('¥').first;
      final streakText = find.textContaining('天').first;
      final palaceText = find.textContaining('个').first;

      expect(studyTimeText, findsOneWidget);
      expect(wageText, findsOneWidget);
      expect(streakText, findsOneWidget);
      expect(palaceText, findsOneWidget);
    });

    testWidgets('应该正确处理数据加载状态', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ProfilePage(),
          ),
        ),
      );

      // 在数据加载期间，页面应该正常显示
      await tester.pump();
      expect(find.text('学习概览'), findsOneWidget);

      // 等待数据加载完成
      await tester.pumpAndSettle();
      expect(find.text('学习概览'), findsOneWidget);
    });

    testWidgets('记忆宫殿数据应该正确显示', (WidgetTester tester) async {
      late WidgetRef testRef;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                testRef = ref;
                return const ProfilePage();
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 获取记忆宫殿统计数据
      final memoryPalaceStats = testRef.read(memoryPalaceStatsProvider);
      
      // 验证数据格式正确
      expect(memoryPalaceStats['formattedCount'], isA<String>());
      expect(memoryPalaceStats['formattedCount'], endsWith('个'));
      expect(memoryPalaceStats['totalCount'], isA<int>());
    });

    testWidgets('学习统计数据应该正确显示', (WidgetTester tester) async {
      late WidgetRef testRef;

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Consumer(
              builder: (context, ref, child) {
                testRef = ref;
                return const ProfilePage();
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 获取今日学习统计数据
      final todayStudySummary = testRef.read(todayStudySummaryProvider);
      
      // 验证数据格式正确
      expect(todayStudySummary['studyTime'], isA<String>());
      expect(todayStudySummary['wage'], isA<String>());
      expect(todayStudySummary['wage'], startsWith('¥'));
      expect(todayStudySummary['streakDays'], isA<String>());
      expect(todayStudySummary['streakDays'], endsWith('天'));
    });

    testWidgets('应该处理数据更新错误', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ProfilePage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 即使有数据错误，页面也应该正常显示
      expect(find.text('学习概览'), findsOneWidget);
      expect(find.text('今日学习'), findsOneWidget);
      expect(find.text('累计工资'), findsOneWidget);
      expect(find.text('连续天数'), findsOneWidget);
      expect(find.text('记忆宫殿'), findsOneWidget);
    });
  });
}
