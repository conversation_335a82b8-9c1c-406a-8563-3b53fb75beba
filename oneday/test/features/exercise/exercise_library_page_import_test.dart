import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/exercise_library_page.dart';
import 'package:oneday/features/exercise/custom_library_editor_page.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

void main() {
  group('ExerciseLibraryPage导入测试', () {
    test('应该能够正确导入所有必需的类', () {
      // 验证ExerciseLibraryPage类存在
      expect(ExerciseLibraryPage, isNotNull);
      
      // 验证CustomLibraryEditorPage类存在
      expect(CustomLibraryEditorPage, isNotNull);
      
      // 验证CustomActionLibraryService类存在
      expect(CustomActionLibraryService, isNotNull);
    });

    test('应该能够创建ExerciseLibraryPage实例', () {
      // 创建ExerciseLibraryPage实例
      const page = ExerciseLibraryPage();
      expect(page, isA<ExerciseLibraryPage>());
    });

    test('应该能够创建CustomActionLibraryService实例', () {
      // 创建CustomActionLibraryService实例
      final service = CustomActionLibraryService();
      expect(service, isA<CustomActionLibraryService>());
    });
  });
}
