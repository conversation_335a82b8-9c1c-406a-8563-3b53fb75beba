import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/exercise/custom_exercise_category.dart';

/// 集成测试：验证分类添加功能的完整流程
void main() {
  group('分类添加功能集成测试', () {
    late CustomExerciseCategoryManager manager;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      manager = CustomExerciseCategoryManager();
      await manager.loadFromStorage();
    });

    testWidgets('完整的分类添加流程测试', (WidgetTester tester) async {
      // 1. 验证初始状态
      expect(manager.categories.length, 0, reason: '初始状态应该没有分类');

      // 2. 模拟添加分类的完整流程
      final testCategory = CustomExerciseCategory(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        name: '新测试分类',
        icon: '🏃‍♂️',
        description: '这是一个新的测试分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 3. 执行添加操作
      await manager.addCategory(testCategory);

      // 4. 验证分类已添加到内存中
      expect(manager.categories.length, 1, reason: '应该有一个分类');
      expect(manager.categories.first.name, '新测试分类', reason: '分类名称应该正确');
      expect(manager.categories.first.icon, '🏃‍♂️', reason: '分类图标应该正确');

      // 5. 验证数据持久化
      final newManager = CustomExerciseCategoryManager();
      await newManager.loadFromStorage();
      
      expect(newManager.categories.length, 1, reason: '重新加载后应该有一个分类');
      expect(newManager.categories.first.name, '新测试分类', reason: '重新加载后分类名称应该正确');

      // 6. 验证分类名称检查功能
      expect(newManager.isCategoryNameExists('新测试分类'), true, reason: '应该能检测到已存在的分类名称');
      expect(newManager.isCategoryNameExists('不存在的分类'), false, reason: '应该能检测到不存在的分类名称');

      // 7. 测试添加重复名称的分类（应该被阻止）
      final duplicateCategory = CustomExerciseCategory(
        id: 'duplicate_id',
        name: '新测试分类', // 重复的名称
        icon: '🤸‍♂️',
        description: '重复名称的分类',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      // 在实际应用中，这应该被UI层阻止，但我们可以测试检查逻辑
      expect(newManager.isCategoryNameExists(duplicateCategory.name), true, 
             reason: '应该检测到重复的分类名称');
    });

    testWidgets('分类列表显示测试', (WidgetTester tester) async {
      // 添加多个测试分类
      final categories = [
        CustomExerciseCategory(
          id: 'cat1',
          name: '健身分类',
          icon: '🏋️‍♂️',
          description: '健身相关动作',
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        ),
        CustomExerciseCategory(
          id: 'cat2',
          name: '瑜伽分类',
          icon: '🧘‍♂️',
          description: '瑜伽相关动作',
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        ),
        CustomExerciseCategory(
          id: 'cat3',
          name: '跑步分类',
          icon: '🏃‍♂️',
          description: '跑步相关动作',
          createdAt: DateTime.now(),
          lastModified: DateTime.now(),
        ),
      ];

      // 添加所有分类
      for (final category in categories) {
        await manager.addCategory(category);
      }

      // 验证所有分类都已添加
      expect(manager.categories.length, 3, reason: '应该有3个分类');
      
      // 验证分类顺序和内容
      expect(manager.categories[0].name, '健身分类');
      expect(manager.categories[1].name, '瑜伽分类');
      expect(manager.categories[2].name, '跑步分类');

      // 验证获取所有分类名称功能
      final categoryNames = manager.getAllCategoryNames();
      expect(categoryNames.length, 3);
      expect(categoryNames.contains('健身分类'), true);
      expect(categoryNames.contains('瑜伽分类'), true);
      expect(categoryNames.contains('跑步分类'), true);
    });

    testWidgets('分类删除功能测试', (WidgetTester tester) async {
      // 添加测试分类
      final testCategory = CustomExerciseCategory(
        id: 'delete_test',
        name: '待删除分类',
        icon: '🗑️',
        description: '这个分类将被删除',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await manager.addCategory(testCategory);
      expect(manager.categories.length, 1, reason: '添加后应该有一个分类');

      // 删除分类
      await manager.deleteCategory('delete_test');
      expect(manager.categories.length, 0, reason: '删除后应该没有分类');

      // 验证持久化
      final newManager = CustomExerciseCategoryManager();
      await newManager.loadFromStorage();
      expect(newManager.categories.length, 0, reason: '重新加载后应该没有分类');
    });

    testWidgets('分类更新功能测试', (WidgetTester tester) async {
      // 添加测试分类
      final originalCategory = CustomExerciseCategory(
        id: 'update_test',
        name: '原始分类',
        icon: '📝',
        description: '原始描述',
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      await manager.addCategory(originalCategory);

      // 更新分类
      final updatedCategory = CustomExerciseCategory(
        id: 'update_test',
        name: '更新后分类',
        icon: '✅',
        description: '更新后描述',
        createdAt: originalCategory.createdAt,
        lastModified: DateTime.now(),
      );

      await manager.updateCategory(updatedCategory);

      // 验证更新
      expect(manager.categories.length, 1, reason: '更新后应该仍有一个分类');
      expect(manager.categories.first.name, '更新后分类', reason: '分类名称应该已更新');
      expect(manager.categories.first.icon, '✅', reason: '分类图标应该已更新');
      expect(manager.categories.first.description, '更新后描述', reason: '分类描述应该已更新');

      // 验证持久化
      final newManager = CustomExerciseCategoryManager();
      await newManager.loadFromStorage();
      expect(newManager.categories.first.name, '更新后分类', reason: '重新加载后分类名称应该正确');
    });
  });
}
