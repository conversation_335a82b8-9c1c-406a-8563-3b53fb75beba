import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/time_box/timebox_list_page.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

void main() {
  group('TimeBox UI Tests', () {
    testWidgets('TaskCreateDialog should display FilterChips for priority and category', (WidgetTester tester) async {
      // 创建测试应用
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskCreateDialog(
                      onTaskCreated: (task) {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示对话框
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证对话框标题
      expect(find.text('创建时间盒子'), findsOneWidget);

      // 验证优先级下拉选择器
      expect(find.text('优先级'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<TaskPriority>), findsOneWidget);

      // 验证学科分类下拉选择器
      expect(find.text('学科分类'), findsOneWidget);
      expect(find.byType(DropdownButtonFormField<String>), findsOneWidget);

      // 验证输入框
      expect(find.text('任务标题'), findsOneWidget);
      expect(find.text('任务描述（可选）'), findsOneWidget);
      expect(find.text('预计时长（分钟）'), findsOneWidget);

      // 验证按钮
      expect(find.text('取消'), findsOneWidget);
      expect(find.text('创建'), findsOneWidget);
    });

    testWidgets('Form fields should be present', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskCreateDialog(
                      onTaskCreated: (task) {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证表单字段存在
      expect(find.byType(TextFormField), findsNWidgets(3)); // 标题、描述、时长
      expect(find.byType(Form), findsOneWidget);
    });

    testWidgets('Input fields should have proper labels', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskCreateDialog(
                      onTaskCreated: (task) {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证输入字段的标签
      expect(find.text('任务标题'), findsOneWidget);
      expect(find.text('任务描述（可选）'), findsOneWidget);
      expect(find.text('预计时长（分钟）'), findsOneWidget);
      expect(find.text('优先级'), findsOneWidget);
      expect(find.text('学科分类'), findsOneWidget);
    });

    testWidgets('Dialog should have proper Notion-style styling', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder: (context) => ElevatedButton(
                onPressed: () {
                  showDialog(
                    context: context,
                    builder: (context) => TaskCreateDialog(
                      onTaskCreated: (task) {},
                    ),
                  );
                },
                child: const Text('Show Dialog'),
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证AlertDialog的样式
      final alertDialog = tester.widget<AlertDialog>(find.byType(AlertDialog));
      expect(alertDialog.backgroundColor, equals(Colors.white));
      expect(alertDialog.shape, isA<RoundedRectangleBorder>());
      expect(alertDialog.elevation, equals(4));

      // 验证标题样式
      final titleText = tester.widget<Text>(find.text('创建时间盒子'));
      expect(titleText.style?.color, equals(const Color(0xFF37352F)));
      expect(titleText.style?.fontWeight, equals(FontWeight.w600));
      expect(titleText.style?.fontSize, equals(18));
    });
  });
}
