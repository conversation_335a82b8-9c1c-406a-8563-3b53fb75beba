import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/vocabulary/word_meaning_service.dart';

void main() {
  group('WordMeaningService Tests', () {
    late ProviderContainer container;
    late WordMeaningService wordMeaningService;

    setUp(() {
      container = ProviderContainer();
      wordMeaningService = container.read(wordMeaningServiceProvider);
    });

    tearDown(() {
      container.dispose();
    });

    test('应该能够创建WordMeaningService实例', () {
      expect(wordMeaningService, isNotNull);
      expect(wordMeaningService, isA<WordMeaningService>());
    });

    test('空字符串应该返回null', () async {
      final result = await wordMeaningService.getWordMeaning('');
      expect(result, isNull);
    });

    test('应该能够处理不存在的单词', () async {
      final result = await wordMeaningService.getWordMeaning('nonexistentword123');
      // 可能返回null，这是正常的
      expect(result, anyOf(isNull, isA<WordMeaningResult>()));
    });

    test('批量获取单词释义应该返回Map', () async {
      final words = ['test', 'example', 'nonexistent'];
      final results = await wordMeaningService.getBatchWordMeanings(words);
      
      expect(results, isA<Map<String, WordMeaningResult>>());
      // 结果可能为空，这取决于词库中是否有这些单词
    });

    test('按释义搜索应该返回列表', () async {
      final results = await wordMeaningService.searchWordsByMeaning('测试');
      
      expect(results, isA<List<WordMeaningResult>>());
      expect(results.length, lessThanOrEqualTo(20)); // 默认限制为20
    });

    test('WordMeaningResult应该包含必要的字段', () {
      final result = WordMeaningResult(
        word: 'test',
        definition: '测试',
        phonetic: '/test/',
        partOfSpeech: 'noun',
        source: WordSource.graduateExam,
        sourceId: 'graduate_exam',
        sourceName: '考研词库',
      );

      expect(result.word, equals('test'));
      expect(result.definition, equals('测试'));
      expect(result.phonetic, equals('/test/'));
      expect(result.partOfSpeech, equals('noun'));
      expect(result.source, equals(WordSource.graduateExam));
      expect(result.sourceId, equals('graduate_exam'));
      expect(result.sourceName, equals('考研词库'));
    });

    test('WordSource枚举应该包含正确的值', () {
      expect(WordSource.values, contains(WordSource.graduateExam));
      expect(WordSource.values, contains(WordSource.custom));
      expect(WordSource.values.length, equals(2));
    });
  });
}
