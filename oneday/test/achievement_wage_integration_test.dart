import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/achievement/services/achievement_service.dart';
import 'package:oneday/features/achievement/models/achievement.dart';
import 'package:oneday/features/wage_system/services/wage_service.dart';

void main() {
  group('Achievement and Wage System Integration Tests', () {
    late AchievementService achievementService;
    late WageService wageService;

    setUp(() async {
      // 设置测试环境
      SharedPreferences.setMockInitialValues({});
      achievementService = AchievementService();
      wageService = WageService();
    });

    tearDown(() async {
      // 清理测试数据
      await achievementService.resetAllData();
      await wageService.resetAllData();
    });

    test('解锁成就应该正确发放工资奖励', () async {
      // 创建一个测试成就
      final testAchievement = Achievement(
        id: 'test_achievement',
        name: '测试成就',
        description: '这是一个测试成就',
        icon: '🏆',
        type: AchievementType.learning,
        rarity: AchievementRarity.common,
        experienceReward: 100,
        wageReward: 50.0,
        unlockCondition: '完成测试',
        createdAt: DateTime.now(),
      );

      // 获取初始余额
      final initialBalance = await wageService.getUserBalance();
      expect(initialBalance, equals(0.0));

      // 模拟解锁成就（这里我们需要手动调用工资奖励方法，因为测试成就不在数据中）
      final success = await wageService.addWageReward(
        testAchievement.wageReward,
        description: '成就奖励: ${testAchievement.name}',
      );

      expect(success, isTrue);

      // 验证余额增加
      final newBalance = await wageService.getUserBalance();
      expect(newBalance, equals(50.0));

      // 验证交易记录
      final transactions = await wageService.getTransactions();
      expect(transactions.length, equals(1));
      expect(transactions.first.amount, equals(50.0));
      expect(transactions.first.description, contains('测试成就'));
    });

    test('工资服务统计数据应该正确计算', () async {
      // 添加多笔交易
      await wageService.addWageReward(100.0, description: '成就奖励1');
      await wageService.addStudyIncome(200.0, description: '学习收入1');
      await wageService.addBonusIncome(50.0, description: '奖励收入1');

      // 获取统计数据
      final stats = await wageService.getStatistics();

      expect(stats['balance'], equals(350.0));
      expect(stats['totalIncome'], equals(350.0));
      expect(stats['transactionCount'], equals(3));
    });

    test('余额不足时扣费应该失败', () async {
      // 尝试扣除费用但余额不足
      final success = await wageService.deductAmount(
        100.0,
        description: '购买道具',
      );

      expect(success, isFalse);

      // 余额应该保持为0
      final balance = await wageService.getUserBalance();
      expect(balance, equals(0.0));
    });

    test('余额充足时扣费应该成功', () async {
      // 先添加一些余额
      await wageService.addWageReward(200.0, description: '初始余额');

      // 扣除费用
      final success = await wageService.deductAmount(
        100.0,
        description: '购买道具',
      );

      expect(success, isTrue);

      // 验证余额减少
      final balance = await wageService.getUserBalance();
      expect(balance, equals(100.0));

      // 验证交易记录
      final transactions = await wageService.getTransactions();
      expect(transactions.length, equals(2));
      expect(transactions.first.amount, equals(-100.0)); // 最新的交易是扣费
    });
  });
}
