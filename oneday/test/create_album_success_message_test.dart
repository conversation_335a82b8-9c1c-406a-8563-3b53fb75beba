import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/memory_palace/palace_manager_page.dart';

void main() {
  group('创建相册成功提示消息测试', () {
    testWidgets('应该显示自定义时长的成功提示消息', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开创建相册对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框已打开
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));

      // 输入相册标题
      final titleField = find.widgetWithText(TextField, '请输入相册标题');
      await tester.enterText(titleField, '测试相册');
      await tester.pump();

      // 注意：在测试环境中无法真正选择图片，所以这个测试主要验证UI逻辑
      // 实际的图片选择和成功提示需要在真实设备上测试
    });

    testWidgets('SnackBar应该支持自定义显示时长', (WidgetTester tester) async {
      // 创建一个简单的测试页面来验证SnackBar功能
      bool snackBarShown = false;
      
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('测试消息'),
                      duration: Duration(milliseconds: 1500),
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                  snackBarShown = true;
                },
                child: const Text('显示SnackBar'),
              ),
            ),
          ),
        ),
      );

      // 点击按钮显示SnackBar
      await tester.tap(find.text('显示SnackBar'));
      await tester.pump();

      // 验证SnackBar已显示
      expect(find.text('测试消息'), findsOneWidget);
      expect(snackBarShown, true);

      // 等待自定义时长后验证SnackBar消失
      await tester.pump(const Duration(milliseconds: 1600));
      expect(find.text('测试消息'), findsNothing);
    });

    testWidgets('应该能够主动清除SnackBar', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: Column(
                children: [
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text('长时间显示的消息'),
                          duration: Duration(seconds: 10), // 长时间显示
                        ),
                      );
                    },
                    child: const Text('显示长时间SnackBar'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      ScaffoldMessenger.of(context).clearSnackBars();
                    },
                    child: const Text('清除SnackBar'),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // 显示长时间SnackBar
      await tester.tap(find.text('显示长时间SnackBar'));
      await tester.pump();
      expect(find.text('长时间显示的消息'), findsOneWidget);

      // 主动清除SnackBar
      await tester.tap(find.text('清除SnackBar'));
      await tester.pump();
      expect(find.text('长时间显示的消息'), findsNothing);
    });

    testWidgets('创建相册对话框应该有正确的延迟时间设置', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PalaceManagerPage(),
        ),
      );

      // 打开创建相册对话框
      final fab = find.byType(FloatingActionButton);
      await tester.tap(fab);
      await tester.pumpAndSettle();

      // 验证对话框组件存在
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));
      expect(find.text('选择图片'), findsOneWidget);
      expect(find.text('相册信息'), findsOneWidget);
    });

    testWidgets('应该有正确的成功消息文本格式', (WidgetTester tester) async {
      const testTitle = '我的测试相册';
      const expectedMessage = '成功创建知忆相册"$testTitle"';
      
      // 验证消息格式正确
      expect(expectedMessage, '成功创建知忆相册"我的测试相册"');
      expect(expectedMessage.contains(testTitle), true);
      expect(expectedMessage.startsWith('成功创建知忆相册'), true);
    });

    testWidgets('SnackBar应该有正确的样式配置', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('成功消息'),
                      backgroundColor: const Color(0xFF4CAF50), // 绿色成功色
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      duration: const Duration(milliseconds: 1500),
                    ),
                  );
                },
                child: const Text('显示成功SnackBar'),
              ),
            ),
          ),
        ),
      );

      // 显示SnackBar
      await tester.tap(find.text('显示成功SnackBar'));
      await tester.pump();

      // 验证SnackBar存在
      expect(find.text('成功消息'), findsOneWidget);
      
      // 验证SnackBar样式（通过查找SnackBar组件）
      final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
      expect(snackBar.backgroundColor, const Color(0xFF4CAF50));
      expect(snackBar.behavior, SnackBarBehavior.floating);
      expect(snackBar.duration, const Duration(milliseconds: 1500));
    });

    testWidgets('错误消息应该有不同的样式', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Builder(
            builder: (context) => Scaffold(
              body: ElevatedButton(
                onPressed: () {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('错误消息'),
                      backgroundColor: const Color(0xFFEB5757), // 红色错误色
                      behavior: SnackBarBehavior.floating,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  );
                },
                child: const Text('显示错误SnackBar'),
              ),
            ),
          ),
        ),
      );

      // 显示错误SnackBar
      await tester.tap(find.text('显示错误SnackBar'));
      await tester.pump();

      // 验证SnackBar存在
      expect(find.text('错误消息'), findsOneWidget);
      
      // 验证错误SnackBar样式
      final snackBar = tester.widget<SnackBar>(find.byType(SnackBar));
      expect(snackBar.backgroundColor, const Color(0xFFEB5757));
      expect(snackBar.behavior, SnackBarBehavior.floating);
    });
  });
}
