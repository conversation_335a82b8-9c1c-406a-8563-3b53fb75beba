import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/date_symbol_data_local.dart';
import 'package:oneday/features/reflection/reflection_log_page.dart';

void main() {
  group('iPad 横屏优化日志日历视图溢出测试', () {
    setUpAll(() async {
      // 初始化本地化数据
      await initializeDateFormatting('zh_CN', null);
    });

    testWidgets('iPad 横屏模式 - 日历视图应该没有溢出错误', (WidgetTester tester) async {
      // 设置 iPad 横屏尺寸 (1366x1024)
      await tester.binding.setSurfaceSize(const Size(1366, 1024));
      
      bool hasOverflowError = false;
      String? errorMessage;

      try {
        // 构建优化日志页面
        await tester.pumpWidget(
          MaterialApp(
            home: const ReflectionLogPage(),
            theme: ThemeData(
              useMaterial3: true,
              primaryColor: const Color(0xFF2E7EED),
            ),
          ),
        );

        // 等待页面完全加载
        await tester.pumpAndSettle();

        // 验证页面标题存在
        expect(find.text('优化日志'), findsOneWidget);

        // 点击日历视图按钮
        final calendarButton = find.byIcon(Icons.calendar_month_outlined);
        expect(calendarButton, findsOneWidget);
        
        await tester.tap(calendarButton);
        await tester.pumpAndSettle();

        // 验证日历视图已显示
        expect(find.byType(GridView), findsOneWidget);

        // 验证没有溢出错误
        expect(tester.takeException(), isNull);

        print('✅ iPad 横屏模式 - 日历视图布局正常，无溢出错误');

      } catch (e) {
        hasOverflowError = true;
        errorMessage = e.toString();
        
        // 特别检查是否是溢出错误
        if (e.toString().contains('overflow') || e.toString().contains('OVERFLOW')) {
          fail('发现布局溢出错误: $errorMessage');
        }
      }

      // 验证没有溢出错误
      expect(hasOverflowError, false, reason: 'Calendar view should not have overflow errors: $errorMessage');
    });

    testWidgets('iPad 横屏模式 - 日历网格响应式布局测试', (WidgetTester tester) async {
      // 设置 iPad 横屏尺寸
      await tester.binding.setSurfaceSize(const Size(1366, 1024));

      await tester.pumpWidget(
        MaterialApp(
          home: const ReflectionLogPage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开日历视图
      await tester.tap(find.byIcon(Icons.calendar_month_outlined));
      await tester.pumpAndSettle();

      // 验证 LayoutBuilder 正确工作
      expect(find.byType(LayoutBuilder), findsWidgets);

      // 验证日历网格存在
      final gridView = find.byType(GridView);
      expect(gridView, findsOneWidget);

      // 获取 GridView 组件
      final GridView grid = tester.widget(gridView);
      final delegate = grid.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount;

      // 验证网格配置适合 iPad 横屏
      expect(delegate.crossAxisCount, 7); // 一周7天
      expect(delegate.childAspectRatio, greaterThan(1.0)); // iPad 横屏应该有更大的宽高比

      print('✅ iPad 横屏模式 - 日历网格响应式布局正确');
    });

    testWidgets('不同屏幕尺寸下的响应式测试', (WidgetTester tester) async {
      final testSizes = [
        const Size(390, 844),   // iPhone 14 竖屏
        const Size(844, 390),   // iPhone 14 横屏
        const Size(820, 1180),  // iPad 竖屏
        const Size(1366, 1024), // iPad 横屏
        const Size(1920, 1080), // 大屏设备
      ];

      for (final size in testSizes) {
        await tester.binding.setSurfaceSize(size);

        await tester.pumpWidget(
          MaterialApp(
            home: const ReflectionLogPage(),
            theme: ThemeData(
              useMaterial3: true,
              primaryColor: const Color(0xFF2E7EED),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // 打开日历视图 - 尝试找到日历按钮
        final calendarButton = find.byIcon(Icons.calendar_month_outlined);
        if (calendarButton.evaluate().isNotEmpty) {
          await tester.tap(calendarButton);
          await tester.pumpAndSettle();
        }

        // 验证没有溢出错误
        expect(tester.takeException(), isNull, 
               reason: 'No overflow should occur at size ${size.width}x${size.height}');

        print('✅ 屏幕尺寸 ${size.width}x${size.height} - 布局正常');
      }
    });

    testWidgets('日历视图组件层次结构测试', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(1366, 1024));

      await tester.pumpWidget(
        MaterialApp(
          home: const ReflectionLogPage(),
          theme: ThemeData(
            useMaterial3: true,
            primaryColor: const Color(0xFF2E7EED),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开日历视图
      await tester.tap(find.byIcon(Icons.calendar_month_outlined));
      await tester.pumpAndSettle();

      // 验证关键组件存在
      expect(find.byType(LayoutBuilder), findsWidgets);
      expect(find.byType(Container), findsWidgets);
      expect(find.byType(Column), findsWidgets);
      expect(find.byType(Row), findsWidgets);
      expect(find.byType(GridView), findsOneWidget);

      // 验证月份导航按钮
      expect(find.byIcon(Icons.chevron_left), findsOneWidget);
      expect(find.byIcon(Icons.chevron_right), findsOneWidget);

      print('✅ 日历视图组件层次结构正确');
    });
  });
}
