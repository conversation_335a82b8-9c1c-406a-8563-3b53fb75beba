import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/action_library_category_manager.dart';

void main() {
  group('ActionLibraryCategoryManager Tests', () {
    late ActionLibraryCategoryManager manager;

    setUp(() {
      manager = ActionLibraryCategoryManager();
      manager.initializeDefaultCategories();
    });

    test('应该包含默认分类', () {
      final categories = manager.getAllCategoryNames();
      expect(categories, contains('默认分类'));
    });

    test('分类名称应该是唯一的', () {
      final categories = manager.getAllCategoryNames();
      final uniqueCategories = categories.toSet().toList();
      expect(categories.length, equals(uniqueCategories.length));
    });

    test('默认分类应该在列表的第一位', () {
      final categories = manager.getAllCategoryNames();
      expect(categories.first, equals('默认分类'));
    });

    test('应该包含所有预期的分类', () {
      final categories = manager.getAllCategoryNames();
      
      // 检查是否包含主要分类
      expect(categories, contains('默认分类'));
      expect(categories, contains('健身'));
      expect(categories, contains('运动'));
      expect(categories, contains('养生'));
      expect(categories, contains('日常'));
      
      // 检查是否包含子分类
      expect(categories, contains('力量训练'));
      expect(categories, contains('有氧运动'));
      expect(categories, contains('瑜伽'));
      expect(categories, contains('护眼'));
    });
  });
}
