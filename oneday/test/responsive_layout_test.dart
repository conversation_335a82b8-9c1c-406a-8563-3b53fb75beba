import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/home/<USER>';

void main() {
  group('主页响应式布局测试', () {
    testWidgets('iPad 台前调度模式 - 窄屏布局测试', (WidgetTester tester) async {
      // 设置窄屏尺寸（iPad 台前调度小窗口）
      await tester.binding.setSurfaceSize(const Size(400, 600));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    // 测试快捷入口组件
                    Container(
                      width: 400,
                      padding: const EdgeInsets.all(16),
                      child: _TestQuickActions(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 验证快捷入口卡片存在
      expect(find.text('具身记忆'), findsOneWidget);
      expect(find.text('时间盒子'), findsOneWidget);
      expect(find.text('知忆相册'), findsOneWidget);
      expect(find.text('词汇管理'), findsOneWidget);
    });

    testWidgets('iPad 台前调度模式 - 中等屏幕布局测试', (WidgetTester tester) async {
      // 设置中等屏幕尺寸（iPad 台前调度中等窗口）
      await tester.binding.setSurfaceSize(const Size(700, 800));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      width: 700,
                      padding: const EdgeInsets.all(16),
                      child: _TestQuickActions(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('iPad 台前调度模式 - 宽屏布局测试', (WidgetTester tester) async {
      // 设置宽屏尺寸（iPad 台前调度大窗口）
      await tester.binding.setSurfaceSize(const Size(1000, 800));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: SingleChildScrollView(
                child: Column(
                  children: [
                    Container(
                      width: 1000,
                      padding: const EdgeInsets.all(16),
                      child: _TestQuickActions(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
    });

    testWidgets('统计卡片响应式布局测试', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(400, 600));
      
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: SingleChildScrollView(
              child: Column(
                children: [
                  Container(
                    width: 400,
                    padding: const EdgeInsets.all(16),
                    child: _TestStatsCards(),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);
      
      // 验证统计卡片存在
      expect(find.text('学习时长'), findsOneWidget);
      expect(find.text('虚拟工资'), findsOneWidget);
    });
  });
}

// 测试用的快捷操作组件
class _TestQuickActions extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    final actions = [
      {
        'title': '具身记忆',
        'subtitle': '五感记忆',
        'icon': Icons.fitness_center,
        'color': const Color(0xFFE03E3E),
      },
      {
        'title': '时间盒子',
        'subtitle': '专注学习',
        'icon': Icons.timer_outlined,
        'color': const Color(0xFF0F7B6C),
      },
      {
        'title': '知忆相册',
        'subtitle': '场景记忆',
        'icon': Icons.account_balance,
        'color': const Color(0xFF7C3AED),
      },
      {
        'title': '词汇管理',
        'subtitle': '考研词汇',
        'icon': Icons.library_books,
        'color': const Color(0xFF2E7EED),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快捷入口',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        LayoutBuilder(
          builder: (context, constraints) {
            int columns = 2;
            double aspectRatio = 1.2;
            double spacing = 12;

            if (constraints.maxWidth > 900) {
              columns = 4;
              aspectRatio = 1.0;
              spacing = 16;
            } else if (constraints.maxWidth > 600) {
              columns = 3;
              aspectRatio = 1.1;
              spacing = 14;
            } else if (constraints.maxWidth > 400) {
              columns = 2;
              aspectRatio = 1.2;
              spacing = 12;
            } else {
              columns = 2;
              aspectRatio = 1.3;
              spacing = 8;
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: columns,
                crossAxisSpacing: spacing,
                mainAxisSpacing: spacing,
                childAspectRatio: aspectRatio,
              ),
              itemCount: actions.length,
              itemBuilder: (context, index) {
                final action = actions[index];
                return QuickActionCard(
                  title: action['title'] as String,
                  subtitle: action['subtitle'] as String,
                  icon: action['icon'] as IconData,
                  color: action['color'] as Color,
                  onTap: () {},
                );
              },
            );
          },
        ),
      ],
    );
  }
}

// 测试用的统计卡片组件
class _TestStatsCards extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: StatsCard(
                title: '学习时长',
                value: '3h 45m',
                subtitle: '今日已学习',
                color: const Color(0xFF2E7EED),
                icon: Icons.schedule,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: StatsCard(
                title: '虚拟工资',
                value: '¥750',
                subtitle: '今日收入',
                color: const Color(0xFF0F7B6C),
                icon: Icons.attach_money,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
