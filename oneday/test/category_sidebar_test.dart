import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/exercise/manage_custom_libraries_dialog.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';

void main() {
  group('分类抽屉栏功能测试', () {
    late CustomActionLibraryService mockService;

    setUp(() {
      mockService = CustomActionLibraryService();
    });

    testWidgets('应该显示左上角三个杠按钮而不是分类筛选按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找三个杠按钮（menu图标）
      expect(find.byIcon(Icons.menu), findsOneWidget);

      // 确保不再有分类筛选按钮（filter_list图标）
      expect(find.byIcon(Icons.filter_list), findsNothing);

      // 验证按钮的tooltip
      final menuButton = find.byIcon(Icons.menu);
      expect(menuButton, findsOneWidget);

      final menuButtonWidget = tester.widget<IconButton>(
        find.ancestor(of: menuButton, matching: find.byType(IconButton)).first,
      );
      expect(menuButtonWidget.tooltip, equals('分类管理'));
    });

    testWidgets('点击三个杠按钮应该打开分类抽屉栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击三个杠按钮
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 抽屉栏应该显示
      expect(find.text('分类'), findsOneWidget);

      // 应该有添加分类按钮
      expect(find.byIcon(Icons.add), findsAtLeastNWidgets(1));
    });

    testWidgets('分类抽屉栏应该显示正确的标题和按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开抽屉栏
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 验证抽屉栏标题
      expect(find.text('分类'), findsOneWidget);

      // 验证文件夹图标（可能有多个）
      expect(find.byIcon(Icons.folder_outlined), findsAtLeastNWidgets(1));

      // 验证添加按钮（可能有多个）
      expect(find.byIcon(Icons.add), findsAtLeastNWidgets(1));

      // 验证关闭按钮（可能有多个）
      expect(find.byIcon(Icons.close), findsAtLeastNWidgets(1));
    });

    testWidgets('分类项应该有更多操作按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 等待分类管理器初始化
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();

      // 打开抽屉栏
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 等待分类数据加载
      await tester.pump(const Duration(milliseconds: 500));

      // 查找更多操作按钮（三个点图标）
      final moreButtons = find.byIcon(Icons.more_horiz);
      print('找到的更多按钮数量: ${tester.widgetList(moreButtons).length}');

      // 查找分类文本
      final categoryTexts = find.textContaining('健身');
      print('找到的分类文本: ${tester.widgetList(categoryTexts).length}');

      // 如果有分类数据，应该有更多按钮
      if (tester.widgetList(categoryTexts).isNotEmpty) {
        expect(moreButtons, findsAtLeastNWidgets(1));
      }
    });

    testWidgets('点击更多操作按钮应该显示菜单', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: ManageCustomLibrariesDialog(
              customLibraryService: mockService,
              onLibrariesChanged: () {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 打开抽屉栏
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 如果有分类项，点击第一个更多按钮
      final moreButtons = find.byIcon(Icons.more_horiz);
      if (tester.widgetList(moreButtons).isNotEmpty) {
        await tester.tap(moreButtons.first);
        await tester.pumpAndSettle();

        // 验证菜单项
        expect(find.text('编辑分类'), findsOneWidget);
        expect(find.text('添加子分类'), findsOneWidget);
        expect(find.text('共享到社区'), findsOneWidget);
        expect(find.text('删除分类'), findsOneWidget);
      }
    });
  });
}
