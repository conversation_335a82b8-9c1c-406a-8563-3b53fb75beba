import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/vocabulary/word_model.dart';
import 'package:oneday/features/vocabulary/vocabulary_learning_service.dart';

void main() {
  group('词汇管理系统测试', () {
    test('WordDetails 模型测试', () {
      final wordDetails = WordDetails(
        id: 1,
        definition: '测试定义',
        frequency: 100,
        difficulty: 'intermediate',
        category: 'basic',
        alternativeSpellings: [],
        partOfSpeech: 'noun',
        examples: ['测试例句'],
        tags: ['test'],
        priority: 'high',
        phonetic: '/test/',
        isCustom: false,
      );

      expect(wordDetails.id, 1);
      expect(wordDetails.definition, '测试定义');
      expect(wordDetails.difficulty, 'intermediate');
      expect(wordDetails.isCustom, false);
    });

    test('VocabularyCategory 模型测试', () {
      final category = VocabularyCategory(
        id: 'test_category',
        name: '测试分类',
        description: '这是一个测试分类',
        totalWords: 100,
        selectedWords: 10,
        isCustom: true,
        createdAt: DateTime.now(),
        wordIds: ['word1', 'word2'],
      );

      expect(category.id, 'test_category');
      expect(category.name, '测试分类');
      expect(category.totalWords, 100);
      expect(category.selectedWords, 10);
      expect(category.isCustom, true);
      expect(category.wordIds.length, 2);
    });

    test('WordLearningProgress 模型测试', () {
      final progress = WordLearningProgress(
        wordId: 'test_word',
        isSelected: true,
        isLearned: false,
        reviewCount: 3,
        masteryLevel: 0.7,
        reviewHistory: [DateTime.now()],
      );

      expect(progress.wordId, 'test_word');
      expect(progress.isSelected, true);
      expect(progress.isLearned, false);
      expect(progress.reviewCount, 3);
      expect(progress.masteryLevel, 0.7);
      expect(progress.reviewHistory.length, 1);
    });

    test('CustomVocabulary 模型测试', () {
      final customWord = CustomWord(
        id: 'custom_word_1',
        word: 'test',
        definition: '测试',
        phonetic: '/test/',
        partOfSpeech: 'noun',
        examples: ['This is a test'],
        tags: ['example'],
        createdAt: DateTime.now(),
      );

      final customVocab = CustomVocabulary(
        id: 'custom_vocab_1',
        name: '自定义词库',
        description: '用于测试的自定义词库',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        words: [customWord],
        metadata: {'version': '1.0'},
      );

      expect(customVocab.id, 'custom_vocab_1');
      expect(customVocab.name, '自定义词库');
      expect(customVocab.words.length, 1);
      expect(customVocab.words.first.word, 'test');
      expect(customVocab.metadata['version'], '1.0');
    });

    test('VocabularyLearningSession 模型测试', () {
      final wordDetails = WordDetails(
        id: 1,
        definition: '测试',
        frequency: 100,
        difficulty: 'intermediate',
        category: 'basic',
        alternativeSpellings: [],
        partOfSpeech: 'noun',
        examples: [],
        tags: [],
        priority: 'high',
      );

      final session = VocabularyLearningSession(
        words: ['test1', 'test2'],
        wordDetails: [MapEntry('test1', wordDetails)],
        sessionType: 'test_session',
        createdAt: DateTime.now(),
      );

      expect(session.words.length, 2);
      expect(session.wordDetails.length, 1);
      expect(session.sessionType, 'test_session');
    });

    test('VocabularyLearningReport 模型测试', () {
      final report = VocabularyLearningReport(
        totalWords: 100,
        learnedWords: 30,
        reviewedWords: 50,
        averageMasteryLevel: 0.6,
        strugglingWords: ['difficult1', 'difficult2'],
        masteredWords: ['easy1', 'easy2'],
        generatedAt: DateTime.now(),
      );

      expect(report.totalWords, 100);
      expect(report.learnedWords, 30);
      expect(report.reviewedWords, 50);
      expect(report.averageMasteryLevel, 0.6);
      expect(report.strugglingWords.length, 2);
      expect(report.masteredWords.length, 2);
      expect(report.learningProgress, 0.3); // 30/100
      expect(report.reviewProgress, 0.5); // 50/100
    });

    test('JSON 序列化测试', () {
      final wordDetails = WordDetails(
        id: 1,
        definition: '测试定义',
        frequency: 100,
        difficulty: 'intermediate',
        category: 'basic',
        alternativeSpellings: ['alternative'],
        partOfSpeech: 'noun',
        examples: ['测试例句'],
        tags: ['test', 'example'],
        priority: 'high',
        phonetic: '/test/',
        synonyms: ['synonym1'],
        antonyms: ['antonym1'],
        etymology: '词源信息',
        isCustom: false,
      );

      // 测试 toJson
      final json = wordDetails.toJson();
      expect(json['id'], 1);
      expect(json['definition'], '测试定义');
      expect(json['frequency'], 100);
      expect(json['difficulty'], 'intermediate');
      expect(json['isCustom'], false);

      // 测试 fromJson
      final reconstructed = WordDetails.fromJson(json);
      expect(reconstructed.id, wordDetails.id);
      expect(reconstructed.definition, wordDetails.definition);
      expect(reconstructed.frequency, wordDetails.frequency);
      expect(reconstructed.difficulty, wordDetails.difficulty);
      expect(reconstructed.isCustom, wordDetails.isCustom);
    });

    test('copyWith 方法测试', () {
      final original = WordDetails(
        id: 1,
        definition: '原始定义',
        frequency: 100,
        difficulty: 'intermediate',
        category: 'basic',
        alternativeSpellings: [],
        partOfSpeech: 'noun',
        examples: [],
        tags: [],
        priority: 'high',
      );

      final modified = original.copyWith(definition: '修改后的定义', frequency: 200);

      expect(modified.id, original.id); // 未修改的字段保持不变
      expect(modified.definition, '修改后的定义'); // 修改的字段更新
      expect(modified.frequency, 200); // 修改的字段更新
      expect(modified.difficulty, original.difficulty); // 未修改的字段保持不变
    });

    test('WordLearningProgress copyWith 测试', () {
      final original = WordLearningProgress(
        wordId: 'test_word',
        isSelected: false,
        isLearned: false,
        reviewCount: 0,
        masteryLevel: 0.0,
      );

      final modified = original.copyWith(
        isSelected: true,
        reviewCount: 5,
        masteryLevel: 0.8,
      );

      expect(modified.wordId, original.wordId);
      expect(modified.isSelected, true);
      expect(modified.isLearned, original.isLearned);
      expect(modified.reviewCount, 5);
      expect(modified.masteryLevel, 0.8);
    });
  });

  group('词汇学习服务测试', () {
    setUp(() {
      // 这里需要模拟 VocabularyService
      // 在实际测试中，你可能需要使用 mockito 或类似的库
      // learningService = VocabularyLearningService(mockVocabularyService);
    });

    test('PAO动作生成测试', () {
      // 这个测试需要实际的 PAOExercisesData
      // 在实际实现中，你需要确保测试数据的可用性
      expect(true, true); // 占位符测试
    });

    test('学习会话创建测试', () {
      // 测试学习会话的创建逻辑
      expect(true, true); // 占位符测试
    });

    test('掌握程度更新测试', () {
      // 测试掌握程度的计算逻辑
      expect(true, true); // 占位符测试
    });
  });
}
