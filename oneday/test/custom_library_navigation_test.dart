import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/exercise/custom_action_library.dart';
import 'package:oneday/features/exercise/custom_action_library_service.dart';
import 'package:oneday/features/exercise/exercise_library_page.dart';
import 'package:oneday/features/exercise/custom_exercise_category.dart';
import 'package:oneday/features/exercise/action_library_category_manager.dart';

void main() {
  group('自定义动作库导航测试', () {
    late CustomActionLibraryService customLibraryService;
    late CustomExerciseCategoryManager customCategoryManager;
    late ActionLibraryCategoryManager treeCategoryManager;

    setUp(() {
      customLibraryService = CustomActionLibraryService();
      customCategoryManager = CustomExerciseCategoryManager();
      treeCategoryManager = ActionLibraryCategoryManager();
    });

    testWidgets('自定义动作库编辑完成后应该自动选择该分类', (WidgetTester tester) async {
      // 创建测试用的自定义动作库
      final testLibrary = await customLibraryService.createLibrary(
        name: 'qd',
        description: '测试动作库',
        category: '默认分类',
      );

      // 添加一个测试动作
      testLibrary.setActionForLetter(
        'A',
        CustomPAOAction(
          letter: 'A',
          nameEn: 'Apple',
          nameCn: '苹果',
          description: '测试动作',
          category: '水果',
          scene: '简单活动',
          keywords: ['apple', 'fruit'],
        ),
      );

      await customLibraryService.updateLibrary(testLibrary);

      String? selectedCategory;
      bool sidebarVisible = true;

      // 模拟 ActionLibrarySidebar 的回调函数
      void onCategorySelected(String category) {
        selectedCategory = category;
        sidebarVisible = false;
      }

      void onCategoriesChanged() {
        // 模拟分类变化回调
      }

      // 构建测试组件
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ActionLibrarySidebar(
                isVisible: sidebarVisible,
                onClose: () => sidebarVisible = false,
                onCategorySelected: onCategorySelected,
                selectedCategory: selectedCategory ?? '全部',
                searchController: TextEditingController(),
                onSearchChanged: () {},
                customCategoryManager: customCategoryManager,
                treeCategoryManager: treeCategoryManager,
                customLibraryService: customLibraryService,
                onCategoriesChanged: onCategoriesChanged,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找自定义动作库项目
      final libraryFinder = find.text('qd');
      expect(libraryFinder, findsOneWidget);

      // 查找编辑按钮（更多操作按钮）
      final moreButtonFinder = find.byIcon(Icons.more_horiz);
      expect(moreButtonFinder, findsWidgets);

      // 点击第一个更多操作按钮（假设是qd动作库的）
      await tester.tap(moreButtonFinder.first);
      await tester.pumpAndSettle();

      // 查找编辑选项
      final editOptionFinder = find.text('编辑动作库');
      expect(editOptionFinder, findsOneWidget);

      // 点击编辑选项
      await tester.tap(editOptionFinder);
      await tester.pumpAndSettle();

      // 验证导航到编辑页面的逻辑
      // 注意：由于我们使用的是模拟的回调函数，我们需要手动模拟编辑完成的流程

      // 模拟编辑完成并返回 true（表示有保存更改）
      // 这里我们直接调用回调函数来模拟导航逻辑
      onCategorySelected('qd');

      // 验证选中的分类是否正确
      expect(selectedCategory, equals('qd'));
      expect(sidebarVisible, isFalse);
    });

    testWidgets('自定义动作库编辑取消后不应该改变选中分类', (WidgetTester tester) async {
      // 创建测试用的自定义动作库
      await customLibraryService.createLibrary(
        name: 'qd',
        description: '测试动作库',
        category: '默认分类',
      );

      String? selectedCategory = '全部';
      bool sidebarVisible = true;

      // 模拟 ActionLibrarySidebar 的回调函数
      void onCategorySelected(String category) {
        selectedCategory = category;
        sidebarVisible = false;
      }

      void onCategoriesChanged() {
        // 模拟分类变化回调
      }

      // 构建测试组件
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: ActionLibrarySidebar(
                isVisible: sidebarVisible,
                onClose: () => sidebarVisible = false,
                onCategorySelected: onCategorySelected,
                selectedCategory: selectedCategory ?? '全部',
                searchController: TextEditingController(),
                onSearchChanged: () {},
                customCategoryManager: customCategoryManager,
                treeCategoryManager: treeCategoryManager,
                customLibraryService: customLibraryService,
                onCategoriesChanged: onCategoriesChanged,
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 模拟编辑取消（返回 null 或 false）
      // 在这种情况下，不应该调用 onCategorySelected

      // 验证选中的分类没有改变
      expect(selectedCategory, equals('全部'));
    });
  });
}
