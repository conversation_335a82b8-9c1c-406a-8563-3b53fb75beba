import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:oneday/services/study_session_completion_service.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/study_time/services/study_time_statistics_service.dart';
import 'package:oneday/features/study_time/models/study_time_models.dart';
import 'package:oneday/features/achievement/services/achievement_trigger_service.dart';
import 'package:oneday/features/daily_plan/notifiers/daily_plan_notifier.dart';
import 'package:oneday/features/daily_plan/models/daily_plan.dart';

import 'study_session_completion_service_test.mocks.dart';

@GenerateMocks([
  StudyTimeStatisticsService,
  AchievementTriggerService,
  DailyPlanNotifier,
])
void main() {
  group('StudySessionCompletionService', () {
    late StudySessionCompletionService service;
    late MockStudyTimeStatisticsService mockStudyTimeService;
    late MockAchievementTriggerService mockAchievementService;
    late MockDailyPlanNotifier mockDailyPlanNotifier;

    setUp(() {
      mockStudyTimeService = MockStudyTimeStatisticsService();
      mockAchievementService = MockAchievementTriggerService();
      mockDailyPlanNotifier = MockDailyPlanNotifier();

      service = StudySessionCompletionService(
        studyTimeService: mockStudyTimeService,
        achievementService: mockAchievementService,
        dailyPlanNotifier: mockDailyPlanNotifier,
      );
    });

    group('handleSessionCompletion', () {
      test('应该成功处理完整的学习会话完成流程', () async {
        // Arrange
        final completedTask = TimeBoxTask(
          id: 'test-task-1',
          title: '测试学习任务',
          description: '这是一个测试任务',
          plannedMinutes: 25,
          status: TaskStatus.completed,
          priority: TaskPriority.medium,
          category: '计算机科学',
          createdAt: DateTime.now().subtract(const Duration(minutes: 30)),
          startTime: DateTime.now().subtract(const Duration(minutes: 25)),
          endTime: DateTime.now(),
        );

        // Mock 服务调用
        when(
          mockStudyTimeService.onTaskCompleted(any),
        ).thenAnswer((_) async {});
        when(
          mockAchievementService.onStudySessionEnd(any),
        ).thenAnswer((_) async {});
        when(mockDailyPlanNotifier.getPlan(any, any)).thenReturn(null);

        // Act
        final result = await service.handleSessionCompletion(completedTask);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.studyTimeUpdated, isTrue);
        expect(result.achievementsUpdated, isTrue);
        expect(result.dailyPlanUpdated, isTrue);
        expect(result.studyMinutes, equals(25));
        expect(result.experienceGained, equals(25));

        // 验证服务调用
        verify(mockStudyTimeService.onTaskCompleted(completedTask)).called(1);
        verify(mockAchievementService.onStudySessionEnd(25)).called(1);
      });

      test('应该处理学习时间统计服务失败的情况', () async {
        // Arrange
        final completedTask = TimeBoxTask(
          id: 'test-task-2',
          title: '测试任务',
          description: '测试描述',
          plannedMinutes: 30,
          status: TaskStatus.completed,
          priority: TaskPriority.high,
          category: '数学',
          createdAt: DateTime.now().subtract(const Duration(minutes: 35)),
          startTime: DateTime.now().subtract(const Duration(minutes: 30)),
          endTime: DateTime.now(),
        );

        // Mock 学习时间统计服务失败
        when(
          mockStudyTimeService.onTaskCompleted(any),
        ).thenThrow(Exception('学习时间统计更新失败'));

        // Act
        final result = await service.handleSessionCompletion(completedTask);

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.studyTimeUpdated, isFalse);
        expect(result.studyTimeError, contains('学习时间统计更新失败'));
        expect(result.hasErrors, isTrue);
      });

      test('应该处理成就系统失败的情况', () async {
        // Arrange
        final completedTask = TimeBoxTask(
          id: 'test-task-3',
          title: '测试任务',
          description: '测试描述',
          plannedMinutes: 45,
          status: TaskStatus.completed,
          priority: TaskPriority.low,
          category: '英语',
          createdAt: DateTime.now().subtract(const Duration(minutes: 50)),
          startTime: DateTime.now().subtract(const Duration(minutes: 45)),
          endTime: DateTime.now(),
        );

        // Mock 服务调用
        when(
          mockStudyTimeService.onTaskCompleted(any),
        ).thenAnswer((_) async {});
        when(
          mockAchievementService.onStudySessionEnd(any),
        ).thenThrow(Exception('成就系统更新失败'));

        // Act
        final result = await service.handleSessionCompletion(completedTask);

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.studyTimeUpdated, isTrue);
        expect(result.achievementsUpdated, isFalse);
        expect(result.achievementError, contains('成就系统更新失败'));
      });

      test('应该拒绝处理无效的任务', () async {
        // Arrange - 未完成的任务
        final incompleteTask = TimeBoxTask(
          id: 'incomplete-task',
          title: '未完成任务',
          description: '这个任务还没完成',
          plannedMinutes: 25,
          status: TaskStatus.inProgress,
          priority: TaskPriority.medium,
          category: '测试',
          createdAt: DateTime.now(),
        );

        // Act
        final result = await service.handleSessionCompletion(incompleteTask);

        // Assert
        expect(result.isSuccess, isFalse);
        expect(result.error, contains('任务状态无效'));

        // 验证没有调用任何服务
        verifyNever(mockStudyTimeService.onTaskCompleted(any));
        verifyNever(mockAchievementService.onStudySessionEnd(any));
      });

      test('应该正确处理每日计划更新', () async {
        // Arrange
        final completedTask = TimeBoxTask(
          id: 'test-task-4',
          title: '长时间学习任务',
          description: '超过60分钟的学习任务',
          plannedMinutes: 90, // 超过60分钟，应该标记计划完成
          status: TaskStatus.completed,
          priority: TaskPriority.high,
          category: '深度学习',
          createdAt: DateTime.now().subtract(const Duration(minutes: 95)),
          startTime: DateTime.now().subtract(const Duration(minutes: 90)),
          endTime: DateTime.now(),
        );

        final mockPlan = DailyPlan(
          id: 'today-plan',
          date: DateTime.now(),
          content: '今日学习计划内容',
          type: DailyPlanType.planning,
          createdAt: DateTime.now().subtract(const Duration(hours: 1)),
          isCompleted: false,
          priority: 3,
        );

        // Mock 服务调用
        when(
          mockStudyTimeService.onTaskCompleted(any),
        ).thenAnswer((_) async {});
        when(
          mockAchievementService.onStudySessionEnd(any),
        ).thenAnswer((_) async {});
        when(
          mockDailyPlanNotifier.getPlan(any, DailyPlanType.planning),
        ).thenReturn(mockPlan);
        when(
          mockDailyPlanNotifier.savePlan(
            date: anyNamed('date'),
            type: anyNamed('type'),
            content: anyNamed('content'),
            priority: anyNamed('priority'),
          ),
        ).thenAnswer((_) async {});

        // Act
        final result = await service.handleSessionCompletion(completedTask);

        // Assert
        expect(result.isSuccess, isTrue);
        expect(result.dailyPlanUpdated, isTrue);
        expect(result.dailyPlanCompleted, isTrue);

        // 验证计划保存被调用
        verify(
          mockDailyPlanNotifier.savePlan(
            date: any,
            type: DailyPlanType.planning,
            content: mockPlan.content,
            priority: mockPlan.priority,
          ),
        ).called(1);
      });
    });

    group('getTodayStudySummary', () {
      test('应该返回正确的今日学习统计摘要', () async {
        // Arrange
        final mockAggregation = StudyTimeAggregation(
          todayMinutes: 120,
          thisWeekMinutes: 480,
          thisMonthMinutes: 1200,
          todayCompletedTasks: 3,
          todayWage: 150.0,
          streakDays: 5,
          todayLearningROI: 85.5,
          weeklyAverageLearningROI: 80.0,
          todayParallelTimeMinutes: 30,
          todayFocusSignalToNoiseRatio: 0.92,
          todayFocusRatingLevel: '专注',
          lastUpdated: DateTime.now(),
        );

        when(
          mockStudyTimeService.currentAggregation,
        ).thenReturn(mockAggregation);

        // Act
        final summary = await service.getTodayStudySummary();

        // Assert
        expect(summary['totalStudyMinutes'], equals(120));
        expect(summary['completedTasks'], equals(3));
        expect(summary['studyStreak'], equals(5));
        expect(summary['weeklyTotal'], equals(480));
        expect(summary['learningROI'], equals(85.5));
        expect(summary['focusRatio'], equals(0.92));
        expect(summary['focusLevel'], equals('专注'));
      });

      test('应该处理获取统计数据失败的情况', () async {
        // Arrange
        when(mockStudyTimeService.currentAggregation).thenReturn(null);

        // Act
        final summary = await service.getTodayStudySummary();

        // Assert
        expect(summary['totalStudyMinutes'], equals(0));
        expect(summary['completedTasks'], equals(0));
        expect(summary['studyStreak'], equals(0));
        expect(summary['weeklyTotal'], equals(0));
        expect(summary['learningROI'], equals(0.0));
        expect(summary['focusRatio'], equals(0.0));
        expect(summary['focusLevel'], equals('需改进'));
      });
    });

    group('StudySessionCompletionResult', () {
      test('应该正确计算更新摘要', () {
        // Arrange
        final result = StudySessionCompletionResult()
          ..studyTimeUpdated = true
          ..achievementsUpdated = true
          ..dailyPlanUpdated = false;

        // Act & Assert
        expect(result.updateSummary, equals('已更新: 学习时间统计、成就系统'));
      });

      test('应该正确检测错误状态', () {
        // Arrange
        final result = StudySessionCompletionResult()
          ..studyTimeError = '学习时间错误'
          ..achievementError = '成就系统错误';

        // Act & Assert
        expect(result.hasErrors, isTrue);
        expect(result.allErrors, hasLength(2));
        expect(result.allErrors[0], contains('学习时间统计'));
        expect(result.allErrors[1], contains('成就系统'));
      });
    });
  });
}
