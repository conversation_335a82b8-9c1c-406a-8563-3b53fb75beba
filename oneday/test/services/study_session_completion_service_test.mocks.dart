// Mocks generated by <PERSON><PERSON><PERSON> 5.4.6 from annotations
// in oneday/test/services/study_session_completion_service_test.dart.
// Do not manually edit this file.

// ignore_for_file: no_leading_underscores_for_library_prefixes
import 'dart:async' as _i4;
import 'dart:ui' as _i7;

import 'package:flutter_riverpod/flutter_riverpod.dart' as _i9;
import 'package:mockito/mockito.dart' as _i1;
import 'package:mockito/src/dummies.dart' as _i11;
import 'package:oneday/features/achievement/services/achievement_trigger_service.dart'
    as _i8;
import 'package:oneday/features/daily_plan/models/daily_plan.dart' as _i10;
import 'package:oneday/features/daily_plan/notifiers/daily_plan_notifier.dart'
    as _i2;
import 'package:oneday/features/study_time/models/study_time_models.dart'
    as _i6;
import 'package:oneday/features/study_time/services/study_time_statistics_service.dart'
    as _i3;
import 'package:oneday/features/time_box/models/timebox_models.dart' as _i5;
import 'package:state_notifier/state_notifier.dart' as _i12;

// ignore_for_file: type=lint
// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: deprecated_member_use
// ignore_for_file: deprecated_member_use_from_same_package
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: must_be_immutable
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types
// ignore_for_file: subtype_of_sealed_class

class _FakeDailyPlanState_0 extends _i1.SmartFake
    implements _i2.DailyPlanState {
  _FakeDailyPlanState_0(Object parent, Invocation parentInvocation)
    : super(parent, parentInvocation);
}

/// A class which mocks [StudyTimeStatisticsService].
///
/// See the documentation for Mockito's code generation for more information.
class MockStudyTimeStatisticsService extends _i1.Mock
    implements _i3.StudyTimeStatisticsService {
  MockStudyTimeStatisticsService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get isLoading =>
      (super.noSuchMethod(Invocation.getter(#isLoading), returnValue: false)
          as bool);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  _i4.Future<void> initialize() =>
      (super.noSuchMethod(
            Invocation.method(#initialize, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> updateFromTimeBoxTasks(List<_i5.TimeBoxTask>? tasks) =>
      (super.noSuchMethod(
            Invocation.method(#updateFromTimeBoxTasks, [tasks]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i6.StudyTimeStatistics? getDailyStatistics(DateTime? date) =>
      (super.noSuchMethod(Invocation.method(#getDailyStatistics, [date]))
          as _i6.StudyTimeStatistics?);

  @override
  _i4.Future<void> onTaskCompleted(_i5.TimeBoxTask? task) =>
      (super.noSuchMethod(
            Invocation.method(#onTaskCompleted, [task]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> simulateTaskCompletion() =>
      (super.noSuchMethod(
            Invocation.method(#simulateTaskCompletion, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> refresh() =>
      (super.noSuchMethod(
            Invocation.method(#refresh, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> forceRefresh() =>
      (super.noSuchMethod(
            Invocation.method(#forceRefresh, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );

  @override
  void addListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#addListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void removeListener(_i7.VoidCallback? listener) => super.noSuchMethod(
    Invocation.method(#removeListener, [listener]),
    returnValueForMissingStub: null,
  );

  @override
  void notifyListeners() => super.noSuchMethod(
    Invocation.method(#notifyListeners, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [AchievementTriggerService].
///
/// See the documentation for Mockito's code generation for more information.
class MockAchievementTriggerService extends _i1.Mock
    implements _i8.AchievementTriggerService {
  MockAchievementTriggerService() {
    _i1.throwOnMissingStub(this);
  }

  @override
  int get todayStudyMinutes =>
      (super.noSuchMethod(Invocation.getter(#todayStudyMinutes), returnValue: 0)
          as int);

  @override
  int get consecutiveDays =>
      (super.noSuchMethod(Invocation.getter(#consecutiveDays), returnValue: 0)
          as int);

  @override
  _i4.Future<void> onStudySessionStart() =>
      (super.noSuchMethod(
            Invocation.method(#onStudySessionStart, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onStudySessionEnd(int? studyMinutes) =>
      (super.noSuchMethod(
            Invocation.method(#onStudySessionEnd, [studyMinutes]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onDailyCheckIn() =>
      (super.noSuchMethod(
            Invocation.method(#onDailyCheckIn, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onWageEarned(double? amount) =>
      (super.noSuchMethod(
            Invocation.method(#onWageEarned, [amount]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onMemoryPalaceCreated() =>
      (super.noSuchMethod(
            Invocation.method(#onMemoryPalaceCreated, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onMemoryAnchorAdded(String? palaceId) =>
      (super.noSuchMethod(
            Invocation.method(#onMemoryAnchorAdded, [palaceId]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onARFeatureUsed() =>
      (super.noSuchMethod(
            Invocation.method(#onARFeatureUsed, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onPAOActionCompleted(String? letter) =>
      (super.noSuchMethod(
            Invocation.method(#onPAOActionCompleted, [letter]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onEyeExerciseCompleted() =>
      (super.noSuchMethod(
            Invocation.method(#onEyeExerciseCompleted, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onFriendAdded() =>
      (super.noSuchMethod(
            Invocation.method(#onFriendAdded, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onChallengeInitiated() =>
      (super.noSuchMethod(
            Invocation.method(#onChallengeInitiated, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onReflectionWritten(int? wordCount) =>
      (super.noSuchMethod(
            Invocation.method(#onReflectionWritten, [wordCount]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onWordLearned() =>
      (super.noSuchMethod(
            Invocation.method(#onWordLearned, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onFirstDayStudy(int? studyMinutes) =>
      (super.noSuchMethod(
            Invocation.method(#onFirstDayStudy, [studyMinutes]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> onAllFeaturesUsed() =>
      (super.noSuchMethod(
            Invocation.method(#onAllFeaturesUsed, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void resetDailyData() => super.noSuchMethod(
    Invocation.method(#resetDailyData, []),
    returnValueForMissingStub: null,
  );
}

/// A class which mocks [DailyPlanNotifier].
///
/// See the documentation for Mockito's code generation for more information.
class MockDailyPlanNotifier extends _i1.Mock implements _i2.DailyPlanNotifier {
  MockDailyPlanNotifier() {
    _i1.throwOnMissingStub(this);
  }

  @override
  bool get mounted =>
      (super.noSuchMethod(Invocation.getter(#mounted), returnValue: false)
          as bool);

  @override
  _i4.Stream<_i2.DailyPlanState> get stream =>
      (super.noSuchMethod(
            Invocation.getter(#stream),
            returnValue: _i4.Stream<_i2.DailyPlanState>.empty(),
          )
          as _i4.Stream<_i2.DailyPlanState>);

  @override
  _i2.DailyPlanState get state =>
      (super.noSuchMethod(
            Invocation.getter(#state),
            returnValue: _FakeDailyPlanState_0(this, Invocation.getter(#state)),
          )
          as _i2.DailyPlanState);

  @override
  _i2.DailyPlanState get debugState =>
      (super.noSuchMethod(
            Invocation.getter(#debugState),
            returnValue: _FakeDailyPlanState_0(
              this,
              Invocation.getter(#debugState),
            ),
          )
          as _i2.DailyPlanState);

  @override
  bool get hasListeners =>
      (super.noSuchMethod(Invocation.getter(#hasListeners), returnValue: false)
          as bool);

  @override
  set onError(_i9.ErrorListener? _onError) => super.noSuchMethod(
    Invocation.setter(#onError, _onError),
    returnValueForMissingStub: null,
  );

  @override
  set state(_i2.DailyPlanState? value) => super.noSuchMethod(
    Invocation.setter(#state, value),
    returnValueForMissingStub: null,
  );

  @override
  _i4.Future<void> savePlan({
    required DateTime? date,
    required _i10.DailyPlanType? type,
    required String? content,
    int? priority = 3,
  }) =>
      (super.noSuchMethod(
            Invocation.method(#savePlan, [], {
              #date: date,
              #type: type,
              #content: content,
              #priority: priority,
            }),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i4.Future<void> deletePlan(DateTime? date, _i10.DailyPlanType? type) =>
      (super.noSuchMethod(
            Invocation.method(#deletePlan, [date, type]),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  _i10.DailyPlan? getPlan(DateTime? date, _i10.DailyPlanType? type) =>
      (super.noSuchMethod(Invocation.method(#getPlan, [date, type]))
          as _i10.DailyPlan?);

  @override
  List<_i10.DailyPlan> getPlansForDate(DateTime? date) =>
      (super.noSuchMethod(
            Invocation.method(#getPlansForDate, [date]),
            returnValue: <_i10.DailyPlan>[],
          )
          as List<_i10.DailyPlan>);

  @override
  bool hasContent(DateTime? date, _i10.DailyPlanType? type) =>
      (super.noSuchMethod(
            Invocation.method(#hasContent, [date, type]),
            returnValue: false,
          )
          as bool);

  @override
  String getContent(DateTime? date, _i10.DailyPlanType? type) =>
      (super.noSuchMethod(
            Invocation.method(#getContent, [date, type]),
            returnValue: _i11.dummyValue<String>(
              this,
              Invocation.method(#getContent, [date, type]),
            ),
          )
          as String);

  @override
  _i4.Future<void> refresh() =>
      (super.noSuchMethod(
            Invocation.method(#refresh, []),
            returnValue: _i4.Future<void>.value(),
            returnValueForMissingStub: _i4.Future<void>.value(),
          )
          as _i4.Future<void>);

  @override
  void clearError() => super.noSuchMethod(
    Invocation.method(#clearError, []),
    returnValueForMissingStub: null,
  );

  @override
  bool updateShouldNotify(
    _i2.DailyPlanState? old,
    _i2.DailyPlanState? current,
  ) =>
      (super.noSuchMethod(
            Invocation.method(#updateShouldNotify, [old, current]),
            returnValue: false,
          )
          as bool);

  @override
  _i9.RemoveListener addListener(
    _i12.Listener<_i2.DailyPlanState>? listener, {
    bool? fireImmediately = true,
  }) =>
      (super.noSuchMethod(
            Invocation.method(
              #addListener,
              [listener],
              {#fireImmediately: fireImmediately},
            ),
            returnValue: () {},
          )
          as _i9.RemoveListener);

  @override
  void dispose() => super.noSuchMethod(
    Invocation.method(#dispose, []),
    returnValueForMissingStub: null,
  );
}
