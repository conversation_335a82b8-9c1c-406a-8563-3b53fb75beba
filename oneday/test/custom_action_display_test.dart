import 'package:flutter_test/flutter_test.dart';

void main() {
  group('自定义动作显示更新测试', () {
    test('编辑动作后数据转换应该正确', () {
      // 模拟自定义动作数据
      final customAction = {
        'letter': 'A',
        'nameEn': 'Push Up',
        'nameCn': '俯卧撑',
        'description': '俯卧，双腿弯曲，双手置于耳旁或胸前，上背部抬离地面。',
        'category': '简单活动',
        'scene': '简单活动',
        'keywords': ['Abdominal', 'Achieve', 'Ability'],
      };

      // 模拟数据转换逻辑（对应 _getCustomLibraryExercises 方法）
      final exercise = {
        'letter': customAction['letter'],
        'nameCn': customAction['nameCn'], // 修复：使用正确的属性名
        'nameEn': (customAction['nameEn'] as String).isNotEmpty
            ? customAction['nameEn']
            : customAction['nameCn'],
        'description': customAction['description'],
        'category': '自定义分类', // 使用动作库的分类
        'scene': '自定义动作库', // 标记为自定义动作库
        'keywords': (customAction['keywords'] as List).isNotEmpty
            ? customAction['keywords'] // 使用自定义的关键词
            : [
                customAction['nameEn'],
                customAction['nameCn'],
                customAction['letter'],
              ],
      };

      // 验证转换结果
      expect(exercise['nameCn'], equals('俯卧撑'));
      expect(exercise['nameEn'], equals('Push Up'));
      expect(exercise['description'], equals('俯卧，双腿弯曲，双手置于耳旁或胸前，上背部抬离地面。'));
      expect(exercise['keywords'], equals(['Abdominal', 'Achieve', 'Ability']));
    });

    test('编辑动作名称后应该正确显示', () {
      // 模拟编辑后的动作
      final editedAction = {
        'letter': 'B',
        'nameEn': 'Bicep Curl Modified',
        'nameCn': '改进版二头肌弯举',
        'description': '更新后的描述',
        'category': '简单活动',
        'scene': '简单活动',
        'keywords': ['Bicep', 'Better', 'Balance'],
      };

      // 模拟数据转换
      final displayExercise = {
        'letter': editedAction['letter'],
        'nameCn': editedAction['nameCn'],
        'nameEn': editedAction['nameEn'],
        'description': editedAction['description'],
        'keywords': editedAction['keywords'],
      };

      // 验证编辑后的数据正确显示
      expect(displayExercise['nameCn'], equals('改进版二头肌弯举'));
      expect(displayExercise['nameEn'], equals('Bicep Curl Modified'));
      expect(displayExercise['description'], equals('更新后的描述'));
      expect(displayExercise['keywords'], contains('Better'));
    });

    test('空关键词时应该使用默认关键词', () {
      // 模拟没有关键词的动作
      final actionWithoutKeywords = {
        'letter': 'C',
        'nameEn': 'Chest Press',
        'nameCn': '胸部推举',
        'description': '胸部训练动作',
        'category': '简单活动',
        'scene': '简单活动',
        'keywords': <String>[], // 空关键词列表
      };

      // 模拟关键词处理逻辑
      final keywords = (actionWithoutKeywords['keywords'] as List).isNotEmpty
          ? actionWithoutKeywords['keywords']
          : [
              actionWithoutKeywords['nameEn'],
              actionWithoutKeywords['nameCn'],
              actionWithoutKeywords['letter'],
            ];

      // 验证默认关键词
      expect(keywords, equals(['Chest Press', '胸部推举', 'C']));
    });
  });
}
