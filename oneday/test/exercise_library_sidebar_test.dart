import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/exercise/exercise_library_page.dart';

void main() {
  group('动作库页面分类抽屉栏功能测试', () {
    testWidgets('分类项应该有更多操作按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 等待数据加载
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();

      // 打开抽屉栏
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 等待分类数据加载
      await tester.pump(const Duration(milliseconds: 500));

      // 查找更多操作按钮（三个点图标）
      final moreButtons = find.byIcon(Icons.more_horiz);
      print('找到的更多按钮数量: ${tester.widgetList(moreButtons).length}');

      // 查找分类文本（除了"全部"）
      final categoryTexts = find.textContaining('健身');
      print('找到的分类文本: ${tester.widgetList(categoryTexts).length}');

      // 应该有更多按钮（除了"全部"分类外的所有分类都应该有）
      expect(moreButtons, findsAtLeastNWidgets(1));
    });

    testWidgets('点击更多操作按钮应该显示菜单', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 等待数据加载
      await tester.pump(const Duration(seconds: 2));
      await tester.pumpAndSettle();

      // 打开抽屉栏
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 等待分类数据加载
      await tester.pump(const Duration(milliseconds: 500));

      // 查找第一个更多操作按钮
      final moreButtons = find.byIcon(Icons.more_horiz);
      if (tester.widgetList(moreButtons).isNotEmpty) {
        // 点击第一个更多按钮
        await tester.tap(moreButtons.first);
        await tester.pumpAndSettle();

        // 验证弹出菜单
        expect(find.text('添加到收藏'), findsOneWidget);
        expect(find.text('创建自定义分类'), findsOneWidget);
        expect(find.text('分享分类'), findsOneWidget);
      }
    });

    testWidgets('应该显示左上角三个杠按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找三个杠按钮（menu图标）
      expect(find.byIcon(Icons.menu), findsOneWidget);
    });

    testWidgets('点击三个杠按钮应该打开抽屉栏', (WidgetTester tester) async {
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const ExerciseLibraryPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 点击三个杠按钮
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();

      // 验证抽屉栏打开
      expect(find.text('动作库'), findsAtLeastNWidgets(1));
      expect(find.text('全部'), findsOneWidget);
    });
  });
}
