import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/exercise/focused_training_page.dart';

/// UI溢出问题测试
/// 验证修复后的布局在不同屏幕尺寸下不会出现溢出
void main() {
  group('UI Overflow Tests', () {
    testWidgets('集中训练页面 - 窄屏设备不应出现溢出', (WidgetTester tester) async {
      // 设置窄屏尺寸 (iPhone SE)
      await tester.binding.setSurfaceSize(const Size(375, 667));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );
      
      // 等待页面完全渲染
      await tester.pumpAndSettle();
      
      // 验证页面正常渲染，没有溢出错误
      expect(find.byType(FocusedTrainingPage), findsOneWidget);
      expect(find.text('集中训练'), findsOneWidget);
      expect(find.text('选择词根'), findsOneWidget);
      
      // 验证词根选择网格存在
      expect(find.byType(GridView), findsAtLeastNWidgets(1));
      
      // 验证基本页面元素存在（不依赖具体词根内容）
      expect(find.text('选择词根'), findsOneWidget);
      expect(find.text('选择难度'), findsOneWidget);
    });

    testWidgets('集中训练页面 - 中等屏幕不应出现溢出', (WidgetTester tester) async {
      // 设置中等屏幕尺寸 (iPad mini)
      await tester.binding.setSurfaceSize(const Size(768, 1024));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面正常渲染
      expect(find.byType(FocusedTrainingPage), findsOneWidget);
      expect(find.byType(GridView), findsAtLeastNWidgets(1));
    });

    testWidgets('集中训练页面 - 宽屏设备不应出现溢出', (WidgetTester tester) async {
      // 设置宽屏尺寸 (Desktop)
      await tester.binding.setSurfaceSize(const Size(1200, 800));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证页面正常渲染
      expect(find.byType(FocusedTrainingPage), findsOneWidget);
      expect(find.byType(GridView), findsAtLeastNWidgets(1));
    });

    testWidgets('验证响应式布局 - LayoutBuilder正确工作', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));
      
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );
      
      await tester.pumpAndSettle();
      
      // 验证LayoutBuilder存在
      expect(find.byType(LayoutBuilder), findsAtLeastNWidgets(1));
      
      // 验证GridView使用了正确的配置
      final gridView = tester.widget<GridView>(find.byType(GridView).first);
      final delegate = gridView.gridDelegate as SliverGridDelegateWithFixedCrossAxisCount;
      
      // 窄屏应该是2列
      expect(delegate.crossAxisCount, equals(2));
      
      // 宽高比应该合理（不是原来的2.0）
      expect(delegate.childAspectRatio, lessThan(2.0));
      expect(delegate.childAspectRatio, greaterThan(1.0));
    });

    testWidgets('验证文本溢出处理', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 查找所有Text组件，验证它们都有适当的overflow处理
      final textWidgets = tester.widgetList<Text>(find.byType(Text));

      for (final textWidget in textWidgets) {
        // 如果设置了maxLines，应该也设置了overflow
        if (textWidget.maxLines != null && textWidget.maxLines! > 0) {
          expect(textWidget.overflow, isNotNull);
        }
      }
    });

    testWidgets('验证词根系统正确加载', (WidgetTester tester) async {
      await tester.binding.setSurfaceSize(const Size(375, 667));

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: const FocusedTrainingPage(),
          ),
        ),
      );

      // 等待词根加载完成
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // 验证页面正常渲染
      expect(find.byType(FocusedTrainingPage), findsOneWidget);
      expect(find.text('集中训练'), findsOneWidget);
      expect(find.text('选择词根'), findsOneWidget);

      // 验证说明文本已更新为词根相关内容
      expect(find.textContaining('act'), findsOneWidget);
      expect(find.textContaining('词根'), findsAtLeastNWidgets(1));
    });
  });
}
