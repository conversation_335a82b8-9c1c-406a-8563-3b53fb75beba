import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:oneday/features/photo_album/photo_album_creator_page.dart';

void main() {
  group('PhotoAlbumCreatorPage Tests', () {
    testWidgets('应该显示创建知忆相册标题', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PhotoAlbumCreatorPage(),
        ),
      );

      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));
    });

    testWidgets('应该显示相册信息输入区域', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PhotoAlbumCreatorPage(),
        ),
      );

      expect(find.text('相册信息'), findsOneWidget);
      expect(find.text('相册标题'), findsOneWidget);
      expect(find.text('相册描述（可选）'), findsOneWidget);
    });

    testWidgets('应该显示创建知忆相册按钮', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PhotoAlbumCreatorPage(),
        ),
      );

      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));
      expect(find.byIcon(Icons.add_photo_alternate_outlined), findsOneWidget);
    });

    testWidgets('应该使用正确的配色方案', (WidgetTester tester) async {
      await tester.pumpWidget(
        const MaterialApp(
          home: PhotoAlbumCreatorPage(),
        ),
      );

      // 检查是否有图片选择图标（这表明按钮存在）
      expect(find.byIcon(Icons.add_photo_alternate_outlined), findsOneWidget);

      // 检查是否有创建知忆相册的文本
      expect(find.text('创建知忆相册'), findsAtLeastNWidgets(1));
    });

    testWidgets('PhotoAlbum模型应该正确处理封面图片', (WidgetTester tester) async {
      final album = PhotoAlbum(
        id: '1',
        title: '测试相册',
        imagePaths: ['/path/to/image1.jpg', '/path/to/image2.jpg'],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      expect(album.coverImagePath, '/path/to/image1.jpg');
      expect(album.imageCount, 2);
    });

    testWidgets('PhotoAlbum模型应该处理空图片列表', (WidgetTester tester) async {
      final album = PhotoAlbum(
        id: '1',
        title: '空相册',
        imagePaths: [],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      expect(album.coverImagePath, '');
      expect(album.imageCount, 0);
    });

    testWidgets('PhotoAlbum copyWith应该正确更新属性', (WidgetTester tester) async {
      final originalAlbum = PhotoAlbum(
        id: '1',
        title: '原始相册',
        imagePaths: ['/path/to/image1.jpg'],
        createdAt: DateTime.now(),
        lastModified: DateTime.now(),
      );

      final updatedAlbum = originalAlbum.copyWith(
        title: '更新的相册',
        imagePaths: ['/path/to/image1.jpg', '/path/to/image2.jpg'],
      );

      expect(updatedAlbum.title, '更新的相册');
      expect(updatedAlbum.imageCount, 2);
      expect(updatedAlbum.id, originalAlbum.id); // ID应该保持不变
    });
  });
}
