import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/time_box/timebox_list_page.dart';

void main() {
  group('TimeBox 日历界面任务显示测试', () {
    testWidgets('测试原始颜色分类系统', (WidgetTester tester) async {
      // 测试各个分类的颜色映射
      expect(
        SubjectColors.getCategoryColor('计算机科学'),
        const Color(0xFFE03E3E),
      ); // 红色
      expect(
        SubjectColors.getCategoryColor('数学'),
        const Color(0xFFFFD700),
      ); // 荧光黄
      expect(
        SubjectColors.getCategoryColor('英语'),
        const Color(0xFF0F7B6C),
      ); // 绿色
      expect(
        SubjectColors.getCategoryColor('政治'),
        const Color(0xFF2E7EED),
      ); // 蓝色
      expect(
        SubjectColors.getCategoryColor('休息'),
        const Color(0xFF9B9A97),
      ); // 灰色
    });

    testWidgets('测试任务名称文本显示优化', (WidgetTester tester) async {
      // 创建测试任务
      final testTasks = [
        TimeBoxTask(
          id: 'test-1',
          title: '短任务',
          description: '测试短任务名称',
          plannedMinutes: 25,
          status: TaskStatus.pending,
          priority: TaskPriority.high,
          category: '数学',
          createdAt: DateTime.now(),
        ),
        TimeBoxTask(
          id: 'test-2',
          title: '这是一个非常长的任务名称，用来测试文本截断和换行功能',
          description: '测试长任务名称',
          plannedMinutes: 50,
          status: TaskStatus.completed,
          priority: TaskPriority.medium,
          category: '英语',
          createdAt: DateTime.now(),
        ),
      ];

      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: Column(
                children: testTasks
                    .map(
                      (task) => Container(
                        width: 100,
                        height: 60,
                        margin: const EdgeInsets.all(4),
                        child: _TestTimeBoxContent(task: task, blockHeight: 60),
                      ),
                    )
                    .toList(),
              ),
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);

      // 验证任务标题存在
      expect(find.text('短任务'), findsOneWidget);

      // 验证长文本被正确截断（应该显示省略号）
      expect(find.textContaining('这是一个非常长的任务名称'), findsOneWidget);
    });

    testWidgets('测试时间块高度自适应', (WidgetTester tester) async {
      final testTask = TimeBoxTask(
        id: 'test-height',
        title: '高度测试任务',
        description: '测试不同高度下的显示效果',
        plannedMinutes: 30,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '计算机科学',
        createdAt: DateTime.now(),
      );

      // 测试不同高度的时间块
      final heights = [10.0, 20.0, 30.0, 50.0, 80.0];

      for (final height in heights) {
        await tester.pumpWidget(
          MaterialApp(
            home: Scaffold(
              body: SizedBox(
                width: 100,
                height: height,
                child: _TestTimeBoxContent(task: testTask, blockHeight: height),
              ),
            ),
          ),
        );

        await tester.pumpAndSettle();

        // 验证没有溢出错误
        expect(tester.takeException(), isNull);
      }
    });

    testWidgets('测试原始风格边框和颜色', (WidgetTester tester) async {
      final completedTask = TimeBoxTask(
        id: 'completed-task',
        title: '已完成任务',
        description: '测试已完成任务的显示',
        plannedMinutes: 25,
        status: TaskStatus.completed,
        priority: TaskPriority.high,
        category: '数学',
        createdAt: DateTime.now(),
        endTime: DateTime.now(),
      );

      final pendingTask = TimeBoxTask(
        id: 'pending-task',
        title: '待完成任务',
        description: '测试待完成任务的显示',
        plannedMinutes: 25,
        status: TaskStatus.pending,
        priority: TaskPriority.medium,
        category: '英语',
        createdAt: DateTime.now(),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Column(
              children: [
                SizedBox(
                  width: 100,
                  height: 50,
                  child: _TestTimeBoxBlock(task: completedTask),
                ),
                SizedBox(
                  width: 100,
                  height: 50,
                  child: _TestTimeBoxBlock(task: pendingTask),
                ),
              ],
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证没有溢出错误
      expect(tester.takeException(), isNull);

      // 验证任务标题存在
      expect(find.text('已完成任务'), findsOneWidget);
      expect(find.text('待完成任务'), findsOneWidget);
    });

    test('测试原始颜色系统基本功能', () {
      // 测试半透明颜色
      final transparentRed = SubjectColors.getCategoryColorWithOpacity(
        '计算机科学',
        0.5,
      );
      expect((transparentRed.a * 255.0).round() & 0xff, (255 * 0.5).round());

      // 测试默认颜色
      expect(SubjectColors.getCategoryColor('未知分类'), const Color(0xFF9B9A97));
    });
  });
}

/// 测试用的时间盒子内容组件
class _TestTimeBoxContent extends StatelessWidget {
  final TimeBoxTask task;
  final double blockHeight;

  const _TestTimeBoxContent({required this.task, required this.blockHeight});

  @override
  Widget build(BuildContext context) {
    final isRestTask = task.category == '休息' || task.category == '具身记忆';

    if (isRestTask) {
      return Center(
        child: Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: task.isCompleted ? Colors.white : task.categoryColor,
            shape: BoxShape.circle,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Note: availableWidth and availableHeight are reserved for future layout calculations
        // final availableWidth = constraints.maxWidth - 4;
        // final availableHeight = constraints.maxHeight - 4;

        double fontSize;
        int maxLines;

        if (blockHeight <= 15) {
          return const SizedBox.shrink();
        } else if (blockHeight <= 25) {
          fontSize = 6;
          maxLines = 1;
        } else if (blockHeight <= 40) {
          fontSize = 7;
          maxLines = 1;
        } else if (blockHeight <= 60) {
          fontSize = 8;
          maxLines = 2;
        } else {
          fontSize = 9;
          maxLines = 3;
        }

        Color textColor;
        if (task.isCompleted) {
          textColor = Colors.white;
        } else {
          final bgColor = task.categoryColor;
          if (bgColor == const Color(0xFFE03E3E) || // 红色
              bgColor == const Color(0xFF2E7EED) || // 蓝色
              bgColor == const Color(0xFF0F7B6C)) {
            // 绿色
            textColor = Colors.white;
          } else {
            textColor = const Color(0xFF37352F); // 深灰色文字（适用于荧光黄和灰色背景）
          }
        }

        return Padding(
          padding: const EdgeInsets.all(2),
          child: Center(
            child: Text(
              task.title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
                color: textColor,
                height: 1.1,
              ),
              maxLines: maxLines,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}

/// 测试用的时间盒子块组件
class _TestTimeBoxBlock extends StatelessWidget {
  final TimeBoxTask task;

  const _TestTimeBoxBlock({required this.task});

  @override
  Widget build(BuildContext context) {
    final isCompleted = task.isCompleted;
    // Note: isRestTask is reserved for future task type-specific styling
    // final isRestTask = task.category == '休息' || task.category == '具身记忆';

    Color backgroundColor;
    Border? border;

    if (isCompleted) {
      backgroundColor = task.categoryColor;
      border = null; // 已完成任务不需要边框
    } else {
      backgroundColor = task.getCategoryColorWithOpacity(0.3);
      border = Border.all(color: task.categoryColor, width: 1);
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 0.5, vertical: 0.5),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(3),
        border: border,
      ),
      child: _TestTimeBoxContent(task: task, blockHeight: 50),
    );
  }
}
