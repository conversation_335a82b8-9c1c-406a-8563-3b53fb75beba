import 'dart:io';
import 'dart:math';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

/// 图片水印工具类
/// 提供为图片添加OneDay水印和标记点的功能
class ImageWatermarkUtils {
  /// 为图片添加水印和标记点，生成分享用的合成图片
  ///
  /// [imagePath] 原始图片路径
  /// [anchors] 标记点列表
  /// [sceneTitle] 场景标题
  /// 返回合成后的图片文件路径
  static Future<String?> createShareImage({
    required String imagePath,
    required List<dynamic> anchors, // MemoryAnchor列表
    required String sceneTitle,
  }) async {
    try {
      print('🎨 开始创建分享图片: $sceneTitle');

      // 1. 加载原始图片
      final originalImage = await _loadImage(imagePath);
      if (originalImage == null) {
        print('❌ 无法加载原始图片: $imagePath');
        return null;
      }

      // 2. 创建画布
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);
      final imageSize = Size(
        originalImage.width.toDouble(),
        originalImage.height.toDouble(),
      );

      // 3. 绘制原始图片
      canvas.drawImage(originalImage, Offset.zero, Paint());

      // 4. 绘制标记点
      await _drawAnchors(canvas, anchors, imageSize);

      // 5. 绘制水印
      await _drawWatermark(canvas, imageSize);

      // 6. 生成最终图片
      final picture = recorder.endRecording();
      final finalImage = await picture.toImage(
        originalImage.width,
        originalImage.height,
      );

      // 7. 保存到临时文件
      final filePath = await _saveImageToTemp(finalImage, sceneTitle);

      print('✅ 分享图片创建完成: $filePath');
      return filePath;
    } catch (e) {
      print('❌ 创建分享图片失败: $e');
      return null;
    }
  }

  /// 加载图片文件
  static Future<ui.Image?> _loadImage(String imagePath) async {
    try {
      print('🔍 尝试加载图片: $imagePath');
      late Uint8List imageBytes;

      // 判断是网络图片还是本地图片
      if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
        // 网络图片 - 这里需要实际的网络请求实现
        print('⚠️ 网络图片暂不支持水印功能: $imagePath');
        return null;
      } else {
        // 本地图片
        final file = File(imagePath);
        print('🔍 检查文件是否存在: ${await file.exists()}');
        if (!await file.exists()) {
          print('❌ 图片文件不存在: $imagePath');
          return null;
        }
        imageBytes = await file.readAsBytes();
        print('✅ 成功读取图片文件，大小: ${imageBytes.length} bytes');
      }

      final codec = await ui.instantiateImageCodec(imageBytes);
      final frame = await codec.getNextFrame();
      final image = frame.image;
      print('✅ 成功解码图片，尺寸: ${image.width}x${image.height}');
      return image;
    } catch (e) {
      print('❌ 加载图片失败: $e');
      return null;
    }
  }

  /// 绘制标记点
  static Future<void> _drawAnchors(
    Canvas canvas,
    List<dynamic> anchors,
    Size imageSize,
  ) async {
    if (anchors.isEmpty) return;

    print('📍 绘制 ${anchors.length} 个标记点');

    // 计算适当的缩放因子，基于图片尺寸
    // 这确保标记点在不同尺寸的图片上保持视觉一致性
    final scaleFactor = _calculateScaleFactor(imageSize);
    print(
      '📏 图片尺寸: ${imageSize.width}x${imageSize.height}, 缩放因子: $scaleFactor',
    );

    // 标记点样式
    final anchorPaint = Paint()
      ..color = const Color(0xFF2F76DA)
      ..style = PaintingStyle.fill;

    final anchorBorderPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0 * scaleFactor; // 根据缩放因子调整边框宽度

    final textPaint = TextPainter(textDirection: TextDirection.ltr);

    for (int i = 0; i < anchors.length; i++) {
      final anchor = anchors[i];

      // 获取标记点位置 - 直接访问属性
      final x = anchor.xRatio * imageSize.width;
      final y = anchor.yRatio * imageSize.height;
      final position = Offset(x, y);

      print(
        '📍 标记点 ${i + 1}: 位置比例(${anchor.xRatio}, ${anchor.yRatio}) → 像素位置(${x.toStringAsFixed(1)}, ${y.toStringAsFixed(1)})',
      );

      // 绘制标记点圆圈 - 使用动态计算的半径
      // 确保标记点有最小尺寸，保证可见性
      final radius = max(20.0 * scaleFactor, 15.0); // 根据缩放因子调整半径，但确保最小半径

      // 添加阴影效果，增强视觉层次感
      final shadowPaint = Paint()
        ..color = Colors.black.withValues(alpha: 0.2)
        ..style = PaintingStyle.fill
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

      // 绘制阴影
      canvas.drawCircle(
        Offset(position.dx + 1, position.dy + 2),
        radius,
        shadowPaint,
      );

      // 绘制白色边框和蓝色填充
      canvas.drawCircle(position, radius, anchorBorderPaint); // 白色边框
      canvas.drawCircle(
        position,
        radius - (1.5 * scaleFactor),
        anchorPaint,
      ); // 蓝色填充

      // 绘制序号 - 使用动态计算的字体大小
      final fontSize = max(16 * scaleFactor, 12.0); // 确保最小字体大小
      textPaint.text = TextSpan(
        text: '${i + 1}',
        style: TextStyle(
          color: Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
          shadows: [
            // 添加文字阴影，增强可读性
            Shadow(
              color: Colors.black.withValues(alpha: 0.3),
              offset: const Offset(0.5, 0.5),
              blurRadius: 1.0,
            ),
          ],
        ),
      );
      textPaint.layout();

      // 精确居中对齐
      final textOffset = Offset(
        position.dx - textPaint.width / 2,
        position.dy - textPaint.height / 2,
      );
      textPaint.paint(canvas, textOffset);

      // 绘制知识点内容（在标记点旁边）
      final content = anchor.content;
      if (content.isNotEmpty) {
        _drawAnchorLabel(canvas, position, content, i + 1, scaleFactor);
      }
    }
  }

  /// 绘制标记点标签
  static void _drawAnchorLabel(
    Canvas canvas,
    Offset anchorPosition,
    String content,
    int index,
    double scaleFactor,
  ) {
    // 限制内容长度，避免标签过长
    final displayContent = content.length > 20
        ? '${content.substring(0, 20)}...'
        : content;

    // 优化文字样式，提高可读性
    final textPainter = TextPainter(
      text: TextSpan(
        text: displayContent,
        style: TextStyle(
          color: const Color(0xFF37352F),
          fontSize: max(14 * scaleFactor, 16.0), // 确保最小字体大小为16，提高可读性
          fontWeight: FontWeight.w600, // 加粗字体，提高可读性
          height: 1.2, // 行高适中，提高可读性
          letterSpacing: 0.2, // 轻微增加字间距，提高可读性
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center, // 居中对齐，美观整齐
    );
    textPainter.layout();

    // 计算标签位置（在标记点右侧）
    final padding = 8.0 * scaleFactor; // 根据缩放因子调整内边距
    final labelWidth = textPainter.width + padding * 2;
    final labelHeight = textPainter.height + padding;

    final labelRect = Rect.fromLTWH(
      anchorPosition.dx + (30 * scaleFactor), // 在标记点右侧，根据缩放因子调整距离
      anchorPosition.dy - labelHeight / 2,
      labelWidth,
      labelHeight,
    );

    // 绘制标签背景 - 优化可读性
    final labelPaint = Paint()
      ..color = Colors.white
          .withValues(alpha: 0.95) // 提高不透明度，增强可读性
      ..style = PaintingStyle.fill;

    // 添加阴影效果，提高视觉层次
    final shadowPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    final borderPaint = Paint()
      ..color = const Color(0xFF37352F)
          .withValues(alpha: 0.15) // 稍微加深边框，提高对比度
      ..style = PaintingStyle.stroke
      ..strokeWidth = max(1.0 * scaleFactor, 1.5); // 确保边框有最小宽度

    // 绘制阴影
    final shadowRect = labelRect.translate(1 * scaleFactor, 2 * scaleFactor);
    canvas.drawRRect(
      RRect.fromRectAndRadius(shadowRect, Radius.circular(8 * scaleFactor)),
      shadowPaint,
    );

    // 绘制标签背景
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        labelRect,
        Radius.circular(8 * scaleFactor),
      ), // 稍微增大圆角，更美观
      labelPaint,
    );

    // 绘制边框
    canvas.drawRRect(
      RRect.fromRectAndRadius(labelRect, Radius.circular(8 * scaleFactor)),
      borderPaint,
    );

    // 绘制文本
    textPainter.paint(
      canvas,
      Offset(labelRect.left + padding, labelRect.top + padding / 2),
    );
  }

  /// 绘制OneDay水印
  static Future<void> _drawWatermark(Canvas canvas, Size imageSize) async {
    print('💧 添加OneDay水印');

    // 水印位置（右下角）
    const watermarkSize = 80.0;
    const margin = 20.0;
    final watermarkRect = Rect.fromLTWH(
      imageSize.width - watermarkSize - margin,
      imageSize.height - watermarkSize - margin,
      watermarkSize,
      watermarkSize,
    );

    // 绘制半透明背景
    final backgroundPaint = Paint()
      ..color = Colors.black.withValues(alpha: 0.3)
      ..style = PaintingStyle.fill;

    canvas.drawRRect(
      RRect.fromRectAndRadius(watermarkRect, const Radius.circular(8)),
      backgroundPaint,
    );

    // 绘制OneDay文字
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'OneDay',
        style: TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
          letterSpacing: -0.5,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();

    final textOffset = Offset(
      watermarkRect.center.dx - textPainter.width / 2,
      watermarkRect.center.dy - textPainter.height / 2,
    );
    textPainter.paint(canvas, textOffset);
  }

  /// 保存图片到临时目录
  static Future<String> _saveImageToTemp(
    ui.Image image,
    String sceneTitle,
  ) async {
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    final bytes = byteData!.buffer.asUint8List();

    final tempDir = await getTemporaryDirectory();
    final fileName =
        'oneday_share_${sceneTitle.replaceAll(RegExp(r'[^\w\s-]'), '')}_${DateTime.now().millisecondsSinceEpoch}.png';
    final file = File('${tempDir.path}/$fileName');

    await file.writeAsBytes(bytes);
    return file.path;
  }

  /// 计算适当的缩放因子，基于图片尺寸
  /// 这确保标记点在不同尺寸的图片上保持视觉一致性
  static double _calculateScaleFactor(Size imageSize) {
    // 基准尺寸 - 基于常见手机屏幕尺寸和应用内显示
    const baseWidth = 1080.0;
    const baseHeight = 1920.0;

    // 计算图片的像素密度
    final imageArea = imageSize.width * imageSize.height;
    final baseArea = baseWidth * baseHeight;
    final areaDensityFactor = imageArea / baseArea;

    // 计算基于宽度和高度的缩放因子
    final widthFactor = imageSize.width / baseWidth;
    final heightFactor = imageSize.height / baseHeight;

    // 综合考虑宽度、高度和像素密度
    // 使用几何平均数来平衡各个因子
    final combinedFactor = pow(
      widthFactor * heightFactor * areaDensityFactor,
      1 / 3,
    );

    // 使用平方根来平滑缩放效果，避免过度缩放
    final scaleFactor = sqrt(combinedFactor.toDouble());

    // 根据图片尺寸类别进行精细调整
    double adjustedFactor = scaleFactor;

    // 小图片（宽度小于800px）- 适当放大标记点
    if (imageSize.width < 800) {
      adjustedFactor = scaleFactor * 1.2;
    }
    // 超大图片（宽度大于3000px）- 适当缩小标记点
    else if (imageSize.width > 3000) {
      adjustedFactor = scaleFactor * 0.8;
    }

    // 限制缩放因子范围，确保在各种图片尺寸下都有良好的可读性
    // 最小0.7确保小图片上的标记点不会消失
    // 最大2.5确保大图片上的标记点不会过大
    final finalFactor = adjustedFactor.clamp(0.7, 2.5);

    print(
      '📏 缩放计算: 图片${imageSize.width.toInt()}x${imageSize.height.toInt()}, '
      '原始因子: ${scaleFactor.toStringAsFixed(2)}, '
      '调整因子: ${adjustedFactor.toStringAsFixed(2)}, '
      '最终因子: ${finalFactor.toStringAsFixed(2)}',
    );

    return finalFactor;
  }
}
