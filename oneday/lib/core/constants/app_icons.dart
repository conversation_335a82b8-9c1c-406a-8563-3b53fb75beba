/// 应用图标相关常量和工具
class AppIcons {
  // 应用图标路径
  static const String appIcon = 'assets/icons/app_icon.png';
  static const String appIconRounded = 'assets/icons/app_icon_rounded.png';
  
  // 不同尺寸的图标路径
  static const String icon16 = 'assets/icons/icon_16.png';
  static const String icon32 = 'assets/icons/icon_32.png';
  static const String icon64 = 'assets/icons/icon_64.png';
  static const String icon128 = 'assets/icons/icon_128.png';
  static const String icon256 = 'assets/icons/icon_256.png';
  static const String icon512 = 'assets/icons/icon_512.png';
  static const String icon1024 = 'assets/icons/icon_1024.png';
  
  // Android图标尺寸
  static const Map<String, String> androidIcons = {
    'mdpi': 'assets/icons/android/ic_launcher_48.png',
    'hdpi': 'assets/icons/android/ic_launcher_72.png',
    'xhdpi': 'assets/icons/android/ic_launcher_96.png',
    'xxhdpi': 'assets/icons/android/ic_launcher_144.png',
    'xxxhdpi': 'assets/icons/android/ic_launcher_192.png',
  };
  
  // iOS图标尺寸
  static const Map<String, String> iosIcons = {
    '20x20': 'assets/icons/ios/<EMAIL>',
    '20x20@2x': 'assets/icons/ios/<EMAIL>',
    '20x20@3x': 'assets/icons/ios/<EMAIL>',
    '29x29': 'assets/icons/ios/<EMAIL>',
    '29x29@2x': 'assets/icons/ios/<EMAIL>',
    '29x29@3x': 'assets/icons/ios/<EMAIL>',
    '40x40': 'assets/icons/ios/<EMAIL>',
    '40x40@2x': 'assets/icons/ios/<EMAIL>',
    '40x40@3x': 'assets/icons/ios/<EMAIL>',
    '60x60@2x': 'assets/icons/ios/<EMAIL>',
    '60x60@3x': 'assets/icons/ios/<EMAIL>',
    '76x76': 'assets/icons/ios/<EMAIL>',
    '76x76@2x': 'assets/icons/ios/<EMAIL>',
    '83.5x83.5@2x': 'assets/icons/ios/<EMAIL>',
    '1024x1024': 'assets/icons/ios/<EMAIL>',
  };
} 