// 动觉动作数据库 - 从Action.html提取的完整数据
// 基于具身认知理论的动作-单词联想记忆法

/// 动觉动作模型
class PAOExercise {
  final String letter;
  final String nameEn;
  final String nameCn;
  final String category;
  final String scene; // '简单活动' | '集中训练' | '简单/集中'
  final String description;
  final List<String> keywords;

  const PAOExercise({
    required this.letter,
    required this.nameEn,
    required this.nameCn,
    required this.category,
    required this.scene,
    required this.description,
    required this.keywords,
  });

  // 兼容性getters，映射到新的属性名
  String get name => nameCn;
  List<String> get scenarios {
    if (scene.contains('/')) {
      return scene.split('/').map((s) => s.trim()).toList();
    } else if (scene == '简单/集中') {
      return ['简单活动', '集中训练'];
    } else {
      return [scene];
    }
  }

  List<String> get paoWords => keywords;
  String get bodyPart => '全身'; // 默认值，可根据需要具体化
  int get duration => 30; // 默认30秒
  String get equipment => '无需器材'; // 默认值
}

/// 动觉动作类别
class PAOCategory {
  final String name;
  final String icon;
  final Map<String, PAOExercise> exercises;

  const PAOCategory({
    required this.name,
    required this.icon,
    required this.exercises,
  });
}

/// 动觉动作数据库
class PAOExercisesData {
  /// 健身类动作
  static const Map<String, PAOExercise> fitnessExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Abdominal Crunch',
      nameCn: '卷腹',
      category: '健身',
      scene: '简单活动',
      description: '仰卧，双腿弯曲，双手置于耳旁或胸前，上背部抬离地面。',
      keywords: ['Abdominal', 'Achieve', 'Ability'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Burpee',
      nameCn: '波比跳',
      category: '健身',
      scene: '集中训练',
      description: '一个高强度复合动作：下蹲、后踢腿成俯卧撑姿势、俯卧撑、收腿、跳跃。',
      keywords: ['Burpee', 'Breakthrough', 'Build'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Calf Raise',
      nameCn: '提踵',
      category: '健身',
      scene: '简单活动',
      description: '站立，双脚缓慢抬起脚跟，用脚尖支撑，在最高点停留后缓慢下落。',
      keywords: ['Calf', 'Challenge', 'Concentrate'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Deadlift',
      nameCn: '硬拉',
      category: '健身',
      scene: '集中训练',
      description: '使用杠铃或哑铃，从地面将重量提起至身体直立。',
      keywords: ['Deadlift', 'Dedication', 'Durable'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Elbow Plank',
      nameCn: '肘部平板支撑',
      category: '健身',
      scene: '简单活动',
      description: '以肘部和脚尖支撑身体，保持身体呈一条直线。',
      keywords: ['Elbow', 'Effective', 'Elevate'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Front Raise',
      nameCn: '前平举',
      category: '健身',
      scene: '集中训练',
      description: '手持哑铃或弹力带，手臂伸直从身前抬起至与肩同高。',
      keywords: ['Front', 'Foundation', 'Future'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Glute Bridge',
      nameCn: '臀桥',
      category: '健身',
      scene: '简单活动',
      description: '仰卧，双脚踩地，将臀部向上抬起，使身体从肩膀到膝盖呈一条直线。',
      keywords: ['Glute', 'Goal', 'Growth'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'High Knees',
      nameCn: '高抬腿',
      category: '健身',
      scene: '简单活动',
      description: '原地快速交替抬高膝盖，尽可能抬至髋部高度。',
      keywords: ['High', 'Honor', 'Hustle'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'Inchworm',
      nameCn: '英寸虫',
      category: '健身',
      scene: '简单活动',
      description: '站立，身体前屈，双手触地，然后双手向前爬行至平板支撑位，再走回。',
      keywords: ['Inchworm', 'Initiative', 'Integrate'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Jumping Jacks',
      nameCn: '开合跳',
      category: '健身',
      scene: '简单活动',
      description: '跳跃的同时双脚向外打开，双臂举过头顶，再跳回起始位置。',
      keywords: ['Jumping', 'Join', 'Justify'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Kettlebell Swing',
      nameCn: '壶铃摇摆',
      category: '健身',
      scene: '集中训练',
      description: '使用壶铃，通过髋部爆发力将壶铃向前甩起至胸部高度。',
      keywords: ['Kettlebell', 'Key', 'Kinetic'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Lunge',
      nameCn: '弓步',
      category: '健身',
      scene: '简单/集中',
      description: '向前迈出一大步，身体下蹲，双膝呈90度。可自重进行，也可负重。',
      keywords: ['Lunge', 'Leap', 'Leadership'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Mountain Climber',
      nameCn: '登山者',
      category: '健身',
      scene: '简单活动',
      description: '在平板支撑姿势下，交替将膝盖提向胸口。',
      keywords: ['Mountain', 'Momentum', 'Motivate'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'Negative Push-up',
      nameCn: '离心俯卧撑',
      category: '健身',
      scene: '简单活动',
      description: '从平板支撑位，用3-5秒时间缓慢将身体下降至地面。',
      keywords: ['Negative', 'Negate', 'Nurture'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'Overhead Press',
      nameCn: '过顶推举',
      category: '健身',
      scene: '集中训练',
      description: '将杠铃或哑铃从肩部位置向上推举至手臂完全伸直。',
      keywords: ['Overhead', 'Overcome', 'Opportunity'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Push-up',
      nameCn: '俯卧撑',
      category: '健身',
      scene: '简单活动',
      description: '经典的自重训练动作，保持身体成直线，屈肘使身体下降再推起。',
      keywords: ['Push-up', 'Potential', 'Progress'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Quick Feet',
      nameCn: '快速小碎步',
      category: '健身',
      scene: '简单活动',
      description: '原地快速地进行小碎步跑，保持低重心，提高反应速度。',
      keywords: ['Quick', 'Qualify', 'Question'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Rowing',
      nameCn: '划船',
      category: '健身',
      scene: '集中训练',
      description: '使用划船机或杠铃/哑铃进行划船动作，锻炼整个背部肌群。',
      keywords: ['Rowing', 'Reinforce', 'Resolve'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Squat',
      nameCn: '深蹲',
      category: '健身',
      scene: '简单/集中',
      description: '身体下蹲至大腿与地面平行或更低，然后站起。可自重或负重。',
      keywords: ['Squat', 'Strength', 'Success'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Triceps Dip',
      nameCn: '臂屈伸',
      category: '健身',
      scene: '简单/集中',
      description: '利用椅子或双杠，背对支撑物，双手支撑，身体下降再撑起。',
      keywords: ['Triceps', 'Triumph', 'Transform'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Upright Row',
      nameCn: '直立划船',
      category: '健身',
      scene: '集中训练',
      description: '手持杠铃或哑铃，沿身体向上提拉至下巴高度。',
      keywords: ['Upright', 'Unique', 'Ultimate'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'V-up',
      nameCn: 'V字起身',
      category: '健身',
      scene: '简单活动',
      description: '仰卧，同时抬起上身和双腿，身体呈V字形，用手触摸脚尖。',
      keywords: ['V-up', 'Value', 'Victory'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Wall Sit',
      nameCn: '靠墙静蹲',
      category: '健身',
      scene: '简单活动',
      description: '背靠墙壁，身体下蹲至膝盖呈90度，保持姿势。',
      keywords: ['Wall', 'Willpower', 'Worthy'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'X-Jumps',
      nameCn: 'X型跳',
      category: '健身',
      scene: '简单活动',
      description: '从深蹲姿势起跳，空中身体和四肢展开呈X形。',
      keywords: ['X-Jumps', 'eXplode', 'eXcel'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Y-Raise',
      nameCn: 'Y字上举',
      category: '健身',
      scene: '简单活动',
      description: '俯身，手臂向前上方伸展呈Y字形，并向上抬起。',
      keywords: ['Y-Raise', 'Yearn', 'Youth'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Zigzag Hops',
      nameCn: 'Z字跳',
      category: '健身',
      scene: '简单活动',
      description: '双脚并拢，以Z字形路线向前或向后连续跳跃。',
      keywords: ['Zigzag', 'Zeal', 'Zest'],
    ),
  };

  /// 瑜伽类动作
  static const Map<String, PAOExercise> yogaExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Ankle Stretch',
      nameCn: '脚踝拉伸',
      category: '瑜伽',
      scene: '简单活动',
      description: '坐姿或跪姿，拉伸脚背和脚踝，促进下肢循环。',
      keywords: ['Ankle', 'Awaken', 'Align'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Boat Pose',
      nameCn: '船式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '坐姿，抬起双腿和上身，身体呈V形。可简化（屈膝）或加强（伸直腿）。',
      keywords: ['Boat', 'Balance', 'Build'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Cat-Cow Pose',
      nameCn: '猫牛式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '吸气时抬头塌腰（牛式），呼气时含胸弓背（猫式）。可在垫上或椅子上做。',
      keywords: ['Cat', 'Cow', 'Cycle'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Downward-Facing Dog',
      nameCn: '下犬式',
      category: '瑜伽',
      scene: '集中训练',
      description: '身体呈倒V形，是经典的全身拉伸和力量体式。',
      keywords: ['Downward', 'Dog', 'Dynamic'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Eagle Pose',
      nameCn: '鸟王式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '站立，双腿和双臂相互缠绕。可简化为坐姿，只做手臂缠绕。',
      keywords: ['Eagle', 'Equilibrium', 'Embrace'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Forward Bend',
      nameCn: '前屈式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '站立或坐姿，身体前屈，拉伸整个背部和腿后侧。',
      keywords: ['Forward', 'Fold', 'Flexibility'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Garland Pose',
      nameCn: '花环式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '瑜伽深蹲，双脚外开，手肘抵住膝盖内侧。可借助椅子辅助。',
      keywords: ['Garland', 'Ground', 'Gratitude'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'Happy Baby Pose',
      nameCn: '快乐婴儿式',
      category: '瑜伽',
      scene: '集中训练',
      description: '仰卧，抓住脚掌外侧，将膝盖拉向腋窝，放松髋部和下背部。',
      keywords: ['Happy', 'Healing', 'Hold'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'Inversion (Legs-Up-the-Wall)',
      nameCn: '倒箭式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '将双腿靠在墙上，身体仰卧。极佳的放松和恢复体式。',
      keywords: ['Inversion', 'Inner', 'Inhale'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Janu Sirsasana',
      nameCn: '头碰膝前屈式',
      category: '瑜伽',
      scene: '集中训练',
      description: '坐姿，一腿伸直，另一腿弯曲，身体向伸直腿前屈。',
      keywords: ['Journey', 'Join', 'Joy'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Kneeling Pose',
      nameCn: '跪姿',
      category: '瑜伽',
      scene: '简单活动',
      description: '跪坐姿势，臀部坐在脚跟上，有助于消化和静心。',
      keywords: ['Kneel', 'Kindness', 'Keep'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Low Lunge',
      nameCn: '低弓步',
      category: '瑜伽',
      scene: '简单/集中',
      description: '低弓步，拉伸髋屈肌和大腿前侧。',
      keywords: ['Low', 'Lunge', 'Length'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Mountain Pose',
      nameCn: '山式',
      category: '瑜伽',
      scene: '简单活动',
      description: '站姿基础，身体挺拔，感受稳定和力量。',
      keywords: ['Mountain', 'Majesty', 'Mindful'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'Needle and Thread Pose',
      nameCn: '穿针引线式',
      category: '瑜伽',
      scene: '集中训练',
      description: '从四足跪姿开始，将一侧手臂从另一侧腋下穿过，扭转上背部。',
      keywords: ['Needle', 'Nourish', 'Nurture'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'One-Legged King Pigeon Pose',
      nameCn: '单腿鸽王式',
      category: '瑜伽',
      scene: '集中训练',
      description: '鸽子式，深度打开髋部，对柔韧性要求高。',
      keywords: ['One', 'Opening', 'Optimal'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Plank Pose',
      nameCn: '平板式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '平板支撑，锻炼全身核心力量。',
      keywords: ['Plank', 'Power', 'Patience'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Queen Pose',
      nameCn: '女王式',
      category: '瑜伽',
      scene: '简单活动',
      description: '坐姿，双腿交叉，一手置于膝上，一手向上伸展，模拟女王的优雅姿态。',
      keywords: ['Queen', 'Quietude', 'Quality'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Reverse Warrior',
      nameCn: '反战士式',
      category: '瑜伽',
      scene: '集中训练',
      description: '战士二式的变体，身体向后腿侧弯，拉伸侧腰。',
      keywords: ['Reverse', 'Radiate', 'Release'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Seated Spinal Twist',
      nameCn: '坐姿扭转',
      category: '瑜伽',
      scene: '简单/集中',
      description: '坐姿扭转，可有效灵活脊柱，按摩腹部器官。',
      keywords: ['Spinal', 'Serenity', 'Soothe'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Tree Pose',
      nameCn: '树式',
      category: '瑜伽',
      scene: '简单/集中',
      description: '经典的站立平衡体式，可扶墙或椅子辅助。',
      keywords: ['Tree', 'Tranquility', 'Trust'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Upward-Facing Dog',
      nameCn: '上犬式',
      category: '瑜伽',
      scene: '集中训练',
      description: '俯卧，用手臂撑起上身，打开胸腔，是拜日式中的一环。',
      keywords: ['Upward', 'Unfold', 'Uplift'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'Warrior I/II/III Pose',
      nameCn: '战士一/二/三式',
      category: '瑜伽',
      scene: '集中训练',
      description: '建立力量、稳定和专注的系列体式。',
      keywords: ['Warrior', 'Victory', 'Valor'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Wheel Pose',
      nameCn: '轮式',
      category: '瑜伽',
      scene: '集中训练',
      description: '深度后弯，需要强大的背部和手臂力量。',
      keywords: ['Wheel', 'Whole', 'Wonder'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'X Pose (Supine)',
      nameCn: '大字伸展式',
      category: '瑜伽',
      scene: '简单活动',
      description: '仰卧，身体和四肢向外伸展成大X形，进行全身的放松伸展。',
      keywords: ['eXpand', 'eXhale', 'eXplore'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Yogi Squat',
      nameCn: '瑜伽蹲',
      category: '瑜伽',
      scene: '简单/集中',
      description: '见 G - Garland Pose。双脚外开，手肘抵住膝盖内侧。',
      keywords: ['Yogi', 'Yield', 'Yearn'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Z-Pose',
      nameCn: 'Z字坐',
      category: '瑜伽',
      scene: '简单活动',
      description: '坐姿，一腿内旋一腿外旋，膝盖弯曲，身体呈Z字形，可进行多种拉伸和扭转。',
      keywords: ['Zen', 'Zest', 'Zone'],
    ),
  };

  /// 养生类动作
  static const Map<String, PAOExercise> traditionalExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Acupressure Massage',
      nameCn: '穴位按压',
      category: '养生',
      scene: '简单活动',
      description: '按揉合谷穴（虎口）或足三里穴（膝下），每个穴位1-2分钟，可提神、缓解疲劳。',
      keywords: ['Acupressure', 'Alleviate', 'Accurate'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Bouncing on the Toes',
      nameCn: '颠趾',
      category: '养生',
      scene: '简单活动',
      description: '八段锦第八式。双脚并拢，脚跟提起，身体上提，然后轻轻下落，温和地震动全身。',
      keywords: ['Bounce', 'Boost', 'Balance'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Cloud Hands',
      nameCn: '云手',
      category: '养生',
      scene: '简单活动',
      description: '太极拳核心动作。身体重心左右移动，双手如行云流水般在身前划圆，锻炼协调性。',
      keywords: ['Cloud', 'Calm', 'Coordinate'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Drawing the Bow',
      nameCn: '左右开弓',
      category: '养生',
      scene: '简单活动',
      description: '八段锦第二式。马步站立，向两侧做出拉弓射箭的姿势，能开阔胸襟。',
      keywords: ['Draw', 'Develop', 'Determine'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Embracing the Tree',
      nameCn: '站桩',
      category: '养生',
      scene: '简单/集中',
      description: '双脚与肩同宽，膝盖微屈，双手在胸前环抱，如抱大树。可静心养气。',
      keywords: ['Embrace', 'Energy', 'Endure'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Five Element Qigong',
      nameCn: '五行气功',
      category: '养生',
      scene: '集中训练',
      description: '基于中医五行理论的气功练习，调和五脏六腑的气血运行。',
      keywords: ['Five', 'Flow', 'Fundamental'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Golden Cock Standing on One Leg',
      nameCn: '金鸡独立',
      category: '养生',
      scene: '简单活动',
      description: '单脚站立，另一只脚抬起，保持平衡。可锻炼平衡感和下肢力量。',
      keywords: ['Golden', 'Grace', 'Ground'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'Hitting the Heavenly Drum',
      nameCn: '鸣天鼓',
      category: '养生',
      scene: '简单活动',
      description: '双手掌心贴住耳朵，手指敲击后脑勺，可醒脑提神，改善听力。',
      keywords: ['Hit', 'Heaven', 'Harmony'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'Internal Energy Circulation',
      nameCn: '内气运行',
      category: '养生',
      scene: '集中训练',
      description: '通过意念引导内气在体内循环，是高级气功的基础练习。',
      keywords: ['Internal', 'Intent', 'Infinite'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Jade Pillow Pressing',
      nameCn: '玉枕穴按摩',
      category: '养生',
      scene: '简单活动',
      description: '按摩后脑勺的玉枕穴，可缓解头痛，改善睡眠质量。',
      keywords: ['Jade', 'Joint', 'Journey'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Kidney Strengthening Exercise',
      nameCn: '补肾功',
      category: '养生',
      scene: '简单/集中',
      description: '腰部转动结合呼吸，强化肾气，提升精力和体质。',
      keywords: ['Kidney', 'Keep', 'Kinetic'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Liver Soothing Massage',
      nameCn: '疏肝理气',
      category: '养生',
      scene: '简单活动',
      description: '按摩太冲穴和期门穴，疏通肝经，调节情绪和气血。',
      keywords: ['Liver', 'Lift', 'Light'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Meridian Stretching',
      nameCn: '经络拉伸',
      category: '养生',
      scene: '简单/集中',
      description: '沿着经络走向进行拉伸，疏通气血，缓解疲劳。',
      keywords: ['Meridian', 'Move', 'Mindful'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'Nourishing Life Breathing',
      nameCn: '养生呼吸法',
      category: '养生',
      scene: '简单活动',
      description: '深呼吸配合意念，调节自律神经，达到养生保健的效果。',
      keywords: ['Nourish', 'Natural', 'Nurture'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'Opening and Closing',
      nameCn: '开合功',
      category: '养生',
      scene: '简单活动',
      description: '双手开合配合呼吸，调节气息，是太极拳的基础动作。',
      keywords: ['Open', 'Operate', 'Optimize'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Phoenix Spreading Wings',
      nameCn: '凤凰展翅',
      category: '养生',
      scene: '简单活动',
      description: '双臂如凤凰展翅般舒展，开阔胸怀，舒展筋骨。',
      keywords: ['Phoenix', 'Peaceful', 'Pure'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Qi Gathering at Dantian',
      nameCn: '气聚丹田',
      category: '养生',
      scene: '集中训练',
      description: '意念引导气息聚集到下丹田，是气功修炼的重要环节。',
      keywords: ['Qi', 'Quiet', 'Quality'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Rubbing the Face',
      nameCn: '搓面法',
      category: '养生',
      scene: '简单活动',
      description: '双手搓热后按摩面部，可促进面部血液循环，美容养颜。',
      keywords: ['Rub', 'Refresh', 'Revitalize'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Spleen and Stomach Care',
      nameCn: '健脾养胃',
      category: '养生',
      scene: '简单活动',
      description: '按摩足三里穴和中脘穴，调理脾胃功能，改善消化。',
      keywords: ['Spleen', 'Stomach', 'Strengthen'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Turtle Breathing',
      nameCn: '龟息法',
      category: '养生',
      scene: '集中训练',
      description: '模仿乌龟的呼吸方式，深长缓慢，可延年益寿。',
      keywords: ['Turtle', 'Tranquil', 'Transform'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Uplifting Qi Exercise',
      nameCn: '升清气',
      category: '养生',
      scene: '简单活动',
      description: '通过特定动作和呼吸，提升体内清气，排出浊气。',
      keywords: ['Uplift', 'Unite', 'Ultimate'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'Vitality Preservation',
      nameCn: '保精养元',
      category: '养生',
      scene: '集中训练',
      description: '通过特定的锻炼方法，保存精气，培养元气。',
      keywords: ['Vitality', 'Value', 'Virtue'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Waist Rotation',
      nameCn: '转腰功',
      category: '养生',
      scene: '简单活动',
      description: '腰部画圆转动，强化腰肾，改善腰部酸痛。',
      keywords: ['Waist', 'Warm', 'Wisdom'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'Xing Yi Stance',
      nameCn: '形意站桩',
      category: '养生',
      scene: '集中训练',
      description: '形意拳的基础站桩，培养内劲和整体力量。',
      keywords: ['Xing', 'eXcellent', 'eXpand'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Yin Yang Balance',
      nameCn: '阴阳调和',
      category: '养生',
      scene: '简单/集中',
      description: '通过动静结合的练习，调和体内阴阳平衡。',
      keywords: ['Yin', 'Yang', 'Yield'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Zhan Zhuang',
      nameCn: '站桩功',
      category: '养生',
      scene: '集中训练',
      description: '静桩练习，培养内力和专注力，是内家拳的基础。',
      keywords: ['Zhan', 'Zen', 'Zone'],
    ),
  };

  /// 篮球类动作
  static const Map<String, PAOExercise> basketballExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Arm Fake',
      nameCn: '手臂假动作',
      category: '篮球',
      scene: '简单/集中',
      description: '不移动脚步，用持球手臂做出投篮或传球的假动作，迷惑防守者。',
      keywords: ['Arm', 'Artful', 'Adapt'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Behind-the-Back Dribble',
      nameCn: '背后运球',
      category: '篮球',
      scene: '集中训练',
      description: '将球从身体一侧运球到背后，再从另一侧接住，摆脱防守。',
      keywords: ['Behind', 'Bold', 'Basketball'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Crossover Dribble',
      nameCn: '交叉运球',
      category: '篮球',
      scene: '简单/集中',
      description: '快速将球从一只手运到另一只手，身体重心跟随球的方向。',
      keywords: ['Crossover', 'Creative', 'Control'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Drive to the Basket',
      nameCn: '突破上篮',
      category: '篮球',
      scene: '集中训练',
      description: '快速启动突破防守，冲向篮筐进行上篮或扣篮。',
      keywords: ['Drive', 'Determined', 'Decisive'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Euro Step',
      nameCn: '欧洲步',
      category: '篮球',
      scene: '集中训练',
      description: '突破时先向一侧迈步，再向另一侧迈步，躲避防守者的封堵。',
      keywords: ['Euro', 'Elegant', 'Effective'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Free Throw',
      nameCn: '罚球',
      category: '篮球',
      scene: '简单/集中',
      description: '站在罚球线上，无人防守的情况下投篮，考验稳定性和专注力。',
      keywords: ['Free', 'Focus', 'Form'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Give and Go',
      nameCn: '传切配合',
      category: '篮球',
      scene: '集中训练',
      description: '传球给队友后立即启动切入，接回传球进攻。',
      keywords: ['Give', 'Go', 'Great'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'Hook Shot',
      nameCn: '勾手投篮',
      category: '篮球',
      scene: '集中训练',
      description: '侧身对篮筐，用远离防守者的手臂勾手投篮。',
      keywords: ['Hook', 'Height', 'Harmony'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'In-and-Out Dribble',
      nameCn: '内外运球',
      category: '篮球',
      scene: '简单/集中',
      description: '假装向一个方向运球，然后迅速将球拉回，向相反方向运球。',
      keywords: ['In', 'Out', 'Intense'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Jump Shot',
      nameCn: '跳投',
      category: '篮球',
      scene: '简单/集中',
      description: '跳起时在最高点出手投篮，是最基本的投篮技术。',
      keywords: ['Jump', 'Joy', 'Justice'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Killer Crossover',
      nameCn: '致命交叉步',
      category: '篮球',
      scene: '集中训练',
      description: '极速的交叉运球结合身体摆动，彻底摆脱防守者。',
      keywords: ['Killer', 'Key', 'Kinetic'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Layup',
      nameCn: '上篮',
      category: '篮球',
      scene: '简单/集中',
      description: '接近篮筐时的近距离投篮，可以是正手、反手或双手上篮。',
      keywords: ['Layup', 'Light', 'Leap'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Move without the Ball',
      nameCn: '无球跑动',
      category: '篮球',
      scene: '简单/集中',
      description: '在没有球时的战术移动，寻找空档和接球机会。',
      keywords: ['Move', 'Motion', 'Master'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'No-Look Pass',
      nameCn: '不看人传球',
      category: '篮球',
      scene: '集中训练',
      description: '眼睛看向一个方向，实际将球传向另一个方向，迷惑防守。',
      keywords: ['No-look', 'Natural', 'Noble'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'Outlet Pass',
      nameCn: '快攻传球',
      category: '篮球',
      scene: '集中训练',
      description: '抢到篮板后迅速长传给快下的队友，发动快攻。',
      keywords: ['Outlet', 'Opportunity', 'Optimal'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Pick and Roll',
      nameCn: '挡拆',
      category: '篮球',
      scene: '集中训练',
      description: '队友设立掩护后顺下或拉开，持球者利用掩护突破或投篮。',
      keywords: ['Pick', 'Powerful', 'Perfect'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Quick Release',
      nameCn: '快速出手',
      category: '篮球',
      scene: '简单/集中',
      description: '接球后立即投篮，不给防守者反应时间。',
      keywords: ['Quick', 'Quality', 'Quest'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Rebound',
      nameCn: '篮板球',
      category: '篮球',
      scene: '集中训练',
      description: '投篮不中后争抢篮板球，包括进攻篮板和防守篮板。',
      keywords: ['Rebound', 'Relentless', 'Rhythm'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Spin Move',
      nameCn: '转身过人',
      category: '篮球',
      scene: '集中训练',
      description: '利用身体转身摆脱防守者，继续进攻或传球。',
      keywords: ['Spin', 'Swift', 'Smooth'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Triple Threat',
      nameCn: '三威胁',
      category: '篮球',
      scene: '简单/集中',
      description: '持球后的基本姿势，可以投篮、传球或突破。',
      keywords: ['Triple', 'Threat', 'Tactical'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Up-and-Under',
      nameCn: '上下假动作',
      category: '篮球',
      scene: '集中训练',
      description: '做投篮假动作，待防守者跳起后再突破上篮。',
      keywords: ['Up', 'Under', 'Ultimate'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'V-Cut',
      nameCn: 'V字切入',
      category: '篮球',
      scene: '简单/集中',
      description: '先向一个方向移动，然后迅速向相反方向切入，摆脱防守。',
      keywords: ['V-cut', 'Victory', 'Versatile'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Wrap Around Pass',
      nameCn: '绕传',
      category: '篮球',
      scene: '集中训练',
      description: '将球从身体一侧绕过防守者传给队友。',
      keywords: ['Wrap', 'Wise', 'Win'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'X-Out',
      nameCn: 'X型跑位',
      category: '篮球',
      scene: '集中训练',
      description: '两名球员交叉跑位，互相为对方创造接球空间。',
      keywords: ['X-out', 'eXcellent', 'eXplosive'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Yo-Yo Dribble',
      nameCn: '悠悠运球',
      category: '篮球',
      scene: '简单/集中',
      description: '控制球的节奏，时快时慢，迷惑防守者的节奏感。',
      keywords: ['Yo-yo', 'Young', 'Yield'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Zone Defense Break',
      nameCn: '破区域防守',
      category: '篮球',
      scene: '集中训练',
      description: '通过快速传球和移动，撕裂对方的区域防守。',
      keywords: ['Zone', 'Zealous', 'Zen'],
    ),
  };

  /// 足球类动作
  static const Map<String, PAOExercise> footballExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Altering the Angle Pass',
      nameCn: '变向传球',
      category: '足球',
      scene: '集中训练',
      description: '接球后不停球，顺势将球向侧方拨动一步再传出，改变进攻角度。',
      keywords: ['Alter', 'Agile', 'Attack'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Bicycle Kick',
      nameCn: '倒钩射门',
      category: '足球',
      scene: '集中训练',
      description: '背对球门，当球在身后时，跳起用倒钩动作射门。',
      keywords: ['Bicycle', 'Bold', 'Beautiful'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Cruyff Turn',
      nameCn: '克鲁伊夫转身',
      category: '足球',
      scene: '简单/集中',
      description: '做出射门或传球假动作，然后用脚内侧将球拨到另一侧，同时转身。',
      keywords: ['Cruyff', 'Creative', 'Clever'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Dribbling',
      nameCn: '盘球',
      category: '足球',
      scene: '简单/集中',
      description: '用双脚控制球的方向和速度，在移动中保持对球的控制。',
      keywords: ['Dribble', 'Dynamic', 'Decisive'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Elastico',
      nameCn: '钟摆过人',
      category: '足球',
      scene: '集中训练',
      description: '先用脚外侧向一个方向推球，立即用脚内侧将球拨向相反方向。',
      keywords: ['Elastico', 'Elegant', 'Effective'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Free Kick',
      nameCn: '任意球',
      category: '足球',
      scene: '集中训练',
      description: '在犯规地点进行的直接或间接任意球，考验精准度和力量控制。',
      keywords: ['Free', 'Focus', 'Force'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Give and Go',
      nameCn: '传切配合',
      category: '足球',
      scene: '集中训练',
      description: '将球传给队友后立即跑向空档，接队友的回传。',
      keywords: ['Give', 'Go', 'Great'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'Header',
      nameCn: '头球',
      category: '足球',
      scene: '简单/集中',
      description: '用头部击球，可以是传球、射门或解围。',
      keywords: ['Header', 'Height', 'Hard'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'Inside Cut',
      nameCn: '内切',
      category: '足球',
      scene: '简单/集中',
      description: '从边路向中路切入，创造射门或传球机会。',
      keywords: ['Inside', 'Incisive', 'Intelligent'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Juggling',
      nameCn: '颠球',
      category: '足球',
      scene: '简单活动',
      description: '用脚、大腿、头等部位连续触球，保持球不落地，练习球感。',
      keywords: ['Juggling', 'Joy', 'Judgment'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Knuckleball',
      nameCn: '落叶球',
      category: '足球',
      scene: '集中训练',
      description: '射门时让球产生不规则飞行轨迹，增加守门员判断难度。',
      keywords: ['Knuckleball', 'Key', 'Killer'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Long Pass',
      nameCn: '长传',
      category: '足球',
      scene: '集中训练',
      description: '跨越较大距离的精准传球，快速转移进攻重点。',
      keywords: ['Long', 'Launch', 'Lead'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Marseille Turn',
      nameCn: '马赛回旋',
      category: '足球',
      scene: '集中训练',
      description: '360度转身过人，用脚底拖球转身摆脱防守者。',
      keywords: ['Marseille', 'Master', 'Magnificent'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'Nutmeg',
      nameCn: '穿裆过人',
      category: '足球',
      scene: '简单/集中',
      description: '将球从对手双腿之间传过，然后从侧面超越对手。',
      keywords: ['Nutmeg', 'Natural', 'Neat'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'Overhead Kick',
      nameCn: '倒勾',
      category: '足球',
      scene: '集中训练',
      description: '身体倒向空中，用脚背击球，通常用于射门。',
      keywords: ['Overhead', 'Outstanding', 'Opportunity'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Penalty Kick',
      nameCn: '点球',
      category: '足球',
      scene: '简单/集中',
      description: '在点球点对守门员一对一的射门，考验心理素质。',
      keywords: ['Penalty', 'Pressure', 'Precision'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Quick Feet',
      nameCn: '快速脚法',
      category: '足球',
      scene: '简单活动',
      description: '快速连续的小触球，提高脚部灵活性和球感。',
      keywords: ['Quick', 'Quality', 'Quest'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Rainbow Flick',
      nameCn: '彩虹过人',
      category: '足球',
      scene: '集中训练',
      description: '用脚跟将球挑起，让球从头顶越过对手。',
      keywords: ['Rainbow', 'Rare', 'Remarkable'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Stepover',
      nameCn: '踩单车',
      category: '足球',
      scene: '简单/集中',
      description: '在球前做假动作，一只脚或双脚从球上方掠过，迷惑防守者。',
      keywords: ['Stepover', 'Swift', 'Smooth'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Through Ball',
      nameCn: '直塞球',
      category: '足球',
      scene: '集中训练',
      description: '传球穿透防线，为队友创造单刀机会。',
      keywords: ['Through', 'Tactical', 'Timing'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Undercut Pass',
      nameCn: '下旋传球',
      category: '足球',
      scene: '集中训练',
      description: '传球时给球施加下旋，使球快速停在队友脚下。',
      keywords: ['Undercut', 'Unique', 'Useful'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'Volley',
      nameCn: '凌空抽射',
      category: '足球',
      scene: '集中训练',
      description: '球在空中时直接射门，不让球落地。',
      keywords: ['Volley', 'Violent', 'Victory'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Wall Pass',
      nameCn: '撞墙配合',
      category: '足球',
      scene: '集中训练',
      description: '与队友进行快速一二传配合，突破防线。',
      keywords: ['Wall', 'Wise', 'Win'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'X-Pass',
      nameCn: 'X型传球',
      category: '足球',
      scene: '集中训练',
      description: '球员交叉跑位时的斜向传球，撕裂防线。',
      keywords: ['X-pass', 'eXact', 'eXtreme'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Yo-yo Technique',
      nameCn: '拉球技术',
      category: '足球',
      scene: '简单/集中',
      description: '将球向前推出再拉回，控制比赛节奏。',
      keywords: ['Yo-yo', 'Yield', 'Young'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Zidane Turn',
      nameCn: '齐达内转身',
      category: '足球',
      scene: '集中训练',
      description: '用脚外侧将球向后拖拽同时转身，摆脱身后防守者。',
      keywords: ['Zidane', 'Zen', 'Zone'],
    ),
  };

  /// 拉伸类动作
  static const Map<String, PAOExercise> officeExercises = {
    'A': PAOExercise(
      letter: 'A',
      nameEn: 'Ankle Alphabet',
      nameCn: '脚踝写字母',
      category: '拉伸',
      scene: '简单活动',
      description: '脱鞋后，抬起一只脚，用大脚趾在空中"书写"从A到Z的全部字母。这个动作能有效提升脚踝的灵活性和活动范围。',
      keywords: ['Ankle', 'Alphabet', 'Articulate'],
    ),
    'B': PAOExercise(
      letter: 'B',
      nameEn: 'Back Arch Stretch',
      nameCn: '背部后弯拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '坐在椅子上，双手放在后脑勺，轻柔地向后弯曲上背部。这有助于抵消长时间前倾的姿势。',
      keywords: ['Back', 'Bend', 'Balance'],
    ),
    'C': PAOExercise(
      letter: 'C',
      nameEn: 'Chair Spinal Twist',
      nameCn: '椅子脊柱扭转',
      category: '拉伸',
      scene: '简单活动',
      description: '坐在椅子上，保持臀部朝前，缓慢将上身向左或右扭转，用对侧手抓住椅背。',
      keywords: ['Chair', 'Twist', 'Core'],
    ),
    'D': PAOExercise(
      letter: 'D',
      nameEn: 'Desk Push-Up',
      nameCn: '桌子俯卧撑',
      category: '拉伸',
      scene: '简单活动',
      description: '双手撑在桌子边缘，身体斜向做俯卧撑动作，这是一个很好的上身激活运动。',
      keywords: ['Desk', 'Dynamic', 'Drive'],
    ),
    'E': PAOExercise(
      letter: 'E',
      nameEn: 'Eye Focus Shift',
      nameCn: '眼部焦点转移',
      category: '拉伸',
      scene: '简单活动',
      description: '看近处物体5秒，再看远处物体5秒，重复进行。这有助于缓解眼部疲劳。',
      keywords: ['Eye', 'Exercise', 'Endurance'],
    ),
    'F': PAOExercise(
      letter: 'F',
      nameEn: 'Forearm Stretch',
      nameCn: '前臂拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '伸直一只手臂，手掌朝上，用另一只手轻轻向下压手指和手掌，拉伸前臂肌肉。',
      keywords: ['Forearm', 'Flex', 'Flow'],
    ),
    'G': PAOExercise(
      letter: 'G',
      nameEn: 'Glute Squeeze',
      nameCn: '臀部收紧',
      category: '拉伸',
      scene: '简单活动',
      description: '坐着或站着时，收紧臀部肌肉保持5秒，然后放松。这有助于激活经常被忽视的臀肌。',
      keywords: ['Glute', 'Grip', 'Generate'],
    ),
    'H': PAOExercise(
      letter: 'H',
      nameEn: 'Hip Flexor Stretch',
      nameCn: '髋屈肌拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '站立，一只脚向后迈一大步，前腿微弯，感受后腿髋部前侧的拉伸。',
      keywords: ['Hip', 'Hold', 'Heal'],
    ),
    'I': PAOExercise(
      letter: 'I',
      nameEn: 'Invisible Chair Sit',
      nameCn: '隐形椅子坐',
      category: '拉伸',
      scene: '简单活动',
      description: '靠墙站立，慢慢下滑至大腿与地面平行的坐姿，保持30秒。是很好的腿部力量练习。',
      keywords: ['Invisible', 'Isometric', 'Intense'],
    ),
    'J': PAOExercise(
      letter: 'J',
      nameEn: 'Jaw Release',
      nameCn: '下颌放松',
      category: '拉伸',
      scene: '简单活动',
      description: '轻轻张开嘴巴，向左右移动下颌，然后做咀嚼动作。这有助于缓解下颌紧张。',
      keywords: ['Jaw', 'Joint', 'Gentle'],
    ),
    'K': PAOExercise(
      letter: 'K',
      nameEn: 'Knee to Chest',
      nameCn: '膝盖贴胸',
      category: '拉伸',
      scene: '简单活动',
      description: '站立或坐着，抬起一只膝盖向胸部靠近，用双手轻抱小腿，拉伸臀部和下背部。',
      keywords: ['Knee', 'Keep', 'Kind'],
    ),
    'L': PAOExercise(
      letter: 'L',
      nameEn: 'Leg Extension',
      nameCn: '腿部伸展',
      category: '拉伸',
      scene: '简单活动',
      description: '坐在椅子上，伸直一条腿并保持几秒，然后慢慢放下。交替进行。',
      keywords: ['Leg', 'Lift', 'Length'],
    ),
    'M': PAOExercise(
      letter: 'M',
      nameEn: 'Marching in Place',
      nameCn: '原地踏步',
      category: '拉伸',
      scene: '简单活动',
      description: '原地抬高膝盖踏步，就像行军一样。这是很好的心血管激活运动。',
      keywords: ['March', 'Move', 'Momentum'],
    ),
    'N': PAOExercise(
      letter: 'N',
      nameEn: 'Neck Roll',
      nameCn: '颈部转动',
      category: '拉伸',
      scene: '简单活动',
      description: '缓慢地将头部向前、向右、向后、向左转动，做完整的圆周运动。',
      keywords: ['Neck', 'Natural', 'Nurture'],
    ),
    'O': PAOExercise(
      letter: 'O',
      nameEn: 'Overhead Reach',
      nameCn: '过顶伸展',
      category: '拉伸',
      scene: '简单活动',
      description: '坐着或站着，双臂向上伸展过头顶，手指交错，感受整个身体侧面的拉伸。',
      keywords: ['Overhead', 'Open', 'Optimum'],
    ),
    'P': PAOExercise(
      letter: 'P',
      nameEn: 'Posture Reset',
      nameCn: '姿势重置',
      category: '拉伸',
      scene: '简单活动',
      description: '站立，肩膀向后，下巴微收，想象头顶有根绳子向上拉。保持这个理想姿势。',
      keywords: ['Posture', 'Perfect', 'Poise'],
    ),
    'Q': PAOExercise(
      letter: 'Q',
      nameEn: 'Quad Stretch',
      nameCn: '股四头肌拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '站立，弯曲一只膝盖，用手抓住脚踝向臀部拉，感受大腿前侧的拉伸。',
      keywords: ['Quad', 'Quick', 'Quality'],
    ),
    'R': PAOExercise(
      letter: 'R',
      nameEn: 'Rib Cage Stretch',
      nameCn: '胸廓拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '一只手臂向上伸展，身体向侧面倾斜，感受侧腰和胸廓的拉伸。',
      keywords: ['Rib', 'Reach', 'Release'],
    ),
    'S': PAOExercise(
      letter: 'S',
      nameEn: 'Shoulder Blade Squeeze',
      nameCn: '肩胛骨收紧',
      category: '拉伸',
      scene: '简单活动',
      description: '将肩胛骨向后收紧，就像要夹住一支铅笔一样，保持5秒。',
      keywords: ['Shoulder', 'Squeeze', 'Stabilize'],
    ),
    'T': PAOExercise(
      letter: 'T',
      nameEn: 'Triceps Stretch',
      nameCn: '三头肌拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '一只手臂举过头顶，肘部弯曲，手掌触摸背部，用另一只手轻推肘部。',
      keywords: ['Triceps', 'Tension', 'Touch'],
    ),
    'U': PAOExercise(
      letter: 'U',
      nameEn: 'Upper Back Release',
      nameCn: '上背部放松',
      category: '拉伸',
      scene: '简单活动',
      description: '双手交叉抱胸，低头同时用力推手，感受上背部和肩胛骨之间的拉伸。',
      keywords: ['Upper', 'Unwind', 'Unite'],
    ),
    'V': PAOExercise(
      letter: 'V',
      nameEn: 'V-Sit Stretch',
      nameCn: 'V字坐伸展',
      category: '拉伸',
      scene: '简单活动',
      description: '坐在地上，双腿分开成V字形，身体向前倾，拉伸大腿内侧和背部。',
      keywords: ['V-sit', 'Versatile', 'Vitality'],
    ),
    'W': PAOExercise(
      letter: 'W',
      nameEn: 'Wrist Circles',
      nameCn: '手腕转圈',
      category: '拉伸',
      scene: '简单活动',
      description: '双手握拳，手腕做顺时针和逆时针转动，缓解手腕僵硬。',
      keywords: ['Wrist', 'Warm', 'Wellness'],
    ),
    'X': PAOExercise(
      letter: 'X',
      nameEn: 'X-Arms Stretch',
      nameCn: 'X型手臂拉伸',
      category: '拉伸',
      scene: '简单活动',
      description: '双臂交叉抱胸，同时扭转身体，感受肩膀和上背部的拉伸。',
      keywords: ['X-arms', 'eXtend', 'eXpand'],
    ),
    'Y': PAOExercise(
      letter: 'Y',
      nameEn: 'Y-Raise',
      nameCn: 'Y字举臂',
      category: '拉伸',
      scene: '简单活动',
      description: '双臂向上举起成Y字形，保持几秒，然后放下。这有助于打开胸部。',
      keywords: ['Y-raise', 'Yield', 'Youthful'],
    ),
    'Z': PAOExercise(
      letter: 'Z',
      nameEn: 'Zen Breathing',
      nameCn: '禅式呼吸',
      category: '拉伸',
      scene: '简单活动',
      description: '深吸气4秒，屏息4秒，呼气4秒。重复几次，帮助身心放松。',
      keywords: ['Zen', 'Zone', 'Zest'],
    ),
  };

  /// 眼保健操类动作
  static const Map<String, PAOExercise> eyeExercises = {
    '1': PAOExercise(
      letter: '1',
      nameEn: 'Press Bamboo Acupoint',
      nameCn: '第一节 揉天应穴',
      category: '护眼',
      scene: '简单活动',
      description: '用双手大拇指螺纹面分别按在两侧穴位上，其余手指自然放松，指端抵在前额上。随音乐口令有节奏地按揉穴位。',
      keywords: ['Press', 'Point', 'Peaceful'],
    ),
    '2': PAOExercise(
      letter: '2',
      nameEn: 'Press Upper Eye Socket',
      nameCn: '第二节 挤按睛明穴',
      category: '护眼',
      scene: '简单活动',
      description: '用双手食指螺纹面分别按在两侧穴位上，其余手指自然放松、握起，呈空心拳状。随音乐口令有节奏地上下挤按穴位。',
      keywords: ['Press', 'Position', 'Precise'],
    ),
    '3': PAOExercise(
      letter: '3',
      nameEn: 'Press Four White Acupoint',
      nameCn: '第三节 按揉四白穴',
      category: '护眼',
      scene: '简单活动',
      description:
          '用双手食指螺纹面分别按在两侧穴位上，拇指抵在下颌凹陷处，其余手指自然放松、握起，呈空心拳状。随音乐口令有节奏地按揉穴位。',
      keywords: ['Press', 'Point', 'Proper'],
    ),
    '4': PAOExercise(
      letter: '4',
      nameEn: 'Press Temples and Wheel-scrape Eye Sockets',
      nameCn: '第四节 按太阳穴转眼球',
      category: '护眼',
      scene: '简单活动',
      description:
          '用双手拇指螺纹面分别按在两侧太阳穴上，其余手指自然放松，弯曲。先按揉太阳穴，再用双手食指第二关节内侧，稍加用力从眉头刮至眉梢。',
      keywords: ['Press', 'Point', 'Pivot'],
    ),
    '5': PAOExercise(
      letter: '5',
      nameEn: 'Press Fengchi Acupoint',
      nameCn: '第五节 按揉风池穴',
      category: '护眼',
      scene: '简单活动',
      description: '用双手食指和中指的螺纹面分别按在两侧穴位上，其余三指自然放松。随音乐口令有节奏地按揉穴位。',
      keywords: ['Press', 'Point', 'Persistent'],
    ),
    '6': PAOExercise(
      letter: '6',
      nameEn: 'Knead Ear and Foot Massage',
      nameCn: '第六节 揉捏耳垂脚趾抓地',
      category: '护眼',
      scene: '简单活动',
      description: '用双手拇指和食指的螺纹面捏住耳垂正中的眼穴，其余三指自然并拢弯曲。伴随揉捏耳垂，用双脚全部脚趾做抓地运动。',
      keywords: ['Press', 'Point', 'Practice'],
    ),
  };

  /// 所有动作类别
  static const Map<String, PAOCategory> allCategories = {
    '健身': PAOCategory(name: '健身', icon: '💪', exercises: fitnessExercises),
    '瑜伽': PAOCategory(name: '瑜伽', icon: '🧘‍♀️', exercises: yogaExercises),
    '养生': PAOCategory(name: '养生', icon: '☯️', exercises: traditionalExercises),
    '篮球': PAOCategory(name: '篮球', icon: '🏀', exercises: basketballExercises),
    '足球': PAOCategory(name: '足球', icon: '⚽', exercises: footballExercises),
    '拉伸': PAOCategory(name: '拉伸', icon: '💼', exercises: officeExercises),
    '护眼': PAOCategory(name: '护眼', icon: '👁️', exercises: eyeExercises),
  };

  /// 获取指定类别的动作
  static Map<String, PAOExercise>? getExercisesByCategory(String category) {
    return allCategories[category]?.exercises;
  }

  /// 获取所有动作（合并所有类别）
  static Map<String, PAOExercise> getAllExercises() {
    final Map<String, PAOExercise> allExercises = {};
    for (final category in allCategories.values) {
      for (final entry in category.exercises.entries) {
        // 使用"类别-字母"作为键来避免冲突
        allExercises['${category.name}-${entry.key}'] = entry.value;
      }
    }
    return allExercises;
  }

  /// 根据字母和类别获取特定动作
  static PAOExercise? getExercise(String letter, String category) {
    return allCategories[category]?.exercises[letter];
  }

  /// 获取字母到动作的简单映射（用于PAO记忆法）
  /// 优先使用健身类动作，如果不存在则使用其他类别的动作
  static Map<String, PAOExercise> getLetterToExerciseMap() {
    final Map<String, PAOExercise> letterMap = {};

    // 按优先级顺序添加动作：健身 > 瑜伽 > 养生 > 其他
    final priorityCategories = ['健身', '瑜伽', '养生', '篮球', '足球', '拉伸', '护眼'];

    for (final categoryName in priorityCategories) {
      final category = allCategories[categoryName];
      if (category != null) {
        for (final entry in category.exercises.entries) {
          // 只有当字母还没有对应动作时才添加
          if (!letterMap.containsKey(entry.key)) {
            letterMap[entry.key] = entry.value;
          }
        }
      }
    }

    return letterMap;
  }

  /// 获取动作（支持自定义动作库）
  /// 如果提供了自定义动作库，优先使用自定义动作，否则使用预设动作
  static Map<String, PAOExercise> getExercisesWithCustomLibrary(
    Map<String, dynamic>? customActions,
  ) {
    final defaultExercises = getLetterToExerciseMap();

    if (customActions != null) {
      // 将自定义动作转换为PAOExercise并覆盖预设动作
      final customExercises = <String, PAOExercise>{};
      customActions.forEach((letter, actionData) {
        if (actionData is Map<String, dynamic>) {
          try {
            // 假设actionData是CustomPAOAction的JSON格式
            final customAction = PAOExercise(
              letter: actionData['letter'] as String,
              nameEn: actionData['nameEn'] as String,
              nameCn: actionData['nameCn'] as String,
              category: actionData['category'] as String,
              scene: actionData['scene'] as String,
              description: actionData['description'] as String,
              keywords: List<String>.from(actionData['keywords'] ?? []),
            );
            customExercises[letter] = customAction;
          } catch (e) {
            // 如果转换失败，使用预设动作
            print('转换自定义动作失败: $e');
          }
        }
      });

      // 合并预设动作和自定义动作（自定义动作优先）
      final mergedExercises = Map<String, PAOExercise>.from(defaultExercises);
      mergedExercises.addAll(customExercises);
      return mergedExercises;
    }

    return defaultExercises;
  }
}
