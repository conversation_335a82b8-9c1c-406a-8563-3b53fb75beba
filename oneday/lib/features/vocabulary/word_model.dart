/// 词汇库数据模型
class Vocabulary {
  final VocabularyMetadata metadata;
  final Map<String, WordDetails> words;

  Vocabulary({required this.metadata, required this.words});

  factory Vocabulary.fromJson(Map<String, dynamic> json) {
    return Vocabulary(
      metadata: VocabularyMetadata.fromJson(json['metadata']),
      words: (json['words'] as Map<String, dynamic>).map(
        (key, value) => MapEntry(key, WordDetails.fromJson(value)),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'metadata': metadata.toJson(),
      'words': words.map((key, value) => MapEntry(key, value.toJson())),
    };
  }
}

/// 词汇分类模型
class VocabularyCategory {
  final String id;
  final String name;
  final String description;
  final int totalWords;
  final int selectedWords;
  final bool isCustom;
  final DateTime? createdAt;
  final List<String> wordIds;

  VocabularyCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.totalWords,
    this.selectedWords = 0,
    this.isCustom = false,
    this.createdAt,
    this.wordIds = const [],
  });

  factory VocabularyCategory.fromJson(Map<String, dynamic> json) {
    return VocabularyCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      totalWords: json['totalWords'],
      selectedWords: json['selectedWords'] ?? 0,
      isCustom: json['isCustom'] ?? false,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : null,
      wordIds: List<String>.from(json['wordIds'] ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'totalWords': totalWords,
      'selectedWords': selectedWords,
      'isCustom': isCustom,
      'createdAt': createdAt?.toIso8601String(),
      'wordIds': wordIds,
    };
  }

  VocabularyCategory copyWith({
    String? id,
    String? name,
    String? description,
    int? totalWords,
    int? selectedWords,
    bool? isCustom,
    DateTime? createdAt,
    List<String>? wordIds,
  }) {
    return VocabularyCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      totalWords: totalWords ?? this.totalWords,
      selectedWords: selectedWords ?? this.selectedWords,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      wordIds: wordIds ?? this.wordIds,
    );
  }
}

/// 学习进度模型 - 支持FSRS算法
class WordLearningProgress {
  final String wordId;
  final bool isSelected;
  final bool isLearned;
  final int reviewCount;
  final DateTime? lastReviewedAt;
  final DateTime? firstLearnedAt;
  final double masteryLevel; // 0.0 - 1.0
  final List<DateTime> reviewHistory;

  // FSRS算法相关字段
  final double stability; // 记忆稳定性 (天)
  final double difficulty; // 记忆难度 (0.0 - 10.0)
  final DateTime? nextReviewDate; // 下次复习日期
  final List<FSRSReviewRecord> fsrsHistory; // FSRS复习记录
  final double retrievability; // 当前记忆保持率 (0.0 - 1.0)

  // 动觉记忆特化字段
  final double kinestheticBonus; // 动觉记忆增强系数 (1.0 - 2.0)
  final List<MotorMemoryData> motorMemoryHistory; // 动作记忆数据

  WordLearningProgress({
    required this.wordId,
    this.isSelected = false,
    this.isLearned = false,
    this.reviewCount = 0,
    this.lastReviewedAt,
    this.firstLearnedAt,
    this.masteryLevel = 0.0,
    this.reviewHistory = const [],
    // FSRS默认值
    this.stability = 1.0,
    this.difficulty = 5.0,
    this.nextReviewDate,
    this.fsrsHistory = const [],
    this.retrievability = 1.0,
    // 动觉记忆默认值
    this.kinestheticBonus = 1.0,
    this.motorMemoryHistory = const [],
  });

  factory WordLearningProgress.fromJson(Map<String, dynamic> json) {
    return WordLearningProgress(
      wordId: json['wordId'],
      isSelected: json['isSelected'] ?? false,
      isLearned: json['isLearned'] ?? false,
      reviewCount: json['reviewCount'] ?? 0,
      lastReviewedAt: json['lastReviewedAt'] != null
          ? DateTime.parse(json['lastReviewedAt'])
          : null,
      firstLearnedAt: json['firstLearnedAt'] != null
          ? DateTime.parse(json['firstLearnedAt'])
          : null,
      masteryLevel: (json['masteryLevel'] ?? 0.0).toDouble(),
      reviewHistory:
          (json['reviewHistory'] as List<dynamic>?)
              ?.map((e) => DateTime.parse(e))
              .toList() ??
          [],
      // FSRS字段
      stability: (json['stability'] ?? 1.0).toDouble(),
      difficulty: (json['difficulty'] ?? 5.0).toDouble(),
      nextReviewDate: json['nextReviewDate'] != null
          ? DateTime.parse(json['nextReviewDate'])
          : null,
      fsrsHistory:
          (json['fsrsHistory'] as List<dynamic>?)
              ?.map((e) => FSRSReviewRecord.fromJson(e))
              .toList() ??
          [],
      retrievability: (json['retrievability'] ?? 1.0).toDouble(),
      // 动觉记忆字段
      kinestheticBonus: (json['kinestheticBonus'] ?? 1.0).toDouble(),
      motorMemoryHistory:
          (json['motorMemoryHistory'] as List<dynamic>?)
              ?.map((e) => MotorMemoryData.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'wordId': wordId,
      'isSelected': isSelected,
      'isLearned': isLearned,
      'reviewCount': reviewCount,
      'lastReviewedAt': lastReviewedAt?.toIso8601String(),
      'firstLearnedAt': firstLearnedAt?.toIso8601String(),
      'masteryLevel': masteryLevel,
      'reviewHistory': reviewHistory.map((e) => e.toIso8601String()).toList(),
      // FSRS字段
      'stability': stability,
      'difficulty': difficulty,
      'nextReviewDate': nextReviewDate?.toIso8601String(),
      'fsrsHistory': fsrsHistory.map((e) => e.toJson()).toList(),
      'retrievability': retrievability,
      // 动觉记忆字段
      'kinestheticBonus': kinestheticBonus,
      'motorMemoryHistory': motorMemoryHistory.map((e) => e.toJson()).toList(),
    };
  }

  WordLearningProgress copyWith({
    String? wordId,
    bool? isSelected,
    bool? isLearned,
    int? reviewCount,
    DateTime? lastReviewedAt,
    DateTime? firstLearnedAt,
    double? masteryLevel,
    List<DateTime>? reviewHistory,
    // FSRS字段
    double? stability,
    double? difficulty,
    DateTime? nextReviewDate,
    List<FSRSReviewRecord>? fsrsHistory,
    double? retrievability,
    // 动觉记忆字段
    double? kinestheticBonus,
    List<MotorMemoryData>? motorMemoryHistory,
  }) {
    return WordLearningProgress(
      wordId: wordId ?? this.wordId,
      isSelected: isSelected ?? this.isSelected,
      isLearned: isLearned ?? this.isLearned,
      reviewCount: reviewCount ?? this.reviewCount,
      lastReviewedAt: lastReviewedAt ?? this.lastReviewedAt,
      firstLearnedAt: firstLearnedAt ?? this.firstLearnedAt,
      masteryLevel: masteryLevel ?? this.masteryLevel,
      reviewHistory: reviewHistory ?? this.reviewHistory,
      // FSRS字段
      stability: stability ?? this.stability,
      difficulty: difficulty ?? this.difficulty,
      nextReviewDate: nextReviewDate ?? this.nextReviewDate,
      fsrsHistory: fsrsHistory ?? this.fsrsHistory,
      retrievability: retrievability ?? this.retrievability,
      // 动觉记忆字段
      kinestheticBonus: kinestheticBonus ?? this.kinestheticBonus,
      motorMemoryHistory: motorMemoryHistory ?? this.motorMemoryHistory,
    );
  }
}

/// 词汇库元数据模型
class VocabularyMetadata {
  final String version;
  final String source;
  final int totalWords;
  final String description;
  final String lastUpdated;
  final List<String> categories;
  final List<String> difficultyLevels;
  final List<String> priorityLevels;
  final List<String> partsOfSpeech;
  final List<VocabularyCategory> defaultCategories;

  VocabularyMetadata({
    required this.version,
    required this.source,
    required this.totalWords,
    required this.description,
    required this.lastUpdated,
    required this.categories,
    required this.difficultyLevels,
    required this.priorityLevels,
    required this.partsOfSpeech,
    this.defaultCategories = const [],
  });

  factory VocabularyMetadata.fromJson(Map<String, dynamic> json) {
    return VocabularyMetadata(
      version: json['version'],
      source: json['source'],
      totalWords: json['totalWords'],
      description: json['description'],
      lastUpdated: json['lastUpdated'],
      categories: List<String>.from(json['categories']),
      difficultyLevels: List<String>.from(json['difficultyLevels']),
      priorityLevels: List<String>.from(json['priorityLevels']),
      partsOfSpeech: List<String>.from(json['partsOfSpeech']),
      defaultCategories:
          (json['defaultCategories'] as List<dynamic>?)
              ?.map((e) => VocabularyCategory.fromJson(e))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'source': source,
      'totalWords': totalWords,
      'description': description,
      'lastUpdated': lastUpdated,
      'categories': categories,
      'difficultyLevels': difficultyLevels,
      'priorityLevels': priorityLevels,
      'partsOfSpeech': partsOfSpeech,
      'defaultCategories': defaultCategories.map((e) => e.toJson()).toList(),
    };
  }
}

/// 单词详情模型
class WordDetails {
  final int id;
  final String definition;
  final int frequency;
  final String difficulty;
  final String category;
  final List<String> alternativeSpellings;
  final String partOfSpeech;
  final List<String> examples;
  final List<String> tags;
  final String priority;
  final String? phonetic; // 音标
  final String? pronunciation; // 发音
  final List<String> synonyms; // 同义词
  final List<String> antonyms; // 反义词
  final String? etymology; // 词源
  final bool isCustom; // 是否为用户自定义单词

  WordDetails({
    required this.id,
    required this.definition,
    required this.frequency,
    required this.difficulty,
    required this.category,
    required this.alternativeSpellings,
    required this.partOfSpeech,
    required this.examples,
    required this.tags,
    required this.priority,
    this.phonetic,
    this.pronunciation,
    this.synonyms = const [],
    this.antonyms = const [],
    this.etymology,
    this.isCustom = false,
  });

  factory WordDetails.fromJson(Map<String, dynamic> json) {
    return WordDetails(
      id: json['id'],
      definition: json['definition'],
      frequency: json['frequency'],
      difficulty: json['difficulty'],
      category: json['category'],
      alternativeSpellings: List<String>.from(json['alternativeSpellings']),
      partOfSpeech: json['partOfSpeech'],
      examples: List<String>.from(json['examples']),
      tags: List<String>.from(json['tags']),
      priority: json['priority'],
      phonetic: json['phonetic'],
      pronunciation: json['pronunciation'],
      synonyms: List<String>.from(json['synonyms'] ?? []),
      antonyms: List<String>.from(json['antonyms'] ?? []),
      etymology: json['etymology'],
      isCustom: json['isCustom'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'definition': definition,
      'frequency': frequency,
      'difficulty': difficulty,
      'category': category,
      'alternativeSpellings': alternativeSpellings,
      'partOfSpeech': partOfSpeech,
      'examples': examples,
      'tags': tags,
      'priority': priority,
      'phonetic': phonetic,
      'pronunciation': pronunciation,
      'synonyms': synonyms,
      'antonyms': antonyms,
      'etymology': etymology,
      'isCustom': isCustom,
    };
  }

  WordDetails copyWith({
    int? id,
    String? definition,
    int? frequency,
    String? difficulty,
    String? category,
    List<String>? alternativeSpellings,
    String? partOfSpeech,
    List<String>? examples,
    List<String>? tags,
    String? priority,
    String? phonetic,
    String? pronunciation,
    List<String>? synonyms,
    List<String>? antonyms,
    String? etymology,
    bool? isCustom,
  }) {
    return WordDetails(
      id: id ?? this.id,
      definition: definition ?? this.definition,
      frequency: frequency ?? this.frequency,
      difficulty: difficulty ?? this.difficulty,
      category: category ?? this.category,
      alternativeSpellings: alternativeSpellings ?? this.alternativeSpellings,
      partOfSpeech: partOfSpeech ?? this.partOfSpeech,
      examples: examples ?? this.examples,
      tags: tags ?? this.tags,
      priority: priority ?? this.priority,
      phonetic: phonetic ?? this.phonetic,
      pronunciation: pronunciation ?? this.pronunciation,
      synonyms: synonyms ?? this.synonyms,
      antonyms: antonyms ?? this.antonyms,
      etymology: etymology ?? this.etymology,
      isCustom: isCustom ?? this.isCustom,
    );
  }
}

/// FSRS复习记录模型
class FSRSReviewRecord {
  final DateTime reviewDate;
  final int rating; // 1-4: Again, Hard, Good, Easy
  final double stability; // 复习时的稳定性
  final double difficulty; // 复习时的难度
  final double retrievability; // 复习时的记忆保持率
  final int responseTime; // 响应时间(毫秒)
  final bool isKinesthetic; // 是否为动觉记忆复习

  const FSRSReviewRecord({
    required this.reviewDate,
    required this.rating,
    required this.stability,
    required this.difficulty,
    required this.retrievability,
    required this.responseTime,
    this.isKinesthetic = false,
  });

  factory FSRSReviewRecord.fromJson(Map<String, dynamic> json) {
    return FSRSReviewRecord(
      reviewDate: DateTime.parse(json['reviewDate']),
      rating: json['rating'],
      stability: (json['stability'] ?? 1.0).toDouble(),
      difficulty: (json['difficulty'] ?? 5.0).toDouble(),
      retrievability: (json['retrievability'] ?? 1.0).toDouble(),
      responseTime: json['responseTime'] ?? 0,
      isKinesthetic: json['isKinesthetic'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'reviewDate': reviewDate.toIso8601String(),
      'rating': rating,
      'stability': stability,
      'difficulty': difficulty,
      'retrievability': retrievability,
      'responseTime': responseTime,
      'isKinesthetic': isKinesthetic,
    };
  }
}

/// 动作记忆数据模型
class MotorMemoryData {
  final DateTime sessionDate;
  final List<String> exerciseSequence; // 执行的动作序列
  final double completionQuality; // 动作完成质量 (0.0 - 1.0)
  final int totalDuration; // 总时长(秒)
  final Map<String, double> exerciseAccuracy; // 各动作准确度
  final double coordinationScore; // 协调性评分
  final bool fullBodyEngagement; // 是否全身参与

  const MotorMemoryData({
    required this.sessionDate,
    required this.exerciseSequence,
    required this.completionQuality,
    required this.totalDuration,
    required this.exerciseAccuracy,
    required this.coordinationScore,
    this.fullBodyEngagement = false,
  });

  factory MotorMemoryData.fromJson(Map<String, dynamic> json) {
    return MotorMemoryData(
      sessionDate: DateTime.parse(json['sessionDate']),
      exerciseSequence: List<String>.from(json['exerciseSequence']),
      completionQuality: (json['completionQuality'] ?? 0.0).toDouble(),
      totalDuration: json['totalDuration'] ?? 0,
      exerciseAccuracy: Map<String, double>.from(
        (json['exerciseAccuracy'] as Map).map(
          (key, value) => MapEntry(key.toString(), (value ?? 0.0).toDouble()),
        ),
      ),
      coordinationScore: (json['coordinationScore'] ?? 0.0).toDouble(),
      fullBodyEngagement: json['fullBodyEngagement'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sessionDate': sessionDate.toIso8601String(),
      'exerciseSequence': exerciseSequence,
      'completionQuality': completionQuality,
      'totalDuration': totalDuration,
      'exerciseAccuracy': exerciseAccuracy,
      'coordinationScore': coordinationScore,
      'fullBodyEngagement': fullBodyEngagement,
    };
  }
}

/// 自定义词库模型
class CustomVocabulary {
  final String id;
  final String name;
  final String description;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<CustomWord> words;
  final Map<String, String> metadata;

  CustomVocabulary({
    required this.id,
    required this.name,
    required this.description,
    required this.createdAt,
    required this.updatedAt,
    required this.words,
    this.metadata = const {},
  });

  factory CustomVocabulary.fromJson(Map<String, dynamic> json) {
    return CustomVocabulary(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
      words: (json['words'] as List<dynamic>)
          .map((e) => CustomWord.fromJson(e))
          .toList(),
      metadata: Map<String, String>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'words': words.map((e) => e.toJson()).toList(),
      'metadata': metadata,
    };
  }

  CustomVocabulary copyWith({
    String? id,
    String? name,
    String? description,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<CustomWord>? words,
    Map<String, String>? metadata,
  }) {
    return CustomVocabulary(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      words: words ?? this.words,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// 自定义单词模型
class CustomWord {
  final String id;
  final String word;
  final String definition;
  final String? phonetic;
  final String partOfSpeech;
  final List<String> examples;
  final List<String> tags;
  final DateTime createdAt;

  CustomWord({
    required this.id,
    required this.word,
    required this.definition,
    this.phonetic,
    required this.partOfSpeech,
    this.examples = const [],
    this.tags = const [],
    required this.createdAt,
  });

  factory CustomWord.fromJson(Map<String, dynamic> json) {
    return CustomWord(
      id: json['id'],
      word: json['word'],
      definition: json['definition'],
      phonetic: json['phonetic'],
      partOfSpeech: json['partOfSpeech'],
      examples: List<String>.from(json['examples'] ?? []),
      tags: List<String>.from(json['tags'] ?? []),
      createdAt: DateTime.parse(json['createdAt']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'word': word,
      'definition': definition,
      'phonetic': phonetic,
      'partOfSpeech': partOfSpeech,
      'examples': examples,
      'tags': tags,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  CustomWord copyWith({
    String? id,
    String? word,
    String? definition,
    String? phonetic,
    String? partOfSpeech,
    List<String>? examples,
    List<String>? tags,
    DateTime? createdAt,
  }) {
    return CustomWord(
      id: id ?? this.id,
      word: word ?? this.word,
      definition: definition ?? this.definition,
      phonetic: phonetic ?? this.phonetic,
      partOfSpeech: partOfSpeech ?? this.partOfSpeech,
      examples: examples ?? this.examples,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
