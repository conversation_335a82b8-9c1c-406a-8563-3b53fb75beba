import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'word_model.dart';

/// 词根服务提供者
final wordRootServiceProvider = Provider<WordRootService>((ref) {
  return WordRootService();
});

/// 词根数据模型
class WordRoot {
  final String root;
  final String meaning;
  final String origin;
  final List<String> examples;

  WordRoot({
    required this.root,
    required this.meaning,
    required this.origin,
    this.examples = const [],
  });

  factory WordRoot.fromJson(Map<String, dynamic> json) {
    return WordRoot(
      root: json['root'] as String,
      meaning: json['meaning'] as String,
      origin: json['origin'] as String,
      examples: (json['examples'] as List<dynamic>?)?.map((e) => e as String).toList() ?? [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'root': root,
      'meaning': meaning,
      'origin': origin,
      'examples': examples,
    };
  }
}

/// 词根服务 - 负责管理词根数据和词根分析
class WordRootService {
  // 词根数据缓存
  Map<String, WordRoot> _rootsCache = {};
  // 单词-词根映射缓存
  final Map<String, List<String>> _wordRootsCache = {};
  // 常见前缀列表
  List<String> _prefixes = [];
  // 常见后缀列表
  List<String> _suffixes = [];
  // 是否已初始化
  bool _isInitialized = false;

  /// 初始化词根服务
  Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // 加载常见前缀
      final prefixesJson = await rootBundle.loadString('assets/data/prefixes.json');
      _prefixes = List<String>.from(json.decode(prefixesJson));

      // 加载常见后缀
      final suffixesJson = await rootBundle.loadString('assets/data/suffixes.json');
      _suffixes = List<String>.from(json.decode(suffixesJson));

      // 加载词根数据
      final rootsJson = await rootBundle.loadString('assets/data/word_roots.json');
      final rootsMap = json.decode(rootsJson) as Map<String, dynamic>;
      
      rootsMap.forEach((key, value) {
        _rootsCache[key] = WordRoot.fromJson(value);
      });

      _isInitialized = true;
      debugPrint('词根服务初始化完成，加载了${_rootsCache.length}个词根');
    } catch (e) {
      debugPrint('词根服务初始化失败: $e');
      // 创建基本的词根数据
      _createBasicRootData();
    }
  }

  /// 创建基本的词根数据（当无法加载完整数据时）
  void _createBasicRootData() {
    // 常见前缀
    _prefixes = [
      'un', 're', 'in', 'im', 'dis', 'en', 'em', 'non', 'de', 'over',
      'mis', 'sub', 'pre', 'inter', 'fore', 'anti', 'auto', 'bi', 'co',
      'ex', 'mid', 'semi', 'under', 'extra', 'post', 'pro', 'tele', 'trans'
    ];

    // 常见后缀
    _suffixes = [
      'able', 'ible', 'al', 'ial', 'ed', 'en', 'er', 'est', 'ful', 'ic',
      'ing', 'ion', 'tion', 'ation', 'ity', 'ty', 'ive', 'ative', 'itive', 'less',
      'ly', 'ment', 'ness', 'ous', 'eous', 'ious', 's', 'es', 'y'
    ];

    // 基本词根数据
    final basicRoots = {
      'act': WordRoot(
        root: 'act',
        meaning: '行动，做',
        origin: '拉丁语',
        examples: ['action', 'activity', 'react', 'activate'],
      ),
      'bio': WordRoot(
        root: 'bio',
        meaning: '生命，生物',
        origin: '希腊语',
        examples: ['biology', 'biography', 'biosphere', 'biodiversity'],
      ),
      'dict': WordRoot(
        root: 'dict',
        meaning: '说，断言',
        origin: '拉丁语',
        examples: ['dictate', 'predict', 'dictionary', 'contradict'],
      ),
      'graph': WordRoot(
        root: 'graph',
        meaning: '写，画，记录',
        origin: '希腊语',
        examples: ['photograph', 'geography', 'biography', 'graphic'],
      ),
      'log': WordRoot(
        root: 'log',
        meaning: '言语，思想，理性',
        origin: '希腊语',
        examples: ['logic', 'dialogue', 'catalog', 'psychology'],
      ),
    };

    _rootsCache = basicRoots;
    _isInitialized = true;
    debugPrint('创建了基本词根数据，共${_rootsCache.length}个词根');
  }

  /// 获取单词的词根
  Future<List<String>> getWordRoots(String word) async {
    await initialize();
    
    if (_wordRootsCache.containsKey(word)) {
      return _wordRootsCache[word]!;
    }

    final normalizedWord = word.toLowerCase();
    final roots = <String>[];

    // 移除前缀
    String stemmedWord = normalizedWord;
    for (final prefix in _prefixes) {
      if (stemmedWord.startsWith(prefix)) {
        stemmedWord = stemmedWord.substring(prefix.length);
        break;
      }
    }

    // 移除后缀
    for (final suffix in _suffixes) {
      if (stemmedWord.endsWith(suffix) && stemmedWord.length > suffix.length) {
        stemmedWord = stemmedWord.substring(0, stemmedWord.length - suffix.length);
        break;
      }
    }

    // 查找词根
    for (final root in _rootsCache.keys) {
      if (stemmedWord.contains(root)) {
        roots.add(root);
      }
    }

    // 如果没有找到词根，使用首字母作为词根
    if (roots.isEmpty && normalizedWord.isNotEmpty) {
      roots.add(normalizedWord[0]);
    }

    _wordRootsCache[word] = roots;
    return roots;
  }

  /// 获取所有词根
  Future<Map<String, WordRoot>> getAllRoots() async {
    await initialize();
    return _rootsCache;
  }

  /// 获取特定词根的信息
  Future<WordRoot?> getRootInfo(String root) async {
    await initialize();
    return _rootsCache[root];
  }

  /// 按词根对单词进行分组
  Future<Map<String, List<MapEntry<String, WordDetails>>>> groupWordsByRoot(
    List<MapEntry<String, WordDetails>> words
  ) async {
    await initialize();
    
    final result = <String, List<MapEntry<String, WordDetails>>>{};
    
    for (final wordEntry in words) {
      final word = wordEntry.key;
      final roots = await getWordRoots(word);
      
      if (roots.isNotEmpty) {
        final mainRoot = roots.first;
        if (!result.containsKey(mainRoot)) {
          result[mainRoot] = [];
        }
        result[mainRoot]!.add(wordEntry);
      } else {
        // 如果没有找到词根，使用"其他"分类
        if (!result.containsKey('other')) {
          result['other'] = [];
        }
        result['other']!.add(wordEntry);
      }
    }
    
    return result;
  }

  /// 获取按字母分组的词根
  Future<Map<String, List<String>>> getRootsByAlphabet() async {
    await initialize();
    
    final result = <String, List<String>>{};
    
    for (final root in _rootsCache.keys) {
      if (root.isNotEmpty) {
        final firstLetter = root[0].toUpperCase();
        if (!result.containsKey(firstLetter)) {
          result[firstLetter] = [];
        }
        result[firstLetter]!.add(root);
      }
    }
    
    // 对每个字母下的词根进行排序
    result.forEach((key, value) {
      value.sort();
    });
    
    return result;
  }
}
