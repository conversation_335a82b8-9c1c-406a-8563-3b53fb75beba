import 'dart:convert';
// Note: dart:math import removed as it was unused
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'word_model.dart';
import 'vocabulary_cache_manager.dart';

/// 词汇管理服务类
class VocabularyService {
  static const String _customVocabulariesKey = 'custom_vocabularies';
  static const String _learningProgressKey = 'learning_progress';
  static const String _categoriesKey = 'vocabulary_categories';
  static const String _selectedWordsKey = 'selected_words';

  /// 加载主词汇库（优化版本，支持缓存）
  Future<Vocabulary> loadVocabulary() async {
    try {
      // 首先尝试从缓存加载
      final cachedVocabulary =
          await VocabularyCacheManager.loadCachedVocabulary();
      if (cachedVocabulary != null) {
        debugPrint('从缓存加载词汇数据');
        return cachedVocabulary;
      }

      // 缓存不存在或过期，从资源文件加载
      debugPrint('从资源文件加载词汇数据');
      Vocabulary vocabulary;

      // 加载优化后的数据
      final String jsonString = await rootBundle.loadString(
        'assets/data/vocabulary.json',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);
      vocabulary = Vocabulary.fromJson(jsonMap);

      // 异步缓存数据（不阻塞返回）
      _cacheVocabularyAsync(vocabulary);

      return vocabulary;
    } catch (e) {
      debugPrint('加载词汇数据失败: $e');
      rethrow;
    }
  }

  /// 异步缓存词汇数据
  void _cacheVocabularyAsync(Vocabulary vocabulary) {
    // 在后台线程中缓存数据
    compute(_cacheVocabularyInBackground, vocabulary).catchError((e) {
      debugPrint('缓存词汇数据失败: $e');
    });
  }

  /// 在后台线程中缓存词汇数据
  static Future<void> _cacheVocabularyInBackground(
    Vocabulary vocabulary,
  ) async {
    await VocabularyCacheManager.cacheVocabularyData(vocabulary);
  }

  /// 获取所有词汇分类
  Future<List<VocabularyCategory>> getCategories() async {
    final prefs = await SharedPreferences.getInstance();
    final vocabulary = await loadVocabulary();

    // 获取默认分类
    List<VocabularyCategory> categories = [
      VocabularyCategory(
        id: 'graduate_exam',
        name: '考研词库',
        description: '5530个考研核心词汇',
        totalWords: vocabulary.metadata.totalWords,
        selectedWords: await _getSelectedWordsCount('graduate_exam'),
        isCustom: false,
      ),
    ];

    // 获取自定义分类
    final customCategoriesJson = prefs.getString(_categoriesKey);
    if (customCategoriesJson != null) {
      final List<dynamic> customCategoriesList = json.decode(
        customCategoriesJson,
      );
      categories.addAll(
        customCategoriesList
            .map((e) => VocabularyCategory.fromJson(e))
            .toList(),
      );
    }

    return categories;
  }

  /// 获取指定分类的单词列表
  Future<List<MapEntry<String, WordDetails>>> getWordsByCategory(
    String categoryId,
  ) async {
    if (categoryId == 'graduate_exam') {
      // 返回考研词库
      final vocabulary = await loadVocabulary();
      return vocabulary.words.entries.toList();
    } else {
      // 返回自定义词库
      final customVocabularies = await getCustomVocabularies();
      final customVocab = customVocabularies.firstWhere(
        (v) => v.id == categoryId,
        orElse: () => throw Exception('Category not found: $categoryId'),
      );

      return customVocab.words.map((word) {
        final wordDetails = WordDetails(
          id: word.id.hashCode,
          definition: word.definition,
          frequency: 0,
          difficulty: 'custom',
          category: 'custom',
          alternativeSpellings: [],
          partOfSpeech: word.partOfSpeech,
          examples: word.examples,
          tags: word.tags,
          priority: 'medium',
          phonetic: word.phonetic,
          isCustom: true,
        );
        return MapEntry(word.word, wordDetails);
      }).toList();
    }
  }

  /// 获取学习进度
  Future<Map<String, WordLearningProgress>> getLearningProgress() async {
    final prefs = await SharedPreferences.getInstance();
    final progressJson = prefs.getString(_learningProgressKey);

    if (progressJson == null) return {};

    final Map<String, dynamic> progressMap = json.decode(progressJson);
    return progressMap.map(
      (key, value) => MapEntry(key, WordLearningProgress.fromJson(value)),
    );
  }

  /// 更新学习进度
  Future<void> updateLearningProgress(
    String wordId,
    WordLearningProgress progress,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final currentProgress = await getLearningProgress();
    currentProgress[wordId] = progress;

    final progressJson = json.encode(
      currentProgress.map((key, value) => MapEntry(key, value.toJson())),
    );
    await prefs.setString(_learningProgressKey, progressJson);
  }

  /// 批量更新学习进度
  Future<void> batchUpdateLearningProgress(
    Map<String, WordLearningProgress> progressMap,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final currentProgress = await getLearningProgress();
    currentProgress.addAll(progressMap);

    final progressJson = json.encode(
      currentProgress.map((key, value) => MapEntry(key, value.toJson())),
    );
    await prefs.setString(_learningProgressKey, progressJson);
  }

  /// 获取选中的单词
  Future<Set<String>> getSelectedWords(String categoryId) async {
    final prefs = await SharedPreferences.getInstance();
    final selectedWordsJson = prefs.getString(
      '${_selectedWordsKey}_$categoryId',
    );

    if (selectedWordsJson == null) return {};

    final List<dynamic> selectedWordsList = json.decode(selectedWordsJson);
    return Set<String>.from(selectedWordsList);
  }

  /// 更新选中的单词
  Future<void> updateSelectedWords(
    String categoryId,
    Set<String> selectedWords,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final selectedWordsJson = json.encode(selectedWords.toList());
    await prefs.setString(
      '${_selectedWordsKey}_$categoryId',
      selectedWordsJson,
    );
  }

  /// 获取选中单词数量
  Future<int> _getSelectedWordsCount(String categoryId) async {
    final selectedWords = await getSelectedWords(categoryId);
    return selectedWords.length;
  }

  /// 获取自定义词库列表
  Future<List<CustomVocabulary>> getCustomVocabularies() async {
    final prefs = await SharedPreferences.getInstance();
    final customVocabsJson = prefs.getString(_customVocabulariesKey);

    if (customVocabsJson == null) return [];

    final List<dynamic> customVocabsList = json.decode(customVocabsJson);
    return customVocabsList.map((e) => CustomVocabulary.fromJson(e)).toList();
  }

  /// 创建自定义词库
  Future<void> createCustomVocabulary(CustomVocabulary vocabulary) async {
    final prefs = await SharedPreferences.getInstance();
    final customVocabs = await getCustomVocabularies();
    customVocabs.add(vocabulary);

    final customVocabsJson = json.encode(
      customVocabs.map((e) => e.toJson()).toList(),
    );
    await prefs.setString(_customVocabulariesKey, customVocabsJson);

    // 同时创建对应的分类
    await _createCategoryForCustomVocabulary(vocabulary);
  }

  /// 为自定义词库创建分类
  Future<void> _createCategoryForCustomVocabulary(
    CustomVocabulary vocabulary,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    final categories = await getCategories();

    final newCategory = VocabularyCategory(
      id: vocabulary.id,
      name: vocabulary.name,
      description: vocabulary.description,
      totalWords: vocabulary.words.length,
      selectedWords: 0,
      isCustom: true,
      createdAt: vocabulary.createdAt,
      wordIds: vocabulary.words.map((w) => w.id).toList(),
    );

    categories.add(newCategory);

    final customCategories = categories.where((c) => c.isCustom).toList();
    final categoriesJson = json.encode(
      customCategories.map((e) => e.toJson()).toList(),
    );
    await prefs.setString(_categoriesKey, categoriesJson);
  }

  /// 更新自定义词库
  Future<void> updateCustomVocabulary(CustomVocabulary vocabulary) async {
    final prefs = await SharedPreferences.getInstance();
    final customVocabs = await getCustomVocabularies();

    final index = customVocabs.indexWhere((v) => v.id == vocabulary.id);
    if (index != -1) {
      customVocabs[index] = vocabulary;

      final customVocabsJson = json.encode(
        customVocabs.map((e) => e.toJson()).toList(),
      );
      await prefs.setString(_customVocabulariesKey, customVocabsJson);
    }
  }

  /// 删除自定义词库
  Future<void> deleteCustomVocabulary(String vocabularyId) async {
    final prefs = await SharedPreferences.getInstance();
    final customVocabs = await getCustomVocabularies();

    customVocabs.removeWhere((v) => v.id == vocabularyId);

    final customVocabsJson = json.encode(
      customVocabs.map((e) => e.toJson()).toList(),
    );
    await prefs.setString(_customVocabulariesKey, customVocabsJson);

    // 同时删除对应的分类和学习进度
    await _deleteCategoryAndProgress(vocabularyId);
  }

  /// 删除分类和相关进度数据
  Future<void> _deleteCategoryAndProgress(String categoryId) async {
    final prefs = await SharedPreferences.getInstance();

    // 删除分类
    final categories = await getCategories();
    final customCategories = categories
        .where((c) => c.isCustom && c.id != categoryId)
        .toList();
    final categoriesJson = json.encode(
      customCategories.map((e) => e.toJson()).toList(),
    );
    await prefs.setString(_categoriesKey, categoriesJson);

    // 删除选中状态
    await prefs.remove('${_selectedWordsKey}_$categoryId');
  }

  /// 搜索单词（性能优化版本）
  Future<List<MapEntry<String, WordDetails>>> searchWords(
    String query, {
    String? categoryId,
    List<String>? difficulties,
    List<String>? partsOfSpeech,
    int limit = 50,
  }) async {
    if (query.isEmpty) {
      return [];
    }

    try {
      // 首先尝试从缓存搜索
      final cachedResults = await VocabularyCacheManager.searchCachedWords(
        query,
        limit: limit,
      );

      if (cachedResults.isNotEmpty) {
        // 应用额外的过滤器
        return _applySearchFilters(
          cachedResults,
          difficulties: difficulties,
          partsOfSpeech: partsOfSpeech,
          categoryId: categoryId,
        );
      }
    } catch (e) {
      debugPrint('缓存搜索失败，使用常规搜索: $e');
    }

    // 缓存搜索失败，使用常规搜索
    List<MapEntry<String, WordDetails>> allWords;

    if (categoryId != null) {
      allWords = await getWordsByCategory(categoryId);
    } else {
      // 搜索所有词库
      final vocabulary = await loadVocabulary();
      allWords = vocabulary.words.entries.toList();

      // 添加自定义词库
      final customVocabs = await getCustomVocabularies();
      for (final customVocab in customVocabs) {
        final customWords = await getWordsByCategory(customVocab.id);
        allWords.addAll(customWords);
      }
    }

    // 应用搜索过滤器
    final results = _applySearchFilters(
      allWords,
      query: query,
      difficulties: difficulties,
      partsOfSpeech: partsOfSpeech,
      categoryId: categoryId,
    );

    return results.take(limit).toList();
  }

  /// 应用搜索过滤器
  List<MapEntry<String, WordDetails>> _applySearchFilters(
    List<MapEntry<String, WordDetails>> words, {
    String? query,
    List<String>? difficulties,
    List<String>? partsOfSpeech,
    String? categoryId,
  }) {
    return words.where((entry) {
      final word = entry.key.toLowerCase();
      final details = entry.value;

      // 文本匹配
      bool matchesQuery = true;
      if (query != null && query.isNotEmpty) {
        final queryLower = query.toLowerCase();
        matchesQuery =
            word.contains(queryLower) ||
            details.definition.toLowerCase().contains(queryLower);
      }

      // 难度过滤
      final matchesDifficulty =
          difficulties == null ||
          difficulties.isEmpty ||
          difficulties.contains(details.difficulty);

      // 词性过滤
      final matchesPartOfSpeech =
          partsOfSpeech == null ||
          partsOfSpeech.isEmpty ||
          partsOfSpeech.contains(details.partOfSpeech);

      // 分类过滤（对于自定义词库）
      bool matchesCategory = true;
      if (categoryId != null && details.isCustom) {
        // 这里可以添加更复杂的分类匹配逻辑
        matchesCategory = true;
      }

      return matchesQuery &&
          matchesDifficulty &&
          matchesPartOfSpeech &&
          matchesCategory;
    }).toList();
  }

  /// 获取学习统计
  Future<Map<String, dynamic>> getLearningStatistics() async {
    final progress = await getLearningProgress();
    final categories = await getCategories();

    int totalWords = 0;
    int learnedWords = 0;
    int selectedWords = 0;
    Map<String, int> categoryStats = {};

    for (final category in categories) {
      totalWords += category.totalWords;
      selectedWords += category.selectedWords;

      final categoryProgress = progress.values.where(
        (p) => p.wordId.startsWith(category.id),
      );
      final categoryLearned = categoryProgress.where((p) => p.isLearned).length;
      learnedWords += categoryLearned;

      categoryStats[category.name] = categoryLearned;
    }

    return {
      'totalWords': totalWords,
      'learnedWords': learnedWords,
      'selectedWords': selectedWords,
      'learningRate': totalWords > 0
          ? (learnedWords / totalWords * 100).round()
          : 0,
      'categoryStats': categoryStats,
    };
  }

  /// 获取推荐学习单词
  Future<List<MapEntry<String, WordDetails>>> getRecommendedWords({
    int limit = 20,
    String? categoryId,
  }) async {
    final progress = await getLearningProgress();
    List<MapEntry<String, WordDetails>> words;

    if (categoryId != null) {
      words = await getWordsByCategory(categoryId);
    } else {
      final vocabulary = await loadVocabulary();
      words = vocabulary.words.entries.toList();
    }

    // 过滤出未学习或需要复习的单词
    final recommendedWords = words.where((entry) {
      final wordProgress = progress[entry.key];
      if (wordProgress == null) return true; // 未学习的单词

      // 需要复习的单词（根据遗忘曲线）
      if (wordProgress.lastReviewedAt != null) {
        final daysSinceReview = DateTime.now()
            .difference(wordProgress.lastReviewedAt!)
            .inDays;
        final reviewInterval = _calculateReviewInterval(
          wordProgress.reviewCount,
        );
        return daysSinceReview >= reviewInterval;
      }

      return !wordProgress.isLearned;
    }).toList();

    // 按优先级和频率排序
    recommendedWords.sort((a, b) {
      final aDetails = a.value;
      final bDetails = b.value;

      // 优先级权重
      final aPriorityWeight = _getPriorityWeight(aDetails.priority);
      final bPriorityWeight = _getPriorityWeight(bDetails.priority);

      if (aPriorityWeight != bPriorityWeight) {
        return bPriorityWeight.compareTo(aPriorityWeight);
      }

      // 频率权重
      return bDetails.frequency.compareTo(aDetails.frequency);
    });

    return recommendedWords.take(limit).toList();
  }

  /// 计算复习间隔（基于艾宾浩斯遗忘曲线）
  int _calculateReviewInterval(int reviewCount) {
    switch (reviewCount) {
      case 0:
        return 1; // 1天后
      case 1:
        return 3; // 3天后
      case 2:
        return 7; // 1周后
      case 3:
        return 15; // 2周后
      case 4:
        return 30; // 1个月后
      default:
        return 60; // 2个月后
    }
  }

  /// 获取优先级权重
  int _getPriorityWeight(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return 3;
      case 'medium':
        return 2;
      case 'low':
        return 1;
      default:
        return 2;
    }
  }

  // Note: _generateId method removed as it was unused
  // String _generateId() {
  //   return DateTime.now().millisecondsSinceEpoch.toString() +
  //       Random().nextInt(1000).toString();
  // }

  /// 检查单词是否在记忆词库中
  Future<bool> isInMemoryVocabulary(String word) async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);

    if (memoryVocabJson == null) return false;

    final Map<String, dynamic> memoryVocab = json.decode(memoryVocabJson);
    return memoryVocab.containsKey(word.toLowerCase());
  }

  /// 添加单词到记忆词库
  Future<void> addToMemoryVocabulary(
    List<String> words,
    List<MapEntry<String, WordDetails>> wordDetails,
  ) async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';

    // 获取现有的记忆词库
    Map<String, dynamic> memoryVocab = {};
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);
    if (memoryVocabJson != null) {
      memoryVocab = json.decode(memoryVocabJson);
    }

    // 添加新单词
    for (int i = 0; i < words.length; i++) {
      final word = words[i].toLowerCase();
      final details = wordDetails[i].value;

      memoryVocab[word] = {
        'word': words[i],
        'definition': details.definition,
        'phonetic': details.phonetic,
        'partOfSpeech': details.partOfSpeech,
        'examples': details.examples,
        'tags': details.tags,
        'addedAt': DateTime.now().toIso8601String(),
        'reviewCount': 0,
        'lastReviewedAt': null,
        'isLearned': false,
      };
    }

    // 保存更新后的记忆词库
    final updatedJson = json.encode(memoryVocab);
    await prefs.setString(memoryVocabularyKey, updatedJson);
  }

  /// 获取已选单词列表（基于学习进度）
  Future<List<String>> getSelectedWordsFromProgress() async {
    final progress = await getLearningProgress();
    return progress.entries
        .where((entry) => entry.value.isSelected)
        .map((entry) => entry.key)
        .toList();
  }

  /// 获取未选单词列表
  Future<List<String>> getUnselectedWords() async {
    final vocabulary = await loadVocabulary();
    final progress = await getLearningProgress();

    return vocabulary.words.keys
        .where((word) => progress[word]?.isSelected != true)
        .toList();
  }

  /// 批量选择单词
  Future<void> batchSelectWords(List<String> words) async {
    final updates = <String, WordLearningProgress>{};
    final currentProgress = await getLearningProgress();

    for (final word in words) {
      final existing = currentProgress[word];
      updates[word] = WordLearningProgress(
        wordId: word,
        isSelected: true,
        isLearned: existing?.isLearned ?? false,
        reviewCount: existing?.reviewCount ?? 0,
        lastReviewedAt: existing?.lastReviewedAt,
        firstLearnedAt: existing?.firstLearnedAt,
        masteryLevel: existing?.masteryLevel ?? 0.0,
        reviewHistory: existing?.reviewHistory ?? [],
      );
    }

    await batchUpdateLearningProgress(updates);
  }

  /// 批量取消选择单词
  Future<void> batchUnselectWords(List<String> words) async {
    final updates = <String, WordLearningProgress>{};
    final currentProgress = await getLearningProgress();

    for (final word in words) {
      final existing = currentProgress[word];
      updates[word] = WordLearningProgress(
        wordId: word,
        isSelected: false,
        isLearned: existing?.isLearned ?? false,
        reviewCount: existing?.reviewCount ?? 0,
        lastReviewedAt: existing?.lastReviewedAt,
        firstLearnedAt: existing?.firstLearnedAt,
        masteryLevel: existing?.masteryLevel ?? 0.0,
        reviewHistory: existing?.reviewHistory ?? [],
      );
    }

    await batchUpdateLearningProgress(updates);
  }

  /// 获取选择状态统计
  Future<Map<String, int>> getSelectionStatistics() async {
    final vocabulary = await loadVocabulary();
    final progress = await getLearningProgress();

    int selectedCount = 0;
    int unselectedCount = 0;

    for (final word in vocabulary.words.keys) {
      if (progress[word]?.isSelected == true) {
        selectedCount++;
      } else {
        unselectedCount++;
      }
    }

    return {
      'selected': selectedCount,
      'unselected': unselectedCount,
      'total': vocabulary.words.length,
    };
  }

  /// 获取记忆词库
  Future<Map<String, dynamic>> getMemoryVocabulary() async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);

    if (memoryVocabJson == null) return {};

    return json.decode(memoryVocabJson);
  }

  /// 从记忆词库中移除单词
  Future<void> removeFromMemoryVocabulary(String word) async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);

    if (memoryVocabJson == null) return;

    final Map<String, dynamic> memoryVocab = json.decode(memoryVocabJson);
    memoryVocab.remove(word.toLowerCase());

    final updatedJson = json.encode(memoryVocab);
    await prefs.setString(memoryVocabularyKey, updatedJson);
  }

  /// 批量添加单词到记忆词库
  Future<void> batchAddToMemoryVocabulary(List<String> words) async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';

    // 获取现有的记忆词库
    Map<String, dynamic> memoryVocab = {};
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);
    if (memoryVocabJson != null) {
      memoryVocab = json.decode(memoryVocabJson);
    }

    // 批量添加新单词
    for (final word in words) {
      final wordKey = word.toLowerCase();
      if (!memoryVocab.containsKey(wordKey)) {
        memoryVocab[wordKey] = {
          'word': word,
          'definition': '', // 可以后续通过词典服务获取
          'phonetic': '',
          'partOfSpeech': '',
          'examples': [],
          'tags': [],
          'addedAt': DateTime.now().toIso8601String(),
          'reviewCount': 0,
          'lastReviewedAt': null,
          'isLearned': false,
        };
      }
    }

    // 保存更新后的记忆词库
    final updatedJson = json.encode(memoryVocab);
    await prefs.setString(memoryVocabularyKey, updatedJson);
  }

  /// 批量从记忆词库中移除单词
  Future<void> batchRemoveFromMemoryVocabulary(List<String> words) async {
    final prefs = await SharedPreferences.getInstance();
    const memoryVocabularyKey = 'memory_vocabulary';
    final memoryVocabJson = prefs.getString(memoryVocabularyKey);

    if (memoryVocabJson == null) return;

    final Map<String, dynamic> memoryVocab = json.decode(memoryVocabJson);

    for (final word in words) {
      memoryVocab.remove(word.toLowerCase());
    }

    final updatedJson = json.encode(memoryVocab);
    await prefs.setString(memoryVocabularyKey, updatedJson);
  }

  /// 获取记忆词库中的单词列表
  Future<List<String>> getMemoryVocabularyWords() async {
    final memoryVocab = await getMemoryVocabulary();
    return memoryVocab.keys.toList();
  }

  /// 搜索记忆词库中的单词
  Future<List<String>> searchMemoryVocabulary(String query) async {
    final memoryVocab = await getMemoryVocabulary();
    if (query.isEmpty) return memoryVocab.keys.toList();

    final queryLower = query.toLowerCase();
    return memoryVocab.keys.where((word) {
      final wordData = memoryVocab[word] as Map<String, dynamic>;
      final wordText = (wordData['word'] as String).toLowerCase();
      final definition = (wordData['definition'] as String).toLowerCase();
      return wordText.contains(queryLower) || definition.contains(queryLower);
    }).toList();
  }
}

// Riverpod Providers
final vocabularyServiceProvider = Provider<VocabularyService>((ref) {
  return VocabularyService();
});

final vocabularyProvider = FutureProvider<Vocabulary>((ref) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return vocabularyService.loadVocabulary();
});

final vocabularyCategoriesProvider = FutureProvider<List<VocabularyCategory>>((
  ref,
) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return vocabularyService.getCategories();
});

final learningProgressProvider =
    FutureProvider<Map<String, WordLearningProgress>>((ref) {
      final vocabularyService = ref.watch(vocabularyServiceProvider);
      return vocabularyService.getLearningProgress();
    });

final learningStatisticsProvider = FutureProvider<Map<String, dynamic>>((ref) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return vocabularyService.getLearningStatistics();
});

// 分类单词列表 Provider
final categoryWordsProvider =
    FutureProvider.family<List<MapEntry<String, WordDetails>>, String>((
      ref,
      categoryId,
    ) {
      final vocabularyService = ref.watch(vocabularyServiceProvider);
      return vocabularyService.getWordsByCategory(categoryId);
    });

// 选中单词 Provider
final selectedWordsProvider = FutureProvider.family<Set<String>, String>((
  ref,
  categoryId,
) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return vocabularyService.getSelectedWords(categoryId);
});

// 推荐单词 Provider
final recommendedWordsProvider =
    FutureProvider.family<
      List<MapEntry<String, WordDetails>>,
      Map<String, dynamic>
    >((ref, params) {
      final vocabularyService = ref.watch(vocabularyServiceProvider);
      return vocabularyService.getRecommendedWords(
        limit: params['limit'] ?? 20,
        categoryId: params['categoryId'],
      );
    });

// 记忆词库 Provider
final memoryVocabularyProvider = FutureProvider<Map<String, dynamic>>((ref) {
  final vocabularyService = ref.watch(vocabularyServiceProvider);
  return vocabularyService.getMemoryVocabulary();
});
