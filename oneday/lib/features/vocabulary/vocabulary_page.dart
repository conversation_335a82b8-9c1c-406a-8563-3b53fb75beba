import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:oneday/features/vocabulary/vocabulary_service.dart';
import 'package:oneday/features/vocabulary/word_model.dart';

class VocabularyPage extends ConsumerStatefulWidget {
  const VocabularyPage({super.key});

  @override
  ConsumerState<VocabularyPage> createState() => _VocabularyPageState();
}

class _VocabularyPageState extends ConsumerState<VocabularyPage> {
  String _searchQuery = '';
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final vocabularyAsyncValue = ref.watch(vocabularyProvider);

    return vocabularyAsyncValue.when(
      loading: () => Scaffold(
        appBar: AppBar(title: const Text('Vocabulary')),
        body: const Center(child: CircularProgressIndicator()),
      ),
      error: (error, stack) => Scaffold(
        appBar: AppBar(title: const Text('Vocabulary')),
        body: Center(child: Text('Error: $error')),
      ),
      data: (vocabulary) {
        // First, get all words and sort them correctly (case-insensitive)
        final allWords = vocabulary.words.entries.toList()
          ..sort((a, b) => a.key.toLowerCase().compareTo(b.key.toLowerCase()));

        // Then, filter the sorted list based on the search query
        final filteredWords = _searchQuery.isEmpty
            ? allWords
            : allWords
                  .where(
                    (entry) => entry.key.toLowerCase().contains(
                      _searchQuery.toLowerCase(),
                    ),
                  )
                  .toList();

        return Scaffold(
          appBar: AppBar(
            backgroundColor: Theme.of(context).colorScheme.surface,
            elevation: 0,
            title: Text(
              'Vocabulary (${filteredWords.length}/${vocabulary.metadata.totalWords})',
            ),
            centerTitle: true,
            bottom: PreferredSize(
              preferredSize: const Size.fromHeight(kToolbarHeight),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText:
                        'Search ${vocabulary.metadata.totalWords} words...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    filled: true,
                    fillColor: Theme.of(context).scaffoldBackgroundColor,
                    contentPadding: EdgeInsets.zero,
                    suffixIcon: _searchQuery.isNotEmpty
                        ? IconButton(
                            icon: const Icon(Icons.clear),
                            onPressed: () {
                              _searchController.clear();
                              setState(() {
                                _searchQuery = '';
                              });
                            },
                          )
                        : null,
                  ),
                  onChanged: (value) {
                    setState(() {
                      _searchQuery = value;
                    });
                  },
                ),
              ),
            ),
          ),
          body: filteredWords.isEmpty
              ? const Center(child: Text('No words found.'))
              : ListView.separated(
                  itemCount: filteredWords.length,
                  separatorBuilder: (context, index) => Divider(
                    height: 1,
                    thickness: 1,
                    indent: 16,
                    endIndent: 16,
                    color: Theme.of(context).colorScheme.surfaceContainerHighest
                        .withValues(alpha: 0.5),
                  ),
                  itemBuilder: (context, index) {
                    final wordEntry = filteredWords[index];
                    final word = wordEntry.key;
                    final details = wordEntry.value;

                    return ListTile(
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 8,
                      ),
                      title: Text(
                        word,
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                        ),
                      ),
                      subtitle: Text(
                        details.definition,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                      onTap: () {
                        showModalBottomSheet(
                          context: context,
                          isScrollControlled: true,
                          builder: (context) {
                            return WordDetailsSheet(
                              word: word,
                              details: details,
                            );
                          },
                        );
                      },
                    );
                  },
                ),
        );
      },
    );
  }
}

class WordDetailsSheet extends StatelessWidget {
  const WordDetailsSheet({
    super.key,
    required this.word,
    required this.details,
  });

  final String word;
  final WordDetails details;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(word, style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 8),
          Text(
            details.definition,
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const Divider(height: 32),
          _buildDetailRow('Part of Speech:', details.partOfSpeech),
          _buildDetailRow('Category:', details.category),
          _buildDetailRow('Difficulty:', details.difficulty),
          _buildDetailRow('Frequency:', details.frequency.toString()),
          _buildDetailRow('Priority:', details.priority),
          if (details.tags.isNotEmpty)
            _buildDetailRow('Tags:', details.tags.join(', ')),
          const SizedBox(height: 24), // Add some padding at the bottom
        ],
      ),
    );
  }

  Widget _buildDetailRow(String title, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
          const SizedBox(width: 8),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
