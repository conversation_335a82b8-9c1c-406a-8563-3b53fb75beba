import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'vocabulary_service.dart';
import 'word_model.dart';

/// 考研词库管理页面
///
/// 提供单词选择状态管理、已选/未选分类显示、搜索和批量操作功能
class GraduateVocabularyManagerPage extends ConsumerStatefulWidget {
  const GraduateVocabularyManagerPage({super.key});

  @override
  ConsumerState<GraduateVocabularyManagerPage> createState() =>
      _GraduateVocabularyManagerPageState();
}

class _GraduateVocabularyManagerPageState
    extends ConsumerState<GraduateVocabularyManagerPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // 状态变量
  String _searchQuery = '';
  bool _isLoading = true;
  bool _isSelectionMode = false;
  final Set<String> _selectedWords = {};

  // 数据
  List<MapEntry<String, WordDetails>> _allWords = [];
  Map<String, WordLearningProgress> _learningProgress = {};

  // 服务
  late VocabularyService _vocabularyService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _vocabularyService = VocabularyService();
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // 加载考研词汇
      final vocabulary = await _vocabularyService.loadVocabulary();
      _allWords = vocabulary.words.entries.toList()
        ..sort((a, b) => a.key.toLowerCase().compareTo(b.key.toLowerCase()));

      // 加载学习进度
      _learningProgress = await _vocabularyService.getLearningProgress();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载数据失败: $e')));
      }
    }
  }

  /// 获取已选单词
  List<MapEntry<String, WordDetails>> get _selectedWordsList {
    return _allWords.where((entry) {
      final progress = _learningProgress[entry.key];
      return progress?.isSelected == true;
    }).toList();
  }

  /// 获取未选单词
  List<MapEntry<String, WordDetails>> get _unselectedWordsList {
    return _allWords.where((entry) {
      final progress = _learningProgress[entry.key];
      return progress?.isSelected != true;
    }).toList();
  }

  /// 根据搜索条件过滤单词
  List<MapEntry<String, WordDetails>> _filterWords(
    List<MapEntry<String, WordDetails>> words,
  ) {
    if (_searchQuery.isEmpty) return words;

    return words.where((entry) {
      final word = entry.key.toLowerCase();
      final definition = entry.value.definition.toLowerCase();
      final query = _searchQuery.toLowerCase();

      return word.contains(query) || definition.contains(query);
    }).toList();
  }

  /// 切换单词选择状态
  Future<void> _toggleWordSelection(String word) async {
    final currentProgress = _learningProgress[word];
    final newProgress = WordLearningProgress(
      wordId: word,
      isSelected: !(currentProgress?.isSelected ?? false),
      isLearned: currentProgress?.isLearned ?? false,
      reviewCount: currentProgress?.reviewCount ?? 0,
      lastReviewedAt: currentProgress?.lastReviewedAt,
      firstLearnedAt: currentProgress?.firstLearnedAt,
      masteryLevel: currentProgress?.masteryLevel ?? 0.0,
      reviewHistory: currentProgress?.reviewHistory ?? [],
    );

    await _vocabularyService.updateLearningProgress(word, newProgress);

    setState(() {
      _learningProgress[word] = newProgress;
    });
  }

  /// 批量更新单词选择状态
  Future<void> _batchUpdateSelection(
    List<String> words,
    bool isSelected,
  ) async {
    try {
      if (isSelected) {
        await _vocabularyService.batchSelectWords(words);
      } else {
        await _vocabularyService.batchUnselectWords(words);
      }

      // 重新加载进度数据
      _learningProgress = await _vocabularyService.getLearningProgress();

      setState(() {
        _selectedWords.clear();
        _isSelectionMode = false;
      });

      // 显示成功提示
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isSelected
                  ? '已添加 ${words.length} 个单词到学习列表'
                  : '已从学习列表移除 ${words.length} 个单词',
            ),
            backgroundColor: const Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }

  /// 全选当前标签页的单词
  void _selectAllInCurrentTab() {
    final currentTabWords = _tabController.index == 0
        ? _filterWords(_selectedWordsList)
        : _filterWords(_unselectedWordsList);

    setState(() {
      for (final wordEntry in currentTabWords) {
        _selectedWords.add(wordEntry.key);
      }
    });
  }

  /// 清空当前选择 (暂未使用)
  /*
  void _clearSelection() {
    setState(() {
      _selectedWords.clear();
    });
  }
  */

  /// 显示统计信息
  void _showStatistics() async {
    try {
      final stats = await _vocabularyService.getSelectionStatistics();

      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text(
              '词库统计',
              style: TextStyle(
                color: Color(0xFF37352F),
                fontWeight: FontWeight.w600,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _StatisticRow(
                  label: '总单词数',
                  value: '${stats['total']}',
                  color: const Color(0xFF37352F),
                ),
                const SizedBox(height: 12),
                _StatisticRow(
                  label: '已选单词',
                  value: '${stats['selected']}',
                  color: const Color(0xFF2F76DA),
                ),
                const SizedBox(height: 12),
                _StatisticRow(
                  label: '未选单词',
                  value: '${stats['unselected']}',
                  color: const Color(0xFF9B9A97),
                ),
                const SizedBox(height: 12),
                _StatisticRow(
                  label: '选择进度',
                  value:
                      '${((stats['selected']! / stats['total']!) * 100).toStringAsFixed(1)}%',
                  color: const Color(0xFF0F7B6C),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text(
                  '确定',
                  style: TextStyle(color: Color(0xFF2F76DA)),
                ),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('获取统计信息失败: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildMainContent(),
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBottomBar() : null,
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        onPressed: () => context.pop(),
      ),
      title: const Text(
        '考研词库管理',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 18,
          fontWeight: FontWeight.w600,
        ),
      ),
      actions: [
        if (_isSelectionMode) ...[
          IconButton(
            icon: const Icon(Icons.select_all, color: Color(0xFF2F76DA)),
            onPressed: _selectAllInCurrentTab,
            tooltip: '全选',
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _selectedWords.clear();
                _isSelectionMode = false;
              });
            },
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
        ] else ...[
          IconButton(
            icon: const Icon(
              Icons.analytics_outlined,
              color: Color(0xFF787774),
            ),
            onPressed: _showStatistics,
            tooltip: '统计信息',
          ),
          IconButton(
            icon: const Icon(Icons.checklist, color: Color(0xFF2F76DA)),
            onPressed: () {
              setState(() {
                _isSelectionMode = true;
              });
            },
            tooltip: '批量选择',
          ),
        ],
      ],
      bottom: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF2F76DA),
        unselectedLabelColor: const Color(0xFF9B9A97),
        indicatorColor: const Color(0xFF2F76DA),
        tabs: [
          Tab(text: '已选 (${_selectedWordsList.length})'),
          Tab(text: '未选 (${_unselectedWordsList.length})'),
        ],
      ),
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(
        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2F76DA)),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return Column(
      children: [
        _buildSearchBar(),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildWordsList(_filterWords(_selectedWordsList), true),
              _buildWordsList(_filterWords(_unselectedWordsList), false),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索单词或释义...',
          hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
          prefixIcon: const Icon(Icons.search, color: Color(0xFF9B9A97)),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear, color: Color(0xFF9B9A97)),
                  onPressed: () {
                    _searchController.clear();
                    setState(() => _searchQuery = '');
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(8),
            borderSide: const BorderSide(color: Color(0xFF2F76DA)),
          ),
          filled: true,
          fillColor: const Color(0xFFF7F6F3),
        ),
        onChanged: (value) {
          setState(() => _searchQuery = value);
        },
      ),
    );
  }

  /// 构建单词列表
  Widget _buildWordsList(
    List<MapEntry<String, WordDetails>> words,
    bool isSelectedTab,
  ) {
    if (words.isEmpty) {
      return _buildEmptyView(isSelectedTab);
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: words.length,
      itemBuilder: (context, index) {
        final wordEntry = words[index];
        final word = wordEntry.key;
        final details = wordEntry.value;
        final progress = _learningProgress[word];
        final isSelected = progress?.isSelected == true;

        return _WordListItem(
          word: word,
          details: details,
          isSelected: isSelected,
          isInSelectionMode: _isSelectionMode,
          isChecked: _selectedWords.contains(word),
          onTap: () => _handleWordTap(word),
          onSelectionChanged: (checked) =>
              _handleSelectionChanged(word, checked),
        );
      },
    );
  }

  /// 构建空状态视图
  Widget _buildEmptyView(bool isSelectedTab) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            isSelectedTab
                ? Icons.bookmark_border
                : Icons.library_books_outlined,
            size: 64,
            color: const Color(0xFF9B9A97),
          ),
          const SizedBox(height: 16),
          Text(
            isSelectedTab ? '暂无已选单词' : '暂无未选单词',
            style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 16),
          ),
          const SizedBox(height: 8),
          Text(
            isSelectedTab ? '点击单词可以添加到学习列表' : '所有单词都已选择',
            style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 处理单词点击
  void _handleWordTap(String word) {
    if (_isSelectionMode) {
      _handleSelectionChanged(word, !_selectedWords.contains(word));
    } else {
      _toggleWordSelection(word);
    }
  }

  /// 处理选择状态变化
  void _handleSelectionChanged(String word, bool checked) {
    setState(() {
      if (checked) {
        _selectedWords.add(word);
      } else {
        _selectedWords.remove(word);
      }
    });
  }

  /// 构建选择模式底部栏
  Widget _buildSelectionBottomBar() {
    final selectedCount = _selectedWords.length;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(
        color: Colors.white,
        border: Border(top: BorderSide(color: Color(0xFFE3E2E0), width: 1)),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Text(
              '已选择 $selectedCount 个单词',
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const Spacer(),
            if (selectedCount > 0) ...[
              TextButton(
                onPressed: () =>
                    _batchUpdateSelection(_selectedWords.toList(), false),
                child: const Text(
                  '取消选择',
                  style: TextStyle(color: Color(0xFF9B9A97)),
                ),
              ),
              const SizedBox(width: 8),
              ElevatedButton(
                onPressed: () =>
                    _batchUpdateSelection(_selectedWords.toList(), true),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F76DA),
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('添加到学习'),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

/// 单词列表项组件
class _WordListItem extends StatelessWidget {
  final String word;
  final WordDetails details;
  final bool isSelected;
  final bool isInSelectionMode;
  final bool isChecked;
  final VoidCallback onTap;
  final ValueChanged<bool> onSelectionChanged;

  const _WordListItem({
    required this.word,
    required this.details,
    required this.isSelected,
    required this.isInSelectionMode,
    required this.isChecked,
    required this.onTap,
    required this.onSelectionChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isSelected ? const Color(0xFF2F76DA) : const Color(0xFFE3E2E0),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: isInSelectionMode
            ? Checkbox(
                value: isChecked,
                onChanged: (value) => onSelectionChanged(value ?? false),
                activeColor: const Color(0xFF2F76DA),
              )
            : Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF2F76DA)
                      : const Color(0xFFF7F6F3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  isSelected ? Icons.bookmark : Icons.bookmark_border,
                  color: isSelected ? Colors.white : const Color(0xFF9B9A97),
                  size: 20,
                ),
              ),
        title: Text(
          word,
          style: TextStyle(
            color: const Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (details.phonetic?.isNotEmpty == true) ...[
              const SizedBox(height: 4),
              Text(
                details.phonetic!,
                style: const TextStyle(
                  color: Color(0xFF2F76DA),
                  fontSize: 14,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              details.definition,
              style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            if (details.partOfSpeech.isNotEmpty) ...[
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: const Color(0xFFF7F6F3),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  details.partOfSpeech,
                  style: const TextStyle(
                    color: Color(0xFF9B9A97),
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ],
        ),
        trailing: isInSelectionMode
            ? null
            : Icon(
                isSelected
                    ? Icons.remove_circle_outline
                    : Icons.add_circle_outline,
                color: isSelected
                    ? const Color(0xFFE03E3E)
                    : const Color(0xFF2F76DA),
              ),
        onTap: onTap,
      ),
    );
  }
}

/// 统计信息行组件
class _StatisticRow extends StatelessWidget {
  final String label;
  final String value;
  final Color color;

  const _StatisticRow({
    required this.label,
    required this.value,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
        ),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
