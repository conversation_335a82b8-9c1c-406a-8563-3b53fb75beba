import 'dart:math' as math;
import 'word_model.dart';

/// FSRS v4.5算法实现 - 专为动觉记忆优化
///
/// 基于Free Spaced Repetition Scheduler算法，结合动觉记忆特性
/// 提供个性化的复习间隔计算和记忆保持率预测
class FSRSAlgorithm {
  // FSRS v4.5 默认参数 (17参数模型)
  static const List<double> _defaultParameters = [
    0.4072, // w[0] - 初始稳定性
    1.1829, // w[1] - 初始难度
    3.1262, // w[2] - 稳定性增长因子
    15.4722, // w[3] - 难度衰减因子
    7.2102, // w[4] - 稳定性衰减因子
    0.5316, // w[5] - 难度增长因子
    1.0651, // w[6] - 稳定性增长指数
    0.0234, // w[7] - 难度增长指数
    1.616, // w[8] - 遗忘指数
    0.1544, // w[9] - 最小稳定性
    1.0824, // w[10] - 稳定性修正因子
    1.9813, // w[11] - 难度修正因子
    0.0953, // w[12] - 稳定性噪声
    0.2975, // w[13] - 难度噪声
    2.2042, // w[14] - 稳定性上限
    0.2407, // w[15] - 难度上限
    2.9466, // w[16] - 稳定性下限
  ];

  // 动觉记忆增强参数
  static const double _kinestheticMemoryBonus = 1.3; // 动觉记忆增强系数
  // static const double _motorCoordinationFactor = 1.2; // 动作协调性因子 (暂未使用)
  static const double _multiSensoryBonus = 1.15; // 多感官记忆奖励

  final List<double> _parameters;
  final String userId;

  FSRSAlgorithm({required this.userId, List<double>? customParameters})
    : _parameters = customParameters ?? List.from(_defaultParameters);

  /// 计算初始学习后的稳定性和难度
  FSRSResult calculateInitialLearning({
    required int rating, // 1-4: Again, Hard, Good, Easy
    bool isKinesthetic = false,
    double kinestheticQuality = 1.0,
  }) {
    // 基础稳定性计算
    double stability = _parameters[0];

    // 根据评级调整稳定性
    switch (rating) {
      case 1: // Again
        stability *= 0.4;
        break;
      case 2: // Hard
        stability *= 0.6;
        break;
      case 3: // Good
        stability *= 1.0;
        break;
      case 4: // Easy
        stability *= 2.5;
        break;
    }

    // 动觉记忆增强
    if (isKinesthetic) {
      stability *= _kinestheticMemoryBonus * kinestheticQuality;
    }

    // 初始难度
    double difficulty = _parameters[1];
    if (rating == 1) {
      difficulty = math.min(difficulty + 2.0, 10.0);
    } else if (rating == 4) {
      difficulty = math.max(difficulty - 1.0, 1.0);
    }

    return FSRSResult(
      stability: math.max(stability, _parameters[9]), // 最小稳定性
      difficulty: difficulty.clamp(1.0, 10.0),
      retrievability: 1.0,
    );
  }

  /// 计算复习后的新参数
  FSRSResult calculateReview({
    required double currentStability,
    required double currentDifficulty,
    required double currentRetrievability,
    required int rating,
    required int daysSinceLastReview,
    bool isKinesthetic = false,
    double kinestheticQuality = 1.0,
    MotorMemoryData? motorData,
  }) {
    // 计算新的稳定性
    double newStability = _calculateNewStability(
      currentStability: currentStability,
      currentDifficulty: currentDifficulty,
      currentRetrievability: currentRetrievability,
      rating: rating,
      isKinesthetic: isKinesthetic,
      kinestheticQuality: kinestheticQuality,
      motorData: motorData,
    );

    // 计算新的难度
    double newDifficulty = _calculateNewDifficulty(
      currentDifficulty: currentDifficulty,
      rating: rating,
      isKinesthetic: isKinesthetic,
      motorData: motorData,
    );

    return FSRSResult(
      stability: newStability,
      difficulty: newDifficulty,
      retrievability: 1.0, // 复习后重置为1.0
    );
  }

  /// 计算记忆保持率
  double calculateRetrievability({
    required double stability,
    required int daysSinceReview,
  }) {
    if (daysSinceReview <= 0) return 1.0;

    // FSRS遗忘曲线: R = exp(-t/S)
    return math.exp(-daysSinceReview / stability);
  }

  /// 计算下次复习的最佳时间间隔
  int calculateOptimalInterval({
    required double stability,
    double targetRetrievability = 0.9, // 目标记忆保持率
  }) {
    // 计算达到目标保持率所需的天数
    if (targetRetrievability >= 1.0) return 1;

    double interval = -stability * math.log(targetRetrievability);
    return math.max(1, interval.round());
  }

  /// 预测未来的记忆保持率
  List<double> predictFutureRetrievability({
    required double stability,
    required int days,
  }) {
    List<double> predictions = [];
    for (int day = 1; day <= days; day++) {
      predictions.add(
        calculateRetrievability(stability: stability, daysSinceReview: day),
      );
    }
    return predictions;
  }

  /// 计算新稳定性（私有方法）
  double _calculateNewStability({
    required double currentStability,
    required double currentDifficulty,
    required double currentRetrievability,
    required int rating,
    required bool isKinesthetic,
    required double kinestheticQuality,
    MotorMemoryData? motorData,
  }) {
    double newStability;

    if (rating == 1) {
      // Again - 稳定性下降
      newStability =
          currentStability *
          _parameters[4] *
          math.pow(currentDifficulty, _parameters[7]);
    } else {
      // Hard, Good, Easy - 稳定性增长
      double growthFactor =
          _parameters[2] *
          math.pow(currentDifficulty, _parameters[6]) *
          math.pow(currentRetrievability, _parameters[8]);

      newStability = currentStability * growthFactor;

      // 根据评级调整
      if (rating == 2) {
        // Hard
        newStability *= 0.85;
      } else if (rating == 4) {
        // Easy
        newStability *= 1.3;
      }
    }

    // 动觉记忆增强
    if (isKinesthetic) {
      double kinestheticMultiplier =
          _kinestheticMemoryBonus * kinestheticQuality;

      // 考虑动作记忆数据
      if (motorData != null) {
        // 协调性奖励
        kinestheticMultiplier *= (1.0 + motorData.coordinationScore * 0.2);

        // 全身参与奖励
        if (motorData.fullBodyEngagement) {
          kinestheticMultiplier *= _multiSensoryBonus;
        }

        // 动作完成质量奖励
        kinestheticMultiplier *= (1.0 + motorData.completionQuality * 0.15);
      }

      newStability *= kinestheticMultiplier;
    }

    // 应用稳定性限制
    return newStability.clamp(_parameters[9], _parameters[14]);
  }

  /// 计算新难度（私有方法）
  double _calculateNewDifficulty({
    required double currentDifficulty,
    required int rating,
    required bool isKinesthetic,
    MotorMemoryData? motorData,
  }) {
    double difficultyChange = 0.0;

    switch (rating) {
      case 1: // Again
        difficultyChange = _parameters[5];
        break;
      case 2: // Hard
        difficultyChange = _parameters[5] * 0.5;
        break;
      case 3: // Good
        difficultyChange = -_parameters[5] * 0.3;
        break;
      case 4: // Easy
        difficultyChange = -_parameters[5];
        break;
    }

    // 动觉记忆降低难度
    if (isKinesthetic && motorData != null) {
      // 高质量的动作记忆降低感知难度
      double qualityBonus = motorData.completionQuality * 0.5;
      difficultyChange -= qualityBonus;

      // 协调性好的动作进一步降低难度
      if (motorData.coordinationScore > 0.8) {
        difficultyChange -= 0.3;
      }
    }

    double newDifficulty = currentDifficulty + difficultyChange;
    return newDifficulty.clamp(1.0, 10.0);
  }

  /// 个性化参数优化
  void optimizeParameters(List<FSRSReviewRecord> reviewHistory) {
    // 基于用户历史数据优化参数
    // 这里可以实现机器学习算法来优化参数
    // 暂时使用简单的启发式调整

    if (reviewHistory.length < 10) return; // 数据不足

    // 分析动觉记忆的效果
    var kinestheticReviews = reviewHistory
        .where((r) => r.isKinesthetic)
        .toList();
    if (kinestheticReviews.isNotEmpty) {
      double avgKinestheticRating =
          kinestheticReviews.map((r) => r.rating).reduce((a, b) => a + b) /
          kinestheticReviews.length;

      // 如果动觉记忆效果好，增强相关参数
      if (avgKinestheticRating > 3.0) {
        // 可以调整参数以反映个人的动觉记忆优势
      }
    }
  }
}

/// FSRS计算结果
class FSRSResult {
  final double stability;
  final double difficulty;
  final double retrievability;

  const FSRSResult({
    required this.stability,
    required this.difficulty,
    required this.retrievability,
  });

  @override
  String toString() {
    return 'FSRSResult(stability: ${stability.toStringAsFixed(2)}, '
        'difficulty: ${difficulty.toStringAsFixed(2)}, '
        'retrievability: ${retrievability.toStringAsFixed(3)})';
  }
}
