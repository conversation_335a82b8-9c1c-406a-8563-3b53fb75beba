import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'vocabulary_service.dart';
import 'word_model.dart';

/// 创建自定义词库页面
class CreateVocabularyPage extends ConsumerStatefulWidget {
  const CreateVocabularyPage({super.key});

  @override
  ConsumerState<CreateVocabularyPage> createState() => _CreateVocabularyPageState();
}

class _CreateVocabularyPageState extends ConsumerState<CreateVocabularyPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _wordController = TextEditingController();
  final _definitionController = TextEditingController();
  final _phoneticController = TextEditingController();
  
  String _selectedPartOfSpeech = 'noun';
  final List<CustomWord> _words = [];
  bool _isLoading = false;

  final List<String> _partsOfSpeech = [
    'noun', 'verb', 'adjective', 'adverb', 
    'preposition', 'conjunction', 'pronoun', 'article'
  ];

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _wordController.dispose();
    _definitionController.dispose();
    _phoneticController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '创建词库',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isLoading ? null : _saveVocabulary,
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text(
                    '保存',
                    style: TextStyle(
                      color: Color(0xFF2F76DA),
                      fontWeight: FontWeight.w600,
                    ),
                  ),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 词库信息
                    _buildSectionTitle('词库信息'),
                    const SizedBox(height: 16),
                    
                    _buildTextField(
                      controller: _nameController,
                      label: '词库名称',
                      hint: '请输入词库名称',
                      validator: (value) {
                        if (value == null || value.trim().isEmpty) {
                          return '请输入词库名称';
                        }
                        return null;
                      },
                    ),
                    
                    const SizedBox(height: 16),
                    
                    _buildTextField(
                      controller: _descriptionController,
                      label: '词库描述',
                      hint: '请输入词库描述（可选）',
                      maxLines: 3,
                    ),
                    
                    const SizedBox(height: 32),
                    
                    // 添加单词
                    Row(
                      children: [
                        Expanded(child: _buildSectionTitle('添加单词')),
                        Text(
                          '已添加 ${_words.length} 个',
                          style: const TextStyle(
                            color: Color(0xFF787774),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    
                    _buildAddWordForm(),
                    
                    const SizedBox(height: 24),
                    
                    // 单词列表
                    if (_words.isNotEmpty) ...[
                      _buildSectionTitle('单词列表'),
                      const SizedBox(height: 16),
                      _buildWordsList(),
                    ],
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color: Color(0xFF37352F),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          maxLines: maxLines,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF2F76DA)),
            ),
            filled: true,
            fillColor: const Color(0xFFF7F6F3),
          ),
        ),
      ],
    );
  }

  Widget _buildAddWordForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF7F6F3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildTextField(
                  controller: _wordController,
                  label: '英文单词',
                  hint: '输入单词',
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return '请输入单词';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildTextField(
                  controller: _phoneticController,
                  label: '音标',
                  hint: '/fəˈnetɪk/',
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildTextField(
            controller: _definitionController,
            label: '中文释义',
            hint: '输入中文释义',
            validator: (value) {
              if (value == null || value.trim().isEmpty) {
                return '请输入中文释义';
              }
              return null;
            },
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '词性',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _selectedPartOfSpeech,
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                        ),
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      items: _partsOfSpeech.map((pos) {
                        return DropdownMenuItem(
                          value: pos,
                          child: Text(_getPartOfSpeechLabel(pos)),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedPartOfSpeech = value!;
                        });
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Padding(
                padding: const EdgeInsets.only(top: 24),
                child: ElevatedButton(
                  onPressed: _addWord,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2F76DA),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('添加'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildWordsList() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: _words.length,
        separatorBuilder: (context, index) => const Divider(height: 1),
        itemBuilder: (context, index) {
          final word = _words[index];
          return ListTile(
            title: Text(
              word.word,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            subtitle: Text(
              word.definition,
              style: const TextStyle(color: Color(0xFF787774)),
            ),
            trailing: IconButton(
              icon: const Icon(Icons.delete_outline, color: Color(0xFFE03E3E)),
              onPressed: () => _removeWord(index),
            ),
          );
        },
      ),
    );
  }

  /// 添加单词
  void _addWord() {
    if (_wordController.text.trim().isEmpty || _definitionController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请填写单词和释义'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    // 检查是否已存在
    final word = _wordController.text.trim().toLowerCase();
    if (_words.any((w) => w.word.toLowerCase() == word)) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('该单词已存在'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    final newWord = CustomWord(
      id: _generateId(),
      word: _wordController.text.trim(),
      definition: _definitionController.text.trim(),
      phonetic: _phoneticController.text.trim().isNotEmpty
          ? _phoneticController.text.trim()
          : null,
      partOfSpeech: _selectedPartOfSpeech,
      createdAt: DateTime.now(),
    );

    setState(() {
      _words.add(newWord);
    });

    // 清空输入框
    _wordController.clear();
    _definitionController.clear();
    _phoneticController.clear();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('单词添加成功'),
        backgroundColor: Color(0xFF0F7B6C),
        duration: Duration(seconds: 1),
      ),
    );
  }

  /// 移除单词
  void _removeWord(int index) {
    setState(() {
      _words.removeAt(index);
    });
  }

  /// 保存词库
  void _saveVocabulary() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_words.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('请至少添加一个单词'),
          backgroundColor: Color(0xFFE03E3E),
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final vocabulary = CustomVocabulary(
        id: _generateId(),
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim().isNotEmpty
            ? _descriptionController.text.trim()
            : '自定义词库',
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        words: _words,
      );

      final vocabularyService = ref.read(vocabularyServiceProvider);
      await vocabularyService.createCustomVocabulary(vocabulary);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('词库创建成功'),
            backgroundColor: Color(0xFF0F7B6C),
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('创建失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 获取词性标签
  String _getPartOfSpeechLabel(String pos) {
    switch (pos) {
      case 'noun': return '名词';
      case 'verb': return '动词';
      case 'adjective': return '形容词';
      case 'adverb': return '副词';
      case 'preposition': return '介词';
      case 'conjunction': return '连词';
      case 'pronoun': return '代词';
      case 'article': return '冠词';
      default: return pos;
    }
  }

  /// 生成唯一ID
  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }
}
