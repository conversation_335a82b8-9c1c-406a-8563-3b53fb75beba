import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'vocabulary_service.dart';

/// 自定义词汇管理页面
///
/// 提供tab-based界面管理自定义词汇，支持已选/未选分类、搜索、批量操作
/// 遵循OneDay的Notion风格设计原则
class CustomVocabularyManagerPage extends ConsumerStatefulWidget {
  const CustomVocabularyManagerPage({super.key});

  @override
  ConsumerState<CustomVocabularyManagerPage> createState() =>
      _CustomVocabularyManagerPageState();
}

class _CustomVocabularyManagerPageState
    extends ConsumerState<CustomVocabularyManagerPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _addWordController = TextEditingController();

  // 状态变量
  String _searchQuery = '';
  bool _isLoading = true;
  bool _isSelectionMode = false;
  final Set<String> _selectedWords = {};

  // 数据
  List<String> _memoryWords = [];
  List<String> _availableWords = [];

  // 服务
  late VocabularyService _vocabularyService;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _vocabularyService = VocabularyService();
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _addWordController.dispose();
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      // 加载记忆词库
      _memoryWords = await _vocabularyService.getMemoryVocabularyWords();

      // 加载考研词汇作为可选词汇
      final vocabulary = await _vocabularyService.loadVocabulary();
      _availableWords =
          vocabulary.words.keys
              .where((word) => !_memoryWords.contains(word.toLowerCase()))
              .toList()
            ..sort();

      setState(() => _isLoading = false);
    } catch (e) {
      setState(() => _isLoading = false);
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('加载数据失败: $e')));
      }
    }
  }

  /// 根据搜索条件过滤单词
  List<String> _filterWords(List<String> words) {
    if (_searchQuery.isEmpty) return words;
    return words
        .where(
          (word) => word.toLowerCase().contains(_searchQuery.toLowerCase()),
        )
        .toList();
  }

  /// 获取当前标签页的单词列表
  List<String> get _currentTabWords {
    final words = _tabController.index == 0 ? _memoryWords : _availableWords;
    return _filterWords(words);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        title: const Text(
          '自定义词汇管理',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => context.pop(),
        ),
        actions: [
          if (_isSelectionMode) ...[
            IconButton(
              icon: const Icon(Icons.select_all, color: Color(0xFF2F76DA)),
              onPressed: _selectAll,
            ),
            IconButton(
              icon: const Icon(Icons.clear, color: Color(0xFF787774)),
              onPressed: _clearSelection,
            ),
          ],
          IconButton(
            icon: Icon(
              _isSelectionMode ? Icons.check : Icons.checklist,
              color: const Color(0xFF2F76DA),
            ),
            onPressed: _toggleSelectionMode,
          ),
          IconButton(
            icon: const Icon(Icons.add, color: Color(0xFF2F76DA)),
            onPressed: _showAddWordDialog,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF2F76DA),
          unselectedLabelColor: const Color(0xFF787774),
          indicatorColor: const Color(0xFF2F76DA),
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('已选'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${_memoryWords.length}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('未选'),
                  const SizedBox(width: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: const Color(0xFF787774).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${_availableWords.length}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: Column(
        children: [
          // 搜索栏
          Padding(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索单词...',
                hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                prefixIcon: const Icon(Icons.search, color: Color(0xFF9B9A97)),
                suffixIcon: _searchQuery.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear, color: Color(0xFF9B9A97)),
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchQuery = '');
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: Color(0xFF2F76DA)),
                ),
                filled: true,
                fillColor: const Color(0xFFF7F6F3),
              ),
              onChanged: (value) => setState(() => _searchQuery = value),
            ),
          ),

          // 批量操作栏
          if (_isSelectionMode && _selectedWords.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: const BoxDecoration(
                color: Color(0xFFF7F6F3),
                border: Border(bottom: BorderSide(color: Color(0xFFE3E2E0))),
              ),
              child: Row(
                children: [
                  Text(
                    '已选择 ${_selectedWords.length} 个单词',
                    style: const TextStyle(
                      color: Color(0xFF37352F),
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const Spacer(),
                  ElevatedButton(
                    onPressed: _batchOperation,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2F76DA),
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: Text(_tabController.index == 0 ? '移除' : '添加'),
                  ),
                ],
              ),
            ),

          // 单词列表
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : TabBarView(
                    controller: _tabController,
                    children: [
                      _buildWordList(_filterWords(_memoryWords), true),
                      _buildWordList(_filterWords(_availableWords), false),
                    ],
                  ),
          ),
        ],
      ),
    );
  }

  /// 构建单词列表
  Widget _buildWordList(List<String> words, bool isMemoryTab) {
    if (words.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              isMemoryTab ? Icons.bookmark_border : Icons.search_off,
              size: 48,
              color: const Color(0xFF9B9A97),
            ),
            const SizedBox(height: 16),
            Text(
              isMemoryTab ? '还没有添加任何单词' : '没有找到匹配的单词',
              style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 16),
            ),
            if (isMemoryTab) ...[
              const SizedBox(height: 8),
              const Text(
                '点击右上角的 + 按钮添加单词',
                style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: words.length,
      itemBuilder: (context, index) {
        final word = words[index];
        final isSelected = _selectedWords.contains(word);

        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF2F76DA)
                  : const Color(0xFFE3E2E0),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: ListTile(
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            leading: _isSelectionMode
                ? Checkbox(
                    value: isSelected,
                    onChanged: (value) => _toggleWordSelection(word),
                    activeColor: const Color(0xFF2F76DA),
                  )
                : Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        word[0].toUpperCase(),
                        style: const TextStyle(
                          color: Color(0xFF2F76DA),
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                    ),
                  ),
            title: Text(
              word,
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            trailing: _isSelectionMode
                ? null
                : IconButton(
                    icon: Icon(
                      isMemoryTab
                          ? Icons.remove_circle_outline
                          : Icons.add_circle_outline,
                      color: isMemoryTab
                          ? const Color(0xFFE7433A)
                          : const Color(0xFF2F76DA),
                    ),
                    onPressed: () => _toggleWordInMemory(word, isMemoryTab),
                  ),
            onTap: _isSelectionMode ? () => _toggleWordSelection(word) : null,
          ),
        );
      },
    );
  }

  /// 切换选择模式
  void _toggleSelectionMode() {
    setState(() {
      _isSelectionMode = !_isSelectionMode;
      if (!_isSelectionMode) {
        _selectedWords.clear();
      }
    });
  }

  /// 切换单词选择状态
  void _toggleWordSelection(String word) {
    setState(() {
      if (_selectedWords.contains(word)) {
        _selectedWords.remove(word);
      } else {
        _selectedWords.add(word);
      }
    });
  }

  /// 切换单词在记忆词库中的状态
  Future<void> _toggleWordInMemory(
    String word,
    bool isCurrentlyInMemory,
  ) async {
    try {
      if (isCurrentlyInMemory) {
        await _vocabularyService.removeFromMemoryVocabulary(word);
      } else {
        await _vocabularyService.batchAddToMemoryVocabulary([word]);
      }

      // 刷新数据
      await _loadData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isCurrentlyInMemory ? '已移除单词 "$word"' : '已添加单词 "$word"',
            ),
            backgroundColor: const Color(0xFF2E7EED),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }

  /// 全选
  void _selectAll() {
    setState(() {
      _selectedWords.clear();
      _selectedWords.addAll(_currentTabWords);
    });
  }

  /// 清除选择
  void _clearSelection() {
    setState(() {
      _selectedWords.clear();
    });
  }

  /// 批量操作
  Future<void> _batchOperation() async {
    if (_selectedWords.isEmpty) return;

    try {
      if (_tabController.index == 0) {
        // 从记忆词库中移除
        await _vocabularyService.batchRemoveFromMemoryVocabulary(
          _selectedWords.toList(),
        );
      } else {
        // 添加到记忆词库
        await _vocabularyService.batchAddToMemoryVocabulary(
          _selectedWords.toList(),
        );
      }

      // 保存选择的数量用于显示消息
      final selectedCount = _selectedWords.length;
      final isRemoving = _tabController.index == 0;

      // 刷新数据
      await _loadData();

      // 清除选择并退出选择模式
      setState(() {
        _selectedWords.clear();
        _isSelectionMode = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              isRemoving ? '已移除 $selectedCount 个单词' : '已添加 $selectedCount 个单词',
            ),
            backgroundColor: const Color(0xFF2E7EED),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('操作失败: $e')));
      }
    }
  }

  /// 显示添加单词对话框
  void _showAddWordDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          '添加自定义单词',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: TextField(
          controller: _addWordController,
          decoration: InputDecoration(
            hintText: '输入单词...',
            hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: Color(0xFF2F76DA)),
            ),
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () {
              _addWordController.clear();
              context.pop();
            },
            child: const Text('取消', style: TextStyle(color: Color(0xFF787774))),
          ),
          ElevatedButton(
            onPressed: () => _addCustomWord(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('添加'),
          ),
        ],
      ),
    );
  }

  /// 添加自定义单词
  Future<void> _addCustomWord() async {
    final word = _addWordController.text.trim();
    if (word.isEmpty) return;

    try {
      await _vocabularyService.batchAddToMemoryVocabulary([word]);
      await _loadData();

      _addWordController.clear();
      if (mounted) {
        context.pop();
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('已添加单词 "$word"'),
            backgroundColor: const Color(0xFF2E7EED),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('添加失败: $e')));
      }
    }
  }
}
