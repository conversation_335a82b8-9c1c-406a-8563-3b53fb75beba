import 'dart:io';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:screenshot/screenshot.dart';
import '../models/learning_report_models.dart';

/// 学习报告导出服务
///
/// 提供PDF导出、图片导出和分享功能
class LearningReportExportService {
  final ScreenshotController _screenshotController = ScreenshotController();

  // 缓存中文字体
  pw.Font? _chineseFont;

  /// OneDay应用颜色方案
  static const Map<String, PdfColor> oneDayColors = {
    // 主色调
    'primary': PdfColor.fromInt(0xFF2E7EED), // OneDay蓝
    'primaryDark': PdfColor.fromInt(0xFF1E5FCC),

    // 学科分类颜色
    'computerScience': PdfColor.fromInt(0xFFE03E3E), // 红色
    'mathematics': PdfColor.fromInt(0xFFFFD700), // 荧光黄
    'english': PdfColor.fromInt(0xFF0F7B6C), // 绿色
    'politics': PdfColor.fromInt(0xFF2E7EED), // 蓝色
    'rest': PdfColor.fromInt(0xFF9B9A97), // 灰色
    // 功能色
    'success': PdfColor.fromInt(0xFF0F7B6C), // 深绿
    'warning': PdfColor.fromInt(0xFFD9730D), // 橙色
    'error': PdfColor.fromInt(0xFFE03E3E), // 柔和红
    'info': PdfColor.fromInt(0xFF2E7EED), // 蓝色
    // 中性色
    'textPrimary': PdfColor.fromInt(0xFF37352F), // 深灰文字
    'textSecondary': PdfColor.fromInt(0xFF787774), // 中灰文字
    'textHint': PdfColor.fromInt(0xFF9B9A97), // 浅灰文字
    'border': PdfColor.fromInt(0xFFE3E2E0), // 边框色
    'background': PdfColors.white, // 背景色
    // 图表颜色
    'chart1': PdfColor.fromInt(0xFF2E7EED), // 蓝色
    'chart2': PdfColor.fromInt(0xFF7C3AED), // 紫色
    'chart3': PdfColor.fromInt(0xFF0F7B6C), // 绿色
    'chart4': PdfColor.fromInt(0xFFE03E3E), // 红色
    'chart5': PdfColor.fromInt(0xFFFFD700), // 黄色
  };

  /// 加载中文字体（暂时禁用，使用英文降级方案）
  Future<pw.Font?> _loadChineseFont() async {
    // 暂时禁用中文字体加载，直接返回null使用英文降级方案
    // 这样可以确保PDF导出功能正常工作
    print('📝 中文字体功能暂时禁用，使用英文降级方案');
    print('💡 这确保了PDF导出的稳定性和兼容性');
    _chineseFont = null;
    return null;

    /* 原字体加载代码保留，待字体问题解决后可重新启用
    if (_chineseFont != null) return _chineseFont!;

    try {
      print('📝 尝试加载中文字体文件...');
      final fontData = await rootBundle.load(
        'assets/fonts/NotoSansSC-Regular.ttf',
      );

      // 检查字体文件大小，确保不是空文件
      if (fontData.lengthInBytes < 10000) {
        print('⚠️ 字体文件太小，可能是无效文件: ${fontData.lengthInBytes} bytes');
        return null;
      }

      print('📊 字体文件大小: ${fontData.lengthInBytes} bytes');

      // 尝试创建字体对象
      final font = pw.Font.ttf(fontData);
      print('✅ 字体对象创建成功');

      // 进行简单的字体验证测试
      print('🧪 测试字体可用性...');
      final testDoc = pw.Document();
      testDoc.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Column(
              children: [
                pw.Text('Test', style: pw.TextStyle(font: font)),
                pw.Text('English Only', style: pw.TextStyle(font: font)),
              ],
            );
          },
        ),
      );

      // 尝试生成测试PDF（不包含中文）
      await testDoc.save();
      print('✅ 字体基础测试通过');

      _chineseFont = font;
      print('✅ 中文字体加载成功');
      return _chineseFont!;
    } catch (e) {
      // 如果加载失败，返回null，使用默认字体
      print('⚠️ 警告：无法加载中文字体，将使用默认字体: $e');
      print('⚠️ 错误详情: ${e.toString()}');
      _chineseFont = null;
      return null;
    }
    */
  }

  /// 清理文本，将中文字符转换为安全的表示形式
  String _sanitizeText(String text) {
    // 如果没有中文字体，将中文字符转换为拼音或英文描述
    final chineseToEnglish = {
      '学习报告': 'Learning Report',
      '测试内容': 'Test Content',
      '中英文混合': 'Chinese-English Mixed',
      '日期': 'Date',
      '核心指标': 'Core Metrics',
      '学科分布': 'Subject Distribution',
      '学习习惯分析': 'Learning Habit Analysis',
      '今日学习时长': 'Today Study Time',
      '完成任务数': 'Completed Tasks',
      '学习效率': 'Learning Efficiency',
      '专注时长': 'Focus Time',
      '休息时长': 'Break Time',
      '学习连续天数': 'Study Streak Days',
    };

    String result = text;
    chineseToEnglish.forEach((chinese, english) {
      result = result.replaceAll(chinese, english);
    });

    // 移除其他中文字符，保留数字、英文和基本符号
    result = result.replaceAll(RegExp(r'[\u4e00-\u9fff]'), '?');

    return result;
  }

  /// 创建安全的文本样式
  pw.TextStyle _createTextStyle({
    double? fontSize,
    pw.FontWeight? fontWeight,
    PdfColor? color,
    pw.Font? font,
  }) {
    return pw.TextStyle(
      fontSize: fontSize,
      fontWeight: fontWeight,
      color: color,
      font: font,
    );
  }

  /// 构建指标卡片
  pw.Widget _buildMetricCard(String title, String value, PdfColor color) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: color.shade(0.1),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(color: color.shade(0.3), width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: pw.TextStyle(
              fontSize: 12,
              color: oneDayColors['textSecondary'],
              fontWeight: pw.FontWeight.normal,
            ),
          ),
          pw.SizedBox(height: 8),
          pw.Text(
            value,
            style: pw.TextStyle(
              fontSize: 18,
              fontWeight: pw.FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 获取学科对应的颜色
  PdfColor _getSubjectColor(String subject) {
    switch (subject) {
      case '计算机科学':
      case 'Computer Science':
        return oneDayColors['computerScience']!;
      case '数学':
      case 'Mathematics':
        return oneDayColors['mathematics']!;
      case '英语':
      case 'English':
        return oneDayColors['english']!;
      case '政治':
      case 'Politics':
        return oneDayColors['politics']!;
      case '休息':
      case 'Rest':
        return oneDayColors['rest']!;
      default:
        return oneDayColors['textSecondary']!;
    }
  }

  /// 翻译学科名称为英文
  String _translateSubject(String subject) {
    switch (subject) {
      case '计算机科学':
        return 'Computer Science';
      case '数学':
        return 'Mathematics';
      case '英语':
        return 'English';
      case '政治':
        return 'Politics';
      case '休息':
        return 'Rest';
      default:
        return subject;
    }
  }

  /// 导出学习报告为PDF
  Future<void> exportToPDF({
    required BuildContext context,
    required LearningReportData reportData,
    LearningHabitAnalysis? habitAnalysis,
  }) async {
    if (!context.mounted) return;

    // 显示加载对话框
    _showLoadingDialog(context, '正在生成PDF...');

    try {
      print('🔄 开始PDF导出过程...');

      // 尝试加载中文字体，如果失败则使用null（默认字体）
      pw.Font? chineseFont;
      try {
        print('📝 正在加载中文字体...');
        chineseFont = await _loadChineseFont();
        print('✅ 字体加载结果: ${chineseFont != null ? "成功" : "失败"}');
      } catch (e) {
        print('⚠️ 字体加载异常，使用默认字体: $e');
        chineseFont = null;
      }

      print('📄 开始创建PDF文档...');
      final pdf = pw.Document();

      // 测试中文字体支持，如果失败则使用降级方案
      print('🧪 开始测试PDF生成...');
      try {
        pdf.addPage(
          pw.Page(
            pageFormat: PdfPageFormat.a4,
            margin: const pw.EdgeInsets.all(32),
            build: (pw.Context context) {
              print('🔨 构建PDF内容...');

              // 安全策略：暂时只使用英文，避免字符编码问题
              print('📝 使用安全的英文模式');

              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  // 彩色标题区域
                  pw.Container(
                    width: double.infinity,
                    padding: const pw.EdgeInsets.all(20),
                    decoration: pw.BoxDecoration(
                      gradient: pw.LinearGradient(
                        colors: [
                          oneDayColors['primary']!,
                          oneDayColors['primaryDark']!,
                        ],
                      ),
                      borderRadius: pw.BorderRadius.circular(12),
                    ),
                    child: pw.Column(
                      crossAxisAlignment: pw.CrossAxisAlignment.start,
                      children: [
                        pw.Text(
                          'OneDay Learning Report',
                          style: pw.TextStyle(
                            fontSize: 24,
                            fontWeight: pw.FontWeight.bold,
                            color: PdfColors.white,
                          ),
                        ),
                        pw.SizedBox(height: 8),
                        pw.Text(
                          'Generated on ${_formatDate(DateTime.now())}',
                          style: pw.TextStyle(
                            fontSize: 14,
                            color: PdfColors.white.shade(0.8),
                          ),
                        ),
                      ],
                    ),
                  ),
                  pw.SizedBox(height: 24),

                  // 核心指标卡片区域
                  pw.Text(
                    'Core Metrics',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color: oneDayColors['textPrimary'],
                    ),
                  ),
                  pw.SizedBox(height: 16),

                  // 指标卡片网格
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: _buildMetricCard(
                          'Study Time',
                          reportData.formattedTotalStudyTime,
                          oneDayColors['success']!,
                        ),
                      ),
                      pw.SizedBox(width: 12),
                      pw.Expanded(
                        child: _buildMetricCard(
                          'Tasks',
                          '${reportData.totalCompletedTasks}',
                          oneDayColors['info']!,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 12),
                  pw.Row(
                    children: [
                      pw.Expanded(
                        child: _buildMetricCard(
                          'Learning ROI',
                          '${reportData.averageLearningROI.toStringAsFixed(1)}',
                          oneDayColors['warning']!,
                        ),
                      ),
                      pw.SizedBox(width: 12),
                      pw.Expanded(
                        child: _buildMetricCard(
                          'Streak Days',
                          '${reportData.streakDays}',
                          oneDayColors['chart2']!,
                        ),
                      ),
                    ],
                  ),
                  pw.SizedBox(height: 24),

                  // 学科分布区域
                  pw.Text(
                    'Subject Distribution',
                    style: pw.TextStyle(
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color: oneDayColors['textPrimary'],
                    ),
                  ),
                  pw.SizedBox(height: 16),

                  // 学科分布条形图
                  ...reportData.subjectDistribution.take(4).map((subject) {
                    final color = _getSubjectColor(subject.subject);
                    final totalMinutes = reportData.subjectDistribution.isEmpty
                        ? 1
                        : reportData.subjectDistribution
                              .map((s) => s.studyMinutes)
                              .reduce((a, b) => a + b);
                    final percentage = reportData.subjectDistribution.isEmpty
                        ? 0.0
                        : (subject.studyMinutes / totalMinutes) * 100;

                    return pw.Container(
                      margin: const pw.EdgeInsets.only(bottom: 8),
                      child: pw.Row(
                        children: [
                          pw.Container(
                            width: 80,
                            child: pw.Text(
                              _translateSubject(subject.subject),
                              style: pw.TextStyle(
                                fontSize: 12,
                                color: oneDayColors['textSecondary'],
                              ),
                            ),
                          ),
                          pw.SizedBox(width: 12),
                          pw.Expanded(
                            child: pw.Stack(
                              children: [
                                // 背景条
                                pw.Container(
                                  height: 20,
                                  decoration: pw.BoxDecoration(
                                    color: oneDayColors['border'],
                                    borderRadius: pw.BorderRadius.circular(10),
                                  ),
                                ),
                                // 进度条
                                pw.Positioned(
                                  left: 0,
                                  top: 0,
                                  child: pw.Container(
                                    height: 20,
                                    width:
                                        (percentage / 100) * 200, // 假设最大宽度为200
                                    decoration: pw.BoxDecoration(
                                      color: color,
                                      borderRadius: pw.BorderRadius.circular(
                                        10,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          pw.SizedBox(width: 12),
                          pw.Container(
                            width: 50,
                            child: pw.Text(
                              '${percentage.toStringAsFixed(1)}%',
                              style: pw.TextStyle(
                                fontSize: 12,
                                fontWeight: pw.FontWeight.bold,
                                color: color,
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }).toList(),
                ],
              );
            },
          ),
        );
        print('✅ PDF页面添加成功');
      } catch (e) {
        print('❌ PDF构建失败: $e');
        print('❌ 错误详情: ${e.toString()}');
        print('❌ 错误类型: ${e.runtimeType}');

        // 最后的降级方案：只使用英文和默认字体
        print('🔄 尝试最简降级方案...');
        try {
          pdf.addPage(
            pw.Page(
              pageFormat: PdfPageFormat.a4,
              margin: const pw.EdgeInsets.all(32),
              build: (pw.Context context) {
                return pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'OneDay Learning Report',
                      style: pw.TextStyle(
                        fontSize: 24,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 20),
                    pw.Text(
                      'Test Content - English Only',
                      style: pw.TextStyle(fontSize: 16),
                    ),
                    pw.SizedBox(height: 20),
                    pw.Text(
                      'Date: ${_formatDate(DateTime.now())}',
                      style: pw.TextStyle(fontSize: 14),
                    ),
                  ],
                );
              },
            ),
          );
          print('✅ 降级方案PDF页面添加成功');
        } catch (fallbackError) {
          print('❌ 降级方案也失败: $fallbackError');
          throw Exception('PDF构建完全失败: $fallbackError');
        }
      }

      // 保存PDF文件到应用文档目录
      print('💾 开始保存PDF文件...');
      final directory = await getApplicationDocumentsDirectory();
      final fileName =
          'learning_report_test_${DateTime.now().millisecondsSinceEpoch}.pdf';
      final file = File('${directory.path}/$fileName');
      print('📁 PDF文件路径: ${file.path}');

      print('⚙️ 开始生成PDF字节数据...');
      final pdfBytes = await pdf.save();
      print('📊 PDF字节数据大小: ${pdfBytes.length} bytes');

      print('💾 开始写入文件...');
      await file.writeAsBytes(pdfBytes);
      print('✅ PDF文件保存成功');

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 使用系统分享功能分享PDF文件
      print('📤 开始分享PDF文件...');
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'OneDay Learning Report Test',
        subject: fileName,
      );
      print('✅ PDF分享完成');

      // 显示成功提示
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('PDF导出成功'),
            backgroundColor: Color(0xFF0F7B6C),
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e, stackTrace) {
      print('PDF导出失败: $e');
      print('错误堆栈: $stackTrace');

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
        // 显示错误信息和备用方案
        _showPDFErrorDialog(context, e.toString());
      }
    }
  }

  /// 导出学习报告为图片
  Future<void> exportToImage({
    required BuildContext context,
    required GlobalKey repaintBoundaryKey,
  }) async {
    if (!context.mounted) return;

    // 显示加载对话框
    _showLoadingDialog(context, '正在生成图片...');

    try {
      // 获取RenderRepaintBoundary
      RenderRepaintBoundary boundary =
          repaintBoundaryKey.currentContext!.findRenderObject()
              as RenderRepaintBoundary;

      // 转换为图片
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );
      Uint8List pngBytes = byteData!.buffer.asUint8List();

      // 保存图片文件
      final directory = await getTemporaryDirectory();
      final file = File(
        '${directory.path}/learning_report_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      await file.writeAsBytes(pngBytes);

      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
      }

      // 分享图片
      await Share.shareXFiles(
        [XFile(file.path)],
        text: '我的学习报告 - OneDay',
        subject: '学习报告分享',
      );
    } catch (e) {
      // 关闭加载对话框
      if (context.mounted) {
        Navigator.of(context).pop();
        // 显示错误信息
        _showErrorSnackBar(context, '导出图片失败: ${e.toString()}');
      }
    }
  }

  /// 分享学习报告
  Future<void> shareReport({
    required BuildContext context,
    required LearningReportData reportData,
    LearningHabitAnalysis? habitAnalysis,
  }) async {
    try {
      // 生成分享文本
      final shareText = _generateShareText(reportData, habitAnalysis);

      // 分享文本
      await Share.share(shareText, subject: '我的学习报告 - OneDay');
    } catch (e) {
      // 显示错误信息
      if (context.mounted) {
        _showErrorSnackBar(context, '分享失败: ${e.toString()}');
      }
    }
  }

  /// 构建PDF核心指标
  pw.Widget _buildPDFCoreMetrics(
    LearningReportData reportData,
    pw.Font? chineseFont,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          '核心指标',
          style: _createTextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: chineseFont,
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Row(
          children: [
            pw.Expanded(
              child: _buildPDFMetricCard(
                '总学习时长',
                reportData.formattedTotalStudyTime,
                PdfColors.blue,
                chineseFont,
              ),
            ),
            pw.SizedBox(width: 12),
            pw.Expanded(
              child: _buildPDFMetricCard(
                '完成任务',
                '${reportData.totalCompletedTasks}个',
                PdfColors.green,
                chineseFont,
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 12),
        pw.Row(
          children: [
            pw.Expanded(
              child: _buildPDFMetricCard(
                '学习ROI',
                reportData.averageLearningROI.toStringAsFixed(1),
                PdfColors.orange,
                chineseFont,
              ),
            ),
            pw.SizedBox(width: 12),
            pw.Expanded(
              child: _buildPDFMetricCard(
                '信噪比',
                reportData.averageFocusSignalToNoiseRatio.toStringAsFixed(1),
                PdfColors.purple,
                chineseFont,
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建PDF指标卡片
  pw.Widget _buildPDFMetricCard(
    String title,
    String value,
    PdfColor color,
    pw.Font? chineseFont,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: color.shade(0.1),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(color: color, width: 1),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            title,
            style: _createTextStyle(
              fontSize: 12,
              color: PdfColors.grey600,
              font: chineseFont,
            ),
          ),
          pw.SizedBox(height: 4),
          pw.Text(
            value,
            style: _createTextStyle(
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: color,
              font: chineseFont,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建PDF学科分布
  pw.Widget _buildPDFSubjectDistribution(
    LearningReportData reportData,
    pw.Font? chineseFont,
  ) {
    // 计算总学习时长用于百分比计算
    final totalMinutes = reportData.subjectDistribution.fold<int>(
      0,
      (sum, subject) => sum + subject.studyMinutes,
    );

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          '学科分布',
          style: _createTextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: chineseFont,
          ),
        ),
        pw.SizedBox(height: 12),
        ...reportData.subjectDistribution.map(
          (subject) => pw.Padding(
            padding: const pw.EdgeInsets.only(bottom: 8),
            child: pw.Row(
              children: [
                pw.Container(
                  width: 12,
                  height: 12,
                  decoration: pw.BoxDecoration(
                    color: _getSubjectColor(subject.subject),
                    borderRadius: pw.BorderRadius.circular(2),
                  ),
                ),
                pw.SizedBox(width: 8),
                pw.Text(
                  subject.subject,
                  style: _createTextStyle(font: chineseFont),
                ),
                pw.Spacer(),
                pw.Text(
                  '${subject.getPercentage(totalMinutes).toStringAsFixed(1)}%',
                  style: _createTextStyle(font: chineseFont),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// 构建PDF习惯分析
  pw.Widget _buildPDFHabitAnalysis(
    LearningHabitAnalysis habitAnalysis,
    pw.Font? chineseFont,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          '学习习惯分析',
          style: _createTextStyle(
            fontSize: 18,
            fontWeight: pw.FontWeight.bold,
            font: chineseFont,
          ),
        ),
        pw.SizedBox(height: 12),
        _buildPDFHabitItem(
          '最佳学习时间',
          habitAnalysis.bestStudyTimeDescription,
          chineseFont,
        ),
        _buildPDFHabitItem(
          '平均每日学习',
          '${(habitAnalysis.averageDailyStudyMinutes / 60).toStringAsFixed(1)}小时',
          chineseFont,
        ),
        _buildPDFHabitItem(
          '当前连续天数',
          '${habitAnalysis.currentStreak}天',
          chineseFont,
        ),
        _buildPDFHabitItem(
          '最长连续天数',
          '${habitAnalysis.longestStreak}天',
          chineseFont,
        ),
        _buildPDFHabitItem('学习习惯等级', habitAnalysis.habitLevel, chineseFont),
      ],
    );
  }

  /// 构建PDF习惯项
  pw.Widget _buildPDFHabitItem(
    String title,
    String value,
    pw.Font? chineseFont,
  ) {
    return pw.Padding(
      padding: const pw.EdgeInsets.only(bottom: 8),
      child: pw.Row(
        children: [
          pw.Text(
            title,
            style: _createTextStyle(
              color: PdfColors.grey600,
              font: chineseFont,
            ),
          ),
          pw.Spacer(),
          pw.Text(
            value,
            style: _createTextStyle(
              fontWeight: pw.FontWeight.bold,
              font: chineseFont,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建PDF页脚
  pw.Widget _buildPDFFooter(pw.Font? chineseFont) {
    return pw.Column(
      children: [
        pw.Divider(),
        pw.Text(
          '由 OneDay 应用生成 • ${_formatDate(DateTime.now())}',
          style: _createTextStyle(
            fontSize: 10,
            color: PdfColors.grey500,
            font: chineseFont,
          ),
        ),
      ],
    );
  }

  /// 生成分享文本
  String _generateShareText(
    LearningReportData reportData,
    LearningHabitAnalysis? habitAnalysis,
  ) {
    final buffer = StringBuffer();

    buffer.writeln('📊 我的学习报告 - OneDay');
    buffer.writeln('');
    buffer.writeln('📅 时间范围: ${reportData.timeRange.label}');
    buffer.writeln('⏰ 总学习时长: ${reportData.formattedTotalStudyTime}');
    buffer.writeln('✅ 完成任务: ${reportData.totalCompletedTasks}个');
    buffer.writeln(
      '📈 学习ROI: ${reportData.averageLearningROI.toStringAsFixed(1)}',
    );
    buffer.writeln(
      '🎯 信噪比: ${reportData.averageFocusSignalToNoiseRatio.toStringAsFixed(1)}',
    );

    if (habitAnalysis != null) {
      buffer.writeln('');
      buffer.writeln('🔥 连续学习: ${habitAnalysis.currentStreak}天');
      buffer.writeln('🏆 学习等级: ${habitAnalysis.habitLevel}');
    }

    buffer.writeln('');
    buffer.writeln('💪 坚持学习，成就更好的自己！');
    buffer.writeln('#OneDay #学习报告 #自我提升');

    return buffer.toString();
  }

  /// 显示加载对话框
  void _showLoadingDialog(BuildContext context, String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        content: Row(
          children: [
            const CircularProgressIndicator(color: Color(0xFF2E7EED)),
            const SizedBox(width: 16),
            Text(message),
          ],
        ),
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE03E3E),
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示PDF导出错误对话框，提供备用方案
  void _showPDFErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text(
          'PDF导出失败',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '无法生成PDF文件，您可以尝试以下备用方案：',
              style: const TextStyle(fontSize: 14, color: Color(0xFF37352F)),
            ),
            const SizedBox(height: 12),
            Text(
              '错误详情：$error',
              style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消', style: TextStyle(color: Color(0xFF9B9A97))),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // 提供图片导出作为备用方案
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('建议使用"导出为图片"功能作为替代'),
                  backgroundColor: Color(0xFF2E7EED),
                  duration: Duration(seconds: 3),
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('了解'),
          ),
        ],
      ),
    );
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }

  /// 获取截图控制器
  ScreenshotController get screenshotController => _screenshotController;
}
