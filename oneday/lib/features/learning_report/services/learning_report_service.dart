import 'package:flutter/foundation.dart';
import 'dart:math' as math;
import '../models/learning_report_models.dart';
import '../../study_time/services/study_time_statistics_service.dart';
import '../../study_time/models/study_time_models.dart';

/// 学习报告服务
///
/// 负责生成和管理学习报告数据，包括：
/// 1. 集成StudyTimeStatisticsService的数据
/// 2. 计算各种学习分析指标
/// 3. 生成图表数据
/// 4. 提供数据导出功能
class LearningReportService extends ChangeNotifier {
  final StudyTimeStatisticsService _studyTimeService;

  // 缓存的报告数据
  LearningReportData? _currentReportData;
  LearningHabitAnalysis? _currentHabitAnalysis;

  // 加载状态
  bool _isLoading = false;
  String? _error;

  LearningReportService(this._studyTimeService);

  /// 获取当前报告数据
  LearningReportData? get currentReportData => _currentReportData;

  /// 获取当前习惯分析数据
  LearningHabitAnalysis? get currentHabitAnalysis => _currentHabitAnalysis;

  /// 获取加载状态
  bool get isLoading => _isLoading;

  /// 获取错误信息
  String? get error => _error;

  /// 生成学习报告
  Future<LearningReportData> generateReport(
    TimeRange timeRange, {
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) async {
    _setLoading(true);
    _clearError();

    try {
      final dateRange = _calculateDateRange(
        timeRange,
        customStartDate,
        customEndDate,
      );
      final startDate = dateRange['start']!;
      final endDate = dateRange['end']!;

      // 获取基础统计数据
      final dailyStats = await _getDailyStatistics(startDate, endDate);

      // 计算聚合数据
      final totalStudyMinutes = dailyStats.fold<int>(
        0,
        (sum, stat) => sum + stat.totalStudyMinutes,
      );
      final totalCompletedTasks = dailyStats.fold<int>(
        0,
        (sum, stat) => sum + stat.completedTasks,
      );
      final averageLearningROI = _calculateAverageLearningROI(dailyStats);
      final totalParallelTimeMinutes = dailyStats.fold<int>(
        0,
        (sum, stat) => sum + stat.parallelTimeMinutes,
      );
      final averageFocusSignalToNoiseRatio =
          _calculateAverageFocusSignalToNoiseRatio(dailyStats);

      // 计算效率等级
      final efficiencyLevel = _calculateEfficiencyLevel(
        averageLearningROI,
        averageFocusSignalToNoiseRatio,
      );

      // 计算连续学习天数
      final streakDays = _calculateStreakDays(dailyStats);

      // 生成各种图表数据
      final subjectDistribution = _generateSubjectDistribution(dailyStats);
      final dailyStudyData = _generateDailyStudyData(dailyStats);
      final roiTrendData = _generateROITrendData(dailyStats);
      final focusAnalysisData = _generateFocusAnalysisData(dailyStats);
      final efficiencyHeatmapData = _generateEfficiencyHeatmapData(dailyStats);

      _currentReportData = LearningReportData(
        timeRange: timeRange,
        startDate: startDate,
        endDate: endDate,
        totalStudyMinutes: totalStudyMinutes,
        totalCompletedTasks: totalCompletedTasks,
        averageLearningROI: averageLearningROI,
        totalParallelTimeMinutes: totalParallelTimeMinutes,
        averageFocusSignalToNoiseRatio: averageFocusSignalToNoiseRatio,
        efficiencyLevel: efficiencyLevel,
        streakDays: streakDays,
        subjectDistribution: subjectDistribution,
        dailyStudyData: dailyStudyData,
        roiTrendData: roiTrendData,
        focusAnalysisData: focusAnalysisData,
        efficiencyHeatmapData: efficiencyHeatmapData,
      );

      notifyListeners();
      return _currentReportData!;
    } catch (e) {
      _setError('生成学习报告失败: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// 生成学习习惯分析
  Future<LearningHabitAnalysis> generateHabitAnalysis() async {
    try {
      // 获取过去30天的数据
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30));
      final dailyStats = await _getDailyStatistics(startDate, endDate);

      // 分析最佳学习时间段
      final bestStudyHours = _analyzeBestStudyHours(dailyStats);

      // 计算平均每日学习时长
      final averageDailyStudyMinutes = dailyStats.isNotEmpty
          ? dailyStats.fold<int>(
                  0,
                  (sum, stat) => sum + stat.totalStudyMinutes,
                ) /
                dailyStats.length
          : 0.0;

      // 计算连续学习天数
      final currentStreak = _calculateCurrentStreak(dailyStats);
      final longestStreak = _calculateLongestStreak(dailyStats);

      // 计算一致性和强度评分
      final consistencyScore = _calculateConsistencyScore(dailyStats);
      final intensityScore = _calculateIntensityScore(dailyStats);

      _currentHabitAnalysis = LearningHabitAnalysis(
        bestStudyHours: bestStudyHours,
        averageDailyStudyMinutes: averageDailyStudyMinutes,
        longestStreak: longestStreak,
        currentStreak: currentStreak,
        consistencyScore: consistencyScore,
        intensityScore: intensityScore,
      );

      return _currentHabitAnalysis!;
    } catch (e) {
      _setError('生成学习习惯分析失败: $e');
      rethrow;
    }
  }

  /// 计算日期范围
  Map<String, DateTime> _calculateDateRange(
    TimeRange timeRange,
    DateTime? customStartDate,
    DateTime? customEndDate,
  ) {
    final now = DateTime.now();

    switch (timeRange) {
      case TimeRange.today:
        final today = DateTime(now.year, now.month, now.day);
        return {'start': today, 'end': today.add(const Duration(days: 1))};

      case TimeRange.thisWeek:
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        final startDate = DateTime(
          startOfWeek.year,
          startOfWeek.month,
          startOfWeek.day,
        );
        return {
          'start': startDate,
          'end': startDate.add(const Duration(days: 7)),
        };

      case TimeRange.thisMonth:
        final startOfMonth = DateTime(now.year, now.month, 1);
        final endOfMonth = DateTime(now.year, now.month + 1, 1);
        return {'start': startOfMonth, 'end': endOfMonth};

      case TimeRange.custom:
        if (customStartDate == null || customEndDate == null) {
          throw ArgumentError('自定义时间范围需要提供开始和结束日期');
        }
        return {'start': customStartDate, 'end': customEndDate};
    }
  }

  /// 获取指定日期范围的每日统计数据
  Future<List<StudyTimeStatistics>> _getDailyStatistics(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final List<StudyTimeStatistics> dailyStats = [];

    for (
      DateTime date = startDate;
      date.isBefore(endDate);
      date = date.add(const Duration(days: 1))
    ) {
      final stats = _studyTimeService.getDailyStatistics(date);
      if (stats != null && stats.hasStudyData) {
        dailyStats.add(stats);
      }
    }

    return dailyStats;
  }

  /// 计算平均学习ROI
  double _calculateAverageLearningROI(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0.0;

    final totalROI = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.learningROI,
    );
    return totalROI / dailyStats.length;
  }

  /// 计算平均信噪比
  double _calculateAverageFocusSignalToNoiseRatio(
    List<StudyTimeStatistics> dailyStats,
  ) {
    if (dailyStats.isEmpty) return 0.0;

    final totalRatio = dailyStats.fold<double>(
      0.0,
      (sum, stat) => sum + stat.focusSignalToNoiseRatio,
    );
    return totalRatio / dailyStats.length;
  }

  /// 计算效率等级
  String _calculateEfficiencyLevel(
    double averageROI,
    double averageFocusRatio,
  ) {
    final combinedScore = (averageROI + averageFocusRatio) / 2;

    if (combinedScore >= 80) return '优秀';
    if (combinedScore >= 60) return '良好';
    if (combinedScore >= 40) return '一般';
    return '需改进';
  }

  /// 计算连续学习天数
  int _calculateStreakDays(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0;

    // 按日期排序
    final sortedStats = List<StudyTimeStatistics>.from(
      dailyStats,
    )..sort((a, b) => DateTime.parse(a.date).compareTo(DateTime.parse(b.date)));

    int streak = 0;
    DateTime? lastDate;

    for (final stat in sortedStats.reversed) {
      final currentDate = DateTime.parse(stat.date);

      if (lastDate == null) {
        streak = 1;
        lastDate = currentDate;
      } else {
        final daysDiff = lastDate.difference(currentDate).inDays;
        if (daysDiff == 1) {
          streak++;
          lastDate = currentDate;
        } else {
          break;
        }
      }
    }

    return streak;
  }

  /// 生成学科分布数据
  List<SubjectDistribution> _generateSubjectDistribution(
    List<StudyTimeStatistics> dailyStats,
  ) {
    final Map<String, int> subjectMinutes = {};
    final Map<String, String> subjectColors = {
      '计算机科学': '#E03E3E',
      '数学': '#FFD700',
      '英语': '#0F7B6C',
      '政治': '#2E7EED',
      '休息': '#9B9A97',
    };

    // 统计各学科的学习时间
    for (final stat in dailyStats) {
      for (final session in stat.sessions) {
        final subject = session.category;
        subjectMinutes[subject] =
            (subjectMinutes[subject] ?? 0) + session.durationMinutes;
      }
    }

    // 转换为SubjectDistribution列表
    return subjectMinutes.entries.map((entry) {
      return SubjectDistribution(
        subject: entry.key,
        studyMinutes: entry.value,
        colorHex: subjectColors[entry.key] ?? '#9B9A97',
      );
    }).toList();
  }

  /// 生成每日学习数据
  List<DailyStudyData> _generateDailyStudyData(
    List<StudyTimeStatistics> dailyStats,
  ) {
    return dailyStats.map((stat) {
      return DailyStudyData(
        date: DateTime.parse(stat.date),
        studyMinutes: stat.totalStudyMinutes,
        completedTasks: stat.completedTasks,
        learningROI: stat.learningROI,
        focusSignalToNoiseRatio: stat.focusSignalToNoiseRatio,
      );
    }).toList();
  }

  /// 生成ROI趋势数据
  List<ROITrendData> _generateROITrendData(
    List<StudyTimeStatistics> dailyStats,
  ) {
    final List<ROITrendData> trendData = [];

    for (int i = 0; i < dailyStats.length; i++) {
      final stat = dailyStats[i];

      // 计算7天移动平均
      double movingAverage = 0.0;
      int count = 0;

      for (int j = math.max(0, i - 6); j <= i; j++) {
        movingAverage += dailyStats[j].learningROI;
        count++;
      }
      movingAverage = count > 0 ? movingAverage / count : 0.0;

      trendData.add(
        ROITrendData(
          date: DateTime.parse(stat.date),
          roiValue: stat.learningROI,
          movingAverage: movingAverage,
        ),
      );
    }

    return trendData;
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 设置错误信息
  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  /// 清除错误信息
  void _clearError() {
    _error = null;
  }

  /// 生成专注度分析数据
  List<FocusAnalysisData> _generateFocusAnalysisData(
    List<StudyTimeStatistics> dailyStats,
  ) {
    return dailyStats.map((stat) {
      // 计算专注时间和干扰时间
      int focusMinutes = 0;
      int distractionMinutes = 0;

      for (final session in stat.sessions) {
        if (session.category != '休息') {
          focusMinutes += session.durationMinutes;
        } else if (!session.title.contains('动觉记忆')) {
          distractionMinutes += session.durationMinutes;
        }
      }

      return FocusAnalysisData(
        date: DateTime.parse(stat.date),
        focusMinutes: focusMinutes,
        distractionMinutes: distractionMinutes,
        signalToNoiseRatio: stat.focusSignalToNoiseRatio,
      );
    }).toList();
  }

  /// 生成学习效率热力图数据
  List<EfficiencyHeatmapData> _generateEfficiencyHeatmapData(
    List<StudyTimeStatistics> dailyStats,
  ) {
    final Map<String, EfficiencyHeatmapData> heatmapMap = {};

    for (final stat in dailyStats) {
      final date = DateTime.parse(stat.date);

      for (final session in stat.sessions) {
        if (session.category != '休息') {
          final hour = session.startTime.hour;
          final dayOfWeek = date.weekday;
          final key = '${dayOfWeek}_$hour';

          final efficiency = _calculateSessionEfficiency(session, stat);

          if (heatmapMap.containsKey(key)) {
            final existing = heatmapMap[key]!;
            final totalMinutes =
                existing.studyMinutes + session.durationMinutes;
            final weightedEfficiency =
                (existing.efficiencyValue * existing.studyMinutes +
                    efficiency * session.durationMinutes) /
                totalMinutes;

            heatmapMap[key] = EfficiencyHeatmapData(
              hour: hour,
              dayOfWeek: dayOfWeek,
              efficiencyValue: weightedEfficiency,
              studyMinutes: totalMinutes,
            );
          } else {
            heatmapMap[key] = EfficiencyHeatmapData(
              hour: hour,
              dayOfWeek: dayOfWeek,
              efficiencyValue: efficiency,
              studyMinutes: session.durationMinutes,
            );
          }
        }
      }
    }

    return heatmapMap.values.toList();
  }

  /// 计算会话效率
  double _calculateSessionEfficiency(
    StudySession session,
    StudyTimeStatistics stat,
  ) {
    // 基于完成度和ROI计算效率
    final completionRate = session.actualMinutes != null
        ? (session.actualMinutes! / session.plannedMinutes).clamp(0.0, 1.0)
        : 1.0;

    final roiNormalized = (stat.learningROI / 100).clamp(0.0, 1.0);

    return (completionRate * 0.6 + roiNormalized * 0.4) * 100;
  }

  /// 分析最佳学习时间段
  List<int> _analyzeBestStudyHours(List<StudyTimeStatistics> dailyStats) {
    final Map<int, double> hourEfficiency = {};
    final Map<int, int> hourCount = {};

    for (final stat in dailyStats) {
      for (final session in stat.sessions) {
        if (session.category != '休息') {
          final hour = session.startTime.hour;
          final efficiency = _calculateSessionEfficiency(session, stat);

          hourEfficiency[hour] = (hourEfficiency[hour] ?? 0.0) + efficiency;
          hourCount[hour] = (hourCount[hour] ?? 0) + 1;
        }
      }
    }

    // 计算平均效率
    final Map<int, double> averageEfficiency = {};
    for (final hour in hourEfficiency.keys) {
      averageEfficiency[hour] = hourEfficiency[hour]! / hourCount[hour]!;
    }

    // 找出效率最高的时间段（前3个）
    final sortedHours = averageEfficiency.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return sortedHours.take(3).map((e) => e.key).toList();
  }

  /// 计算当前连续学习天数
  int _calculateCurrentStreak(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0;

    final sortedStats = List<StudyTimeStatistics>.from(
      dailyStats,
    )..sort((a, b) => DateTime.parse(b.date).compareTo(DateTime.parse(a.date)));

    int streak = 0;
    DateTime? lastDate;

    for (final stat in sortedStats) {
      final currentDate = DateTime.parse(stat.date);

      if (lastDate == null) {
        // 检查是否是今天或昨天
        final now = DateTime.now();
        final today = DateTime(now.year, now.month, now.day);
        final yesterday = today.subtract(const Duration(days: 1));

        if (currentDate.isAtSameMomentAs(today) ||
            currentDate.isAtSameMomentAs(yesterday)) {
          streak = 1;
          lastDate = currentDate;
        } else {
          break;
        }
      } else {
        final daysDiff = lastDate.difference(currentDate).inDays;
        if (daysDiff == 1) {
          streak++;
          lastDate = currentDate;
        } else {
          break;
        }
      }
    }

    return streak;
  }

  /// 计算最长连续学习天数
  int _calculateLongestStreak(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0;

    final sortedStats = List<StudyTimeStatistics>.from(
      dailyStats,
    )..sort((a, b) => DateTime.parse(a.date).compareTo(DateTime.parse(b.date)));

    int maxStreak = 0;
    int currentStreak = 0;
    DateTime? lastDate;

    for (final stat in sortedStats) {
      final currentDate = DateTime.parse(stat.date);

      if (lastDate == null) {
        currentStreak = 1;
        lastDate = currentDate;
      } else {
        final daysDiff = currentDate.difference(lastDate).inDays;
        if (daysDiff == 1) {
          currentStreak++;
        } else {
          maxStreak = math.max(maxStreak, currentStreak);
          currentStreak = 1;
        }
        lastDate = currentDate;
      }
    }

    return math.max(maxStreak, currentStreak);
  }

  /// 计算学习一致性评分
  double _calculateConsistencyScore(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0.0;

    // 计算学习天数占总天数的比例
    final totalDays = 30; // 过去30天
    final studyDays = dailyStats.length;
    final consistencyRatio = studyDays / totalDays;

    // 计算学习时长的标准差（一致性）
    final studyMinutes = dailyStats
        .map((stat) => stat.totalStudyMinutes.toDouble())
        .toList();
    final mean =
        studyMinutes.fold(0.0, (sum, minutes) => sum + minutes) /
        studyMinutes.length;
    final variance =
        studyMinutes.fold(
          0.0,
          (sum, minutes) => sum + math.pow(minutes - mean, 2),
        ) /
        studyMinutes.length;
    final standardDeviation = math.sqrt(variance);

    // 一致性评分：频率 * 稳定性
    final stabilityScore = mean > 0
        ? (1 - (standardDeviation / mean)).clamp(0.0, 1.0)
        : 0.0;

    return (consistencyRatio * 0.7 + stabilityScore * 0.3) * 100;
  }

  /// 计算学习强度评分
  double _calculateIntensityScore(List<StudyTimeStatistics> dailyStats) {
    if (dailyStats.isEmpty) return 0.0;

    // 计算平均每日学习时长
    final totalMinutes = dailyStats.fold<int>(
      0,
      (sum, stat) => sum + stat.totalStudyMinutes,
    );
    final averageMinutes = totalMinutes / dailyStats.length;

    // 基于平均学习时长计算强度评分
    // 假设理想的学习时长是4小时（240分钟）
    final idealMinutes = 240.0;
    final intensityRatio = (averageMinutes / idealMinutes).clamp(0.0, 1.0);

    // 考虑学习ROI的影响
    final averageROI = _calculateAverageLearningROI(dailyStats);
    final roiBonus = (averageROI / 100).clamp(0.0, 0.2); // 最多20%的加成

    return ((intensityRatio + roiBonus) * 100).clamp(0.0, 100.0);
  }
}
