import 'package:json_annotation/json_annotation.dart';

part 'learning_report_models.g.dart';

/// 时间范围枚举
enum TimeRange {
  today('今日'),
  thisWeek('本周'),
  thisMonth('本月'),
  custom('自定义');

  const TimeRange(this.label);
  final String label;
}

/// 学习报告数据模型
@JsonSerializable()
class LearningReportData {
  /// 时间范围
  final TimeRange timeRange;

  /// 开始日期
  final DateTime startDate;

  /// 结束日期
  final DateTime endDate;

  /// 总学习时长（分钟）
  final int totalStudyMinutes;

  /// 总完成任务数
  final int totalCompletedTasks;

  /// 平均学习ROI
  final double averageLearningROI;

  /// 总并行时间（分钟）
  final int totalParallelTimeMinutes;

  /// 平均信噪比
  final double averageFocusSignalToNoiseRatio;

  /// 学习效率等级
  final String efficiencyLevel;

  /// 连续学习天数
  final int streakDays;

  /// 学科分布数据
  final List<SubjectDistribution> subjectDistribution;

  /// 每日学习时长数据
  final List<DailyStudyData> dailyStudyData;

  /// 学习ROI趋势数据
  final List<ROITrendData> roiTrendData;

  /// 专注度分析数据
  final List<FocusAnalysisData> focusAnalysisData;

  /// 学习效率热力图数据
  final List<EfficiencyHeatmapData> efficiencyHeatmapData;

  const LearningReportData({
    required this.timeRange,
    required this.startDate,
    required this.endDate,
    required this.totalStudyMinutes,
    required this.totalCompletedTasks,
    required this.averageLearningROI,
    required this.totalParallelTimeMinutes,
    required this.averageFocusSignalToNoiseRatio,
    required this.efficiencyLevel,
    required this.streakDays,
    required this.subjectDistribution,
    required this.dailyStudyData,
    required this.roiTrendData,
    required this.focusAnalysisData,
    required this.efficiencyHeatmapData,
  });

  factory LearningReportData.fromJson(Map<String, dynamic> json) =>
      _$LearningReportDataFromJson(json);

  Map<String, dynamic> toJson() => _$LearningReportDataToJson(this);

  /// 获取格式化的总学习时长
  String get formattedTotalStudyTime {
    final hours = totalStudyMinutes ~/ 60;
    final minutes = totalStudyMinutes % 60;
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  /// 获取任务完成率
  double get taskCompletionRate {
    if (totalCompletedTasks == 0) return 0.0;
    // 这里可以根据实际的计划任务数来计算完成率
    // 暂时使用一个简化的计算方式
    return (totalCompletedTasks / (totalCompletedTasks + 2)).clamp(0.0, 1.0);
  }

  /// 获取并行时间收益率
  double get parallelTimeBenefitRate {
    if (totalStudyMinutes == 0) return 0.0;
    return (totalParallelTimeMinutes / totalStudyMinutes) * 100;
  }
}

/// 学科分布数据
@JsonSerializable()
class SubjectDistribution {
  /// 学科名称
  final String subject;

  /// 学习时长（分钟）
  final int studyMinutes;

  /// 颜色值（十六进制）
  final String colorHex;

  const SubjectDistribution({
    required this.subject,
    required this.studyMinutes,
    required this.colorHex,
  });

  factory SubjectDistribution.fromJson(Map<String, dynamic> json) =>
      _$SubjectDistributionFromJson(json);

  Map<String, dynamic> toJson() => _$SubjectDistributionToJson(this);

  /// 获取百分比
  double getPercentage(int totalMinutes) {
    if (totalMinutes == 0) return 0.0;
    return (studyMinutes / totalMinutes) * 100;
  }
}

/// 每日学习数据
@JsonSerializable()
class DailyStudyData {
  /// 日期
  final DateTime date;

  /// 学习时长（分钟）
  final int studyMinutes;

  /// 完成任务数
  final int completedTasks;

  /// 学习ROI
  final double learningROI;

  /// 信噪比
  final double focusSignalToNoiseRatio;

  const DailyStudyData({
    required this.date,
    required this.studyMinutes,
    required this.completedTasks,
    required this.learningROI,
    required this.focusSignalToNoiseRatio,
  });

  factory DailyStudyData.fromJson(Map<String, dynamic> json) =>
      _$DailyStudyDataFromJson(json);

  Map<String, dynamic> toJson() => _$DailyStudyDataToJson(this);

  /// 获取格式化的日期字符串
  String get formattedDate {
    return '${date.month}/${date.day}';
  }

  /// 获取学习时长（小时）
  double get studyHours {
    return studyMinutes / 60.0;
  }
}

/// ROI趋势数据
@JsonSerializable()
class ROITrendData {
  /// 日期
  final DateTime date;

  /// ROI值
  final double roiValue;

  /// 7天移动平均
  final double movingAverage;

  const ROITrendData({
    required this.date,
    required this.roiValue,
    required this.movingAverage,
  });

  factory ROITrendData.fromJson(Map<String, dynamic> json) =>
      _$ROITrendDataFromJson(json);

  Map<String, dynamic> toJson() => _$ROITrendDataToJson(this);
}

/// 专注度分析数据
@JsonSerializable()
class FocusAnalysisData {
  /// 日期
  final DateTime date;

  /// 专注时间（分钟）
  final int focusMinutes;

  /// 干扰时间（分钟）
  final int distractionMinutes;

  /// 信噪比
  final double signalToNoiseRatio;

  const FocusAnalysisData({
    required this.date,
    required this.focusMinutes,
    required this.distractionMinutes,
    required this.signalToNoiseRatio,
  });

  factory FocusAnalysisData.fromJson(Map<String, dynamic> json) =>
      _$FocusAnalysisDataFromJson(json);

  Map<String, dynamic> toJson() => _$FocusAnalysisDataToJson(this);

  /// 获取专注度百分比
  double get focusPercentage {
    final totalMinutes = focusMinutes + distractionMinutes;
    if (totalMinutes == 0) return 0.0;
    return (focusMinutes / totalMinutes) * 100;
  }
}

/// 学习效率热力图数据
@JsonSerializable()
class EfficiencyHeatmapData {
  /// 小时（0-23）
  final int hour;

  /// 星期几（1-7，1为周一）
  final int dayOfWeek;

  /// 效率值（0-100）
  final double efficiencyValue;

  /// 学习时长（分钟）
  final int studyMinutes;

  const EfficiencyHeatmapData({
    required this.hour,
    required this.dayOfWeek,
    required this.efficiencyValue,
    required this.studyMinutes,
  });

  factory EfficiencyHeatmapData.fromJson(Map<String, dynamic> json) =>
      _$EfficiencyHeatmapDataFromJson(json);

  Map<String, dynamic> toJson() => _$EfficiencyHeatmapDataToJson(this);

  /// 获取星期几的中文名称
  String get dayOfWeekName {
    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    return dayNames[dayOfWeek - 1];
  }

  /// 获取小时的显示格式
  String get hourDisplay {
    return '${hour.toString().padLeft(2, '0')}:00';
  }
}

/// 学习习惯分析数据
@JsonSerializable()
class LearningHabitAnalysis {
  /// 最佳学习时间段
  final List<int> bestStudyHours;

  /// 平均每日学习时长（分钟）
  final double averageDailyStudyMinutes;

  /// 最长连续学习天数
  final int longestStreak;

  /// 当前连续学习天数
  final int currentStreak;

  /// 学习一致性评分（0-100）
  final double consistencyScore;

  /// 学习强度评分（0-100）
  final double intensityScore;

  const LearningHabitAnalysis({
    required this.bestStudyHours,
    required this.averageDailyStudyMinutes,
    required this.longestStreak,
    required this.currentStreak,
    required this.consistencyScore,
    required this.intensityScore,
  });

  factory LearningHabitAnalysis.fromJson(Map<String, dynamic> json) =>
      _$LearningHabitAnalysisFromJson(json);

  Map<String, dynamic> toJson() => _$LearningHabitAnalysisToJson(this);

  /// 获取最佳学习时间段的描述
  String get bestStudyTimeDescription {
    if (bestStudyHours.isEmpty) return '暂无数据';

    final sortedHours = List<int>.from(bestStudyHours)..sort();
    if (sortedHours.length == 1) {
      return '${sortedHours.first}:00';
    } else if (sortedHours.length == 2) {
      return '${sortedHours.first}:00-${sortedHours.last}:00';
    } else {
      return '${sortedHours.first}:00-${sortedHours.last}:00等${sortedHours.length}个时段';
    }
  }

  /// 获取学习习惯等级
  String get habitLevel {
    final averageScore = (consistencyScore + intensityScore) / 2;
    if (averageScore >= 80) return '优秀';
    if (averageScore >= 60) return '良好';
    if (averageScore >= 40) return '一般';
    return '需改进';
  }
}
