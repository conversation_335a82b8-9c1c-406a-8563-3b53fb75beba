// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'learning_report_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LearningReportData _$LearningReportDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('LearningReportData', json, ($checkedConvert) {
  final val = LearningReportData(
    timeRange: $checkedConvert(
      'timeRange',
      (v) => $enumDecode(_$TimeRangeEnumMap, v),
    ),
    startDate: $checkedConvert('startDate', (v) => DateTime.parse(v as String)),
    endDate: $checkedConvert('endDate', (v) => DateTime.parse(v as String)),
    totalStudyMinutes: $checkedConvert(
      'totalStudyMinutes',
      (v) => (v as num).toInt(),
    ),
    totalCompletedTasks: $checkedConvert(
      'totalCompletedTasks',
      (v) => (v as num).toInt(),
    ),
    averageLearningROI: $checkedConvert(
      'averageLearningROI',
      (v) => (v as num).toDouble(),
    ),
    totalParallelTimeMinutes: $checkedConvert(
      'totalParallelTimeMinutes',
      (v) => (v as num).toInt(),
    ),
    averageFocusSignalToNoiseRatio: $checkedConvert(
      'averageFocusSignalToNoiseRatio',
      (v) => (v as num).toDouble(),
    ),
    efficiencyLevel: $checkedConvert('efficiencyLevel', (v) => v as String),
    streakDays: $checkedConvert('streakDays', (v) => (v as num).toInt()),
    subjectDistribution: $checkedConvert(
      'subjectDistribution',
      (v) => (v as List<dynamic>)
          .map((e) => SubjectDistribution.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    dailyStudyData: $checkedConvert(
      'dailyStudyData',
      (v) => (v as List<dynamic>)
          .map((e) => DailyStudyData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    roiTrendData: $checkedConvert(
      'roiTrendData',
      (v) => (v as List<dynamic>)
          .map((e) => ROITrendData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    focusAnalysisData: $checkedConvert(
      'focusAnalysisData',
      (v) => (v as List<dynamic>)
          .map((e) => FocusAnalysisData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    efficiencyHeatmapData: $checkedConvert(
      'efficiencyHeatmapData',
      (v) => (v as List<dynamic>)
          .map((e) => EfficiencyHeatmapData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$LearningReportDataPerFieldToJson {
  // ignore: unused_element
  static Object? timeRange(TimeRange instance) => _$TimeRangeEnumMap[instance]!;
  // ignore: unused_element
  static Object? startDate(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? endDate(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? totalStudyMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? totalCompletedTasks(int instance) => instance;
  // ignore: unused_element
  static Object? averageLearningROI(double instance) => instance;
  // ignore: unused_element
  static Object? totalParallelTimeMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? averageFocusSignalToNoiseRatio(double instance) => instance;
  // ignore: unused_element
  static Object? efficiencyLevel(String instance) => instance;
  // ignore: unused_element
  static Object? streakDays(int instance) => instance;
  // ignore: unused_element
  static Object? subjectDistribution(List<SubjectDistribution> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? dailyStudyData(List<DailyStudyData> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? roiTrendData(List<ROITrendData> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? focusAnalysisData(List<FocusAnalysisData> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? efficiencyHeatmapData(List<EfficiencyHeatmapData> instance) =>
      instance.map((e) => e.toJson()).toList();
}

Map<String, dynamic> _$LearningReportDataToJson(LearningReportData instance) =>
    <String, dynamic>{
      'timeRange': _$TimeRangeEnumMap[instance.timeRange]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'totalStudyMinutes': instance.totalStudyMinutes,
      'totalCompletedTasks': instance.totalCompletedTasks,
      'averageLearningROI': instance.averageLearningROI,
      'totalParallelTimeMinutes': instance.totalParallelTimeMinutes,
      'averageFocusSignalToNoiseRatio': instance.averageFocusSignalToNoiseRatio,
      'efficiencyLevel': instance.efficiencyLevel,
      'streakDays': instance.streakDays,
      'subjectDistribution': instance.subjectDistribution
          .map((e) => e.toJson())
          .toList(),
      'dailyStudyData': instance.dailyStudyData.map((e) => e.toJson()).toList(),
      'roiTrendData': instance.roiTrendData.map((e) => e.toJson()).toList(),
      'focusAnalysisData': instance.focusAnalysisData
          .map((e) => e.toJson())
          .toList(),
      'efficiencyHeatmapData': instance.efficiencyHeatmapData
          .map((e) => e.toJson())
          .toList(),
    };

const _$TimeRangeEnumMap = {
  TimeRange.today: 'today',
  TimeRange.thisWeek: 'thisWeek',
  TimeRange.thisMonth: 'thisMonth',
  TimeRange.custom: 'custom',
};

SubjectDistribution _$SubjectDistributionFromJson(Map<String, dynamic> json) =>
    $checkedCreate('SubjectDistribution', json, ($checkedConvert) {
      final val = SubjectDistribution(
        subject: $checkedConvert('subject', (v) => v as String),
        studyMinutes: $checkedConvert(
          'studyMinutes',
          (v) => (v as num).toInt(),
        ),
        colorHex: $checkedConvert('colorHex', (v) => v as String),
      );
      return val;
    });

// ignore: unused_element
abstract class _$SubjectDistributionPerFieldToJson {
  // ignore: unused_element
  static Object? subject(String instance) => instance;
  // ignore: unused_element
  static Object? studyMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? colorHex(String instance) => instance;
}

Map<String, dynamic> _$SubjectDistributionToJson(
  SubjectDistribution instance,
) => <String, dynamic>{
  'subject': instance.subject,
  'studyMinutes': instance.studyMinutes,
  'colorHex': instance.colorHex,
};

DailyStudyData _$DailyStudyDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('DailyStudyData', json, ($checkedConvert) {
  final val = DailyStudyData(
    date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
    studyMinutes: $checkedConvert('studyMinutes', (v) => (v as num).toInt()),
    completedTasks: $checkedConvert(
      'completedTasks',
      (v) => (v as num).toInt(),
    ),
    learningROI: $checkedConvert('learningROI', (v) => (v as num).toDouble()),
    focusSignalToNoiseRatio: $checkedConvert(
      'focusSignalToNoiseRatio',
      (v) => (v as num).toDouble(),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$DailyStudyDataPerFieldToJson {
  // ignore: unused_element
  static Object? date(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? studyMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? completedTasks(int instance) => instance;
  // ignore: unused_element
  static Object? learningROI(double instance) => instance;
  // ignore: unused_element
  static Object? focusSignalToNoiseRatio(double instance) => instance;
}

Map<String, dynamic> _$DailyStudyDataToJson(DailyStudyData instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'studyMinutes': instance.studyMinutes,
      'completedTasks': instance.completedTasks,
      'learningROI': instance.learningROI,
      'focusSignalToNoiseRatio': instance.focusSignalToNoiseRatio,
    };

ROITrendData _$ROITrendDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('ROITrendData', json, ($checkedConvert) {
      final val = ROITrendData(
        date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
        roiValue: $checkedConvert('roiValue', (v) => (v as num).toDouble()),
        movingAverage: $checkedConvert(
          'movingAverage',
          (v) => (v as num).toDouble(),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$ROITrendDataPerFieldToJson {
  // ignore: unused_element
  static Object? date(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? roiValue(double instance) => instance;
  // ignore: unused_element
  static Object? movingAverage(double instance) => instance;
}

Map<String, dynamic> _$ROITrendDataToJson(ROITrendData instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'roiValue': instance.roiValue,
      'movingAverage': instance.movingAverage,
    };

FocusAnalysisData _$FocusAnalysisDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('FocusAnalysisData', json, ($checkedConvert) {
      final val = FocusAnalysisData(
        date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
        focusMinutes: $checkedConvert(
          'focusMinutes',
          (v) => (v as num).toInt(),
        ),
        distractionMinutes: $checkedConvert(
          'distractionMinutes',
          (v) => (v as num).toInt(),
        ),
        signalToNoiseRatio: $checkedConvert(
          'signalToNoiseRatio',
          (v) => (v as num).toDouble(),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$FocusAnalysisDataPerFieldToJson {
  // ignore: unused_element
  static Object? date(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? focusMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? distractionMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? signalToNoiseRatio(double instance) => instance;
}

Map<String, dynamic> _$FocusAnalysisDataToJson(FocusAnalysisData instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'focusMinutes': instance.focusMinutes,
      'distractionMinutes': instance.distractionMinutes,
      'signalToNoiseRatio': instance.signalToNoiseRatio,
    };

EfficiencyHeatmapData _$EfficiencyHeatmapDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('EfficiencyHeatmapData', json, ($checkedConvert) {
  final val = EfficiencyHeatmapData(
    hour: $checkedConvert('hour', (v) => (v as num).toInt()),
    dayOfWeek: $checkedConvert('dayOfWeek', (v) => (v as num).toInt()),
    efficiencyValue: $checkedConvert(
      'efficiencyValue',
      (v) => (v as num).toDouble(),
    ),
    studyMinutes: $checkedConvert('studyMinutes', (v) => (v as num).toInt()),
  );
  return val;
});

// ignore: unused_element
abstract class _$EfficiencyHeatmapDataPerFieldToJson {
  // ignore: unused_element
  static Object? hour(int instance) => instance;
  // ignore: unused_element
  static Object? dayOfWeek(int instance) => instance;
  // ignore: unused_element
  static Object? efficiencyValue(double instance) => instance;
  // ignore: unused_element
  static Object? studyMinutes(int instance) => instance;
}

Map<String, dynamic> _$EfficiencyHeatmapDataToJson(
  EfficiencyHeatmapData instance,
) => <String, dynamic>{
  'hour': instance.hour,
  'dayOfWeek': instance.dayOfWeek,
  'efficiencyValue': instance.efficiencyValue,
  'studyMinutes': instance.studyMinutes,
};

LearningHabitAnalysis _$LearningHabitAnalysisFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('LearningHabitAnalysis', json, ($checkedConvert) {
  final val = LearningHabitAnalysis(
    bestStudyHours: $checkedConvert(
      'bestStudyHours',
      (v) => (v as List<dynamic>).map((e) => (e as num).toInt()).toList(),
    ),
    averageDailyStudyMinutes: $checkedConvert(
      'averageDailyStudyMinutes',
      (v) => (v as num).toDouble(),
    ),
    longestStreak: $checkedConvert('longestStreak', (v) => (v as num).toInt()),
    currentStreak: $checkedConvert('currentStreak', (v) => (v as num).toInt()),
    consistencyScore: $checkedConvert(
      'consistencyScore',
      (v) => (v as num).toDouble(),
    ),
    intensityScore: $checkedConvert(
      'intensityScore',
      (v) => (v as num).toDouble(),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$LearningHabitAnalysisPerFieldToJson {
  // ignore: unused_element
  static Object? bestStudyHours(List<int> instance) => instance;
  // ignore: unused_element
  static Object? averageDailyStudyMinutes(double instance) => instance;
  // ignore: unused_element
  static Object? longestStreak(int instance) => instance;
  // ignore: unused_element
  static Object? currentStreak(int instance) => instance;
  // ignore: unused_element
  static Object? consistencyScore(double instance) => instance;
  // ignore: unused_element
  static Object? intensityScore(double instance) => instance;
}

Map<String, dynamic> _$LearningHabitAnalysisToJson(
  LearningHabitAnalysis instance,
) => <String, dynamic>{
  'bestStudyHours': instance.bestStudyHours,
  'averageDailyStudyMinutes': instance.averageDailyStudyMinutes,
  'longestStreak': instance.longestStreak,
  'currentStreak': instance.currentStreak,
  'consistencyScore': instance.consistencyScore,
  'intensityScore': instance.intensityScore,
};
