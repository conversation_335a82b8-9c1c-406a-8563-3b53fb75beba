import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../../core/data/pao_exercises_data.dart';

/// 自定义动作分类模型
class CustomExerciseCategory {
  final String id;
  final String name;
  final String icon;
  final String? description;
  final DateTime createdAt;
  final DateTime lastModified;
  final Map<String, PAOExercise> exercises;

  CustomExerciseCategory({
    required this.id,
    required this.name,
    required this.icon,
    this.description,
    required this.createdAt,
    required this.lastModified,
    Map<String, PAOExercise>? exercises,
  }) : exercises = exercises ?? {};

  /// 从JSON创建实例
  factory CustomExerciseCategory.fromJson(Map<String, dynamic> json) {
    final exercisesMap = <String, PAOExercise>{};
    if (json['exercises'] != null) {
      final exercisesJson = json['exercises'] as Map<String, dynamic>;
      exercisesJson.forEach((key, value) {
        exercisesMap[key] = PAOExercise(
          letter: value['letter'] ?? key,
          nameEn: value['nameEn'] ?? '',
          nameCn: value['nameCn'] ?? '',
          category: value['category'] ?? json['name'],
          scene: value['scene'] ?? '简单活动',
          description: value['description'] ?? '',
          keywords: List<String>.from(value['keywords'] ?? []),
        );
      });
    }

    return CustomExerciseCategory(
      id: json['id'],
      name: json['name'],
      icon: json['icon'],
      description: json['description'],
      createdAt: DateTime.parse(json['createdAt']),
      lastModified: DateTime.parse(json['lastModified']),
      exercises: exercisesMap,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final exercisesJson = <String, dynamic>{};
    exercises.forEach((key, exercise) {
      exercisesJson[key] = {
        'letter': exercise.letter,
        'nameEn': exercise.nameEn,
        'nameCn': exercise.nameCn,
        'category': exercise.category,
        'scene': exercise.scene,
        'description': exercise.description,
        'keywords': exercise.keywords,
      };
    });

    return {
      'id': id,
      'name': name,
      'icon': icon,
      'description': description,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'exercises': exercisesJson,
    };
  }

  /// 创建副本
  CustomExerciseCategory copyWith({
    String? id,
    String? name,
    String? icon,
    String? description,
    DateTime? createdAt,
    DateTime? lastModified,
    Map<String, PAOExercise>? exercises,
  }) {
    return CustomExerciseCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      icon: icon ?? this.icon,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
      exercises: exercises ?? Map.from(this.exercises),
    );
  }
}

/// 自定义动作分类管理器
class CustomExerciseCategoryManager {
  static const String _storageKey = 'custom_exercise_categories';
  
  List<CustomExerciseCategory> _categories = [];
  
  List<CustomExerciseCategory> get categories => List.unmodifiable(_categories);
  
  /// 从本地存储加载分类
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString != null) {
        final jsonList = json.decode(jsonString) as List;
        _categories = jsonList
            .map((json) => CustomExerciseCategory.fromJson(json))
            .toList();
        print('📚 成功加载 ${_categories.length} 个自定义动作分类');
      } else {
        _categories = [];
        print('📚 未找到自定义动作分类数据，初始化为空列表');
      }
    } catch (e) {
      print('❌ 加载自定义动作分类失败: $e');
      _categories = [];
    }
  }
  
  /// 保存分类到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _categories.map((category) => category.toJson()).toList();
      final jsonString = json.encode(jsonList);
      
      await prefs.setString(_storageKey, jsonString);
      print('💾 成功保存 ${_categories.length} 个自定义动作分类');
    } catch (e) {
      print('❌ 保存自定义动作分类失败: $e');
    }
  }
  
  /// 添加新分类
  Future<void> addCategory(CustomExerciseCategory category) async {
    _categories.add(category);
    await saveToStorage();
  }
  
  /// 更新分类
  Future<void> updateCategory(CustomExerciseCategory updatedCategory) async {
    final index = _categories.indexWhere((cat) => cat.id == updatedCategory.id);
    if (index != -1) {
      _categories[index] = updatedCategory;
      await saveToStorage();
    }
  }
  
  /// 删除分类
  Future<void> deleteCategory(String categoryId) async {
    _categories.removeWhere((cat) => cat.id == categoryId);
    await saveToStorage();
  }
  
  /// 根据ID查找分类
  CustomExerciseCategory? findCategoryById(String id) {
    try {
      return _categories.firstWhere((cat) => cat.id == id);
    } catch (e) {
      return null;
    }
  }
  
  /// 根据名称查找分类
  CustomExerciseCategory? findCategoryByName(String name) {
    try {
      return _categories.firstWhere((cat) => cat.name == name);
    } catch (e) {
      return null;
    }
  }
  
  /// 检查分类名称是否已存在
  bool isCategoryNameExists(String name, {String? excludeId}) {
    return _categories.any((cat) => 
        cat.name == name && (excludeId == null || cat.id != excludeId));
  }
  
  /// 获取所有分类名称
  List<String> getAllCategoryNames() {
    return _categories.map((cat) => cat.name).toList();
  }
  
  /// 清空所有分类
  Future<void> clearAllCategories() async {
    _categories.clear();
    await saveToStorage();
  }
}
