import '../../core/data/pao_exercises_data.dart';

/// 自定义动作库模型
class CustomActionLibrary {
  final String id;
  final String name;
  final String description;
  final String category;
  final DateTime createdAt;
  final DateTime lastModified;
  final Map<String, CustomPAOAction> actions; // A-Z字母映射

  CustomActionLibrary({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.createdAt,
    required this.lastModified,
    Map<String, CustomPAOAction>? actions,
  }) : actions = actions ?? {};

  /// 从JSON创建实例
  factory CustomActionLibrary.fromJson(Map<String, dynamic> json) {
    final actionsMap = <String, CustomPAOAction>{};
    if (json['actions'] != null) {
      final actionsJson = json['actions'] as Map<String, dynamic>;
      actionsJson.forEach((key, value) {
        actionsMap[key] = CustomPAOAction.fromJson(value);
      });
    }

    return CustomActionLibrary(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String? ?? '',
      category: json['category'] as String? ?? '默认分类',
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
      actions: actionsMap,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    final actionsJson = <String, dynamic>{};
    actions.forEach((key, value) {
      actionsJson[key] = value.toJson();
    });

    return {
      'id': id,
      'name': name,
      'description': description,
      'category': category,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
      'actions': actionsJson,
    };
  }

  /// 复制并更新
  CustomActionLibrary copyWith({
    String? name,
    String? description,
    String? category,
    DateTime? lastModified,
    Map<String, CustomPAOAction>? actions,
  }) {
    return CustomActionLibrary(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      createdAt: createdAt,
      lastModified: lastModified ?? DateTime.now(),
      actions: actions ?? Map.from(this.actions),
    );
  }

  /// 获取已设置动作的字母数量
  int get completedActionsCount => actions.length;

  /// 获取完成度百分比
  double get completionPercentage => completedActionsCount / 26.0;

  /// 检查字母是否已设置动作
  bool hasActionForLetter(String letter) {
    return actions.containsKey(letter.toUpperCase());
  }

  /// 获取字母对应的动作
  CustomPAOAction? getActionForLetter(String letter) {
    return actions[letter.toUpperCase()];
  }

  /// 设置字母对应的动作
  void setActionForLetter(String letter, CustomPAOAction action) {
    actions[letter.toUpperCase()] = action;
  }

  /// 移除字母对应的动作
  void removeActionForLetter(String letter) {
    actions.remove(letter.toUpperCase());
  }
}

/// 自定义PAO动作模型
class CustomPAOAction {
  final String letter;
  final String nameEn;
  final String nameCn;
  final String description;
  final String category;
  final String scene; // '简单活动' | '集中训练' | '简单/集中'
  final List<String> keywords;

  const CustomPAOAction({
    required this.letter,
    required this.nameEn,
    required this.nameCn,
    required this.description,
    required this.category,
    required this.scene,
    this.keywords = const [],
  });

  /// 从JSON创建实例
  factory CustomPAOAction.fromJson(Map<String, dynamic> json) {
    return CustomPAOAction(
      letter: json['letter'] as String,
      nameEn: json['nameEn'] as String,
      nameCn: json['nameCn'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      scene: json['scene'] as String,
      keywords: List<String>.from(json['keywords'] ?? []),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'letter': letter,
      'nameEn': nameEn,
      'nameCn': nameCn,
      'description': description,
      'category': category,
      'scene': scene,
      'keywords': keywords,
    };
  }

  /// 转换为PAOExercise（用于兼容现有系统）
  PAOExercise toPAOExercise() {
    return PAOExercise(
      letter: letter,
      nameEn: nameEn,
      nameCn: nameCn,
      category: category,
      scene: scene,
      description: description,
      keywords: keywords,
    );
  }

  /// 从PAOExercise创建（用于从预设动作复制）
  factory CustomPAOAction.fromPAOExercise(PAOExercise exercise) {
    return CustomPAOAction(
      letter: exercise.letter,
      nameEn: exercise.nameEn,
      nameCn: exercise.nameCn,
      description: exercise.description,
      category: exercise.category,
      scene: exercise.scene,
      keywords: exercise.keywords,
    );
  }

  /// 复制并更新
  CustomPAOAction copyWith({
    String? nameEn,
    String? nameCn,
    String? description,
    String? category,
    String? scene,
    List<String>? keywords,
  }) {
    return CustomPAOAction(
      letter: letter,
      nameEn: nameEn ?? this.nameEn,
      nameCn: nameCn ?? this.nameCn,
      description: description ?? this.description,
      category: category ?? this.category,
      scene: scene ?? this.scene,
      keywords: keywords ?? this.keywords,
    );
  }

  /// 获取场景列表（兼容性）
  List<String> get scenarios {
    if (scene.contains('/')) {
      return scene.split('/').map((s) => s.trim()).toList();
    } else if (scene == '简单/集中') {
      return ['简单活动', '集中训练'];
    } else {
      return [scene];
    }
  }

  /// 获取名称（兼容性）
  String get name => nameCn;
}

/// 默认动作模板
class DefaultActionTemplates {
  /// 获取所有可用的动作分类
  static List<String> getAvailableCategories() {
    return ['健身', '瑜伽', '养生', '篮球', '足球', '拉伸', '护眼', '自定义'];
  }

  /// 获取所有可用的场景
  static List<String> getAvailableScenes() {
    return ['简单活动', '集中训练', '简单/集中'];
  }

  /// 创建空白动作模板
  static CustomPAOAction createEmptyTemplate(String letter) {
    return CustomPAOAction(
      letter: letter.toUpperCase(),
      nameEn: '',
      nameCn: '',
      description: '',
      category: '自定义',
      scene: '简单活动',
      keywords: [],
    );
  }

  /// 从预设动作库获取字母对应的默认动作
  static CustomPAOAction? getDefaultActionForLetter(String letter) {
    final allExercises = PAOExercisesData.getAllExercises();
    final exercise = allExercises[letter.toUpperCase()];
    if (exercise != null) {
      return CustomPAOAction.fromPAOExercise(exercise);
    }
    return null;
  }

  /// 获取推荐的动作关键词
  static List<String> getRecommendedKeywords(String letter) {
    final defaultAction = getDefaultActionForLetter(letter);
    if (defaultAction != null) {
      return defaultAction.keywords;
    }

    // 如果没有默认动作，返回一些通用关键词
    return ['Action', 'Activity', 'Achievement'];
  }
}
