import 'package:flutter/material.dart';
import 'custom_action_library.dart';
import 'custom_action_library_service.dart';

/// 创建自定义动作库对话框
class CreateCustomLibraryDialog extends StatefulWidget {
  final CustomActionLibraryService customLibraryService;
  final Function(CustomActionLibrary) onLibraryCreated;

  const CreateCustomLibraryDialog({
    super.key,
    required this.customLibraryService,
    required this.onLibraryCreated,
  });

  @override
  State<CreateCustomLibraryDialog> createState() =>
      _CreateCustomLibraryDialogState();
}

class _CreateCustomLibraryDialogState extends State<CreateCustomLibraryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();
  bool _isCreating = false;

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        constraints: BoxConstraints(
          maxWidth: 400,
          maxHeight: MediaQuery.of(context).size.height * 0.8, // 限制最大高度
        ),
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题
                Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFFE03E3E).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.library_add_outlined,
                        color: Color(0xFFE03E3E),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '创建自定义动作库',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF37352F),
                            ),
                          ),
                          Text(
                            '为您的专属动作创建一个新的动作库',
                            style: TextStyle(
                              fontSize: 14,
                              color: Color(0xFF9B9A97),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: Color(0xFF9B9A97)),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 32,
                        minHeight: 32,
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // 表单
                Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 动作库名称
                      const Text(
                        '动作库名称',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _nameController,
                        decoration: InputDecoration(
                          hintText: '例如：我的健身动作库',
                          hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(0xFFE3E2E0),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(0xFFE03E3E),
                            ),
                          ),
                          filled: true,
                          fillColor: const Color(0xFFF7F6F3),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入动作库名称';
                          }
                          if (value.trim().length < 2) {
                            return '动作库名称至少需要2个字符';
                          }
                          if (widget.customLibraryService.isLibraryNameExists(
                            value.trim(),
                          )) {
                            return '该名称已存在，请选择其他名称';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 16),

                      // 动作库描述
                      const Text(
                        '动作库描述',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF37352F),
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _descriptionController,
                        maxLines: 3,
                        decoration: InputDecoration(
                          hintText: '描述这个动作库的用途和特点...',
                          hintStyle: const TextStyle(color: Color(0xFF9B9A97)),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(0xFFE3E2E0),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(
                              color: Color(0xFFE03E3E),
                            ),
                          ),
                          filled: true,
                          fillColor: const Color(0xFFF7F6F3),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                        validator: (value) {
                          if (value != null && value.trim().length > 200) {
                            return '描述不能超过200个字符';
                          }
                          return null;
                        },
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 24),

                // 操作按钮
                Row(
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: _isCreating
                            ? null
                            : () => Navigator.of(context).pop(),
                        style: TextButton.styleFrom(
                          backgroundColor: Colors.white, // 白色背景
                          padding: const EdgeInsets.symmetric(
                            vertical: 14,
                          ), // 增加垂直内边距
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12), // 使用12px圆角
                            side: const BorderSide(
                              color: Color(0xFFE3E2E0),
                            ), // 添加边框
                          ),
                        ),
                        child: const Text(
                          '取消',
                          style: TextStyle(
                            color: Color(0xFF9B9A97),
                            fontSize: 16,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isCreating ? null : _createLibrary,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: const Color(0xFFE03E3E),
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            vertical: 14,
                          ), // 增加垂直内边距
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12), // 使用12px圆角
                          ),
                          elevation: 0,
                        ),
                        child: _isCreating
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    Colors.white,
                                  ),
                                ),
                              )
                            : const Text(
                                '创建',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 创建动作库
  Future<void> _createLibrary() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isCreating = true;
    });

    try {
      final library = await widget.customLibraryService.createLibrary(
        name: _nameController.text.trim(),
        description: _descriptionController.text.trim(),
      );

      if (mounted) {
        Navigator.of(context).pop();
        widget.onLibraryCreated(library);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('创建失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isCreating = false;
        });
      }
    }
  }
}
