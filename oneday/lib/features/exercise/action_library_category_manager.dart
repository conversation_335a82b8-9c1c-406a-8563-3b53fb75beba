import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// 动作库分类节点（复用知忆相册的分类结构）
class ActionLibraryCategoryNode {
  final String id;
  String title;
  List<ActionLibraryCategoryNode> children;
  bool isExpanded;
  bool isEditing;
  bool isNew;
  int level;

  ActionLibraryCategoryNode({
    String? id,
    required this.title,
    List<ActionLibraryCategoryNode>? children,
    this.isExpanded = false,
    this.isEditing = false,
    this.isNew = false,
    this.level = 0,
  }) : id = id ?? DateTime.now().millisecondsSinceEpoch.toString(),
       children = children ?? [];

  /// 复制节点
  ActionLibraryCategoryNode copyWith({
    String? id,
    String? title,
    List<ActionLibraryCategoryNode>? children,
    bool? isExpanded,
    bool? isEditing,
    bool? isNew,
    int? level,
  }) {
    return ActionLibraryCategoryNode(
      id: id ?? this.id,
      title: title ?? this.title,
      children: children ?? List.from(this.children),
      isExpanded: isExpanded ?? this.isExpanded,
      isEditing: isEditing ?? this.isEditing,
      isNew: isNew ?? this.isNew,
      level: level ?? this.level,
    );
  }

  /// 序列化为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'children': children.map((child) => child.toJson()).toList(),
      'isExpanded': isExpanded,
      'level': level,
    };
  }

  /// 从JSON反序列化
  static ActionLibraryCategoryNode fromJson(Map<String, dynamic> json) {
    return ActionLibraryCategoryNode(
      id: json['id'] as String,
      title: json['title'] as String,
      children: (json['children'] as List<dynamic>)
          .map(
            (child) => ActionLibraryCategoryNode.fromJson(
              child as Map<String, dynamic>,
            ),
          )
          .toList(),
      isExpanded: json['isExpanded'] as bool? ?? false,
      level: json['level'] as int? ?? 0,
    );
  }

  /// 递归更新所有子节点的层级
  void updateChildrenLevels() {
    for (var child in children) {
      child.level = level + 1;
      child.updateChildrenLevels();
    }
  }

  /// 查找节点（递归）
  ActionLibraryCategoryNode? findNodeById(String nodeId) {
    if (id == nodeId) return this;
    for (var child in children) {
      final found = child.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  /// 获取所有后代节点（扁平化）
  List<ActionLibraryCategoryNode> getAllDescendants() {
    List<ActionLibraryCategoryNode> descendants = [];
    for (var child in children) {
      descendants.add(child);
      descendants.addAll(child.getAllDescendants());
    }
    return descendants;
  }
}

/// 动作库分类管理器（复用知忆相册的分类管理逻辑）
class ActionLibraryCategoryManager {
  static const String _storageKey = 'action_library_category_tree_data';

  List<ActionLibraryCategoryNode> _categories = [];
  List<ActionLibraryCategoryNode> _searchResults = [];
  String _searchQuery = '';

  List<ActionLibraryCategoryNode> get categories => _categories;
  List<ActionLibraryCategoryNode> get searchResults => _searchResults;
  String get searchQuery => _searchQuery;
  bool get isSearching => _searchQuery.isNotEmpty;

  /// 初始化默认分类数据
  void initializeDefaultCategories() {
    _categories = [
      // 添加默认分类作为第一个选项
      ActionLibraryCategoryNode(id: 'default', title: '默认分类', level: 0),
      ActionLibraryCategoryNode(
        id: 'fitness',
        title: '健身',
        level: 0,
        isExpanded: true,
        children: [
          ActionLibraryCategoryNode(id: 'strength', title: '力量训练', level: 1),
          ActionLibraryCategoryNode(id: 'cardio', title: '有氧运动', level: 1),
          ActionLibraryCategoryNode(id: 'flexibility', title: '柔韧性', level: 1),
        ],
      ),
      ActionLibraryCategoryNode(
        id: 'sports',
        title: '运动',
        level: 0,
        children: [
          ActionLibraryCategoryNode(
            id: 'ball_sports',
            title: '球类运动',
            level: 1,
            children: [
              ActionLibraryCategoryNode(
                id: 'basketball',
                title: '篮球',
                level: 2,
              ),
              ActionLibraryCategoryNode(id: 'football', title: '足球', level: 2),
            ],
          ),
          ActionLibraryCategoryNode(
            id: 'water_sports',
            title: '水上运动',
            level: 1,
          ),
          ActionLibraryCategoryNode(id: 'outdoor', title: '户外运动', level: 1),
        ],
      ),
      ActionLibraryCategoryNode(
        id: 'wellness',
        title: '养生',
        level: 0,
        children: [
          ActionLibraryCategoryNode(id: 'yoga', title: '瑜伽', level: 1),
          ActionLibraryCategoryNode(id: 'meditation', title: '冥想', level: 1),
          ActionLibraryCategoryNode(id: 'traditional', title: '传统养生', level: 1),
        ],
      ),
      ActionLibraryCategoryNode(
        id: 'daily',
        title: '日常',
        level: 0,
        children: [
          ActionLibraryCategoryNode(id: 'eye_care', title: '护眼', level: 1),
          ActionLibraryCategoryNode(id: 'stretching', title: '拉伸', level: 1),
          ActionLibraryCategoryNode(id: 'breathing', title: '呼吸练习', level: 1),
        ],
      ),
    ];
  }

  /// 添加新节点
  void addNode(
    String title, {
    ActionLibraryCategoryNode? parent,
    int? insertIndex,
  }) {
    final newNode = ActionLibraryCategoryNode(
      title: title,
      isNew: true,
      level: parent?.level ?? 0,
    );

    if (parent != null) {
      // 添加为子节点
      newNode.level = parent.level + 1;
      if (insertIndex != null && insertIndex <= parent.children.length) {
        parent.children.insert(insertIndex, newNode);
      } else {
        parent.children.add(newNode);
      }
      parent.isExpanded = true; // 自动展开父节点
    } else {
      // 添加为根节点
      if (insertIndex != null && insertIndex <= _categories.length) {
        _categories.insert(insertIndex, newNode);
      } else {
        _categories.add(newNode);
      }
    }

    _updateSearchResults();
  }

  /// 删除节点
  bool deleteNode(String nodeId) {
    return _deleteNodeRecursive(_categories, nodeId);
  }

  bool _deleteNodeRecursive(
    List<ActionLibraryCategoryNode> nodes,
    String nodeId,
  ) {
    for (int i = 0; i < nodes.length; i++) {
      if (nodes[i].id == nodeId) {
        nodes.removeAt(i);
        _updateSearchResults();
        return true;
      }
      if (_deleteNodeRecursive(nodes[i].children, nodeId)) {
        return true;
      }
    }
    return false;
  }

  /// 更新节点标题
  void updateNodeTitle(String nodeId, String newTitle) {
    final node = _findNodeById(nodeId);
    if (node != null) {
      node.title = newTitle;
      node.isEditing = false;
      node.isNew = false;
      _updateSearchResults();
    }
  }

  /// 查找节点
  ActionLibraryCategoryNode? _findNodeById(String nodeId) {
    for (var category in _categories) {
      final found = category.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  /// 搜索分类
  void searchCategories(String query) {
    _searchQuery = query.toLowerCase();
    _updateSearchResults();
  }

  /// 更新搜索结果
  void _updateSearchResults() {
    if (_searchQuery.isEmpty) {
      _searchResults = [];
      return;
    }

    _searchResults = [];
    for (var category in _categories) {
      _searchInNode(category, _searchResults);
    }
  }

  void _searchInNode(
    ActionLibraryCategoryNode node,
    List<ActionLibraryCategoryNode> results,
  ) {
    if (node.title.toLowerCase().contains(_searchQuery)) {
      results.add(node);
    }
    for (var child in node.children) {
      _searchInNode(child, results);
    }
  }

  /// 获取所有分类名称（扁平化，去重）
  List<String> getAllCategoryNames() {
    final names = <String>[];
    for (var category in _categories) {
      _collectCategoryNames(category, names);
    }
    // 去重并返回
    return names.toSet().toList();
  }

  void _collectCategoryNames(
    ActionLibraryCategoryNode node,
    List<String> names,
  ) {
    names.add(node.title);
    for (var child in node.children) {
      _collectCategoryNames(child, names);
    }
  }

  /// 保存到本地存储
  Future<void> saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _categories
          .map((category) => category.toJson())
          .toList();
      final jsonString = json.encode(jsonList);

      await prefs.setString(_storageKey, jsonString);
      print('💾 成功保存动作库分类树数据');
    } catch (e) {
      print('❌ 保存动作库分类树数据失败: $e');
    }
  }

  /// 从本地存储加载
  Future<void> loadFromStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      if (jsonString != null && jsonString.isNotEmpty) {
        final jsonList = json.decode(jsonString) as List<dynamic>;
        _categories = jsonList
            .map(
              (json) => ActionLibraryCategoryNode.fromJson(
                json as Map<String, dynamic>,
              ),
            )
            .toList();

        // 更新层级
        for (var category in _categories) {
          category.updateChildrenLevels();
        }

        print('✅ 成功加载 ${_categories.length} 个动作库分类');
      } else {
        print('📂 未找到动作库分类数据，使用默认分类');
        initializeDefaultCategories();
      }
    } catch (e) {
      print('❌ 加载动作库分类数据失败，使用默认数据: $e');
      initializeDefaultCategories();
    }
  }

  /// 重置为默认分类
  Future<void> resetToDefault() async {
    initializeDefaultCategories();
    await saveToStorage();
  }
}
