import 'dart:async';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../services/first_time_service.dart';

/// 启动页 - 应用初始化与品牌展示
///
/// 设计风格：Notion风格极简设计
/// - 柔和的色彩系统
/// - 极简的视觉元素
/// - 优雅的动画效果
/// - 内容优先的布局
class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeIn),
    );

    _animationController.forward();

    // 检查首次使用状态并导航到相应页面
    Timer(const Duration(seconds: 2), () async {
      if (mounted) {
        final initialRoute = await FirstTimeService.instance.getInitialRoute();
        if (mounted) {
          context.go(initialRoute);
        }
      }
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      body: Center(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // OneDay Logo
              ShaderMask(
                shaderCallback: (bounds) {
                  return const LinearGradient(
                    colors: [Color(0xFF2E7EED), Color(0xFF7C3AED)],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ).createShader(bounds);
                },
                child: const Text(
                  'OneDay',
                  style: TextStyle(
                    fontFamily: 'Inter',
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                    color: Colors
                        .white, // This color will be overridden by the shader
                    letterSpacing: -1.5,
                  ),
                ),
              ),

              // 品牌标语
              const SizedBox(height: 24),
              const Text(
                '怕一天浪费，就用OneDay',
                style: TextStyle(
                  fontFamily: 'Inter',
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF9B9A97), // Notion风格的灰色
                  letterSpacing: 0.5,
                  height: 1.4,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 可复用的简洁背景组件
class CleanBackground extends StatelessWidget {
  final Widget child;
  final Color? backgroundColor;

  const CleanBackground({super.key, required this.child, this.backgroundColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(color: backgroundColor ?? Colors.white),
      child: child,
    );
  }
}

/// 可复用的简洁Logo组件
class CleanLogo extends StatefulWidget {
  final double size;
  final Duration animationDuration;
  final bool enableAnimation;
  final Color? backgroundColor;

  const CleanLogo({
    super.key,
    this.size = 80,
    this.animationDuration = const Duration(milliseconds: 1200),
    this.enableAnimation = true,
    this.backgroundColor,
  });

  @override
  State<CleanLogo> createState() => _CleanLogoState();
}

class _CleanLogoState extends State<CleanLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    if (widget.enableAnimation) {
      _controller.repeat(reverse: true);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.enableAnimation ? _scaleAnimation.value : 1.0,
          child: Container(
            width: widget.size,
            height: widget.size,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: widget.backgroundColor ?? const Color(0xFF2E7EED),
              boxShadow: [
                BoxShadow(
                  color: (widget.backgroundColor ?? const Color(0xFF2E7EED))
                      .withValues(alpha: 0.1),
                  blurRadius: 20,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: Text(
                'O',
                style: TextStyle(
                  fontSize: widget.size * 0.4,
                  fontWeight: FontWeight.w600,
                  color: Colors.white,
                  height: 1,
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
