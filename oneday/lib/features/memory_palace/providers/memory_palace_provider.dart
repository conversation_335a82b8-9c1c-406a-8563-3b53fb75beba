import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../palace_manager_page.dart';

/// 记忆宫殿状态
class MemoryPalaceState {
  final List<MemoryPalace> palaces;
  final bool isLoading;
  final String? error;
  final DateTime? lastUpdated;

  const MemoryPalaceState({
    this.palaces = const [],
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  MemoryPalaceState copyWith({
    List<MemoryPalace>? palaces,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return MemoryPalaceState(
      palaces: palaces ?? this.palaces,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// 获取记忆宫殿总数
  int get totalCount => palaces.length;

  /// 获取今日使用的记忆宫殿数量
  int get todayUsedCount {
    final today = DateTime.now();
    return palaces.where((palace) {
      return palace.lastUsed.year == today.year &&
          palace.lastUsed.month == today.month &&
          palace.lastUsed.day == today.day;
    }).length;
  }

  /// 获取总知识点数量
  int get totalAnchorCount {
    return palaces.fold(0, (sum, palace) => sum + palace.anchorCount);
  }

  /// 获取活跃的记忆宫殿数量（有知识点的）
  int get activeCount {
    return palaces.where((palace) => palace.anchorCount > 0).length;
  }
}

/// 记忆宫殿状态管理器
class MemoryPalaceNotifier extends StateNotifier<MemoryPalaceState> {
  static const String _storageKey = 'oneday_memory_palaces';

  MemoryPalaceNotifier() : super(const MemoryPalaceState()) {
    _loadFromStorage();
  }

  /// 从本地存储加载数据
  Future<void> _loadFromStorage() async {
    state = state.copyWith(isLoading: true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);

      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        final palaces = jsonList
            .map((json) => MemoryPalace.fromJson(json))
            .toList();

        state = state.copyWith(
          palaces: palaces,
          isLoading: false,
          error: null,
        );

        print('✅ 记忆宫殿Provider：成功加载 ${palaces.length} 个记忆宫殿');
      } else {
        // 首次启动，使用默认数据
        await _loadDefaultPalaces();
      }
    } catch (e) {
      print('❌ 记忆宫殿Provider：加载数据失败 - $e');
      state = state.copyWith(
        isLoading: false,
        error: '加载数据失败: $e',
      );
      // 加载失败时使用默认数据
      await _loadDefaultPalaces();
    }
  }

  /// 加载默认示例数据
  Future<void> _loadDefaultPalaces() async {
    final defaultPalaces = [
      MemoryPalace(
        id: '1',
        title: '我的卧室',
        imagePaths: ['https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400'],
        anchorCount: 12,
        tags: ['睡眠', '休息'],
        category: '己舍',
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
        lastUsed: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      MemoryPalace(
        id: '2',
        title: '图书馆',
        imagePaths: ['https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400'],
        anchorCount: 8,
        tags: ['学习', '安静'],
        category: '大学',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
        lastUsed: DateTime.now().subtract(const Duration(days: 1)),
      ),
      MemoryPalace(
        id: '3',
        title: '小学教室',
        imagePaths: ['https://images.unsplash.com/photo-1580582932707-520aed937b7b?w=400'],
        anchorCount: 15,
        tags: ['基础', '启蒙'],
        category: '小学',
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
        lastUsed: DateTime.now().subtract(const Duration(days: 2)),
      ),
    ];

    state = state.copyWith(
      palaces: defaultPalaces,
      isLoading: false,
      error: null,
    );

    print('🆕 记忆宫殿Provider：加载默认示例数据');
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadFromStorage();
  }

  /// 更新记忆宫殿的知识点数量
  Future<void> updatePalaceAnchorCount(String palaceId, int anchorCount) async {
    final updatedPalaces = state.palaces.map((palace) {
      if (palace.id == palaceId) {
        return MemoryPalace(
          id: palace.id,
          title: palace.title,
          imagePaths: palace.imagePaths,
          anchorCount: anchorCount,
          tags: palace.tags,
          category: palace.category,
          createdAt: palace.createdAt,
          lastUsed: DateTime.now(),
          isFromGallery: palace.isFromGallery,
        );
      }
      return palace;
    }).toList();

    state = state.copyWith(palaces: updatedPalaces);
    await _saveToStorage();

    print('📊 记忆宫殿Provider：更新知识点数量 - $palaceId: $anchorCount');
  }

  /// 添加新的记忆宫殿
  Future<void> addPalace(MemoryPalace palace) async {
    final updatedPalaces = [palace, ...state.palaces];
    state = state.copyWith(palaces: updatedPalaces);
    await _saveToStorage();

    print('➕ 记忆宫殿Provider：添加新记忆宫殿 - ${palace.title}');
  }

  /// 删除记忆宫殿
  Future<void> deletePalace(String palaceId) async {
    final updatedPalaces = state.palaces.where((p) => p.id != palaceId).toList();
    state = state.copyWith(palaces: updatedPalaces);
    await _saveToStorage();

    print('🗑️ 记忆宫殿Provider：删除记忆宫殿 - $palaceId');
  }

  /// 保存数据到本地存储
  Future<void> _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = state.palaces.map((palace) => palace.toJson()).toList();
      final jsonString = json.encode(jsonList);

      await prefs.setString(_storageKey, jsonString);
      print('💾 记忆宫殿Provider：已保存 ${state.palaces.length} 个记忆宫殿到本地存储');
    } catch (e) {
      print('❌ 记忆宫殿Provider：保存数据失败 - $e');
      state = state.copyWith(error: '保存数据失败: $e');
    }
  }
}

/// 记忆宫殿Provider
final memoryPalaceProvider = StateNotifierProvider<MemoryPalaceNotifier, MemoryPalaceState>(
  (ref) => MemoryPalaceNotifier(),
);

/// 记忆宫殿统计Provider
final memoryPalaceStatsProvider = Provider<Map<String, dynamic>>((ref) {
  final state = ref.watch(memoryPalaceProvider);

  return {
    'totalCount': state.totalCount,
    'activeCount': state.activeCount,
    'todayUsedCount': state.todayUsedCount,
    'totalAnchorCount': state.totalAnchorCount,
    'formattedCount': '${state.totalCount}个',
    'formattedActiveCount': '${state.activeCount}个',
  };
});

/// 今日记忆宫殿使用统计Provider
final todayMemoryPalaceUsageProvider = Provider<Map<String, dynamic>>((ref) {
  final state = ref.watch(memoryPalaceProvider);
  final todayUsed = state.todayUsedCount;
  final totalActive = state.activeCount;

  return {
    'todayUsed': todayUsed,
    'totalActive': totalActive,
    'usageRate': totalActive > 0 ? (todayUsed / totalActive * 100).round() : 0,
    'formattedUsage': '$todayUsed/$totalActive',
  };
});
