import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'image_coordinate_system.dart' as coord;

/// 锚点数据迁移和校准工具
///
/// 负责检测和转换基于旧压缩尺寸的锚点坐标到新的标准化坐标系统，
/// 确保现有用户数据在压缩配置变化后仍能正确显示。
class AnchorDataMigration {
  static const String _migrationVersionKey = 'anchor_migration_version';
  static const String _currentMigrationVersion = '1.0.0';

  /// 检查是否需要进行数据迁移
  static Future<bool> needsMigration() async {
    final prefs = await SharedPreferences.getInstance();
    final currentVersion = prefs.getString(_migrationVersionKey);
    return currentVersion != _currentMigrationVersion;
  }

  /// 执行锚点数据迁移
  static Future<void> migrateAnchorData() async {
    if (!await needsMigration()) {
      print('🔄 [数据迁移] 无需迁移，当前版本已是最新');
      return;
    }

    print('🔄 [数据迁移] 开始迁移锚点数据到标准化坐标系统');

    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('scene_'))
          .toList();

      int migratedScenes = 0;
      int migratedAnchors = 0;

      for (final key in keys) {
        final sceneData = prefs.getString(key);
        if (sceneData == null) continue;

        try {
          final data = json.decode(sceneData) as Map<String, dynamic>;
          final anchors = data['anchors'] as List<dynamic>?;

          if (anchors == null || anchors.isEmpty) continue;

          // 获取场景图片路径
          final imagePath = data['imagePath'] as String?;
          if (imagePath == null) continue;

          // 检查是否需要校准
          final needsCalibration = await _checkIfNeedsCalibration(
            imagePath,
            anchors,
          );
          if (!needsCalibration) continue;

          // 执行校准
          final calibratedAnchors = await _calibrateAnchors(imagePath, anchors);
          if (calibratedAnchors != null) {
            data['anchors'] = calibratedAnchors;
            data['migrationVersion'] = _currentMigrationVersion;
            data['migrationTimestamp'] = DateTime.now().toIso8601String();

            // 保存校准后的数据
            await prefs.setString(key, json.encode(data));

            migratedScenes++;
            migratedAnchors += calibratedAnchors.length;

            print('✅ [数据迁移] 场景 $key: 校准了 ${calibratedAnchors.length} 个锚点');
          }
        } catch (e) {
          print('❌ [数据迁移] 场景 $key 迁移失败: $e');
        }
      }

      // 标记迁移完成
      await prefs.setString(_migrationVersionKey, _currentMigrationVersion);

      print('✅ [数据迁移] 完成！迁移了 $migratedScenes 个场景，$migratedAnchors 个锚点');
    } catch (e) {
      print('❌ [数据迁移] 迁移过程失败: $e');
    }
  }

  /// 检查锚点是否需要校准
  static Future<bool> _checkIfNeedsCalibration(
    String imagePath,
    List<dynamic> anchors,
  ) async {
    try {
      // 获取图片尺寸信息
      final sizeInfo = await coord.ImageCoordinateSystem.getImageSizeInfo(
        imagePath,
      );
      if (sizeInfo == null) return false;

      // 如果图片没有被压缩，不需要校准
      if (!sizeInfo.needsCoordinateTransform) return false;

      // 检查锚点数据是否包含迁移标记
      for (final anchor in anchors) {
        if (anchor is Map<String, dynamic>) {
          final migrationVersion = anchor['migrationVersion'] as String?;
          if (migrationVersion == _currentMigrationVersion) {
            return false; // 已经迁移过
          }
        }
      }

      return true; // 需要校准
    } catch (e) {
      print('❌ [数据迁移] 检查校准需求失败: $e');
      return false;
    }
  }

  /// 校准锚点坐标
  static Future<List<Map<String, dynamic>>?> _calibrateAnchors(
    String imagePath,
    List<dynamic> anchors,
  ) async {
    try {
      final sizeInfo = await coord.ImageCoordinateSystem.getImageSizeInfo(
        imagePath,
      );
      if (sizeInfo == null) return null;

      final calibratedAnchors = <Map<String, dynamic>>[];

      for (final anchor in anchors) {
        if (anchor is! Map<String, dynamic>) continue;

        final xRatio = (anchor['xRatio'] as num?)?.toDouble();
        final yRatio = (anchor['yRatio'] as num?)?.toDouble();

        if (xRatio == null || yRatio == null) continue;

        // 检查是否已经校准过
        final migrationVersion = anchor['migrationVersion'] as String?;
        if (migrationVersion == _currentMigrationVersion) {
          calibratedAnchors.add(anchor);
          continue;
        }

        // 执行坐标校准
        final calibratedCoord = _calibrateCoordinate(xRatio, yRatio, sizeInfo);

        // 创建校准后的锚点数据
        final calibratedAnchor = Map<String, dynamic>.from(anchor);
        calibratedAnchor['xRatio'] = calibratedCoord.dx;
        calibratedAnchor['yRatio'] = calibratedCoord.dy;
        calibratedAnchor['migrationVersion'] = _currentMigrationVersion;
        calibratedAnchor['originalXRatio'] = xRatio; // 保留原始坐标用于调试
        calibratedAnchor['originalYRatio'] = yRatio;

        calibratedAnchors.add(calibratedAnchor);

        print(
          '🔧 [坐标校准] 锚点${anchor['id']}: (${xRatio.toStringAsFixed(3)}, ${yRatio.toStringAsFixed(3)}) → (${calibratedCoord.dx.toStringAsFixed(3)}, ${calibratedCoord.dy.toStringAsFixed(3)})',
        );
      }

      return calibratedAnchors;
    } catch (e) {
      print('❌ [数据迁移] 校准锚点失败: $e');
      return null;
    }
  }

  /// 校准单个坐标
  static Offset _calibrateCoordinate(
    double oldXRatio,
    double oldYRatio,
    coord.ImageSizeInfo sizeInfo,
  ) {
    // 假设旧坐标是基于压缩后图片尺寸的比例坐标
    // 需要转换为基于原始图片尺寸的比例坐标

    // 1. 从旧比例坐标恢复为压缩图片的像素坐标
    final oldImageX = oldXRatio * sizeInfo.currentSize.width;
    final oldImageY = oldYRatio * sizeInfo.currentSize.height;

    // 2. 转换为原始图片的像素坐标
    final scaleFactorX =
        sizeInfo.originalSize.width / sizeInfo.currentSize.width;
    final scaleFactorY =
        sizeInfo.originalSize.height / sizeInfo.currentSize.height;

    final originalImageX = oldImageX * scaleFactorX;
    final originalImageY = oldImageY * scaleFactorY;

    // 3. 转换为基于原始图片尺寸的比例坐标
    final newXRatio = (originalImageX / sizeInfo.originalSize.width).clamp(
      0.0,
      1.0,
    );
    final newYRatio = (originalImageY / sizeInfo.originalSize.height).clamp(
      0.0,
      1.0,
    );

    return Offset(newXRatio, newYRatio);
  }

  /// 获取迁移统计信息
  static Future<MigrationStats> getMigrationStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys = prefs
          .getKeys()
          .where((key) => key.startsWith('scene_'))
          .toList();

      int totalScenes = 0;
      int migratedScenes = 0;
      int totalAnchors = 0;
      int migratedAnchors = 0;

      for (final key in keys) {
        final sceneData = prefs.getString(key);
        if (sceneData == null) continue;

        try {
          final data = json.decode(sceneData) as Map<String, dynamic>;
          final anchors = data['anchors'] as List<dynamic>?;

          totalScenes++;
          if (anchors != null) {
            totalAnchors += anchors.length;

            // 检查是否已迁移
            final migrationVersion = data['migrationVersion'] as String?;
            if (migrationVersion == _currentMigrationVersion) {
              migratedScenes++;
              migratedAnchors += anchors.length;
            }
          }
        } catch (e) {
          // 忽略解析错误的数据
        }
      }

      return MigrationStats(
        totalScenes: totalScenes,
        migratedScenes: migratedScenes,
        totalAnchors: totalAnchors,
        migratedAnchors: migratedAnchors,
      );
    } catch (e) {
      print('❌ [数据迁移] 获取统计信息失败: $e');
      return MigrationStats(
        totalScenes: 0,
        migratedScenes: 0,
        totalAnchors: 0,
        migratedAnchors: 0,
      );
    }
  }

  /// 重置迁移状态（用于测试）
  static Future<void> resetMigrationState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_migrationVersionKey);
    print('🔄 [数据迁移] 迁移状态已重置');
  }
}

/// 迁移统计信息
class MigrationStats {
  final int totalScenes;
  final int migratedScenes;
  final int totalAnchors;
  final int migratedAnchors;

  const MigrationStats({
    required this.totalScenes,
    required this.migratedScenes,
    required this.totalAnchors,
    required this.migratedAnchors,
  });

  bool get isComplete => totalScenes > 0 && migratedScenes == totalScenes;
  double get progress => totalScenes > 0 ? migratedScenes / totalScenes : 1.0;

  @override
  String toString() {
    return 'MigrationStats(scenes: $migratedScenes/$totalScenes, anchors: $migratedAnchors/$totalAnchors, progress: ${(progress * 100).toStringAsFixed(1)}%)';
  }
}
