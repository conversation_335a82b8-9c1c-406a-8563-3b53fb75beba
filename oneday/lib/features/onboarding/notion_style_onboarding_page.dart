import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../../services/first_time_service.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

class NotionStyleOnboardingPage extends StatefulWidget {
  const NotionStyleOnboardingPage({super.key});

  @override
  State<NotionStyleOnboardingPage> createState() =>
      _NotionStyleOnboardingPageState();
}

class _NotionStyleOnboardingPageState extends State<NotionStyleOnboardingPage> {
  final _controller = PageController();

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// 导航到指定页面（Web 平台专用）
  void _navigateToPage(int targetPage) {
    if (!kIsWeb) return; // 仅在 Web 平台启用

    // 使用平滑动画跳转到目标页面
    _controller.animateToPage(
      targetPage,
      duration: const Duration(milliseconds: 350),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      body: SafeArea(
        child: Stack(
          children: [
            // 主要内容
            Column(
              children: [
                Expanded(
                  child: PageView(
                    controller: _controller,
                    children: const [
                      _OnboardingPageContent(
                        title: 'More focus,\nless chaos.',
                        features: [
                          {'icon': '💪', 'text': '具身记忆'},
                          {'icon': '📷', 'text': '知忆相册'},
                        ],
                        isFirstPage: true,
                      ),
                      _OnboardingPageContent(
                        title: 'Track your time,\nmaster your life.',
                        features: [
                          {'icon': '⏳', 'text': '时间盒子'},
                          {'icon': '🗓️', 'text': '学习日历'},
                        ],
                      ),
                      _OnboardingPageContent(
                        title: 'Reflect and grow.\nBuild your wisdom.',
                        features: [
                          {'icon': '✍️', 'text': '每日反思'},
                          {'icon': '📈', 'text': '成长统计'},
                        ],
                      ),
                      _OnboardingPageContent(
                        title: 'All your tools.\nIn one place.',
                        showButton: true,
                        features: [
                          {'icon': '🪙', 'text': '激励工资体系'},
                          {'icon': '👥', 'text': '互助社区'},
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 24.0),
                  child: SmoothPageIndicator(
                    controller: _controller,
                    count: 4,
                    effect: const WormEffect(
                      dotColor: Color(0xFFE3E2DE),
                      activeDotColor: Color(0xFF2E7EED),
                      dotHeight: 8,
                      dotWidth: 8,
                    ),
                    // Web 平台启用点击导航功能
                    onDotClicked: kIsWeb
                        ? (index) => _navigateToPage(index)
                        : null,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _OnboardingPageContent extends StatefulWidget {
  final String title;
  final List<Map<String, String>> features;
  final bool showButton;
  final bool isFirstPage;

  const _OnboardingPageContent({
    required this.title,
    required this.features,
    this.showButton = false,
    this.isFirstPage = false,
  });

  @override
  State<_OnboardingPageContent> createState() => _OnboardingPageContentState();
}

class _OnboardingPageContentState extends State<_OnboardingPageContent> {
  int _logoTapCount = 0;
  DateTime? _lastLogoTapTime;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 32.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 第1页：Logo在上方区域，调整间距以适应双行标语
          if (widget.isFirstPage) ...[
            const SizedBox(height: 80), // 顶部间距，将Logo放在更上方
            Center(child: _buildOneDayLogo(context)),
            const SizedBox(height: 160), // 增加间距，因为标语从三行变为两行，需要向下移动以与其他双行标语对齐
          ],

          // 非第1页：使用固定间距确保标语位置一致
          if (!widget.isFirstPage)
            const SizedBox(height: 268), // 与第1页的总间距(80+28+160)保持一致
          // 英文标语 - 所有页面在此位置对齐
          Text(
            widget.title,
            style: const TextStyle(
              fontFamily: 'Inter',
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Color(0xFF37352F),
              height: 1.3,
            ),
          ),
          const SizedBox(height: 48),

          // emoji功能说明区域 - 所有页面在此位置对齐
          ...widget.features.map(
            (feature) => Padding(
              padding: const EdgeInsets.only(bottom: 24.0),
              child: _buildFeatureRow(feature['icon']!, feature['text']!),
            ),
          ),

          // 使用Spacer填充剩余空间，为第4页按钮留出适当空间
          if (widget.showButton)
            const Spacer(flex: 2) // 第4页：适度的空间分配
          else
            const Spacer(flex: 3), // 其他页面：更多空间
          // 第4页：立即开始按钮
          if (widget.showButton)
            Center(
              child: FilledButton(
                onPressed: () async {
                  await FirstTimeService.instance.markOnboardingCompleted();
                  if (context.mounted) {
                    context.go('/home');
                  }
                },
                style: FilledButton.styleFrom(
                  backgroundColor: const Color(0xFF2E7EED),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 48,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '立即开始',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ),
            ),

          // 底部间距
          const SizedBox(height: 40),
        ],
      ),
    );
  }

  Widget _buildFeatureRow(String icon, String text) {
    return Row(
      children: [
        Text(icon, style: const TextStyle(fontSize: 28)),
        const SizedBox(width: 16),
        Text(
          text,
          style: const TextStyle(
            fontFamily: 'Inter',
            fontSize: 22,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  /// 构建OneDay Logo（开发者入口）
  Widget _buildOneDayLogo(BuildContext context) {
    return GestureDetector(
      onTap: _onLogoTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'OneDay',
            style: const TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: Color(0xFF37352F),
            ),
          ),
          // 开发者模式指示器（仅在Debug模式下显示）
          if (kDebugMode && _logoTapCount > 0) ...[
            const SizedBox(width: 6),
            Container(
              width: 6,
              height: 6,
              decoration: BoxDecoration(
                color: _logoTapCount >= 3
                    ? const Color(0xFF059669)
                    : const Color(0xFF2E7EED),
                shape: BoxShape.circle,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Logo点击处理 - 开发者测试模式入口
  void _onLogoTap() async {
    if (!kDebugMode) return;

    final now = DateTime.now();

    // 重置计数器（如果距离上次点击超过2秒）
    if (_lastLogoTapTime == null ||
        now.difference(_lastLogoTapTime!).inSeconds > 2) {
      _logoTapCount = 0;
    }

    _lastLogoTapTime = now;
    _logoTapCount++;

    setState(() {});

    // 连续点击3次直接跳转到主页
    if (_logoTapCount >= 3) {
      // 轻微震动反馈
      HapticFeedback.lightImpact();
      // 标记引导页已完成并跳转到主页
      await FirstTimeService.instance.markOnboardingCompleted();
      if (mounted) {
        context.go('/home');
      }
    }
  }
}
