import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/widget_models.dart';

/// 小组件错误处理器
class WidgetErrorHandler {
  /// 处理小组件错误
  static Future<void> handleError(
    BuildContext context,
    WidgetActionResult result, {
    bool showSnackBar = true,
    VoidCallback? onRetry,
  }) async {
    if (result.isSuccess) return;

    print('🚨 小组件错误: ${result.errorCode} - ${result.message}');

    switch (result.errorCode) {
      case 'CAMERA_PERMISSION_DENIED':
        await _handleCameraPermissionDenied(context, showSnackBar);
        break;

      case 'CAMERA_PERMISSION_PERMANENTLY_DENIED':
        await _handleCameraPermissionPermanentlyDenied(context);
        break;

      case 'CAMERA_UNAVAILABLE':
        await _handleCameraUnavailable(context, showSnackBar);
        break;

      case 'USER_CANCELLED':
        // 用户取消，不显示错误
        break;

      case 'CAPTURE_FAILED':
        await _handleCaptureFailed(context, showSnackBar, onRetry);
        break;

      case 'COMPRESSION_FAILED':
        await _handleCompressionFailed(context, showSnackBar, onRetry);
        break;

      case 'ALBUM_CREATION_FAILED':
        await _handleAlbumCreationFailed(context, showSnackBar, onRetry);
        break;

      default:
        await _handleGenericError(context, result, showSnackBar, onRetry);
        break;
    }
  }

  /// 处理相机权限被拒绝
  static Future<void> _handleCameraPermissionDenied(
    BuildContext context,
    bool showSnackBar,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        '需要相机权限才能拍照',
        actionLabel: '重新授权',
        onAction: () async {
          await Permission.camera.request();
        },
      );
    }
  }

  /// 处理相机权限被永久拒绝
  static Future<void> _handleCameraPermissionPermanentlyDenied(
    BuildContext context,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Row(
          children: [
            Icon(Icons.camera_alt_outlined, color: Color(0xFF2E7EED), size: 24),
            SizedBox(width: 12),
            Text(
              '需要相机权限',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ],
        ),
        content: const Text(
          '相机权限被永久拒绝，请在设置中手动开启相机权限，以便使用随手拍功能。',
          style: TextStyle(fontSize: 14, color: Color(0xFF6F6E69), height: 1.5),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              '取消',
              style: TextStyle(
                color: Color(0xFF9B9A97),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text(
              '去设置',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );
  }

  /// 处理相机不可用
  static Future<void> _handleCameraUnavailable(
    BuildContext context,
    bool showSnackBar,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        '相机当前不可用，请稍后再试',
        icon: Icons.camera_alt_outlined,
      );
    }
  }

  /// 处理拍照失败
  static Future<void> _handleCaptureFailed(
    BuildContext context,
    bool showSnackBar,
    VoidCallback? onRetry,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        '拍照失败，请重试',
        actionLabel: onRetry != null ? '重试' : null,
        onAction: onRetry,
      );
    }
  }

  /// 处理图片压缩失败
  static Future<void> _handleCompressionFailed(
    BuildContext context,
    bool showSnackBar,
    VoidCallback? onRetry,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        '图片处理失败，请重试',
        actionLabel: onRetry != null ? '重试' : null,
        onAction: onRetry,
      );
    }
  }

  /// 处理相册创建失败
  static Future<void> _handleAlbumCreationFailed(
    BuildContext context,
    bool showSnackBar,
    VoidCallback? onRetry,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        '创建相册失败，请重试',
        actionLabel: onRetry != null ? '重试' : null,
        onAction: onRetry,
      );
    }
  }

  /// 处理通用错误
  static Future<void> _handleGenericError(
    BuildContext context,
    WidgetActionResult result,
    bool showSnackBar,
    VoidCallback? onRetry,
  ) async {
    if (showSnackBar) {
      _showErrorSnackBar(
        context,
        result.message,
        actionLabel: onRetry != null ? '重试' : null,
        onAction: onRetry,
      );
    }
  }

  /// 显示错误SnackBar
  static void _showErrorSnackBar(
    BuildContext context,
    String message, {
    IconData? icon,
    String? actionLabel,
    VoidCallback? onAction,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon ?? Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFFEB5757),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// 显示成功SnackBar
  static void showSuccessSnackBar(
    BuildContext context,
    String message, {
    IconData? icon,
    String? actionLabel,
    VoidCallback? onAction,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              icon ?? Icons.check_circle_outline,
              color: Colors.white,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF4CAF50),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        action: actionLabel != null && onAction != null
            ? SnackBarAction(
                label: actionLabel,
                textColor: Colors.white,
                onPressed: onAction,
              )
            : null,
        duration: const Duration(milliseconds: 300),
      ),
    );
  }

  /// 显示加载对话框
  static void showLoadingDialog(
    BuildContext context, {
    String message = '处理中...',
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2E7EED)),
              ),
              const SizedBox(height: 16),
              Text(
                message,
                style: const TextStyle(fontSize: 16, color: Color(0xFF37352F)),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 隐藏加载对话框
  static void hideLoadingDialog(BuildContext context) {
    Navigator.of(context, rootNavigator: true).pop();
  }

  /// 显示确认对话框
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String message,
    String confirmText = '确认',
    String cancelText = '取消',
    IconData? icon,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: Row(
          children: [
            if (icon != null) ...[
              Icon(icon, color: const Color(0xFF2E7EED), size: 24),
              const SizedBox(width: 12),
            ],
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
          ],
        ),
        content: Text(
          message,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF6F6E69),
            height: 1.5,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              cancelText,
              style: const TextStyle(
                color: Color(0xFF9B9A97),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2E7EED),
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              confirmText,
              style: const TextStyle(fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
    );

    return result ?? false;
  }
}
