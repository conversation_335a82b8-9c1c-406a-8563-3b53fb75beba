import 'dart:convert';
// import 'package:home_widget/home_widget.dart'; // 暂时注释，包未安装
import 'package:shared_preferences/shared_preferences.dart';
import '../models/widget_models.dart';

/// 桌面小组件服务 (暂时禁用，缺少依赖包)
class WidgetService {
  static const String _widgetDataKey = 'widget_data';
  static const String _quickCameraWidgetId = 'quick_camera_widget';

  /// 单例实例
  static final WidgetService _instance = WidgetService._internal();
  factory WidgetService() => _instance;
  WidgetService._internal();

  /// 初始化小组件服务 (暂时禁用)
  Future<void> initialize() async {
    try {
      print('🔧 初始化桌面小组件服务...(暂时禁用)');

      // 注册小组件更新回调 (暂时注释)
      // await HomeWidget.setAppGroupId('group.com.oneday.widget');

      // 设置小组件点击回调 (暂时注释)
      // HomeWidget.widgetClicked.listen(_handleWidgetClick);

      // 初始化快速拍照小组件 (暂时注释)
      // await _initializeQuickCameraWidget();

      print('✅ 桌面小组件服务初始化完成 (暂时禁用)');
    } catch (e) {
      print('❌ 桌面小组件服务初始化失败: $e');
    }
  }

  /// 初始化快速拍照小组件 (暂未使用)
  /*
  Future<void> _initializeQuickCameraWidget() async {
    try {
      final config = WidgetConfig(
        id: _quickCameraWidgetId,
        type: WidgetType.quickCamera,
        title: '随手拍',
        enabled: true,
        createdAt: DateTime.now(),
        lastUpdated: DateTime.now(),
      );

      await _saveWidgetConfig(config);
      await updateQuickCameraWidget();

      print('✅ 快速拍照小组件初始化完成');
    } catch (e) {
      print('❌ 快速拍照小组件初始化失败: $e');
    }
  }
  */

  /// 处理小组件点击事件 (暂未使用)
  /*
  void _handleWidgetClick(Uri? uri) {
    if (uri == null) return;

    try {
      print('📱 收到小组件点击事件: $uri');

      final widgetId = uri.queryParameters['widget_id'];
      final action = uri.queryParameters['action'];

      if (widgetId == _quickCameraWidgetId && action == 'quick_camera') {
        _handleQuickCameraClick();
      }
    } catch (e) {
      print('❌ 处理小组件点击事件失败: $e');
    }
  }
  */

  /// 更新快速拍照小组件
  Future<void> updateQuickCameraWidget({
    String? lastImagePath,
    DateTime? lastCaptureTime,
  }) async {
    try {
      print('🔄 更新快速拍照小组件数据...');

      // 获取当前数据
      final currentData = await getQuickCameraWidgetData();

      // 计算今日和本月拍照数量
      final todayCount = await _getTodayPhotoCount();
      final monthlyCount = await _getMonthlyPhotoCount();

      // 更新数据
      final updatedData = currentData.copyWith(
        lastImagePath: lastImagePath,
        lastCaptureTime: lastCaptureTime ?? currentData.lastCaptureTime,
        todayCount: todayCount,
        monthlyCount: monthlyCount,
      );

      // 保存数据
      await _saveQuickCameraWidgetData(updatedData);

      // 更新小组件UI
      await _updateWidgetUI(updatedData);

      print('✅ 快速拍照小组件更新完成');
    } catch (e) {
      print('❌ 更新快速拍照小组件失败: $e');
    }
  }

  /// 更新小组件UI (暂时禁用)
  Future<void> _updateWidgetUI(QuickCameraWidgetData data) async {
    try {
      print(
        '🔄 更新小组件UI (暂时禁用): 今日 ${data.todayCount} 张 · 本月 ${data.monthlyCount} 张',
      );
      // 设置小组件数据 (暂时注释)
      // await HomeWidget.saveWidgetData<String>('widget_title', '随手拍');
      // await HomeWidget.saveWidgetData<String>(
      //   'widget_subtitle',
      //   '今日 ${data.todayCount} 张 · 本月 ${data.monthlyCount} 张',
      // );
      // await HomeWidget.saveWidgetData<String>(
      //   'last_capture_time',
      //   data.lastCaptureTime?.toIso8601String() ?? '',
      // );
      // await HomeWidget.saveWidgetData<String>(
      //   'click_action',
      //   'oneday://widget?widget_id=$_quickCameraWidgetId&action=quick_camera',
      // );

      // 更新小组件 (暂时注释)
      // await HomeWidget.updateWidget(
      //   name: 'QuickCameraWidget',
      //   androidName: 'QuickCameraWidget',
      //   iOSName: 'QuickCameraWidget',
      // );
    } catch (e) {
      print('❌ 更新小组件UI失败: $e');
    }
  }

  /// 获取快速拍照小组件数据
  Future<QuickCameraWidgetData> getQuickCameraWidgetData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = prefs.getString(
        '${_widgetDataKey}_$_quickCameraWidgetId',
      );

      if (dataJson != null) {
        final data = json.decode(dataJson);
        return QuickCameraWidgetData.fromJson(data);
      }

      return QuickCameraWidgetData.defaultData();
    } catch (e) {
      print('❌ 获取快速拍照小组件数据失败: $e');
      return QuickCameraWidgetData.defaultData();
    }
  }

  /// 保存快速拍照小组件数据
  Future<void> _saveQuickCameraWidgetData(QuickCameraWidgetData data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final dataJson = json.encode(data.toJson());
      await prefs.setString(
        '${_widgetDataKey}_$_quickCameraWidgetId',
        dataJson,
      );
    } catch (e) {
      print('❌ 保存快速拍照小组件数据失败: $e');
    }
  }

  /// 获取今日拍照数量
  Future<int> _getTodayPhotoCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final todayKey = 'photo_count_${today.year}_${today.month}_${today.day}';
      return prefs.getInt(todayKey) ?? 0;
    } catch (e) {
      print('❌ 获取今日拍照数量失败: $e');
      return 0;
    }
  }

  /// 获取本月拍照数量
  Future<int> _getMonthlyPhotoCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final monthKey = 'photo_count_${now.year}_${now.month}';
      return prefs.getInt(monthKey) ?? 0;
    } catch (e) {
      print('❌ 获取本月拍照数量失败: $e');
      return 0;
    }
  }

  /// 增加拍照计数
  Future<void> incrementPhotoCount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();

      // 更新今日计数
      final todayKey = 'photo_count_${now.year}_${now.month}_${now.day}';
      final todayCount = (prefs.getInt(todayKey) ?? 0) + 1;
      await prefs.setInt(todayKey, todayCount);

      // 更新本月计数
      final monthKey = 'photo_count_${now.year}_${now.month}';
      final monthCount = (prefs.getInt(monthKey) ?? 0) + 1;
      await prefs.setInt(monthKey, monthCount);

      print('📊 拍照计数更新: 今日 $todayCount 张, 本月 $monthCount 张');
    } catch (e) {
      print('❌ 增加拍照计数失败: $e');
    }
  }

  /// 处理拍照完成事件
  Future<void> onPhotoCaptured(String imagePath) async {
    try {
      print('📸 处理拍照完成事件: $imagePath');

      // 增加拍照计数
      await incrementPhotoCount();

      // 更新小组件数据
      await updateQuickCameraWidget(
        lastImagePath: imagePath,
        lastCaptureTime: DateTime.now(),
      );
    } catch (e) {
      print('❌ 处理拍照完成事件失败: $e');
    }
  }
}
