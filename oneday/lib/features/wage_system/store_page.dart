import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import '../../shared/utils/ui_utils.dart';

/// 道具商城页面
///
/// 提供各种学习道具的购买功能
/// 采用Notion风格设计，白纸黑字极简美学
class StorePage extends StatefulWidget {
  const StorePage({super.key});

  @override
  State<StorePage> createState() => _StorePageState();
}

class _StorePageState extends State<StorePage> with TickerProviderStateMixin {
  // 搜索和筛选状态
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  ItemCategory _selectedCategory = ItemCategory.all;

  // 用户数据
  double _userBalance = 1575.0; // 从工资钱包同步
  Map<String, int> _userInventory = {}; // 用户背包

  // 道具数据
  List<ShopItem> _shopItems = [];

  @override
  void initState() {
    super.initState();
    _loadShopItems();
    _loadUserInventory();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// 加载商城道具数据
  void _loadShopItems() {
    _shopItems = [
      // 专注道具
      ShopItem(
        id: '1',
        name: '专注药水',
        description: '使用后30分钟内专注力大幅提升，学习效率翻倍',
        effect: '学习时长计算翻倍',
        price: 50.0,
        icon: Icons.local_drink,
        category: ItemCategory.focus,
        rarity: ItemRarity.common,
        duration: const Duration(minutes: 30),
      ),
      ShopItem(
        id: '2',
        name: '时间暂停器',
        description: '暂停计时器而不影响学习状态，适合紧急情况',
        effect: '允许暂停计时5分钟',
        price: 30.0,
        icon: Icons.pause_circle,
        category: ItemCategory.focus,
        rarity: ItemRarity.common,
      ),
      ShopItem(
        id: '3',
        name: '防打扰护盾',
        description: '自动屏蔽所有通知和干扰，创造纯净学习环境',
        effect: '屏蔽通知60分钟',
        price: 80.0,
        icon: Icons.shield,
        category: ItemCategory.focus,
        rarity: ItemRarity.rare,
        duration: const Duration(hours: 1),
      ),

      // 记忆工具
      ShopItem(
        id: '4',
        name: '记忆增强剂',
        description: '显著提升短期记忆能力，背单词效率提升200%',
        effect: '记忆任务效果翻倍',
        price: 100.0,
        icon: Icons.psychology,
        category: ItemCategory.memory,
        rarity: ItemRarity.rare,
        duration: const Duration(hours: 2),
      ),
      ShopItem(
        id: '5',
        name: '宫殿建造包',
        description: '包含10个精美记忆场景模板，快速构建记忆宫殿',
        effect: '解锁10个记忆场景',
        price: 150.0,
        icon: Icons.architecture,
        category: ItemCategory.memory,
        rarity: ItemRarity.epic,
      ),
      ShopItem(
        id: '6',
        name: '联想连接器',
        description: 'AI助手帮你建立知识点之间的联系，提升理解效率',
        effect: 'AI联想提示功能',
        price: 120.0,
        icon: Icons.connect_without_contact,
        category: ItemCategory.memory,
        rarity: ItemRarity.rare,
      ),

      // 学习助手
      ShopItem(
        id: '7',
        name: '答题加速器',
        description: '解题思路更加清晰，计算速度显著提升',
        effect: '习题正确率+20%',
        price: 75.0,
        icon: Icons.speed,
        category: ItemCategory.assistant,
        rarity: ItemRarity.uncommon,
        duration: const Duration(hours: 1),
      ),
      ShopItem(
        id: '8',
        name: '灵感之泉',
        description: '激发创造性思维，写作和创作能力大幅提升',
        effect: '创作任务效率提升',
        price: 90.0,
        icon: Icons.lightbulb,
        category: ItemCategory.assistant,
        rarity: ItemRarity.uncommon,
        duration: const Duration(hours: 3),
      ),
      ShopItem(
        id: '9',
        name: '语言天赋',
        description: '外语学习能力显著增强，口语和听力快速提升',
        effect: '语言学习效率翻倍',
        price: 200.0,
        icon: Icons.translate,
        category: ItemCategory.assistant,
        rarity: ItemRarity.legendary,
        duration: const Duration(hours: 4),
      ),

      // 内容服务
      ShopItem(
        id: '12',
        name: '优质文章阅读券',
        description: '解锁社区精选优质学习文章，获取高质量学习内容和经验分享',
        effect: '解锁精选文章访问权限',
        price: 75.0,
        icon: Icons.menu_book_outlined,
        category: ItemCategory.content,
        rarity: ItemRarity.rare,
        duration: const Duration(days: 7), // 一周有效期
      ),

      // AI功能
      ShopItem(
        id: '13',
        name: 'AI学习伙伴',
        description: '专属AI学习助手，提供个性化学习建议、答疑解惑和学习计划制定',
        effect: '激活AI助手功能',
        price: 350.0,
        icon: Icons.smart_toy_outlined,
        category: ItemCategory.ai,
        rarity: ItemRarity.legendary,
        isLimited: true, // 标记为限量商品
      ),

      // 限时特惠
      ShopItem(
        id: '10',
        name: '学霸大礼包',
        description: '包含专注药水×3、记忆增强剂×2、答题加速器×2',
        effect: '多种道具组合',
        price: 180.0,
        originalPrice: 320.0,
        icon: Icons.card_giftcard,
        category: ItemCategory.special,
        rarity: ItemRarity.epic,
        isLimited: true,
        endTime: DateTime.now().add(const Duration(days: 3)),
      ),
      ShopItem(
        id: '11',
        name: '神秘宝箱',
        description: '有机会获得任意稀有道具，运气爆棚时还能获得传说级道具',
        effect: '随机获得道具',
        price: 100.0,
        icon: Icons.auto_awesome,
        category: ItemCategory.special,
        rarity: ItemRarity.mystery,
        isLootBox: true,
      ),
    ];
  }

  /// 加载用户背包数据
  void _loadUserInventory() {
    // 模拟用户已有道具
    _userInventory = {
      '1': 2, // 专注药水×2
      '4': 1, // 记忆增强剂×1
      '7': 1, // 答题加速器×1
    };
  }

  /// 获取筛选后的道具列表
  List<ShopItem> get _filteredItems {
    var items = _shopItems.where((item) {
      if (_selectedCategory != ItemCategory.all &&
          item.category != _selectedCategory) {
        return false;
      }

      if (_searchQuery.isNotEmpty) {
        return item.name.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            item.description.toLowerCase().contains(_searchQuery.toLowerCase());
      }

      return true;
    }).toList();

    return items;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white, // 修改为纯白色背景，与底部导航栏保持一致
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false, // 禁用自动生成的返回按钮
        title: const Text(
          '道具商城',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.inventory, color: Color(0xFF9B9A97)),
            onPressed: () => _showInventory(),
          ),
        ],
      ),
      body: Column(
        children: [
          // 余额和搜索区域
          _buildBalanceAndSearch(),

          // 分类标签栏
          _buildCategoryTabs(),

          // 道具网格
          Expanded(
            child: Container(
              color: const Color(0xFFF7F6F3), // 只在内容区域使用灰白色背景
              child: _buildItemGrid(),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建余额和搜索区域
  Widget _buildBalanceAndSearch() {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 余额显示
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      const Color(0xFF2E7EED),
                      const Color(0xFF2E7EED).withValues(alpha: 0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.account_balance_wallet,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '¥${_userBalance.toStringAsFixed(0)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        fontFamily: 'monospace',
                      ),
                    ),
                  ],
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () {
                  // 显示充值功能提示
                  UIUtils.showComingSoonSnackBar(context);
                },
                icon: const Icon(
                  Icons.add_circle_outline,
                  size: 16,
                  color: Color(0xFF2E7EED),
                ),
                label: const Text(
                  '充值',
                  style: TextStyle(
                    color: Color(0xFF2E7EED),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 搜索框
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFFF7F6F3),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: TextField(
              controller: _searchController,
              style: const TextStyle(color: Color(0xFF37352F), fontSize: 14),
              decoration: const InputDecoration(
                hintText: '搜索道具名称或效果...',
                hintStyle: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
                prefixIcon: Icon(
                  Icons.search,
                  color: Color(0xFF9B9A97),
                  size: 20,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分类标签栏
  Widget _buildCategoryTabs() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          const Divider(height: 1, color: Color(0xFFEAE9E7)),
          Container(
            height: 50,
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: ItemCategory.values.map((category) {
                final isSelected = _selectedCategory == category;
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 4),
                  child: FilterChip(
                    selected: isSelected,
                    label: Text(
                      category.displayName,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: isSelected
                            ? Colors.white
                            : const Color(0xFF37352F),
                      ),
                    ),
                    backgroundColor: Colors.white,
                    selectedColor: const Color(0xFF2E7EED),
                    checkmarkColor: Colors.white,
                    side: BorderSide(
                      color: isSelected
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFFE3E2E0),
                      width: 1,
                    ),
                    onSelected: (selected) {
                      setState(() {
                        _selectedCategory = category;
                      });
                    },
                  ),
                );
              }).toList(),
            ),
          ),
          const Divider(height: 1, color: Color(0xFFEAE9E7)),
        ],
      ),
    );
  }

  /// 构建道具网格
  Widget _buildItemGrid() {
    final filteredItems = _filteredItems;

    if (filteredItems.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: const EdgeInsets.fromLTRB(
        16,
        16,
        16,
        0,
      ), // 移除底部padding，避免与底部导航栏产生额外空间
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 响应式布局调整
          int columns = 2;
          double aspectRatio = 0.8;

          if (constraints.maxWidth > 900) {
            columns = 4;
            aspectRatio = 0.75;
          } else if (constraints.maxWidth > 600) {
            columns = 3;
            aspectRatio = 0.78;
          }

          return GridView.builder(
            padding: const EdgeInsets.only(
              bottom: 16,
            ), // 在GridView内部添加底部padding
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              childAspectRatio: aspectRatio,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: filteredItems.length,
            itemBuilder: (context, index) {
              final item = filteredItems[index];
              return ShopItemCard(
                item: item,
                userBalance: _userBalance,
                ownedCount: _userInventory[item.id] ?? 0,
                onPurchase: () => _handlePurchase(item),
                onTap: () => _showItemDetail(item),
              );
            },
          );
        },
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      padding: const EdgeInsets.all(16),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: const Color(0xFF9B9A97).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(40),
              ),
              child: const Icon(
                Icons.search_off,
                size: 40,
                color: Color(0xFF9B9A97),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _searchQuery.isNotEmpty
                  ? '未找到相关道具'
                  : '暂无${_selectedCategory.displayName}道具',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '试试其他关键词或分类',
              style: TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
            ),
          ],
        ),
      ),
    );
  }

  /// 处理购买
  void _handlePurchase(ShopItem item) {
    // 检查余额
    if (_userBalance < item.price) {
      _showInsufficientBalance();
      return;
    }

    // 显示购买确认对话框
    showDialog(
      context: context,
      builder: (context) => PurchaseDialog(
        item: item,
        userBalance: _userBalance,
        onConfirm: () => _completePurchase(item),
      ),
    );
  }

  /// 完成购买
  void _completePurchase(ShopItem item) {
    setState(() {
      _userBalance -= item.price;
      _userInventory[item.id] = (_userInventory[item.id] ?? 0) + 1;
    });

    // 震动反馈
    HapticFeedback.mediumImpact();

    // 处理特殊商品的购买后逻辑
    _handleSpecialItemPurchase(item);

    // 显示购买成功
    _showPurchaseSuccess(item);
  }

  /// 处理特殊商品购买后的逻辑
  void _handleSpecialItemPurchase(ShopItem item) {
    switch (item.id) {
      case '12': // 优质文章阅读券
        _activateArticleAccess();
        break;
      case '13': // AI学习伙伴
        _activateAIAssistant();
        break;
    }
  }

  /// 激活优质文章访问权限
  void _activateArticleAccess() {
    // TODO: 实现优质文章访问权限激活
    // 这里可以调用相关服务来解锁社区精选文章
    debugPrint('✅ 优质文章阅读券已激活，有效期7天');
  }

  /// 激活AI学习助手
  void _activateAIAssistant() {
    // TODO: 实现AI助手功能激活
    // 这里可以调用AI服务初始化，设置用户权限等
    debugPrint('✅ AI学习伙伴已激活，开始提供个性化学习服务');
  }

  /// 显示余额不足
  void _showInsufficientBalance() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('余额不足'),
        content: const Text('您的余额不足以购买此道具，请先完成学习任务赚取工资。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('知道了'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // 关闭对话框
              Navigator.of(context).pop(); // 返回上一页面去赚钱
            },
            child: const Text('去赚钱'),
          ),
        ],
      ),
    );
  }

  /// 显示购买成功
  void _showPurchaseSuccess(ShopItem item) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(item.icon, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '成功购买 ${item.name}！',
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: const Color(0xFF0F7B6C),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: '查看背包',
          textColor: Colors.white,
          onPressed: () => _showInventory(),
        ),
      ),
    );
  }

  /// 显示道具详情
  void _showItemDetail(ShopItem item) {
    showDialog(
      context: context,
      builder: (context) => ItemDetailDialog(
        item: item,
        ownedCount: _userInventory[item.id] ?? 0,
        onPurchase: () {
          Navigator.of(context).pop();
          _handlePurchase(item);
        },
      ),
    );
  }

  /// 显示背包
  void _showInventory() {
    context.push(
      '/inventory',
      extra: {'inventory': _userInventory, 'allItems': _shopItems},
    );
  }
}

/// 道具卡片组件
class ShopItemCard extends StatelessWidget {
  final ShopItem item;
  final double userBalance;
  final int ownedCount;
  final VoidCallback onPurchase;
  final VoidCallback onTap;

  const ShopItemCard({
    super.key,
    required this.item,
    required this.userBalance,
    required this.ownedCount,
    required this.onPurchase,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final canAfford = userBalance >= item.price;
    final hasDiscount = item.originalPrice != null;
    final isAIItem = item.category == ItemCategory.ai;

    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isAIItem
                ? const Color(0xFFD9730D) // AI商品使用金色边框
                : item.rarity.color.withValues(alpha: 0.3),
            width: isAIItem ? 2.0 : 1.5, // AI商品边框更粗
          ),
          boxShadow: [
            BoxShadow(
              color: isAIItem
                  ? const Color(0xFFD9730D).withValues(alpha: 0.15) // AI商品金色阴影
                  : Colors.black.withValues(alpha: 0.04),
              blurRadius: isAIItem ? 12 : 8, // AI商品阴影更大
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 头部：图标和稀有度
            Container(
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: item.category == ItemCategory.ai
                      ? [
                          const Color(
                            0xFFD9730D,
                          ).withValues(alpha: 0.2), // AI商品使用金色渐变
                          const Color(0xFFFFD700).withValues(alpha: 0.1),
                        ]
                      : [
                          item.rarity.color.withValues(alpha: 0.1),
                          item.rarity.color.withValues(alpha: 0.05),
                        ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Stack(
                children: [
                  // AI商品添加背景装饰
                  if (item.category == ItemCategory.ai)
                    Positioned.fill(
                      child: CustomPaint(painter: AIItemBackgroundPainter()),
                    ),
                  Center(
                    child: Container(
                      width: item.category == ItemCategory.ai
                          ? 56
                          : 48, // AI商品图标更大
                      height: item.category == ItemCategory.ai ? 56 : 48,
                      decoration: BoxDecoration(
                        color: item.category == ItemCategory.ai
                            ? const Color(0xFFD9730D).withValues(alpha: 0.3)
                            : item.rarity.color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(
                          item.category == ItemCategory.ai ? 28 : 24,
                        ),
                        boxShadow: item.category == ItemCategory.ai
                            ? [
                                BoxShadow(
                                  color: const Color(
                                    0xFFD9730D,
                                  ).withValues(alpha: 0.3),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ]
                            : null,
                      ),
                      child: Icon(
                        item.icon,
                        size: item.category == ItemCategory.ai ? 32 : 28,
                        color: item.category == ItemCategory.ai
                            ? const Color(0xFFD9730D)
                            : item.rarity.color,
                      ),
                    ),
                  ),
                  if (ownedCount > 0)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFF0F7B6C),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          '×$ownedCount',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  if (item.isLimited)
                    Positioned(
                      top: 8,
                      left: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFE03E3E),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Text(
                          '限时',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  // AI商品特殊标签
                  if (item.category == ItemCategory.ai)
                    Positioned(
                      top: 8,
                      left: item.isLimited ? 40 : 8, // 如果已有限时标签，则向右偏移
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 4,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: const Color(0xFFD9730D),
                          borderRadius: BorderRadius.circular(4),
                          boxShadow: [
                            BoxShadow(
                              color: const Color(
                                0xFFD9730D,
                              ).withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(0, 1),
                            ),
                          ],
                        ),
                        child: const Text(
                          '高级',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 8,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // 内容区域
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 名称
                    Text(
                      item.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: const Color(0xFF37352F),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const SizedBox(height: 4),

                    // 效果描述
                    Text(
                      item.effect,
                      style: const TextStyle(
                        fontSize: 11,
                        color: Color(0xFF787774),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),

                    const Spacer(),

                    // 价格和购买按钮
                    Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (hasDiscount) ...[
                                Text(
                                  '¥${item.originalPrice!.toStringAsFixed(0)}',
                                  style: const TextStyle(
                                    fontSize: 10,
                                    color: Color(0xFF9B9A97),
                                    decoration: TextDecoration.lineThrough,
                                  ),
                                ),
                                const SizedBox(height: 2),
                              ],
                              Text(
                                '¥${item.price.toStringAsFixed(0)}',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                  color: hasDiscount
                                      ? const Color(0xFFE03E3E)
                                      : const Color(0xFF2E7EED),
                                  fontFamily: 'monospace',
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(width: 8),
                        SizedBox(
                          width: 60,
                          height: 28,
                          child: ElevatedButton(
                            onPressed: canAfford ? onPurchase : null,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: canAfford
                                  ? const Color(0xFF2E7EED)
                                  : Colors.grey.shade300,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.zero,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                              elevation: 0,
                            ),
                            child: Text(
                              '购买',
                              style: TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w600,
                                color: canAfford
                                    ? Colors.white
                                    : Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// 购买确认对话框
class PurchaseDialog extends StatelessWidget {
  final ShopItem item;
  final double userBalance;
  final VoidCallback onConfirm;

  const PurchaseDialog({
    super.key,
    required this.item,
    required this.userBalance,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(item.icon, color: item.rarity.color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              '购买 ${item.name}',
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
            ),
          ),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            item.description,
            style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
          ),

          // AI商品特殊说明
          if (item.category == ItemCategory.ai) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFD9730D).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: const Color(0xFFD9730D).withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        size: 16,
                        color: const Color(0xFFD9730D),
                      ),
                      const SizedBox(width: 8),
                      const Text(
                        'AI功能特权',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFD9730D),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• 个性化学习建议和计划制定\n• 24/7智能答疑解惑服务\n• 学习进度分析和优化建议\n• 专属AI学习伙伴陪伴',
                    style: TextStyle(
                      fontSize: 11,
                      color: Color(0xFF787774),
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],

          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: const Color(0xFFF7F6F3),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              children: [
                _buildDetailRow('道具效果', item.effect),
                if (item.duration != null) ...[
                  const SizedBox(height: 8),
                  _buildDetailRow('持续时间', _formatDuration(item.duration!)),
                ],
                const SizedBox(height: 8),
                _buildDetailRow('稀有度', item.rarity.displayName),
                const SizedBox(height: 8),
                const Divider(height: 1),
                const SizedBox(height: 8),
                _buildDetailRow(
                  '购买价格',
                  '¥${item.price.toStringAsFixed(0)}',
                  valueColor: const Color(0xFF2E7EED),
                ),
                _buildDetailRow(
                  '余额',
                  '¥${userBalance.toStringAsFixed(0)}',
                  valueColor: const Color(0xFF0F7B6C),
                ),
                _buildDetailRow(
                  '购买后余额',
                  '¥${(userBalance - item.price).toStringAsFixed(0)}',
                  valueColor: const Color(0xFF9B9A97),
                ),
              ],
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            Navigator.of(context).pop();
            onConfirm();
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: item.category == ItemCategory.ai
                ? const Color(0xFFD9730D) // AI商品使用金色按钮
                : const Color(0xFF2E7EED),
            foregroundColor: Colors.white,
          ),
          child: Text(item.category == ItemCategory.ai ? '激活AI助手' : '确认购买'),
        ),
      ],
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: valueColor ?? const Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}小时';
    } else {
      return '${duration.inMinutes}分钟';
    }
  }
}

/// 道具详情对话框
class ItemDetailDialog extends StatelessWidget {
  final ShopItem item;
  final int ownedCount;
  final VoidCallback onPurchase;

  const ItemDetailDialog({
    super.key,
    required this.item,
    required this.ownedCount,
    required this.onPurchase,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        constraints: const BoxConstraints(maxHeight: 500),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 头部
            Container(
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: item.category == ItemCategory.ai
                      ? [
                          const Color(0xFFD9730D).withValues(alpha: 0.3),
                          const Color(0xFFFFD700).withValues(alpha: 0.1),
                        ]
                      : [
                          item.rarity.color.withValues(alpha: 0.2),
                          item.rarity.color.withValues(alpha: 0.1),
                        ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 64,
                      height: 64,
                      decoration: BoxDecoration(
                        color: item.rarity.color.withValues(alpha: 0.3),
                        borderRadius: BorderRadius.circular(32),
                      ),
                      child: Icon(
                        item.icon,
                        size: 36,
                        color: item.rarity.color,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      item.name,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 内容
            Flexible(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '道具描述',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      item.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Color(0xFF787774),
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 16),

                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: const Color(0xFFF7F6F3),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        children: [
                          _buildInfoRow('道具效果', item.effect),
                          if (item.duration != null) ...[
                            const SizedBox(height: 8),
                            _buildInfoRow(
                              '持续时间',
                              _formatDuration(item.duration!),
                            ),
                          ],
                          const SizedBox(height: 8),
                          _buildInfoRow('稀有度', item.rarity.displayName),
                          const SizedBox(height: 8),
                          _buildInfoRow('已拥有', '$ownedCount个'),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // 底部按钮
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(top: BorderSide(color: Color(0xFFEAE9E7))),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '¥${item.price.toStringAsFixed(0)}',
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF2E7EED),
                        fontFamily: 'monospace',
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: onPurchase,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(
                        horizontal: 24,
                        vertical: 12,
                      ),
                    ),
                    child: const Text('立即购买'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF9B9A97)),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}小时';
    } else {
      return '${duration.inMinutes}分钟';
    }
  }
}

/// 背包页面
class InventoryPage extends StatelessWidget {
  final Map<String, int> inventory;
  final List<ShopItem> allItems;

  const InventoryPage({
    super.key,
    required this.inventory,
    required this.allItems,
  });

  @override
  Widget build(BuildContext context) {
    final ownedItems = allItems
        .where((item) => (inventory[item.id] ?? 0) > 0)
        .toList();

    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          '我的背包',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: SafeArea(
        child: ownedItems.isEmpty
            ? _buildEmptyInventory()
            : _buildInventoryGrid(ownedItems),
      ),
    );
  }

  Widget _buildEmptyInventory() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: const Color(0xFF9B9A97).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(40),
            ),
            child: const Icon(
              Icons.inventory_2,
              size: 40,
              color: Color(0xFF9B9A97),
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            '背包空空如也',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '去商城购买一些实用的学习道具吧',
            style: TextStyle(fontSize: 14, color: Color(0xFF9B9A97)),
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryGrid(List<ShopItem> ownedItems) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          // 响应式布局调整
          int columns = 2;
          double aspectRatio = 1.1;

          if (constraints.maxWidth > 900) {
            columns = 4;
            aspectRatio = 1.0;
          } else if (constraints.maxWidth > 600) {
            columns = 3;
            aspectRatio = 1.05;
          }

          return GridView.builder(
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: columns,
              childAspectRatio: aspectRatio,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
            ),
            itemCount: ownedItems.length,
            itemBuilder: (context, index) {
              final item = ownedItems[index];
              final count = inventory[item.id] ?? 0;

              return InventoryItemCard(
                item: item,
                count: count,
                onUse: () => _useItem(item),
              );
            },
          );
        },
      ),
    );
  }

  void _useItem(ShopItem item) {
    // TODO: 实现道具使用逻辑
  }
}

/// 背包道具卡片
class InventoryItemCard extends StatelessWidget {
  final ShopItem item;
  final int count;
  final VoidCallback onUse;

  const InventoryItemCard({
    super.key,
    required this.item,
    required this.count,
    required this.onUse,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: item.rarity.color.withValues(alpha: 0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 防止Column占用过多空间
        children: [
          // 头部：图标和数量
          Flexible(
            flex: 3,
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(
                minHeight: 80, // 确保最小高度
                maxHeight: 120, // 限制最大高度
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    item.rarity.color.withValues(alpha: 0.1),
                    item.rarity.color.withValues(alpha: 0.05),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(12),
                  topRight: Radius.circular(12),
                ),
              ),
              child: Stack(
                children: [
                  Center(
                    child: Container(
                      width: 48,
                      height: 48,
                      decoration: BoxDecoration(
                        color: item.rarity.color.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(24),
                      ),
                      child: Icon(
                        item.icon,
                        size: 28,
                        color: item.rarity.color,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 8,
                    right: 8,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: const Color(0xFF0F7B6C),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        '×$count',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 底部：名称和使用按钮
          Flexible(
            flex: 2,
            child: Container(
              width: double.infinity,
              constraints: const BoxConstraints(
                minHeight: 60, // 确保底部区域有足够空间
              ),
              padding: const EdgeInsets.all(8), // 减少padding避免空间不足
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      item.name,
                      style: const TextStyle(
                        fontSize: 13, // 稍微减小字体
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF37352F),
                      ),
                      maxLines: 2, // 允许两行文本
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  const SizedBox(height: 6), // 固定间距替代Spacer
                  SizedBox(
                    width: double.infinity,
                    height: 26, // 稍微减小按钮高度
                    child: ElevatedButton(
                      onPressed: onUse,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: item.rarity.color,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.zero,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(6),
                        ),
                        elevation: 0,
                      ),
                      child: const Text(
                        '使用',
                        style: TextStyle(
                          fontSize: 11, // 稍微减小字体
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 道具数据模型
class ShopItem {
  final String id;
  final String name;
  final String description;
  final String effect;
  final double price;
  final double? originalPrice; // 原价（用于折扣显示）
  final IconData icon;
  final ItemCategory category;
  final ItemRarity rarity;
  final Duration? duration; // 道具持续时间
  final bool isLimited; // 是否限时
  final bool isLootBox; // 是否为抽奖盒
  final DateTime? endTime; // 限时结束时间

  ShopItem({
    required this.id,
    required this.name,
    required this.description,
    required this.effect,
    required this.price,
    this.originalPrice,
    required this.icon,
    required this.category,
    required this.rarity,
    this.duration,
    this.isLimited = false,
    this.isLootBox = false,
    this.endTime,
  });
}

/// 道具分类枚举
enum ItemCategory {
  all, // 全部
  focus, // 专注道具
  memory, // 记忆工具
  assistant, // 学习助手
  content, // 内容服务
  ai, // AI功能
  special; // 限时特惠

  String get displayName {
    switch (this) {
      case ItemCategory.all:
        return '全部';
      case ItemCategory.focus:
        return '专注道具';
      case ItemCategory.memory:
        return '记忆工具';
      case ItemCategory.assistant:
        return '学习助手';
      case ItemCategory.content:
        return '内容服务';
      case ItemCategory.ai:
        return 'AI功能';
      case ItemCategory.special:
        return '限时特惠';
    }
  }
}

/// 道具稀有度枚举
enum ItemRarity {
  common, // 普通
  uncommon, // 不凡
  rare, // 稀有
  epic, // 史诗
  legendary, // 传说
  mystery; // 神秘

  String get displayName {
    switch (this) {
      case ItemRarity.common:
        return '普通';
      case ItemRarity.uncommon:
        return '不凡';
      case ItemRarity.rare:
        return '稀有';
      case ItemRarity.epic:
        return '史诗';
      case ItemRarity.legendary:
        return '传说';
      case ItemRarity.mystery:
        return '神秘';
    }
  }

  Color get color {
    switch (this) {
      case ItemRarity.common:
        return const Color(0xFF9B9A97);
      case ItemRarity.uncommon:
        return const Color(0xFF0F7B6C);
      case ItemRarity.rare:
        return const Color(0xFF2E7EED);
      case ItemRarity.epic:
        return const Color(0xFF7C3AED);
      case ItemRarity.legendary:
        return const Color(0xFFD9730D);
      case ItemRarity.mystery:
        return const Color(0xFFE03E3E);
    }
  }
}

/// AI商品背景装饰器
class AIItemBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFD9730D).withValues(alpha: 0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0;

    // 绘制装饰性圆形
    final center = Offset(size.width / 2, size.height / 2);

    // 绘制多个同心圆
    for (var i = 0; i < 3; i++) {
      final radius = 20.0 + (i * 10.0);
      canvas.drawCircle(center, radius, paint);
    }

    // 绘制装饰性点
    final dotPaint = Paint()
      ..color = const Color(0xFFD9730D).withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;

    // 在圆周上均匀分布一些点
    const numDots = 8;
    const dotRadius = 2.0;
    const outerRadius = 45.0;

    for (var i = 0; i < numDots; i++) {
      final angle = (i / numDots) * 2 * 3.14159;
      final x = center.dx + outerRadius * cos(angle);
      final y = center.dy + outerRadius * sin(angle);
      canvas.drawCircle(Offset(x, y), dotRadius, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
