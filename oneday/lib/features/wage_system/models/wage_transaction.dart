import 'package:flutter/material.dart';

/// 工资交易数据模型
class WageTransaction {
  final String id;
  final double amount;
  final TransactionType type;
  final String description;
  final DateTime timestamp;
  final String? relatedTaskId;

  WageTransaction({
    required this.id,
    required this.amount,
    required this.type,
    required this.description,
    required this.timestamp,
    this.relatedTaskId,
  });

  /// 从JSON创建实例
  factory WageTransaction.fromJson(Map<String, dynamic> json) {
    return WageTransaction(
      id: json['id'] as String,
      amount: (json['amount'] as num).toDouble(),
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.studyIncome,
      ),
      description: json['description'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      relatedTaskId: json['relatedTaskId'] as String?,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'amount': amount,
      'type': type.name,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'relatedTaskId': relatedTaskId,
    };
  }

  /// 复制并修改部分属性
  WageTransaction copyWith({
    String? id,
    double? amount,
    TransactionType? type,
    String? description,
    DateTime? timestamp,
    String? relatedTaskId,
  }) {
    return WageTransaction(
      id: id ?? this.id,
      amount: amount ?? this.amount,
      type: type ?? this.type,
      description: description ?? this.description,
      timestamp: timestamp ?? this.timestamp,
      relatedTaskId: relatedTaskId ?? this.relatedTaskId,
    );
  }

  @override
  String toString() {
    return 'WageTransaction(id: $id, amount: $amount, type: $type, description: $description, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is WageTransaction &&
        other.id == id &&
        other.amount == amount &&
        other.type == type &&
        other.description == description &&
        other.timestamp == timestamp &&
        other.relatedTaskId == relatedTaskId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      amount,
      type,
      description,
      timestamp,
      relatedTaskId,
    );
  }
}

/// 交易类型枚举
enum TransactionType {
  studyIncome, // 学习收入
  bonus, // 奖励
  achievement, // 成就奖励
  itemPurchase; // 道具购买

  String get displayName {
    switch (this) {
      case TransactionType.studyIncome:
        return '学习收入';
      case TransactionType.bonus:
        return '奖励';
      case TransactionType.achievement:
        return '成就奖励';
      case TransactionType.itemPurchase:
        return '道具购买';
    }
  }

  IconData get icon {
    switch (this) {
      case TransactionType.studyIncome:
        return Icons.school;
      case TransactionType.bonus:
        return Icons.card_giftcard;
      case TransactionType.achievement:
        return Icons.emoji_events;
      case TransactionType.itemPurchase:
        return Icons.shopping_bag;
    }
  }

  Color get color {
    switch (this) {
      case TransactionType.studyIncome:
        return const Color(0xFF0F7B6C);
      case TransactionType.bonus:
        return const Color(0xFF2E7EED);
      case TransactionType.achievement:
        return const Color(0xFFD9730D);
      case TransactionType.itemPurchase:
        return const Color(0xFFE03E3E);
    }
  }
}
