import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'user_guide_page.dart';

/// 帮助与反馈页面
///
/// 提供常见问题、用户反馈、联系开发者、版本信息等功能
/// 采用Notion风格设计，白色背景，12px圆角边框
class HelpFeedbackPage extends StatefulWidget {
  const HelpFeedbackPage({super.key});

  @override
  State<HelpFeedbackPage> createState() => _HelpFeedbackPageState();
}

class _HelpFeedbackPageState extends State<HelpFeedbackPage> {
  final TextEditingController _feedbackController = TextEditingController();
  final FocusNode _feedbackFocusNode = FocusNode();

  String _selectedFeedbackType = '功能建议';
  String _appVersion = '';
  bool _isSubmitting = false;

  // 反馈类型选项
  final List<String> _feedbackTypes = ['功能建议', '问题反馈', '使用体验', '其他'];

  // 常见问题数据
  final List<Map<String, String>> _faqItems = [
    {
      'question': '如何创建时间盒子任务？',
      'answer': '在时间盒子页面点击右上角的"+"按钮，填写任务名称、选择分类和设置时长即可创建新任务。',
    },
    {
      'question': '知忆相册如何使用？',
      'answer': '知忆相册基于记忆宫殿法，您可以拍摄或选择场景图片，然后在图片上添加知识点标记，通过空间记忆提升学习效果。',
    },
    {
      'question': '如何查看学习统计？',
      'answer': '在首页可以查看今日学习概览，点击"学习报告"可以查看详细的学习数据分析和趋势图表。',
    },
    {
      'question': '动觉记忆训练是什么？',
      'answer': '动觉记忆训练结合身体动作和记忆，通过自定义动作库将抽象知识转化为具体动作，提升记忆效果。',
    },
    {
      'question': '如何备份我的学习数据？',
      'answer': '在设置页面的"数据管理"部分，可以开启自动备份功能，或手动导出学习数据到本地文件。',
    },
  ];

  // 社交媒体联系方式
  final List<Map<String, dynamic>> _socialContacts = [
    {
      'name': '小红书',
      'icon': Icons.favorite,
      'color': Color(0xFFFF2442),
      'description': '@OneDay学习助手',
      'url': 'https://www.xiaohongshu.com/user/profile/oneday',
    },
    {
      'name': '抖音',
      'icon': Icons.music_note,
      'color': Color(0xFF000000),
      'description': '@OneDay官方',
      'url': 'https://www.douyin.com/user/oneday',
    },
    {
      'name': '微博',
      'icon': Icons.public,
      'color': Color(0xFFE6162D),
      'description': '@OneDay学习平台',
      'url': 'https://weibo.com/oneday',
    },
    {
      'name': 'B站',
      'icon': Icons.play_circle_filled,
      'color': Color(0xFF00A1D6),
      'description': '@OneDay学习方法',
      'url': 'https://space.bilibili.com/oneday',
    },
    {
      'name': '知乎',
      'icon': Icons.quiz,
      'color': Color(0xFF0084FF),
      'description': '@OneDay团队',
      'url': 'https://www.zhihu.com/people/oneday',
    },
  ];

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
    _loadFeedbackDraft();

    // 监听焦点变化，失去焦点时自动保存草稿
    _feedbackFocusNode.addListener(() {
      if (!_feedbackFocusNode.hasFocus) {
        _saveFeedbackDraft();
      }
    });
  }

  @override
  void dispose() {
    _feedbackController.dispose();
    _feedbackFocusNode.dispose();
    super.dispose();
  }

  /// 加载应用版本信息
  Future<void> _loadAppVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _appVersion = '${packageInfo.version}+${packageInfo.buildNumber}';
      });
    } catch (e) {
      setState(() {
        _appVersion = '1.0.0+1';
      });
    }
  }

  /// 加载反馈草稿
  Future<void> _loadFeedbackDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final draft = prefs.getString('feedback_draft');
      final type = prefs.getString('feedback_type');

      if (draft != null && draft.isNotEmpty) {
        setState(() {
          _feedbackController.text = draft;
        });
      }

      if (type != null && _feedbackTypes.contains(type)) {
        setState(() {
          _selectedFeedbackType = type;
        });
      }
    } catch (e) {
      print('加载反馈草稿失败: $e');
    }
  }

  /// 保存反馈草稿
  Future<void> _saveFeedbackDraft() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('feedback_draft', _feedbackController.text);
      await prefs.setString('feedback_type', _selectedFeedbackType);
    } catch (e) {
      print('保存反馈草稿失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF7F6F3),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        centerTitle: false,
        title: const Text(
          '帮助与反馈',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 22,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          onPressed: () => Navigator.of(context).pop(),
          icon: const Icon(Icons.arrow_back, color: Color(0xFF37352F)),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 常见问题部分
            _buildFAQSection(),

            const SizedBox(height: 24),

            // 用户反馈部分
            _buildFeedbackSection(),

            const SizedBox(height: 24),

            // 联系开发者部分
            _buildContactSection(),

            const SizedBox(height: 24),

            // 应用信息部分
            _buildAppInfoSection(),

            const SizedBox(height: 24),

            // 用户指南部分
            _buildUserGuideSection(),

            const SizedBox(height: 32),
          ],
        ),
      ),
    );
  }

  /// 构建常见问题部分
  Widget _buildFAQSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('常见问题', Icons.help_outline),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: _faqItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              return Column(
                children: [
                  _buildFAQItem(item['question']!, item['answer']!),
                  if (index < _faqItems.length - 1) _buildDivider(),
                ],
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// 构建FAQ项目
  Widget _buildFAQItem(String question, String answer) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: ExpansionTile(
        title: Text(
          question,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: Text(
              answer,
              style: TextStyle(
                color: const Color(0xFF37352F).withValues(alpha: 0.7),
                fontSize: 14,
                height: 1.5,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建用户反馈部分
  Widget _buildFeedbackSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('用户反馈', Icons.feedback_outlined),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 反馈类型选择
              const Text(
                '反馈类型',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              Wrap(
                spacing: 8,
                children: _feedbackTypes.map((type) {
                  final isSelected = type == _selectedFeedbackType;
                  return FilterChip(
                    label: Text(type),
                    selected: isSelected,
                    onSelected: (selected) {
                      if (selected) {
                        setState(() {
                          _selectedFeedbackType = type;
                        });
                        _saveFeedbackDraft();
                      }
                    },
                    backgroundColor: Colors.white,
                    selectedColor: const Color(
                      0xFF2E7EED,
                    ).withValues(alpha: 0.1),
                    checkmarkColor: const Color(0xFF2E7EED),
                    labelStyle: TextStyle(
                      color: isSelected
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFF37352F),
                    ),
                    side: BorderSide(
                      color: isSelected
                          ? const Color(0xFF2E7EED)
                          : const Color(0xFF37352F).withValues(alpha: 0.2),
                    ),
                  );
                }).toList(),
              ),

              const SizedBox(height: 20),

              // 详细描述输入框
              const Text(
                '详细描述',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 12),
              TextField(
                controller: _feedbackController,
                focusNode: _feedbackFocusNode,
                maxLines: 6,
                decoration: InputDecoration(
                  hintText: '请详细描述您的问题或建议...',
                  hintStyle: TextStyle(
                    color: const Color(0xFF37352F).withValues(alpha: 0.5),
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(
                      color: const Color(0xFF37352F).withValues(alpha: 0.2),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                  ),
                ),
                onSubmitted: (_) => _submitFeedback(),
              ),

              const SizedBox(height: 16),

              // 提交按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _isSubmitting ? null : _submitFeedback,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2E7EED),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isSubmitting
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                      : const Text('提交反馈'),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建联系开发者部分
  Widget _buildContactSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('联系开发者', Icons.contact_support_outlined),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '通过以下渠道联系我们：',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 16),
              ..._socialContacts.map(
                (contact) => Column(
                  children: [
                    _buildContactItem(
                      contact['name'],
                      contact['icon'],
                      contact['color'],
                      contact['description'],
                    ),
                    if (contact != _socialContacts.last)
                      const SizedBox(height: 12),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建联系方式项目
  Widget _buildContactItem(
    String name,
    IconData icon,
    Color color,
    String description,
  ) {
    return InkWell(
      onTap: () => _openSocialMedia(name),
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: const Color(0xFF37352F).withValues(alpha: 0.1),
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 20),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    name,
                    style: const TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    description,
                    style: TextStyle(
                      color: const Color(0xFF37352F).withValues(alpha: 0.6),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: const Color(0xFF37352F).withValues(alpha: 0.3),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建应用信息部分
  Widget _buildAppInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('应用信息', Icons.info_outline),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              _buildInfoRow('应用名称', 'OneDay'),
              _buildDivider(),
              _buildInfoRow('当前版本', _appVersion),
              _buildDivider(),
              _buildInfoRow('开发团队', 'OneDay团队'),
              _buildDivider(),
              _buildInfoRow('应用简介', '让每一天都充满收获'),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建用户指南部分
  Widget _buildUserGuideSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle('用户指南', Icons.menu_book_outlined),
        const SizedBox(height: 16),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: const Color(0xFF37352F).withValues(alpha: 0.1),
            ),
          ),
          child: Column(
            children: [
              _buildGuideItem(
                '快速入门',
                '了解OneDay的基本功能和使用方法',
                Icons.rocket_launch_outlined,
                () => _openUserGuide('quick_start'),
              ),
              _buildDivider(),
              _buildGuideItem(
                '功能详解',
                '深入了解时间盒子、知忆相册等核心功能',
                Icons.explore_outlined,
                () => _openUserGuide('features'),
              ),
              _buildDivider(),
              _buildGuideItem(
                '学习方法',
                '掌握高效的学习技巧和记忆方法',
                Icons.psychology_outlined,
                () => _openUserGuide('methods'),
              ),
              _buildDivider(),
              _buildGuideItem(
                '常见问题',
                '查看更多常见问题和解决方案',
                Icons.quiz_outlined,
                () => _openUserGuide('faq'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建指南项目
  Widget _buildGuideItem(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: const Color(0xFF2E7EED), size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      color: Color(0xFF37352F),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: const Color(0xFF37352F).withValues(alpha: 0.6),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: const Color(0xFF37352F).withValues(alpha: 0.3),
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              color: const Color(0xFF37352F).withValues(alpha: 0.7),
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分节标题
  Widget _buildSectionTitle(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFF2E7EED), size: 24),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建分割线
  Widget _buildDivider() {
    return Divider(
      height: 1,
      color: const Color(0xFF37352F).withValues(alpha: 0.1),
    );
  }

  /// 提交反馈
  Future<void> _submitFeedback() async {
    if (_feedbackController.text.trim().isEmpty) {
      _showSnackBar('请输入反馈内容');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      // 模拟提交过程
      await Future.delayed(const Duration(seconds: 1));

      // 清除草稿
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('feedback_draft');
      await prefs.remove('feedback_type');

      // 清空输入框
      _feedbackController.clear();

      _showSnackBar('感谢您的反馈，我们会认真处理');
    } catch (e) {
      _showSnackBar('提交失败，请稍后重试');
    } finally {
      setState(() {
        _isSubmitting = false;
      });
    }
  }

  /// 打开社交媒体
  Future<void> _openSocialMedia(String platform) async {
    try {
      // 查找对应平台的URL
      final contact = _socialContacts.firstWhere(
        (contact) => contact['name'] == platform,
        orElse: () => {},
      );

      if (contact.isEmpty || contact['url'] == null) {
        _showSnackBar('暂未配置$platform链接');
        return;
      }

      final url = Uri.parse(contact['url']);

      // 检查是否可以启动URL
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
      } else {
        _showSnackBar('无法打开$platform，请检查是否安装了相应应用');
      }
    } catch (e) {
      print('打开社交媒体失败: $e');
      _showSnackBar('打开$platform失败，请稍后重试');
    }
  }

  /// 打开用户指南
  void _openUserGuide(String section) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => UserGuidePage(section: section)),
    );
  }

  /// 显示SnackBar
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFF2E7EED),
        duration: const Duration(seconds: 1),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
