import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../models/ability_radar_models.dart';

/// 能力雷达图组件
///
/// 使用fl_chart库实现五维雷达图，支持：
/// 1. 动画效果和平滑过渡
/// 2. 交互功能（点击查看详情）
/// 3. 自定义颜色和样式
/// 4. 响应式布局
class AbilityRadarChart extends StatefulWidget {
  final AbilityRadarData data;
  final double size;
  final bool showAnimation;
  final Function(AbilityDimension)? onDimensionTap;
  final bool showLabels;
  final bool showGrid;

  const AbilityRadarChart({
    super.key,
    required this.data,
    this.size = 300,
    this.showAnimation = true,
    this.onDimensionTap,
    this.showLabels = true,
    this.showGrid = true,
  });

  @override
  State<AbilityRadarChart> createState() => _AbilityRadarChartState();
}

class _AbilityRadarChartState extends State<AbilityRadarChart>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOutCubic,
    );

    if (widget.showAnimation) {
      _animationController.forward();
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.size,
      height: widget.size,
      padding: const EdgeInsets.all(24),
      child: AnimatedBuilder(
        animation: _animation,
        builder: (context, child) {
          return RadarChart(
            RadarChartData(
              radarTouchData: RadarTouchData(
                enabled: true,
                touchCallback: _handleTouch,
              ),
              dataSets: _buildDataSets(),
              radarBackgroundColor: Colors.transparent,
              borderData: FlBorderData(show: false),
              radarBorderData: const BorderSide(color: Colors.transparent),
              titlePositionPercentageOffset: 0.15, // 关键修复：调整为0.15确保轴线与圆周完美对齐
              titleTextStyle: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              getTitle: (index, angle) =>
                  RadarChartTitle(text: _getTitleText(index), angle: angle),
              tickCount: 5, // 关键修复：改为5个刻度(0,25,50,75,100)确保100分对应最外层
              ticksTextStyle: const TextStyle(
                color: Colors.transparent, // 设置为透明，隐藏刻度数字标签
                fontSize: 0, // 设置字体大小为0，进一步确保不显示
              ),
              tickBorderData: const BorderSide(
                color: Color(0xFFE3E2E0),
                width: 1,
              ),
              gridBorderData: const BorderSide(
                color: Color(0xFFE3E2E0),
                width: 1,
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建雷达图数据集
  List<RadarDataSet> _buildDataSets() {
    final animationValue = widget.showAnimation ? _animation.value : 1.0;

    // 主数据集 - 当前能力值
    final mainDataSet = RadarDataSet(
      fillColor: const Color(
        0xFF2E7EED,
      ).withValues(alpha: 0.2 * animationValue),
      borderColor: const Color(0xFF2E7EED),
      borderWidth: 2,
      entryRadius: 4,
      dataEntries: widget.data.dimensions.map((dimension) {
        return RadarEntry(value: dimension.score * animationValue);
      }).toList(),
    );

    // 最小值参考线数据集 - 确保雷达图从0分开始计算
    final minRangeDataSet = RadarDataSet(
      fillColor: Colors.transparent,
      borderColor: Colors.transparent, // 不可见的边框
      borderWidth: 0,
      entryRadius: 0,
      dataEntries: List.generate(
        widget.data.dimensions.length,
        (index) => const RadarEntry(value: 0), // 0分为最小值，确保从中心开始
      ),
    );

    // 内接五边形数据集 - 80分参考线，顶点精确位于第二大圆周上
    final innerPentagonDataSet = RadarDataSet(
      fillColor: Colors.transparent,
      borderColor: const Color(0xFF9B9A97).withValues(alpha: 0.6), // 稍微增强可见度
      borderWidth: 1.2, // 稍微加粗，与外切五边形区分
      entryRadius: 0,
      dataEntries: List.generate(
        widget.data.dimensions.length,
        (index) => const RadarEntry(value: 80), // 80分内接五边形，顶点位于80分圆周上
      ),
    );

    // 坐标系边界数据集 - 扩展坐标系以容纳外切五边形
    const circumscribedPentagonValue = 123.6; // 外切五边形顶点值
    final maxRangeDataSet = RadarDataSet(
      fillColor: Colors.transparent,
      borderColor: Colors.transparent, // 完全透明，仅用于坐标系范围控制
      borderWidth: 0, // 无边框
      entryRadius: 0,
      dataEntries: List.generate(
        widget.data.dimensions.length,
        (index) => const RadarEntry(
          value: circumscribedPentagonValue,
        ), // 扩展坐标系到外切五边形范围
      ),
    );

    // 100分圆周参考线数据集 - 确保轴线延伸到圆周边界
    final circleReferenceDataSet = RadarDataSet(
      fillColor: Colors.transparent,
      borderColor: const Color(0xFFE3E2E0).withValues(alpha: 0.6), // 淡灰色圆周
      borderWidth: 1, // 细线条，表示100分圆周
      entryRadius: 0,
      dataEntries: List.generate(
        widget.data.dimensions.length,
        (index) => const RadarEntry(value: 100), // 100分圆周
      ),
    );

    // 外切五边形数据集 - 几何修复：五边形边与圆周相切
    // 数学原理：正五边形外切圆时，顶点到圆心距离 = R / cos(π/5) ≈ 1.236R
    // 如果100分对应圆周(R)，则外切五边形顶点对应 123.6分
    final axisBoundaryDataSet = RadarDataSet(
      fillColor: Colors.transparent,
      borderColor: const Color(0xFF9B9A97).withValues(alpha: 0.8), // 灰色五边形
      borderWidth: 1.5, // 稍粗的线条，作为最外层五边形
      entryRadius: 0,
      dataEntries: List.generate(
        widget.data.dimensions.length,
        (index) =>
            const RadarEntry(value: circumscribedPentagonValue), // 外切五边形顶点
      ),
    );

    // 数据集顺序很重要：
    // 1. 最小值数据集(0分) - 确保雷达图从中心开始
    // 2. 坐标系边界数据集(123.6分) - 扩展坐标系以容纳外切五边形
    // 3. 100分圆周参考线数据集(100分) - 显示轴线延伸到的圆周边界
    // 4. 外切五边形数据集(123.6分) - 最外层五边形，边与100分圆周相切
    // 5. 内接五边形数据集(80分) - 内层五边形，顶点位于80分圆周上
    // 6. 主数据集(实际分数) - 最后绘制，显示在最上层
    return [
      minRangeDataSet,
      maxRangeDataSet,
      circleReferenceDataSet,
      axisBoundaryDataSet,
      innerPentagonDataSet,
      mainDataSet,
    ];
  }

  /// 获取维度标题文本
  String _getTitleText(int index) {
    if (!widget.showLabels || index >= widget.data.dimensions.length) {
      return '';
    }

    final dimension = widget.data.dimensions[index];
    return '${dimension.dimension.icon}\n${dimension.dimension.nameCn}\n${dimension.score.toStringAsFixed(0)}分';
  }

  /// 处理触摸事件
  void _handleTouch(FlTouchEvent event, RadarTouchResponse? response) {
    if (response?.touchedSpot != null && widget.onDimensionTap != null) {
      final touchedIndex = response!.touchedSpot!.touchedDataSetIndex;
      if (touchedIndex ==
              3 && // 主数据集（索引3：minRange=0, maxRange=1, reference=2, main=3）
          response.touchedSpot!.touchedRadarEntryIndex <
              widget.data.dimensions.length) {
        final dimension = widget
            .data
            .dimensions[response.touchedSpot!.touchedRadarEntryIndex];
        widget.onDimensionTap!(dimension.dimension);
      }
    }
  }

  /// 重新播放动画
  void replay() {
    _animationController.reset();
    _animationController.forward();
  }
}

/// 能力雷达图图例组件
class AbilityRadarLegend extends StatelessWidget {
  final AbilityRadarData data;
  final Function(AbilityDimension)? onDimensionTap;

  const AbilityRadarLegend({
    super.key,
    required this.data,
    this.onDimensionTap,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE3E2E0)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '能力详情',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          ...data.dimensions.map((dimension) => _buildLegendItem(dimension)),
        ],
      ),
    );
  }

  Widget _buildLegendItem(AbilityDimensionData dimension) {
    return GestureDetector(
      onTap: () => onDimensionTap?.call(dimension.dimension),
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: dimension.radarColor.withValues(alpha: 0.05),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: dimension.radarColor.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // 能力图标和颜色指示器
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: dimension.radarColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Center(
                child: Text(
                  dimension.dimension.icon,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(width: 12),

            // 能力信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        dimension.dimension.nameCn,
                        style: const TextStyle(
                          color: Color(0xFF37352F),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Row(
                        children: [
                          Text(
                            dimension.level.icon,
                            style: const TextStyle(fontSize: 12),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            dimension.level.name,
                            style: TextStyle(
                              color: dimension.levelColor,
                              fontSize: 11,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        dimension.formattedScore,
                        style: TextStyle(
                          color: dimension.radarColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // 进度条
                      Expanded(
                        child: Container(
                          margin: const EdgeInsets.only(left: 12),
                          height: 4,
                          decoration: BoxDecoration(
                            color: const Color(0xFFE3E2E0),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: dimension.score / 100,
                            child: Container(
                              decoration: BoxDecoration(
                                color: dimension.radarColor,
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // 箭头指示器
            const Icon(Icons.chevron_right, color: Color(0xFF9B9A97), size: 16),
          ],
        ),
      ),
    );
  }
}
