import 'package:flutter/material.dart';
import '../models/ability_radar_models.dart';

/// 游戏风格等级显示组件
///
/// 参考和平精英战绩雷达图的等级显示风格
class GameStyleRankWidget extends StatefulWidget {
  final double score;
  final bool showAnimation;
  final bool showProgress;
  final double fontSize;
  final bool showDescription;

  const GameStyleRankWidget({
    super.key,
    required this.score,
    this.showAnimation = true,
    this.showProgress = false,
    this.fontSize = 16,
    this.showDescription = false,
  });

  @override
  State<GameStyleRankWidget> createState() => _GameStyleRankWidgetState();
}

class _GameStyleRankWidgetState extends State<GameStyleRankWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    if (widget.showAnimation) {
      _animationController = AnimationController(
        duration: const Duration(milliseconds: 800),
        vsync: this,
      );

      _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
        CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
      );

      _animationController.forward();
    }
  }

  @override
  void dispose() {
    if (widget.showAnimation) {
      _animationController.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final rank = GameStyleRank.fromScore(widget.score);

    if (widget.showAnimation) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: _buildRankDisplay(rank),
          );
        },
      );
    }

    return _buildRankDisplay(rank);
  }

  /// 构建等级显示
  Widget _buildRankDisplay(GameStyleRank rank) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 主要等级标识
        _buildRankBadge(rank),

        // 进度条（可选）
        if (widget.showProgress) ...[
          const SizedBox(height: 8),
          _buildProgressBar(rank),
        ],

        // 描述文字（可选）
        if (widget.showDescription) ...[
          const SizedBox(height: 4),
          _buildDescription(rank),
        ],
      ],
    );
  }

  /// 构建等级徽章
  Widget _buildRankBadge(GameStyleRank rank) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        color: rank.color,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: rank.color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
        // 高等级添加渐变效果
        gradient: _getGradientForRank(rank),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 等级文字
          Text(
            rank.displayName,
            style: TextStyle(
              color: _getTextColorForRank(rank),
              fontSize: widget.fontSize,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),

          // 高等级添加特效图标
          if (rank.index >= GameStyleRank.s.index) ...[
            const SizedBox(width: 4),
            Icon(
              _getIconForRank(rank),
              color: _getTextColorForRank(rank),
              size: widget.fontSize * 0.8,
            ),
          ],
        ],
      ),
    );
  }

  /// 构建进度条
  Widget _buildProgressBar(GameStyleRank rank) {
    final progress = rank.getProgressInRank(widget.score);
    final nextRank = rank.nextRank;

    if (nextRank == null) {
      // 已经是最高等级
      return Container(
        width: 100,
        height: 4,
        decoration: BoxDecoration(
          color: rank.color,
          borderRadius: BorderRadius.circular(2),
        ),
      );
    }

    return Column(
      children: [
        // 进度条
        Container(
          width: 100,
          height: 4,
          decoration: BoxDecoration(
            color: const Color(0xFFE3E2E0),
            borderRadius: BorderRadius.circular(2),
          ),
          child: FractionallySizedBox(
            alignment: Alignment.centerLeft,
            widthFactor: progress,
            child: Container(
              decoration: BoxDecoration(
                color: rank.color,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
          ),
        ),

        const SizedBox(height: 4),

        // 升级提示
        Text(
          '距离${nextRank.displayName}级还需${rank.getScoreToNextRank(widget.score)}分',
          style: const TextStyle(
            color: Color(0xFF9B9A97),
            fontSize: 10,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// 构建描述文字
  Widget _buildDescription(GameStyleRank rank) {
    return Text(
      rank.description,
      style: TextStyle(
        color: rank.color,
        fontSize: widget.fontSize * 0.7,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  /// 获取等级渐变效果
  Gradient? _getGradientForRank(GameStyleRank rank) {
    switch (rank) {
      case GameStyleRank.ss:
        return LinearGradient(
          colors: [rank.color, rank.color.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      case GameStyleRank.sss:
        return LinearGradient(
          colors: [
            rank.color,
            const Color(0xFFFFA500), // 橙金色
            rank.color,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        );
      default:
        return null;
    }
  }

  /// 获取文字颜色
  Color _getTextColorForRank(GameStyleRank rank) {
    switch (rank) {
      case GameStyleRank.b:
      case GameStyleRank.a:
      case GameStyleRank.s:
      case GameStyleRank.ss:
        return Colors.white;
      case GameStyleRank.sss:
        return const Color(0xFF37352F); // 金色背景用深色文字
    }
  }

  /// 获取等级图标
  IconData _getIconForRank(GameStyleRank rank) {
    switch (rank) {
      case GameStyleRank.s:
        return Icons.star;
      case GameStyleRank.ss:
        return Icons.star_border;
      case GameStyleRank.sss:
        return Icons.diamond;
      default:
        return Icons.circle;
    }
  }
}

/// 简化的等级标签组件
class SimpleRankLabel extends StatelessWidget {
  final double score;
  final double fontSize;

  const SimpleRankLabel({super.key, required this.score, this.fontSize = 14});

  @override
  Widget build(BuildContext context) {
    final rank = GameStyleRank.fromScore(score);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: rank.color,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        rank.displayName,
        style: TextStyle(
          color: rank == GameStyleRank.sss
              ? const Color(0xFF37352F)
              : Colors.white,
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
