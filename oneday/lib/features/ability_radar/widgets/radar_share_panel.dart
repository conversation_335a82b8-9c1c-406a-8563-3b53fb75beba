import 'package:flutter/material.dart';
import '../models/ability_radar_models.dart';
import '../services/radar_share_service.dart';
import 'radar_community_share_dialog.dart';

/// 雷达图分享选项面板
///
/// 提供多种分享选项的底部弹出面板
class RadarSharePanel extends StatelessWidget {
  final GlobalKey radarChartKey;
  final AbilityRadarData data;

  const RadarSharePanel({
    super.key,
    required this.radarChartKey,
    required this.data,
  });

  /// 显示分享面板
  static Future<void> show({
    required BuildContext context,
    required GlobalKey radarChartKey,
    required AbilityRadarData data,
  }) async {
    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) =>
          RadarSharePanel(radarChartKey: radarChartKey, data: data),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 顶部拖拽指示器
            _buildDragIndicator(),

            // 标题
            _buildTitle(),

            // 分享选项
            _buildShareOptions(context),

            // 取消按钮
            _buildCancelButton(context),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  /// 构建拖拽指示器
  Widget _buildDragIndicator() {
    return Container(
      margin: const EdgeInsets.only(top: 12, bottom: 20),
      width: 40,
      height: 4,
      decoration: BoxDecoration(
        color: const Color(0xFFE3E2E0),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  /// 构建标题
  Widget _buildTitle() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      child: const Text(
        '分享雷达图',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// 构建分享选项
  Widget _buildShareOptions(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // 第一行：OneDay社区 + 微信
          Row(
            children: [
              Expanded(
                child: _buildShareOption(
                  context: context,
                  icon: Icons.people_outline,
                  iconColor: const Color(0xFF2E7EED),
                  label: 'OneDay社区',
                  onTap: () => _shareToOneDayCommunity(context),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildShareOption(
                  context: context,
                  icon: Icons.chat_bubble_outline,
                  iconColor: const Color(0xFF07C160),
                  label: '微信好友',
                  onTap: () => _shareToWeChat(context),
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 第二行：朋友圈 + 更多
          Row(
            children: [
              Expanded(
                child: _buildShareOption(
                  context: context,
                  icon: Icons.public,
                  iconColor: const Color(0xFF07C160),
                  label: '朋友圈',
                  onTap: () => _shareToMoments(context),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildShareOption(
                  context: context,
                  icon: Icons.more_horiz,
                  iconColor: const Color(0xFF9B9A97),
                  label: '更多选项',
                  onTap: () => _shareToMore(context),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建单个分享选项
  Widget _buildShareOption({
    required BuildContext context,
    required IconData icon,
    required Color iconColor,
    required String label,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: const Color(0xFFE3E2E0)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: iconColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(icon, color: iconColor, size: 24),
            ),
            const SizedBox(height: 8),
            Text(
              label,
              style: const TextStyle(
                color: Color(0xFF37352F),
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建取消按钮
  Widget _buildCancelButton(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
      width: double.infinity,
      child: TextButton(
        onPressed: () => Navigator.of(context).pop(),
        style: TextButton.styleFrom(
          padding: const EdgeInsets.symmetric(vertical: 16),
          backgroundColor: const Color(0xFFF7F6F3),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: const Text(
          '取消',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// 分享到OneDay社区
  void _shareToOneDayCommunity(BuildContext context) async {
    Navigator.of(context).pop();

    // 显示社区分享对话框
    final result = await RadarCommunityShareDialog.show(
      context: context,
      radarChartKey: radarChartKey,
      data: data,
    );

    // 如果分享成功，显示成功提示
    if (result == true && context.mounted) {
      // 成功提示已在对话框中显示，这里不需要额外处理
    }
  }

  /// 分享到微信好友
  void _shareToWeChat(BuildContext context) {
    Navigator.of(context).pop();
    _performShare(context);
  }

  /// 分享到朋友圈
  void _shareToMoments(BuildContext context) {
    Navigator.of(context).pop();
    _performShare(context);
  }

  /// 分享到更多平台
  void _shareToMore(BuildContext context) {
    Navigator.of(context).pop();
    _performShare(context);
  }

  /// 执行分享操作
  void _performShare(BuildContext context) async {
    try {
      // 显示加载提示
      _showLoadingSnackBar(context);

      // 执行分享
      await RadarShareService.shareRadarChart(
        globalKey: radarChartKey,
        data: data,
        context: context,
      );
    } catch (e) {
      print('❌ 分享失败: $e');

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('分享失败，请稍后重试'),
            backgroundColor: Color(0xFFE03E3E),
          ),
        );
      }
    }
  }

  /// 显示加载提示
  void _showLoadingSnackBar(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Row(
          children: [
            SizedBox(
              width: 16,
              height: 16,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text('正在生成分享图片...'),
          ],
        ),
        backgroundColor: Color(0xFF2E7EED),
        duration: Duration(seconds: 3),
      ),
    );
  }
}
