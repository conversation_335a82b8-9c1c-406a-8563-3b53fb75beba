import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../models/ability_radar_models.dart';

/// 雷达图分享服务
///
/// 提供雷达图的截图、水印添加和分享功能
class RadarShareService {
  static const String _tempDirName = 'radar_share';

  /// 分享雷达图
  ///
  /// [globalKey] 雷达图组件的GlobalKey，用于截图
  /// [data] 雷达图数据
  /// [context] 用于显示错误信息的上下文
  static Future<void> shareRadarChart({
    required GlobalKey globalKey,
    required AbilityRadarData data,
    BuildContext? context,
  }) async {
    try {
      print('📤 开始分享雷达图');

      // 1. 截取雷达图
      final imageBytes = await _captureRadarChart(globalKey);
      if (imageBytes == null) {
        throw Exception('无法截取雷达图');
      }

      // 2. 添加水印
      final watermarkedImageBytes = await _addWatermark(imageBytes, data);

      // 3. 保存到临时文件
      final tempFile = await _saveToTempFile(watermarkedImageBytes);

      // 4. 构建分享文本
      final shareText = _buildShareText(data);

      // 5. 分享
      await Share.shareXFiles(
        [XFile(tempFile.path)],
        text: shareText,
        subject: 'OneDay能力雷达图分享',
      );

      print('✅ 雷达图分享完成');

      // 6. 清理临时文件
      _cleanupTempFile(tempFile.path);
    } catch (e) {
      print('❌ 雷达图分享失败: $e');

      // 回退到纯文本分享
      await _shareTextOnly(data);

      if (context != null && context.mounted) {
        _showErrorSnackBar(context, '分享失败，已切换为文本分享');
      }
    }
  }

  /// 截取雷达图组件（公共方法，供社区分享使用）
  static Future<Uint8List?> captureRadarChart(GlobalKey globalKey) async {
    return await _captureRadarChart(globalKey);
  }

  /// 截取雷达图组件（内部方法）
  static Future<Uint8List?> _captureRadarChart(GlobalKey globalKey) async {
    try {
      final RenderRepaintBoundary boundary =
          globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;

      // 等待渲染完成
      await Future.delayed(const Duration(milliseconds: 100));

      // 高质量截图
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(
        format: ui.ImageByteFormat.png,
      );

      return byteData?.buffer.asUint8List();
    } catch (e) {
      print('❌ 截取雷达图失败: $e');
      return null;
    }
  }

  /// 添加OneDay水印
  static Future<Uint8List> _addWatermark(
    Uint8List imageBytes,
    AbilityRadarData data,
  ) async {
    // 解码原图
    final ui.Codec codec = await ui.instantiateImageCodec(imageBytes);
    final ui.FrameInfo frameInfo = await codec.getNextFrame();
    final ui.Image originalImage = frameInfo.image;

    // 创建画布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(
      originalImage.width.toDouble(),
      originalImage.height.toDouble(),
    );

    // 绘制原图
    canvas.drawImage(originalImage, Offset.zero, Paint());

    // 绘制水印
    await _drawWatermark(canvas, size, data);

    // 转换为图片
    final picture = recorder.endRecording();
    final watermarkedImage = await picture.toImage(
      originalImage.width,
      originalImage.height,
    );

    // 转换为字节数组
    final byteData = await watermarkedImage.toByteData(
      format: ui.ImageByteFormat.png,
    );
    return byteData!.buffer.asUint8List();
  }

  /// 绘制水印
  static Future<void> _drawWatermark(
    Canvas canvas,
    Size size,
    AbilityRadarData data,
  ) async {
    // 水印区域配置
    const watermarkHeight = 80.0;

    // 水印背景
    final watermarkRect = Rect.fromLTWH(
      0,
      size.height - watermarkHeight,
      size.width,
      watermarkHeight,
    );

    // 绘制半透明背景
    final backgroundPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.95)
      ..style = PaintingStyle.fill;
    canvas.drawRect(watermarkRect, backgroundPaint);

    // 绘制顶部分割线
    final linePaint = Paint()
      ..color = const Color(0xFFE3E2E0)
      ..strokeWidth = 1.0;
    canvas.drawLine(
      Offset(0, watermarkRect.top),
      Offset(size.width, watermarkRect.top),
      linePaint,
    );

    // OneDay Logo和文本
    await _drawLogoAndText(canvas, watermarkRect, data);

    // 综合评分
    _drawOverallScore(canvas, watermarkRect, data);
  }

  /// 绘制Logo和文本
  static Future<void> _drawLogoAndText(
    Canvas canvas,
    Rect watermarkRect,
    AbilityRadarData data,
  ) async {
    const padding = 16.0;

    // OneDay文本
    final titlePainter = TextPainter(
      text: const TextSpan(
        text: 'OneDay',
        style: TextStyle(
          color: Color(0xFF2E7EED),
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    titlePainter.layout();

    // 副标题
    final subtitlePainter = TextPainter(
      text: const TextSpan(
        text: '怕一天浪费，就用OneDay',
        style: TextStyle(
          color: Color(0xFF9B9A97),
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    subtitlePainter.layout();

    // 绘制文本
    final textStartY = watermarkRect.top + padding;
    titlePainter.paint(canvas, Offset(padding, textStartY));
    subtitlePainter.paint(
      canvas,
      Offset(padding, textStartY + titlePainter.height + 4),
    );
  }

  /// 绘制综合评分和等级
  static void _drawOverallScore(
    Canvas canvas,
    Rect watermarkRect,
    AbilityRadarData data,
  ) {
    const padding = 16.0;

    // 计算综合评分
    final overallScore =
        data.dimensions.map((d) => d.score).reduce((a, b) => a + b) /
        data.dimensions.length;

    // 获取游戏风格等级
    final rank = GameStyleRank.fromScore(overallScore);

    // 评分文本
    final scorePainter = TextPainter(
      text: TextSpan(
        text: '综合评分: ${overallScore.toStringAsFixed(1)}',
        style: const TextStyle(
          color: Color(0xFF37352F),
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    scorePainter.layout();

    // 等级标识
    final rankPainter = TextPainter(
      text: TextSpan(
        text: '${rank.displayName}级',
        style: TextStyle(
          color: rank.color,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    rankPainter.layout();

    // 右对齐绘制评分
    final scoreX = watermarkRect.width - padding - scorePainter.width;
    final scoreY = watermarkRect.top + padding;
    scorePainter.paint(canvas, Offset(scoreX, scoreY));

    // 在评分下方绘制等级
    final rankX = watermarkRect.width - padding - rankPainter.width;
    final rankY = scoreY + scorePainter.height + 2;

    // 绘制等级背景
    final rankBgRect = RRect.fromRectAndRadius(
      Rect.fromLTWH(
        rankX - 6,
        rankY - 2,
        rankPainter.width + 12,
        rankPainter.height + 4,
      ),
      const Radius.circular(10),
    );

    final rankBgPaint = Paint()
      ..color = rank.color.withValues(alpha: 0.2)
      ..style = PaintingStyle.fill;
    canvas.drawRRect(rankBgRect, rankBgPaint);

    // 绘制等级文字
    rankPainter.paint(canvas, Offset(rankX, rankY));

    // 时间戳
    final timestampPainter = TextPainter(
      text: TextSpan(
        text: _formatDateTime(DateTime.now()),
        style: const TextStyle(
          color: Color(0xFF9B9A97),
          fontSize: 10,
          fontWeight: FontWeight.w400,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    timestampPainter.layout();

    final timestampX = watermarkRect.width - padding - timestampPainter.width;
    final timestampY = rankY + rankPainter.height + 6;
    timestampPainter.paint(canvas, Offset(timestampX, timestampY));
  }

  /// 保存图片到临时文件（公共方法，供社区分享使用）
  static Future<String?> saveImageToTemp(
    Uint8List imageBytes,
    String prefix,
  ) async {
    try {
      final tempDir = await getTemporaryDirectory();
      final shareDir = Directory('${tempDir.path}/$_tempDirName');

      if (!await shareDir.exists()) {
        await shareDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final tempFile = File('${shareDir.path}/${prefix}_$timestamp.png');

      await tempFile.writeAsBytes(imageBytes);
      return tempFile.path;
    } catch (e) {
      print('❌ 保存临时图片失败: $e');
      return null;
    }
  }

  /// 保存图片到永久目录（供社区分享使用）
  static Future<String?> saveImageToCommunity(
    Uint8List imageBytes,
    String prefix,
  ) async {
    try {
      // 使用应用文档目录而不是临时目录
      final appDir = await getApplicationDocumentsDirectory();
      final communityImagesDir = Directory('${appDir.path}/community_images');

      if (!await communityImagesDir.exists()) {
        await communityImagesDir.create(recursive: true);
      }

      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final imageFile = File(
        '${communityImagesDir.path}/${prefix}_$timestamp.png',
      );

      await imageFile.writeAsBytes(imageBytes);
      print('💾 社区图片已保存: ${imageFile.path}');
      return imageFile.path;
    } catch (e) {
      print('❌ 保存社区图片失败: $e');
      return null;
    }
  }

  /// 保存到临时文件（内部方法）
  static Future<File> _saveToTempFile(Uint8List imageBytes) async {
    final tempDir = await getTemporaryDirectory();
    final shareDir = Directory('${tempDir.path}/$_tempDirName');

    if (!await shareDir.exists()) {
      await shareDir.create(recursive: true);
    }

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final tempFile = File('${shareDir.path}/radar_chart_$timestamp.png');

    await tempFile.writeAsBytes(imageBytes);
    return tempFile;
  }

  /// 构建分享文本
  static String _buildShareText(AbilityRadarData data) {
    final buffer = StringBuffer();
    buffer.writeln('📊 OneDay能力雷达图');
    buffer.writeln('');

    // 综合评分
    final overallScore =
        data.dimensions.map((d) => d.score).reduce((a, b) => a + b) /
        data.dimensions.length;
    buffer.writeln('🎯 综合评分: ${overallScore.toStringAsFixed(1)}/100');
    buffer.writeln('');

    // 各维度评分
    buffer.writeln('📈 各维度评分:');
    for (final dimensionData in data.dimensions) {
      buffer.writeln(
        '${dimensionData.dimension.icon} ${dimensionData.dimension.nameCn}: ${dimensionData.score.toStringAsFixed(1)}分',
      );
    }
    buffer.writeln('');

    buffer.writeln('💡 通过OneDay，科学提升个人能力！');
    buffer.writeln('📱 怕一天浪费，就用OneDay');

    return buffer.toString();
  }

  /// 纯文本分享（回退方案）
  static Future<void> _shareTextOnly(AbilityRadarData data) async {
    final shareText = _buildShareText(data);
    await Share.share(shareText, subject: 'OneDay能力雷达图分享');
  }

  /// 清理临时文件
  static void _cleanupTempFile(String filePath) {
    Future.delayed(const Duration(seconds: 5), () async {
      try {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
          print('🗑️ 临时文件已清理: $filePath');
        }
      } catch (e) {
        print('⚠️ 清理临时文件失败: $e');
      }
    });
  }

  /// 清理旧的社区图片（保留最近30天的图片）
  static Future<void> cleanupOldCommunityImages() async {
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final communityImagesDir = Directory('${appDir.path}/community_images');

      if (!await communityImagesDir.exists()) {
        return;
      }

      final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
      final files = await communityImagesDir.list().toList();
      int deletedCount = 0;

      for (final entity in files) {
        if (entity is File) {
          final stat = await entity.stat();
          if (stat.modified.isBefore(cutoffDate)) {
            await entity.delete();
            deletedCount++;
          }
        }
      }

      if (deletedCount > 0) {
        print('🗑️ 已清理 $deletedCount 个旧的社区图片');
      }
    } catch (e) {
      print('⚠️ 清理旧社区图片失败: $e');
    }
  }

  /// 显示错误提示
  static void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: const Color(0xFFE03E3E),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// 格式化日期时间
  static String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
