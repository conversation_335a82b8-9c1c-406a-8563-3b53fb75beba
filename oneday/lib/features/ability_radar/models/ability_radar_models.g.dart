// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ability_radar_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AbilityDimensionData _$AbilityDimensionDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('AbilityDimensionData', json, ($checkedConvert) {
  final val = AbilityDimensionData(
    dimension: $checkedConvert(
      'dimension',
      (v) => $enumDecode(_$AbilityDimensionEnumMap, v),
    ),
    score: $checkedConvert('score', (v) => (v as num).toDouble()),
    level: $checkedConvert(
      'level',
      (v) => $enumDecode(_$AbilityLevelEnumMap, v),
    ),
    subScores: $checkedConvert(
      'subScores',
      (v) => (v as Map<String, dynamic>).map(
        (k, e) => MapEntry(k, (e as num).toDouble()),
      ),
    ),
    improvements: $checkedConvert(
      'improvements',
      (v) => (v as List<dynamic>).map((e) => e as String).toList(),
    ),
    featureGuides: $checkedConvert(
      'featureGuides',
      (v) => (v as List<dynamic>).map((e) => e as String).toList(),
    ),
    lastUpdated: $checkedConvert(
      'lastUpdated',
      (v) => DateTime.parse(v as String),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$AbilityDimensionDataPerFieldToJson {
  // ignore: unused_element
  static Object? dimension(AbilityDimension instance) =>
      _$AbilityDimensionEnumMap[instance]!;
  // ignore: unused_element
  static Object? score(double instance) => instance;
  // ignore: unused_element
  static Object? level(AbilityLevel instance) =>
      _$AbilityLevelEnumMap[instance]!;
  // ignore: unused_element
  static Object? subScores(Map<String, double> instance) => instance;
  // ignore: unused_element
  static Object? improvements(List<String> instance) => instance;
  // ignore: unused_element
  static Object? featureGuides(List<String> instance) => instance;
  // ignore: unused_element
  static Object? lastUpdated(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$AbilityDimensionDataToJson(
  AbilityDimensionData instance,
) => <String, dynamic>{
  'dimension': _$AbilityDimensionEnumMap[instance.dimension]!,
  'score': instance.score,
  'level': _$AbilityLevelEnumMap[instance.level]!,
  'subScores': instance.subScores,
  'improvements': instance.improvements,
  'featureGuides': instance.featureGuides,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};

const _$AbilityDimensionEnumMap = {
  AbilityDimension.timeManagement: 'timeManagement',
  AbilityDimension.memoryCapacity: 'memoryCapacity',
  AbilityDimension.focusPower: 'focusPower',
  AbilityDimension.motorSkills: 'motorSkills',
  AbilityDimension.creativity: 'creativity',
};

const _$AbilityLevelEnumMap = {
  AbilityLevel.beginner: 'beginner',
  AbilityLevel.intermediate: 'intermediate',
  AbilityLevel.advanced: 'advanced',
  AbilityLevel.expert: 'expert',
};

AbilityRadarData _$AbilityRadarDataFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('AbilityRadarData', json, ($checkedConvert) {
  final val = AbilityRadarData(
    timeRange: $checkedConvert(
      'timeRange',
      (v) => $enumDecode(_$RadarTimeRangeEnumMap, v),
    ),
    startDate: $checkedConvert('startDate', (v) => DateTime.parse(v as String)),
    endDate: $checkedConvert('endDate', (v) => DateTime.parse(v as String)),
    dimensions: $checkedConvert(
      'dimensions',
      (v) => (v as List<dynamic>)
          .map((e) => AbilityDimensionData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    overallScore: $checkedConvert('overallScore', (v) => (v as num).toDouble()),
    overallLevel: $checkedConvert(
      'overallLevel',
      (v) => $enumDecode(_$AbilityLevelEnumMap, v),
    ),
    balanceScore: $checkedConvert('balanceScore', (v) => (v as num).toDouble()),
    trendData: $checkedConvert(
      'trendData',
      (v) => (v as List<dynamic>)
          .map((e) => AbilityTrendData.fromJson(e as Map<String, dynamic>))
          .toList(),
    ),
    generatedAt: $checkedConvert(
      'generatedAt',
      (v) => DateTime.parse(v as String),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$AbilityRadarDataPerFieldToJson {
  // ignore: unused_element
  static Object? timeRange(RadarTimeRange instance) =>
      _$RadarTimeRangeEnumMap[instance]!;
  // ignore: unused_element
  static Object? startDate(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? endDate(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? dimensions(List<AbilityDimensionData> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? overallScore(double instance) => instance;
  // ignore: unused_element
  static Object? overallLevel(AbilityLevel instance) =>
      _$AbilityLevelEnumMap[instance]!;
  // ignore: unused_element
  static Object? balanceScore(double instance) => instance;
  // ignore: unused_element
  static Object? trendData(List<AbilityTrendData> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? generatedAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$AbilityRadarDataToJson(AbilityRadarData instance) =>
    <String, dynamic>{
      'timeRange': _$RadarTimeRangeEnumMap[instance.timeRange]!,
      'startDate': instance.startDate.toIso8601String(),
      'endDate': instance.endDate.toIso8601String(),
      'dimensions': instance.dimensions.map((e) => e.toJson()).toList(),
      'overallScore': instance.overallScore,
      'overallLevel': _$AbilityLevelEnumMap[instance.overallLevel]!,
      'balanceScore': instance.balanceScore,
      'trendData': instance.trendData.map((e) => e.toJson()).toList(),
      'generatedAt': instance.generatedAt.toIso8601String(),
    };

const _$RadarTimeRangeEnumMap = {
  RadarTimeRange.thisWeek: 'thisWeek',
  RadarTimeRange.thisMonth: 'thisMonth',
  RadarTimeRange.thisQuarter: 'thisQuarter',
};

AbilityTrendData _$AbilityTrendDataFromJson(Map<String, dynamic> json) =>
    $checkedCreate('AbilityTrendData', json, ($checkedConvert) {
      final val = AbilityTrendData(
        date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
        scores: $checkedConvert(
          'scores',
          (v) => (v as Map<String, dynamic>).map(
            (k, e) => MapEntry(
              $enumDecode(_$AbilityDimensionEnumMap, k),
              (e as num).toDouble(),
            ),
          ),
        ),
        overallScore: $checkedConvert(
          'overallScore',
          (v) => (v as num).toDouble(),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$AbilityTrendDataPerFieldToJson {
  // ignore: unused_element
  static Object? date(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? scores(Map<AbilityDimension, double> instance) =>
      instance.map((k, e) => MapEntry(_$AbilityDimensionEnumMap[k]!, e));
  // ignore: unused_element
  static Object? overallScore(double instance) => instance;
}

Map<String, dynamic> _$AbilityTrendDataToJson(AbilityTrendData instance) =>
    <String, dynamic>{
      'date': instance.date.toIso8601String(),
      'scores': instance.scores.map(
        (k, e) => MapEntry(_$AbilityDimensionEnumMap[k]!, e),
      ),
      'overallScore': instance.overallScore,
    };

AbilityAnalysisAdvice _$AbilityAnalysisAdviceFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('AbilityAnalysisAdvice', json, ($checkedConvert) {
  final val = AbilityAnalysisAdvice(
    type: $checkedConvert('type', (v) => v as String),
    title: $checkedConvert('title', (v) => v as String),
    content: $checkedConvert('content', (v) => v as String),
    relatedFeature: $checkedConvert('relatedFeature', (v) => v as String?),
    priority: $checkedConvert('priority', (v) => (v as num).toInt()),
  );
  return val;
});

// ignore: unused_element
abstract class _$AbilityAnalysisAdvicePerFieldToJson {
  // ignore: unused_element
  static Object? type(String instance) => instance;
  // ignore: unused_element
  static Object? title(String instance) => instance;
  // ignore: unused_element
  static Object? content(String instance) => instance;
  // ignore: unused_element
  static Object? relatedFeature(String? instance) => instance;
  // ignore: unused_element
  static Object? priority(int instance) => instance;
}

Map<String, dynamic> _$AbilityAnalysisAdviceToJson(
  AbilityAnalysisAdvice instance,
) => <String, dynamic>{
  'type': instance.type,
  'title': instance.title,
  'content': instance.content,
  'relatedFeature': instance.relatedFeature,
  'priority': instance.priority,
};
