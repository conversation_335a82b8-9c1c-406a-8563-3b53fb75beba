import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ability_radar_models.dart';
import '../services/ability_radar_service.dart';

/// 模拟的能力雷达图服务Provider（用于演示）
final mockAbilityRadarServiceProvider = Provider<AbilityRadarService>((ref) {
  // 这里应该注入真实的依赖服务
  // 暂时返回null，实际使用时需要替换为真实的服务
  throw UnimplementedError('需要注入真实的依赖服务');
});

/// 模拟雷达图数据Provider
final mockRadarDataProvider = Provider<AbilityRadarData>((ref) {
  return _createMockRadarData();
});

/// 创建模拟雷达图数据
AbilityRadarData _createMockRadarData() {
  final dimensions = [
    AbilityDimensionData(
      dimension: AbilityDimension.timeManagement,
      score: 95.0, // 测试高分数值，接近最外层
      level: AbilityLevel.expert,
      subScores: {
        '任务完成率': 70.0,
        '时间预估准确性': 65.0,
        '并行时间利用率': 72.0,
        '学习连续性': 67.0,
      },
      improvements: [
        '建议每天制定明确的学习计划，使用TimeBox功能设定具体的学习目标',
        '提高任务完成率：将大任务分解为小任务，设定合理的时间预期',
      ],
      featureGuides: ['使用TimeBox创建每日学习计划', '查看学习报告分析时间使用效率'],
      lastUpdated: DateTime.now(),
    ),
    AbilityDimensionData(
      dimension: AbilityDimension.memoryCapacity,
      score: 88.0, // 测试高分数值
      level: AbilityLevel.expert,
      subScores: {'词汇掌握率': 80.0, '记忆保持率': 75.0, '动觉记忆质量': 72.0, '复习效率': 74.0},
      improvements: [
        '加强记忆训练：每天进行动觉记忆练习，结合PAO记忆法',
        '提高词汇掌握：使用FSRS算法进行科学复习，关注遗忘曲线',
      ],
      featureGuides: ['使用记忆宫殿创建视觉记忆场景', '进行动觉记忆训练提升记忆效果'],
      lastUpdated: DateTime.now(),
    ),
    AbilityDimensionData(
      dimension: AbilityDimension.focusPower,
      score: 100.0, // 测试最高分数值显示，确保五角星扩展到最外层
      level: AbilityLevel.expert,
      subScores: {
        '专注信噪比': 90.0,
        '深度工作时长': 82.0,
        '任务切换频率': 85.0,
        '学习效率等级': 84.0,
      },
      improvements: ['继续保持优秀的专注力水平', '可以尝试更长时间的深度工作'],
      featureGuides: ['使用TimeBox专注计时器', '查看学习报告中的专注度分析'],
      lastUpdated: DateTime.now(),
    ),
    AbilityDimensionData(
      dimension: AbilityDimension.motorSkills,
      score: 58.7,
      level: AbilityLevel.intermediate,
      subScores: {'动作完成质量': 60.0, '协调性评分': 55.0, '训练频率': 62.0, '动作多样性': 58.0},
      improvements: [
        '加强运动训练：每天进行PAO动作练习，提升身体协调性',
        '提高动作质量：注重动作标准性，逐步提升完成度',
        '增加训练频率：将运动融入学习休息，形成规律训练',
      ],
      featureGuides: ['使用动觉记忆训练功能', '创建自定义动作库', '参与PAO记忆法训练'],
      lastUpdated: DateTime.now(),
    ),
    AbilityDimensionData(
      dimension: AbilityDimension.creativity,
      score: 72.8,
      level: AbilityLevel.advanced,
      subScores: {
        '学习方法多样性': 75.0,
        '自定义内容创建': 70.0,
        '学科交叉学习': 76.0,
        '创新使用模式': 70.0,
      },
      improvements: ['激发创造力：尝试多样化学习方法，探索跨学科知识连接', '增加创作活动：创建个人记忆宫殿、自定义动作库等'],
      featureGuides: ['创建个性化记忆宫殿', '设计自定义动作库', '参与学习社区分享创作'],
      lastUpdated: DateTime.now(),
    ),
  ];

  final trendData = <AbilityTrendData>[
    AbilityTrendData(
      date: DateTime.now().subtract(const Duration(days: 21)),
      scores: {
        AbilityDimension.timeManagement: 62.0,
        AbilityDimension.memoryCapacity: 68.0,
        AbilityDimension.focusPower: 78.0,
        AbilityDimension.motorSkills: 52.0,
        AbilityDimension.creativity: 65.0,
      },
      overallScore: 65.0,
    ),
    AbilityTrendData(
      date: DateTime.now().subtract(const Duration(days: 14)),
      scores: {
        AbilityDimension.timeManagement: 65.0,
        AbilityDimension.memoryCapacity: 71.0,
        AbilityDimension.focusPower: 81.0,
        AbilityDimension.motorSkills: 55.0,
        AbilityDimension.creativity: 68.0,
      },
      overallScore: 68.0,
    ),
    AbilityTrendData(
      date: DateTime.now().subtract(const Duration(days: 7)),
      scores: {
        AbilityDimension.timeManagement: 67.0,
        AbilityDimension.memoryCapacity: 73.0,
        AbilityDimension.focusPower: 83.0,
        AbilityDimension.motorSkills: 57.0,
        AbilityDimension.creativity: 70.0,
      },
      overallScore: 70.0,
    ),
    AbilityTrendData(
      date: DateTime.now(),
      scores: {
        AbilityDimension.timeManagement: 68.5,
        AbilityDimension.memoryCapacity: 75.2,
        AbilityDimension.focusPower: 85.2,
        AbilityDimension.motorSkills: 58.7,
        AbilityDimension.creativity: 72.8,
      },
      overallScore: 72.1,
    ),
  ];

  return AbilityRadarData(
    timeRange: RadarTimeRange.thisMonth,
    startDate: DateTime.now().subtract(const Duration(days: 30)),
    endDate: DateTime.now(),
    dimensions: dimensions,
    overallScore: 72.1,
    overallLevel: AbilityLevel.advanced,
    balanceScore: 0.75,
    trendData: trendData,
    generatedAt: DateTime.now(),
  );
}
