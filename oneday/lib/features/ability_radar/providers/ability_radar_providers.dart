import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/ability_radar_models.dart';
import '../services/ability_radar_service.dart';
import '../../study_time/providers/study_time_providers.dart';
// TODO: 以下导入暂时注释掉，等相关功能实现后再启用
// import '../../learning_report/providers/learning_report_providers.dart';
// import '../../vocabulary/providers/vocabulary_providers.dart';
// import '../../exercise/providers/exercise_providers.dart';

/// 能力雷达图服务Provider
final abilityRadarServiceProvider = Provider<AbilityRadarService>((ref) {
  final studyTimeService = ref.watch(studyTimeStatisticsServiceProvider);
  // TODO: 以下服务暂时注释掉，等相关功能实现后再启用
  // final learningReportService = ref.watch(learningReportServiceProvider);
  // final fsrsService = ref.watch(fsrsServiceProvider);
  // final paoService = ref.watch(paoIntegrationServiceProvider);
  // final customLibraryService = ref.watch(customActionLibraryServiceProvider);

  return AbilityRadarService(
    studyTimeService,
    // TODO: 暂时传入null，等相关功能实现后再传入实际服务
    null, // paoService,
    null, // customLibraryService,
  );
});

/// 当前雷达图数据Provider
final currentRadarDataProvider =
    StateNotifierProvider<RadarDataNotifier, AsyncValue<AbilityRadarData?>>((
      ref,
    ) {
      final service = ref.watch(abilityRadarServiceProvider);
      return RadarDataNotifier(service);
    });

/// 选中的时间范围Provider
final selectedTimeRangeProvider = StateProvider<RadarTimeRange>((ref) {
  return RadarTimeRange.thisMonth;
});

/// 选中的维度Provider
final selectedDimensionProvider = StateProvider<AbilityDimension?>((ref) {
  return null;
});

/// 雷达图数据状态管理器
class RadarDataNotifier extends StateNotifier<AsyncValue<AbilityRadarData?>> {
  final AbilityRadarService _service;

  RadarDataNotifier(this._service) : super(const AsyncValue.data(null));

  /// 生成雷达图数据
  Future<void> generateRadarData({
    RadarTimeRange timeRange = RadarTimeRange.thisMonth,
    DateTime? customStartDate,
    DateTime? customEndDate,
  }) async {
    state = const AsyncValue.loading();

    try {
      final data = await _service.generateRadarData(
        timeRange: timeRange,
        customStartDate: customStartDate,
        customEndDate: customEndDate,
      );
      state = AsyncValue.data(data);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// 刷新数据
  Future<void> refresh() async {
    if (state.value != null) {
      await generateRadarData(timeRange: state.value!.timeRange);
    } else {
      await generateRadarData();
    }
  }

  /// 获取当前数据
  AbilityRadarData? get currentData => state.value;

  /// 获取加载状态
  bool get isLoading => state.isLoading;

  /// 获取错误信息
  Object? get error => state.error;
}

/// 维度详细分析Provider
final dimensionAnalysisProvider =
    FutureProvider.family<DimensionAnalysis, AbilityDimension>((
      ref,
      dimension,
    ) async {
      final radarData = ref.watch(currentRadarDataProvider).value;
      if (radarData == null) {
        throw Exception('雷达图数据未加载');
      }

      final dimensionData = radarData.getDimensionData(dimension);
      if (dimensionData == null) {
        throw Exception('维度数据不存在');
      }

      // 生成详细分析
      return DimensionAnalysis(
        dimension: dimensionData,
        detailedAnalysis: _generateDetailedAnalysis(dimensionData),
        comparisonData: _generateComparisonData(dimensionData, radarData),
        actionPlan: _generateActionPlan(dimensionData),
      );
    });

/// 能力趋势分析Provider
final abilityTrendProvider = FutureProvider<AbilityTrendAnalysis>((ref) async {
  final radarData = ref.watch(currentRadarDataProvider).value;
  if (radarData == null) {
    throw Exception('雷达图数据未加载');
  }

  return AbilityTrendAnalysis(
    trendData: radarData.trendData,
    growthRate: _calculateGrowthRate(radarData.trendData),
    predictions: _generatePredictions(radarData),
    milestones: _generateMilestones(radarData),
  );
});

/// 个性化建议Provider
final personalizedAdviceProvider = FutureProvider<List<PersonalizedAdvice>>((
  ref,
) async {
  final radarData = ref.watch(currentRadarDataProvider).value;
  if (radarData == null) {
    return [];
  }

  return _generatePersonalizedAdvice(radarData);
});

// 辅助数据模型
class DimensionAnalysis {
  final AbilityDimensionData dimension;
  final String detailedAnalysis;
  final Map<String, double> comparisonData;
  final List<ActionItem> actionPlan;

  const DimensionAnalysis({
    required this.dimension,
    required this.detailedAnalysis,
    required this.comparisonData,
    required this.actionPlan,
  });
}

class AbilityTrendAnalysis {
  final List<AbilityTrendData> trendData;
  final Map<AbilityDimension, double> growthRate;
  final Map<AbilityDimension, double> predictions;
  final List<Milestone> milestones;

  const AbilityTrendAnalysis({
    required this.trendData,
    required this.growthRate,
    required this.predictions,
    required this.milestones,
  });
}

class PersonalizedAdvice {
  final String title;
  final String description;
  final String actionText;
  final String? relatedFeature;
  final int priority;
  final AbilityDimension? targetDimension;

  const PersonalizedAdvice({
    required this.title,
    required this.description,
    required this.actionText,
    this.relatedFeature,
    required this.priority,
    this.targetDimension,
  });
}

class ActionItem {
  final String title;
  final String description;
  final bool isCompleted;
  final DateTime? dueDate;
  final String? relatedFeature;

  const ActionItem({
    required this.title,
    required this.description,
    this.isCompleted = false,
    this.dueDate,
    this.relatedFeature,
  });
}

class Milestone {
  final String title;
  final String description;
  final DateTime targetDate;
  final double targetScore;
  final AbilityDimension dimension;
  final bool isAchieved;

  const Milestone({
    required this.title,
    required this.description,
    required this.targetDate,
    required this.targetScore,
    required this.dimension,
    this.isAchieved = false,
  });
}

// 辅助函数
String _generateDetailedAnalysis(AbilityDimensionData dimension) {
  final score = dimension.score;
  final level = dimension.level;

  String analysis =
      '您在${dimension.dimension.nameCn}方面的表现为${level.name}水平（${dimension.formattedScore}）。';

  if (score >= 80) {
    analysis += '这是您的优势能力，建议继续保持并发挥带动作用。';
  } else if (score >= 60) {
    analysis += '表现良好，还有进一步提升的空间。';
  } else if (score >= 40) {
    analysis += '需要重点关注和改进的能力领域。';
  } else {
    analysis += '建议制定专门的提升计划，循序渐进地改善。';
  }

  // 添加子指标分析
  final weakestSubScore = dimension.subScores.entries.reduce(
    (a, b) => a.value < b.value ? a : b,
  );
  analysis += '\n\n其中"${weakestSubScore.key}"是需要重点提升的方面。';

  return analysis;
}

Map<String, double> _generateComparisonData(
  AbilityDimensionData dimension,
  AbilityRadarData radarData,
) {
  final avgScore = radarData.overallScore;
  return {
    '个人平均': avgScore,
    '当前维度': dimension.score,
    '目标分数': 80.0,
    '同期用户平均': 65.0, // 模拟数据
  };
}

List<ActionItem> _generateActionPlan(AbilityDimensionData dimension) {
  final actions = <ActionItem>[];

  for (final improvement in dimension.improvements) {
    actions.add(
      ActionItem(
        title: improvement,
        description: '根据您的${dimension.dimension.nameCn}分析生成的改进建议',
        dueDate: DateTime.now().add(const Duration(days: 7)),
      ),
    );
  }

  for (final guide in dimension.featureGuides) {
    actions.add(
      ActionItem(
        title: '使用$guide功能',
        description: '通过使用相关功能来提升${dimension.dimension.nameCn}',
        relatedFeature: guide,
      ),
    );
  }

  return actions;
}

Map<AbilityDimension, double> _calculateGrowthRate(
  List<AbilityTrendData> trendData,
) {
  if (trendData.length < 2) return {};

  final first = trendData.first;
  final last = trendData.last;
  final days = last.date.difference(first.date).inDays;

  final growthRates = <AbilityDimension, double>{};

  for (final dimension in AbilityDimension.values) {
    final firstScore = first.scores[dimension] ?? 0;
    final lastScore = last.scores[dimension] ?? 0;
    final growthRate = days > 0 ? (lastScore - firstScore) / days : 0.0;
    growthRates[dimension] = growthRate.toDouble();
  }

  return growthRates;
}

Map<AbilityDimension, double> _generatePredictions(AbilityRadarData radarData) {
  final predictions = <AbilityDimension, double>{};

  for (final dimension in radarData.dimensions) {
    // 简单的线性预测，实际可以使用更复杂的算法
    final currentScore = dimension.score;
    final growthRate = 0.5; // 假设每周增长0.5分
    final predictedScore = (currentScore + growthRate * 4).clamp(0.0, 100.0);
    predictions[dimension.dimension] = predictedScore;
  }

  return predictions;
}

List<Milestone> _generateMilestones(AbilityRadarData radarData) {
  final milestones = <Milestone>[];

  for (final dimension in radarData.dimensions) {
    if (dimension.score < 80) {
      milestones.add(
        Milestone(
          title: '${dimension.dimension.nameCn}达到高级水平',
          description: '将${dimension.dimension.nameCn}提升至80分以上',
          targetDate: DateTime.now().add(const Duration(days: 30)),
          targetScore: 80.0,
          dimension: dimension.dimension,
        ),
      );
    }
  }

  return milestones;
}

List<PersonalizedAdvice> _generatePersonalizedAdvice(
  AbilityRadarData radarData,
) {
  final advice = <PersonalizedAdvice>[];

  // 基于最弱能力生成建议
  final weakestDimension = radarData.weakestAbility;
  advice.add(
    PersonalizedAdvice(
      title: '重点提升${weakestDimension.dimension.nameCn}',
      description: '这是您当前最需要改进的能力领域',
      actionText: '制定专项提升计划',
      priority: 5,
      targetDimension: weakestDimension.dimension,
    ),
  );

  // 基于平衡度生成建议
  if (radarData.balanceScore < 0.6) {
    advice.add(
      PersonalizedAdvice(
        title: '平衡发展各项能力',
        description: '您的能力发展不够均衡，建议关注薄弱环节',
        actionText: '制定均衡发展计划',
        priority: 4,
      ),
    );
  }

  // 基于最强能力生成建议
  final strongestDimension = radarData.strongestAbility;
  advice.add(
    PersonalizedAdvice(
      title: '发挥${strongestDimension.dimension.nameCn}优势',
      description: '这是您的优势能力，可以带动其他能力发展',
      actionText: '深化优势能力应用',
      priority: 3,
      targetDimension: strongestDimension.dimension,
    ),
  );

  return advice..sort((a, b) => b.priority.compareTo(a.priority));
}
