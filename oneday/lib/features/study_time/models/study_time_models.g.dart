// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'study_time_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

StudyTimeStatistics _$StudyTimeStatisticsFromJson(Map<String, dynamic> json) =>
    $checkedCreate('StudyTimeStatistics', json, ($checkedConvert) {
      final val = StudyTimeStatistics(
        date: $checkedConvert('date', (v) => v as String),
        totalStudyMinutes: $checkedConvert(
          'totalStudyMinutes',
          (v) => (v as num).toInt(),
        ),
        completedTasks: $checkedConvert(
          'completedTasks',
          (v) => (v as num).toInt(),
        ),
        totalPlannedMinutes: $checkedConvert(
          'totalPlannedMinutes',
          (v) => (v as num).toInt(),
        ),
        totalWage: $checkedConvert('totalWage', (v) => (v as num).toDouble()),
        sessions: $checkedConvert(
          'sessions',
          (v) => (v as List<dynamic>)
              .map((e) => StudySession.fromJson(e as Map<String, dynamic>))
              .toList(),
        ),
        lastUpdated: $checkedConvert(
          'lastUpdated',
          (v) => DateTime.parse(v as String),
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$StudyTimeStatisticsPerFieldToJson {
  // ignore: unused_element
  static Object? date(String instance) => instance;
  // ignore: unused_element
  static Object? totalStudyMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? completedTasks(int instance) => instance;
  // ignore: unused_element
  static Object? totalPlannedMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? totalWage(double instance) => instance;
  // ignore: unused_element
  static Object? sessions(List<StudySession> instance) =>
      instance.map((e) => e.toJson()).toList();
  // ignore: unused_element
  static Object? lastUpdated(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$StudyTimeStatisticsToJson(
  StudyTimeStatistics instance,
) => <String, dynamic>{
  'date': instance.date,
  'totalStudyMinutes': instance.totalStudyMinutes,
  'completedTasks': instance.completedTasks,
  'totalPlannedMinutes': instance.totalPlannedMinutes,
  'totalWage': instance.totalWage,
  'sessions': instance.sessions.map((e) => e.toJson()).toList(),
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};

StudySession _$StudySessionFromJson(Map<String, dynamic> json) =>
    $checkedCreate('StudySession', json, ($checkedConvert) {
      final val = StudySession(
        sessionId: $checkedConvert('sessionId', (v) => v as String),
        title: $checkedConvert('title', (v) => v as String),
        category: $checkedConvert('category', (v) => v as String),
        startTime: $checkedConvert(
          'startTime',
          (v) => DateTime.parse(v as String),
        ),
        endTime: $checkedConvert(
          'endTime',
          (v) => v == null ? null : DateTime.parse(v as String),
        ),
        plannedMinutes: $checkedConvert(
          'plannedMinutes',
          (v) => (v as num).toInt(),
        ),
        actualMinutes: $checkedConvert(
          'actualMinutes',
          (v) => (v as num?)?.toInt(),
        ),
        wage: $checkedConvert('wage', (v) => (v as num).toDouble()),
      );
      return val;
    });

// ignore: unused_element
abstract class _$StudySessionPerFieldToJson {
  // ignore: unused_element
  static Object? sessionId(String instance) => instance;
  // ignore: unused_element
  static Object? title(String instance) => instance;
  // ignore: unused_element
  static Object? category(String instance) => instance;
  // ignore: unused_element
  static Object? startTime(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? endTime(DateTime? instance) => instance?.toIso8601String();
  // ignore: unused_element
  static Object? plannedMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? actualMinutes(int? instance) => instance;
  // ignore: unused_element
  static Object? wage(double instance) => instance;
}

Map<String, dynamic> _$StudySessionToJson(StudySession instance) =>
    <String, dynamic>{
      'sessionId': instance.sessionId,
      'title': instance.title,
      'category': instance.category,
      'startTime': instance.startTime.toIso8601String(),
      'endTime': instance.endTime?.toIso8601String(),
      'plannedMinutes': instance.plannedMinutes,
      'actualMinutes': instance.actualMinutes,
      'wage': instance.wage,
    };

StudyTimeAggregation _$StudyTimeAggregationFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('StudyTimeAggregation', json, ($checkedConvert) {
  final val = StudyTimeAggregation(
    todayMinutes: $checkedConvert('todayMinutes', (v) => (v as num).toInt()),
    thisWeekMinutes: $checkedConvert(
      'thisWeekMinutes',
      (v) => (v as num).toInt(),
    ),
    thisMonthMinutes: $checkedConvert(
      'thisMonthMinutes',
      (v) => (v as num).toInt(),
    ),
    todayCompletedTasks: $checkedConvert(
      'todayCompletedTasks',
      (v) => (v as num).toInt(),
    ),
    todayWage: $checkedConvert('todayWage', (v) => (v as num).toDouble()),
    streakDays: $checkedConvert('streakDays', (v) => (v as num).toInt()),
    todayLearningROI: $checkedConvert(
      'todayLearningROI',
      (v) => (v as num).toDouble(),
    ),
    weeklyAverageLearningROI: $checkedConvert(
      'weeklyAverageLearningROI',
      (v) => (v as num).toDouble(),
    ),
    todayParallelTimeMinutes: $checkedConvert(
      'todayParallelTimeMinutes',
      (v) => (v as num).toInt(),
    ),
    todayFocusSignalToNoiseRatio: $checkedConvert(
      'todayFocusSignalToNoiseRatio',
      (v) => (v as num).toDouble(),
    ),
    todayFocusRatingLevel: $checkedConvert(
      'todayFocusRatingLevel',
      (v) => v as String,
    ),
    lastUpdated: $checkedConvert(
      'lastUpdated',
      (v) => DateTime.parse(v as String),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$StudyTimeAggregationPerFieldToJson {
  // ignore: unused_element
  static Object? todayMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? thisWeekMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? thisMonthMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? todayCompletedTasks(int instance) => instance;
  // ignore: unused_element
  static Object? todayWage(double instance) => instance;
  // ignore: unused_element
  static Object? streakDays(int instance) => instance;
  // ignore: unused_element
  static Object? todayLearningROI(double instance) => instance;
  // ignore: unused_element
  static Object? weeklyAverageLearningROI(double instance) => instance;
  // ignore: unused_element
  static Object? todayParallelTimeMinutes(int instance) => instance;
  // ignore: unused_element
  static Object? todayFocusSignalToNoiseRatio(double instance) => instance;
  // ignore: unused_element
  static Object? todayFocusRatingLevel(String instance) => instance;
  // ignore: unused_element
  static Object? lastUpdated(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$StudyTimeAggregationToJson(
  StudyTimeAggregation instance,
) => <String, dynamic>{
  'todayMinutes': instance.todayMinutes,
  'thisWeekMinutes': instance.thisWeekMinutes,
  'thisMonthMinutes': instance.thisMonthMinutes,
  'todayCompletedTasks': instance.todayCompletedTasks,
  'todayWage': instance.todayWage,
  'streakDays': instance.streakDays,
  'todayLearningROI': instance.todayLearningROI,
  'weeklyAverageLearningROI': instance.weeklyAverageLearningROI,
  'todayParallelTimeMinutes': instance.todayParallelTimeMinutes,
  'todayFocusSignalToNoiseRatio': instance.todayFocusSignalToNoiseRatio,
  'todayFocusRatingLevel': instance.todayFocusRatingLevel,
  'lastUpdated': instance.lastUpdated.toIso8601String(),
};
