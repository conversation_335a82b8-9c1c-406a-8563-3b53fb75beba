import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 举报类型
enum ReportType {
  profanity, // 脏话/不当言论
  spam, // 垃圾信息
  harassment, // 骚扰
  violence, // 暴力威胁
  inappropriate, // 不当内容
  other, // 其他
}

/// 举报状态
enum ReportStatus {
  pending, // 待处理
  reviewing, // 审核中
  resolved, // 已处理
  rejected, // 已拒绝
}

/// 举报记录
class ContentReport {
  final String id;
  final String contentId;
  final String contentType; // 'post', 'comment', etc.
  final String reporterId;
  final ReportType type;
  final String reason;
  final String? additionalInfo;
  final DateTime createdAt;
  final ReportStatus status;
  final String? adminNote;
  final DateTime? resolvedAt;

  const ContentReport({
    required this.id,
    required this.contentId,
    required this.contentType,
    required this.reporterId,
    required this.type,
    required this.reason,
    this.additionalInfo,
    required this.createdAt,
    this.status = ReportStatus.pending,
    this.adminNote,
    this.resolvedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'contentId': contentId,
    'contentType': contentType,
    'reporterId': reporterId,
    'type': type.name,
    'reason': reason,
    'additionalInfo': additionalInfo,
    'createdAt': createdAt.toIso8601String(),
    'status': status.name,
    'adminNote': adminNote,
    'resolvedAt': resolvedAt?.toIso8601String(),
  };

  factory ContentReport.fromJson(Map<String, dynamic> json) => ContentReport(
    id: json['id'] as String,
    contentId: json['contentId'] as String,
    contentType: json['contentType'] as String,
    reporterId: json['reporterId'] as String,
    type: ReportType.values.firstWhere(
      (e) => e.name == json['type'],
      orElse: () => ReportType.other,
    ),
    reason: json['reason'] as String,
    additionalInfo: json['additionalInfo'] as String?,
    createdAt: DateTime.parse(json['createdAt'] as String),
    status: ReportStatus.values.firstWhere(
      (e) => e.name == json['status'],
      orElse: () => ReportStatus.pending,
    ),
    adminNote: json['adminNote'] as String?,
    resolvedAt: json['resolvedAt'] != null
        ? DateTime.parse(json['resolvedAt'] as String)
        : null,
  );

  ContentReport copyWith({
    String? id,
    String? contentId,
    String? contentType,
    String? reporterId,
    ReportType? type,
    String? reason,
    String? additionalInfo,
    DateTime? createdAt,
    ReportStatus? status,
    String? adminNote,
    DateTime? resolvedAt,
  }) => ContentReport(
    id: id ?? this.id,
    contentId: contentId ?? this.contentId,
    contentType: contentType ?? this.contentType,
    reporterId: reporterId ?? this.reporterId,
    type: type ?? this.type,
    reason: reason ?? this.reason,
    additionalInfo: additionalInfo ?? this.additionalInfo,
    createdAt: createdAt ?? this.createdAt,
    status: status ?? this.status,
    adminNote: adminNote ?? this.adminNote,
    resolvedAt: resolvedAt ?? this.resolvedAt,
  );
}

/// 内容举报服务
class ContentReportService {
  static const String _reportsKey = 'content_reports';
  static const String _userReportsKey = 'user_reports_count';
  // Note: _reportSettingsKey is reserved for future use
  // static const String _reportSettingsKey = 'report_settings';

  // 缓存
  List<ContentReport>? _reports;
  Map<String, int>? _userReportCounts;

  /// 初始化服务
  Future<void> initialize() async {
    await _loadReports();
    await _loadUserReportCounts();
  }

  /// 提交举报
  Future<bool> submitReport({
    required String contentId,
    required String contentType,
    required String reporterId,
    required ReportType type,
    required String reason,
    String? additionalInfo,
  }) async {
    try {
      // 检查是否已经举报过相同内容
      if (await _hasUserReportedContent(reporterId, contentId)) {
        debugPrint('用户已举报过此内容');
        return false;
      }

      // 检查用户举报频率限制
      if (await _isUserReportLimitExceeded(reporterId)) {
        debugPrint('用户举报频率超限');
        return false;
      }

      final report = ContentReport(
        id: _generateReportId(),
        contentId: contentId,
        contentType: contentType,
        reporterId: reporterId,
        type: type,
        reason: reason,
        additionalInfo: additionalInfo,
        createdAt: DateTime.now(),
      );

      await _addReport(report);
      await _incrementUserReportCount(reporterId);

      debugPrint('举报提交成功: ${report.id}');
      return true;
    } catch (e) {
      debugPrint('提交举报失败: $e');
      return false;
    }
  }

  /// 获取举报列表（管理员用）
  Future<List<ContentReport>> getReports({
    ReportStatus? status,
    ReportType? type,
    int? limit,
  }) async {
    await initialize();

    var reports = List<ContentReport>.from(_reports ?? []);

    // 按状态过滤
    if (status != null) {
      reports = reports.where((r) => r.status == status).toList();
    }

    // 按类型过滤
    if (type != null) {
      reports = reports.where((r) => r.type == type).toList();
    }

    // 按时间排序（最新的在前）
    reports.sort((a, b) => b.createdAt.compareTo(a.createdAt));

    // 限制数量
    if (limit != null && reports.length > limit) {
      reports = reports.take(limit).toList();
    }

    return reports;
  }

  /// 获取用户的举报记录
  Future<List<ContentReport>> getUserReports(String userId) async {
    await initialize();

    final userReports = (_reports ?? [])
        .where((r) => r.reporterId == userId)
        .toList();

    userReports.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    return userReports;
  }

  /// 处理举报（管理员用）
  Future<bool> resolveReport(
    String reportId,
    ReportStatus status, {
    String? adminNote,
  }) async {
    try {
      await initialize();

      final reportIndex = (_reports ?? []).indexWhere((r) => r.id == reportId);
      if (reportIndex == -1) {
        debugPrint('举报记录不存在: $reportId');
        return false;
      }

      final updatedReport = _reports![reportIndex].copyWith(
        status: status,
        adminNote: adminNote,
        resolvedAt: DateTime.now(),
      );

      _reports![reportIndex] = updatedReport;
      await _saveReports();

      debugPrint('举报处理完成: $reportId -> ${status.name}');
      return true;
    } catch (e) {
      debugPrint('处理举报失败: $e');
      return false;
    }
  }

  /// 获取举报统计
  Future<Map<String, int>> getReportStatistics() async {
    await initialize();

    final reports = _reports ?? [];
    final stats = <String, int>{
      'total': reports.length,
      'pending': 0,
      'reviewing': 0,
      'resolved': 0,
      'rejected': 0,
    };

    for (final report in reports) {
      stats[report.status.name] = (stats[report.status.name] ?? 0) + 1;
    }

    // 按类型统计
    for (final type in ReportType.values) {
      final count = reports.where((r) => r.type == type).length;
      stats[type.name] = count;
    }

    return stats;
  }

  /// 检查内容是否被多次举报
  Future<bool> isContentFrequentlyReported(
    String contentId, {
    int threshold = 3,
  }) async {
    await initialize();

    final reportCount = (_reports ?? [])
        .where((r) => r.contentId == contentId)
        .length;

    return reportCount >= threshold;
  }

  /// 加载举报记录
  Future<void> _loadReports() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_reportsKey);

      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _reports = jsonList
            .map((json) => ContentReport.fromJson(json))
            .toList();
        debugPrint('加载 ${_reports!.length} 条举报记录');
      } else {
        _reports = [];
      }
    } catch (e) {
      debugPrint('加载举报记录失败: $e');
      _reports = [];
    }
  }

  /// 保存举报记录
  Future<void> _saveReports() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = (_reports ?? []).map((r) => r.toJson()).toList();
      await prefs.setString(_reportsKey, json.encode(jsonList));
      debugPrint('举报记录已保存');
    } catch (e) {
      debugPrint('保存举报记录失败: $e');
    }
  }

  /// 添加举报记录
  Future<void> _addReport(ContentReport report) async {
    _reports ??= [];
    _reports!.add(report);
    await _saveReports();
  }

  /// 加载用户举报次数
  Future<void> _loadUserReportCounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_userReportsKey);

      if (jsonString != null) {
        final Map<String, dynamic> jsonMap = json.decode(jsonString);
        _userReportCounts = jsonMap.map((k, v) => MapEntry(k, v as int));
      } else {
        _userReportCounts = {};
      }
    } catch (e) {
      debugPrint('加载用户举报次数失败: $e');
      _userReportCounts = {};
    }
  }

  /// 保存用户举报次数
  Future<void> _saveUserReportCounts() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _userReportsKey,
        json.encode(_userReportCounts ?? {}),
      );
    } catch (e) {
      debugPrint('保存用户举报次数失败: $e');
    }
  }

  /// 增加用户举报次数
  Future<void> _incrementUserReportCount(String userId) async {
    _userReportCounts ??= {};
    _userReportCounts![userId] = (_userReportCounts![userId] ?? 0) + 1;
    await _saveUserReportCounts();
  }

  /// 检查用户是否已举报过内容
  Future<bool> _hasUserReportedContent(String userId, String contentId) async {
    await initialize();

    return (_reports ?? []).any(
      (r) => r.reporterId == userId && r.contentId == contentId,
    );
  }

  /// 检查用户举报频率是否超限
  Future<bool> _isUserReportLimitExceeded(
    String userId, {
    int dailyLimit = 10,
  }) async {
    await initialize();

    final today = DateTime.now();
    final todayStart = DateTime(today.year, today.month, today.day);

    final todayReports = (_reports ?? [])
        .where((r) => r.reporterId == userId && r.createdAt.isAfter(todayStart))
        .length;

    return todayReports >= dailyLimit;
  }

  /// 生成举报ID
  String _generateReportId() {
    return 'report_${DateTime.now().millisecondsSinceEpoch}_${DateTime.now().microsecond}';
  }

  /// 获取举报类型显示名称
  static String getReportTypeName(ReportType type) {
    switch (type) {
      case ReportType.profanity:
        return '脏话/不当言论';
      case ReportType.spam:
        return '垃圾信息';
      case ReportType.harassment:
        return '骚扰';
      case ReportType.violence:
        return '暴力威胁';
      case ReportType.inappropriate:
        return '不当内容';
      case ReportType.other:
        return '其他';
    }
  }

  /// 获取举报状态显示名称
  static String getReportStatusName(ReportStatus status) {
    switch (status) {
      case ReportStatus.pending:
        return '待处理';
      case ReportStatus.reviewing:
        return '审核中';
      case ReportStatus.resolved:
        return '已处理';
      case ReportStatus.rejected:
        return '已拒绝';
    }
  }

  /// 清除缓存
  Future<void> clearCache() async {
    _reports = null;
    _userReportCounts = null;
  }
}
