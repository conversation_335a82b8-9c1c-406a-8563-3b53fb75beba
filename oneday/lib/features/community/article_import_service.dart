// import 'dart:io';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../vocabulary/vocabulary_service.dart';
import 'profanity_filter_service.dart';
import '../vocabulary/word_model.dart';

/// 高亮设置模型
class HighlightSettings {
  final bool expertOnly; // 是否只高亮expert级别词汇
  final bool enabled; // 是否启用高亮功能
  final bool filterProfanity; // 是否过滤脏话

  const HighlightSettings({
    this.expertOnly = false,
    this.enabled = true,
    this.filterProfanity = true,
  });

  Map<String, dynamic> toJson() => {
    'expertOnly': expertOnly,
    'enabled': enabled,
    'filterProfanity': filterProfanity,
  };

  factory HighlightSettings.fromJson(Map<String, dynamic> json) {
    return HighlightSettings(
      expertOnly: json['expertOnly'] ?? false,
      enabled: json['enabled'] ?? true,
      filterProfanity: json['filterProfanity'] ?? true,
    );
  }
}

/// 导入结果模型
class ImportResult {
  final String originalText;
  final String highlightedText;
  final List<HighlightedWord> highlightedWords;
  final int totalWords;
  final int vocabularyWordsFound;
  final bool hasProfanityViolations; // 是否包含脏话
  final int profanityWordsFiltered; // 过滤的脏话数量

  const ImportResult({
    required this.originalText,
    required this.highlightedText,
    required this.highlightedWords,
    required this.totalWords,
    required this.vocabularyWordsFound,
    this.hasProfanityViolations = false,
    this.profanityWordsFiltered = 0,
  });
}

/// 高亮词汇模型
class HighlightedWord {
  final String word;
  final String definition;
  final String difficulty;
  final int startIndex;
  final int endIndex;
  final bool isVariant; // 是否为变体形式
  final String originalForm; // 原始词汇形式

  const HighlightedWord({
    required this.word,
    required this.definition,
    required this.difficulty,
    required this.startIndex,
    required this.endIndex,
    this.isVariant = false,
    required this.originalForm,
  });
}

/// 文章导入和词汇高亮服务
class ArticleImportService {
  static const String _highlightSettingsKey = 'vocabulary_highlight_settings';

  final VocabularyService _vocabularyService = VocabularyService();
  final ProfanityFilterService _profanityFilterService =
      ProfanityFilterService();

  /// 从文本导入文章内容（暂时替代文件导入）
  Future<String> importFromText(String content) async {
    try {
      // 检查内容长度（限制5000行）
      final lines = content.split('\n');
      if (lines.length > 5000) {
        throw Exception('文本行数超过限制（最大5000行）');
      }

      // 基本内容验证
      if (content.trim().isEmpty) {
        throw Exception('文本内容为空');
      }

      return content;
    } catch (e) {
      debugPrint('文本导入失败: $e');
      rethrow;
    }
  }

  /// 处理文章内容，高亮考研词汇并过滤脏话
  Future<ImportResult> processArticleContent(String content) async {
    try {
      debugPrint(
        '📄 ArticleImportService.processArticleContent() 开始处理: "$content"',
      );

      // 获取高亮设置
      final settings = await getHighlightSettings();
      debugPrint(
        '📄 高亮设置: enabled=${settings.enabled}, expertOnly=${settings.expertOnly}, filterProfanity=${settings.filterProfanity}',
      );

      // 第一步：脏话过滤
      String processedContent = content;
      bool hasProfanityViolations = false;
      int profanityWordsFiltered = 0;

      if (settings.filterProfanity) {
        debugPrint('📄 开始脏话过滤...');
        final profanityResult = await _profanityFilterService.filterText(
          content,
        );
        processedContent = profanityResult.filteredText;
        hasProfanityViolations = profanityResult.hasViolations;
        profanityWordsFiltered = profanityResult.detectedWords.length;
        debugPrint(
          '📄 脏话过滤结果: 违禁=$hasProfanityViolations, 过滤数=$profanityWordsFiltered',
        );
        debugPrint('📄 过滤后文本: "$processedContent"');
      } else {
        debugPrint('📄 脏话过滤功能已禁用');
      }

      // 如果高亮功能未启用，直接返回过滤后的结果
      if (!settings.enabled) {
        return ImportResult(
          originalText: content,
          highlightedText: processedContent,
          highlightedWords: [],
          totalWords: _countWords(content),
          vocabularyWordsFound: 0,
          hasProfanityViolations: hasProfanityViolations,
          profanityWordsFiltered: profanityWordsFiltered,
        );
      }

      // 第二步：词汇高亮（在过滤后的文本上进行）
      // 加载考研词汇库
      final vocabulary = await _vocabularyService.loadVocabulary();

      // 创建词汇查找映射（包括变体）
      final vocabularyMap = <String, MapEntry<String, WordDetails>>{};
      for (final entry in vocabulary.words.entries) {
        final word = entry.key.toLowerCase();
        final details = entry.value;

        // 检查难度过滤
        if (settings.expertOnly && details.difficulty != 'expert') {
          continue;
        }

        // 添加主词汇
        vocabularyMap[word] = entry;

        // 添加变体形式
        for (final variant in details.alternativeSpellings) {
          vocabularyMap[variant.toLowerCase()] = entry;
        }
      }

      // 处理文本高亮（在过滤后的文本上）
      final highlightResult = _highlightVocabularyInText(
        processedContent,
        vocabularyMap,
      );

      // 返回综合结果
      return ImportResult(
        originalText: content,
        highlightedText: highlightResult.highlightedText,
        highlightedWords: highlightResult.highlightedWords,
        totalWords: highlightResult.totalWords,
        vocabularyWordsFound: highlightResult.vocabularyWordsFound,
        hasProfanityViolations: hasProfanityViolations,
        profanityWordsFiltered: profanityWordsFiltered,
      );
    } catch (e) {
      debugPrint('文章处理失败: $e');
      rethrow;
    }
  }

  /// 获取高亮设置
  Future<HighlightSettings> getHighlightSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final settingsJson = prefs.getString(_highlightSettingsKey);

      if (settingsJson != null) {
        final Map<String, dynamic> json = jsonDecode(settingsJson);
        return HighlightSettings.fromJson(json);
      }

      return const HighlightSettings();
    } catch (e) {
      debugPrint('获取高亮设置失败: $e');
      return const HighlightSettings();
    }
  }

  /// 保存高亮设置
  Future<void> saveHighlightSettings(HighlightSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final json = settings.toJson();
      await prefs.setString(_highlightSettingsKey, jsonEncode(json));
    } catch (e) {
      debugPrint('保存高亮设置失败: $e');
      rethrow;
    }
  }

  /// 统计文本中的单词数量
  int _countWords(String text) {
    final words = text
        .replaceAll(RegExp(r'[^\w\s]'), ' ')
        .split(RegExp(r'\s+'))
        .where((word) => word.isNotEmpty)
        .toList();
    return words.length;
  }

  /// 在文本中高亮词汇
  ImportResult _highlightVocabularyInText(
    String content,
    Map<String, MapEntry<String, WordDetails>> vocabularyMap,
  ) {
    final highlightedWords = <HighlightedWord>[];
    final words = RegExp(r'\b[a-zA-Z]+\b').allMatches(content);

    for (final match in words) {
      final word = match.group(0)!.toLowerCase();
      final vocabularyEntry = vocabularyMap[word];

      if (vocabularyEntry != null) {
        final originalWord = vocabularyEntry.key;
        final details = vocabularyEntry.value;
        final isVariant = word != originalWord.toLowerCase();

        highlightedWords.add(
          HighlightedWord(
            word: match.group(0)!,
            definition: details.definition,
            difficulty: details.difficulty,
            startIndex: match.start,
            endIndex: match.end,
            isVariant: isVariant,
            originalForm: originalWord,
          ),
        );
      }
    }

    // 生成高亮HTML文本
    String highlightedText = content;

    // 按位置排序，从后往前处理避免索引偏移
    highlightedWords.sort((a, b) => b.startIndex.compareTo(a.startIndex));

    for (final highlightedWord in highlightedWords) {
      final start = highlightedWord.startIndex;
      final end = highlightedWord.endIndex;
      final word = highlightedWord.word;

      // 创建高亮标签
      final highlightTag =
          '<span style="color: #E03E3E; font-weight: bold; background-color: #FFF2F2;">$word</span>';

      highlightedText =
          highlightedText.substring(0, start) +
          highlightTag +
          highlightedText.substring(end);
    }

    return ImportResult(
      originalText: content,
      highlightedText: highlightedText,
      highlightedWords: highlightedWords.reversed.toList(), // 恢复原始顺序
      totalWords: _countWords(content),
      vocabularyWordsFound: highlightedWords.length,
      hasProfanityViolations: false, // 此方法不处理脏话过滤
      profanityWordsFiltered: 0,
    );
  }

  /// 添加词汇到背诵列表
  Future<void> addWordToStudyList(String word) async {
    try {
      // 添加新单词到选择列表
      await _vocabularyService.batchSelectWords([word]);

      debugPrint('已添加单词到背诵列表: $word');
    } catch (e) {
      debugPrint('添加单词到背诵列表失败: $e');
      rethrow;
    }
  }
}
