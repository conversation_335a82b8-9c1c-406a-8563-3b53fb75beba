import 'package:flutter/material.dart';
import 'content_report_service.dart';

/// 内容举报对话框
class ContentReportDialog extends StatefulWidget {
  final String contentId;
  final String contentType;
  final String reporterId;
  final VoidCallback? onReported;

  const ContentReportDialog({
    super.key,
    required this.contentId,
    required this.contentType,
    required this.reporterId,
    this.onReported,
  });

  @override
  State<ContentReportDialog> createState() => _ContentReportDialogState();
}

class _ContentReportDialogState extends State<ContentReportDialog> {
  final ContentReportService _reportService = ContentReportService();
  final TextEditingController _reasonController = TextEditingController();
  final TextEditingController _additionalInfoController =
      TextEditingController();

  ReportType? _selectedType;
  bool _isSubmitting = false;

  @override
  void dispose() {
    _reasonController.dispose();
    _additionalInfoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFE03E3E).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.report_outlined,
              color: Color(0xFFE03E3E),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          const Text(
            '举报内容',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '请选择举报类型：',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 12),
            _buildReportTypeSelection(),
            const SizedBox(height: 16),
            const Text(
              '举报原因：',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _reasonController,
              decoration: InputDecoration(
                hintText: '请详细说明举报原因',
                hintStyle: const TextStyle(
                  color: Color(0xFF787774),
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
              maxLines: 3,
              maxLength: 200,
            ),
            const SizedBox(height: 16),
            const Text(
              '补充信息（可选）：',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _additionalInfoController,
              decoration: InputDecoration(
                hintText: '提供更多相关信息',
                hintStyle: const TextStyle(
                  color: Color(0xFF787774),
                  fontSize: 14,
                ),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: const BorderSide(color: Color(0xFF2E7EED)),
                ),
                contentPadding: const EdgeInsets.all(12),
              ),
              maxLines: 2,
              maxLength: 100,
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Color(0xFF787774),
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '我们会认真处理每一个举报，恶意举报可能导致账号受限。',
                      style: TextStyle(
                        fontSize: 12,
                        color: const Color(0xFF787774),
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isSubmitting ? null : () => Navigator.pop(context),
          child: const Text(
            '取消',
            style: TextStyle(color: Color(0xFF787774), fontSize: 14),
          ),
        ),
        ElevatedButton(
          onPressed: _isSubmitting || !_canSubmit() ? null : _submitReport,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFFE03E3E),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
          child: _isSubmitting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text('提交举报', style: TextStyle(fontSize: 14)),
        ),
      ],
    );
  }

  /// 构建举报类型选择
  Widget _buildReportTypeSelection() {
    return Column(
      children: ReportType.values
          .map((type) => _buildReportTypeOption(type))
          .toList(),
    );
  }

  /// 构建举报类型选项
  Widget _buildReportTypeOption(ReportType type) {
    final isSelected = _selectedType == type;
    final typeName = ContentReportService.getReportTypeName(type);
    final typeIcon = _getReportTypeIcon(type);

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () {
          setState(() {
            _selectedType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: isSelected
                ? const Color(0xFFE03E3E).withValues(alpha: 0.1)
                : null,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isSelected
                  ? const Color(0xFFE03E3E)
                  : const Color(0xFFE3E2E0),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Icon(
                isSelected
                    ? Icons.radio_button_checked
                    : Icons.radio_button_unchecked,
                color: isSelected
                    ? const Color(0xFFE03E3E)
                    : const Color(0xFF787774),
                size: 20,
              ),
              const SizedBox(width: 12),
              Icon(
                typeIcon,
                color: isSelected
                    ? const Color(0xFFE03E3E)
                    : const Color(0xFF787774),
                size: 18,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  typeName,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: isSelected
                        ? const Color(0xFFE03E3E)
                        : const Color(0xFF37352F),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取举报类型图标
  IconData _getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.profanity:
        return Icons.speaker_notes_off_outlined;
      case ReportType.spam:
        return Icons.block_outlined;
      case ReportType.harassment:
        return Icons.person_off_outlined;
      case ReportType.violence:
        return Icons.warning_outlined;
      case ReportType.inappropriate:
        return Icons.visibility_off_outlined;
      case ReportType.other:
        return Icons.more_horiz_outlined;
    }
  }

  /// 检查是否可以提交
  bool _canSubmit() {
    return _selectedType != null && _reasonController.text.trim().isNotEmpty;
  }

  /// 提交举报
  Future<void> _submitReport() async {
    if (!_canSubmit()) return;

    setState(() {
      _isSubmitting = true;
    });

    try {
      final success = await _reportService.submitReport(
        contentId: widget.contentId,
        contentType: widget.contentType,
        reporterId: widget.reporterId,
        type: _selectedType!,
        reason: _reasonController.text.trim(),
        additionalInfo: _additionalInfoController.text.trim().isNotEmpty
            ? _additionalInfoController.text.trim()
            : null,
      );

      if (mounted) {
        if (success) {
          Navigator.pop(context);
          widget.onReported?.call();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('举报已提交，我们会尽快处理'),
              backgroundColor: Color(0xFF0F7B6C),
              duration: Duration(milliseconds: 300),
            ),
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('举报提交失败，请稍后重试'),
              backgroundColor: Color(0xFFE03E3E),
              duration: Duration(seconds: 2),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('举报提交失败：$e'),
            backgroundColor: const Color(0xFFE03E3E),
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }
}

/// 显示举报对话框的便捷方法
Future<void> showContentReportDialog({
  required BuildContext context,
  required String contentId,
  required String contentType,
  required String reporterId,
  VoidCallback? onReported,
}) {
  return showDialog<void>(
    context: context,
    builder: (context) => ContentReportDialog(
      contentId: contentId,
      contentType: contentType,
      reporterId: reporterId,
      onReported: onReported,
    ),
  );
}
