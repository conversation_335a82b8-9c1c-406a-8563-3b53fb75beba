import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 脏话过滤级别
enum ProfanityLevel {
  mild, // 轻度：一般不当用词
  moderate, // 中度：明显不当用词
  severe, // 重度：严重不当用词
  extreme, // 极度：极端不当用词
}

/// 脏话词条模型
class ProfanityWord {
  final String word;
  final ProfanityLevel level;
  final List<String> variants; // 变体形式（拼音、谐音、变形等）
  final bool isRegex; // 是否为正则表达式
  final String? category; // 分类（如：政治敏感、色情、暴力等）

  const ProfanityWord({
    required this.word,
    required this.level,
    this.variants = const [],
    this.isRegex = false,
    this.category,
  });

  Map<String, dynamic> toJson() => {
    'word': word,
    'level': level.name,
    'variants': variants,
    'isRegex': isRegex,
    'category': category,
  };

  factory ProfanityWord.fromJson(Map<String, dynamic> json) => ProfanityWord(
    word: json['word'] as String,
    level: ProfanityLevel.values.firstWhere(
      (e) => e.name == json['level'],
      orElse: () => ProfanityLevel.mild,
    ),
    variants: List<String>.from(json['variants'] ?? []),
    isRegex: json['isRegex'] as bool? ?? false,
    category: json['category'] as String?,
  );
}

/// 脏话过滤设置
class ProfanityFilterSettings {
  final bool enabled;
  final ProfanityLevel filterLevel; // 过滤级别阈值
  final bool showReplacementText; // 是否显示替换文本
  final String replacementText; // 替换文本（如：***）
  final bool enableUserReporting; // 是否启用用户举报
  final bool enableCustomWords; // 是否启用自定义词库

  const ProfanityFilterSettings({
    this.enabled = true,
    this.filterLevel = ProfanityLevel.moderate,
    this.showReplacementText = true,
    this.replacementText = '***',
    this.enableUserReporting = true,
    this.enableCustomWords = true,
  });

  Map<String, dynamic> toJson() => {
    'enabled': enabled,
    'filterLevel': filterLevel.name,
    'showReplacementText': showReplacementText,
    'replacementText': replacementText,
    'enableUserReporting': enableUserReporting,
    'enableCustomWords': enableCustomWords,
  };

  factory ProfanityFilterSettings.fromJson(Map<String, dynamic> json) =>
      ProfanityFilterSettings(
        enabled: json['enabled'] as bool? ?? true,
        filterLevel: ProfanityLevel.values.firstWhere(
          (e) => e.name == json['filterLevel'],
          orElse: () => ProfanityLevel.moderate,
        ),
        showReplacementText: json['showReplacementText'] as bool? ?? true,
        replacementText: json['replacementText'] as String? ?? '***',
        enableUserReporting: json['enableUserReporting'] as bool? ?? true,
        enableCustomWords: json['enableCustomWords'] as bool? ?? true,
      );

  ProfanityFilterSettings copyWith({
    bool? enabled,
    ProfanityLevel? filterLevel,
    bool? showReplacementText,
    String? replacementText,
    bool? enableUserReporting,
    bool? enableCustomWords,
  }) => ProfanityFilterSettings(
    enabled: enabled ?? this.enabled,
    filterLevel: filterLevel ?? this.filterLevel,
    showReplacementText: showReplacementText ?? this.showReplacementText,
    replacementText: replacementText ?? this.replacementText,
    enableUserReporting: enableUserReporting ?? this.enableUserReporting,
    enableCustomWords: enableCustomWords ?? this.enableCustomWords,
  );
}

/// 检测结果
class ProfanityDetectionResult {
  final String originalText;
  final String filteredText;
  final List<DetectedProfanity> detectedWords;
  final bool hasViolations;

  const ProfanityDetectionResult({
    required this.originalText,
    required this.filteredText,
    required this.detectedWords,
    required this.hasViolations,
  });
}

/// 检测到的脏话
class DetectedProfanity {
  final String word;
  final int startIndex;
  final int endIndex;
  final ProfanityLevel level;
  final String? category;
  final bool isCustomWord; // 是否为用户自定义词汇

  const DetectedProfanity({
    required this.word,
    required this.startIndex,
    required this.endIndex,
    required this.level,
    this.category,
    this.isCustomWord = false,
  });
}

/// 脏话过滤服务
class ProfanityFilterService {
  static const String _settingsKey = 'profanity_filter_settings';
  static const String _customWordsKey = 'custom_profanity_words';
  static const String _systemWordsKey = 'system_profanity_words';
  static const String _lastUpdateKey = 'profanity_words_last_update';

  // 缓存
  List<ProfanityWord>? _systemWords;
  List<ProfanityWord>? _customWords;
  ProfanityFilterSettings? _settings;

  /// 初始化服务
  Future<void> initialize() async {
    await _loadSystemWords();
    await _loadCustomWords();
    await _loadSettings();
  }

  /// 加载系统词库
  Future<void> _loadSystemWords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedData = prefs.getString(_systemWordsKey);

      if (cachedData != null) {
        // 从缓存加载
        final List<dynamic> jsonList = json.decode(cachedData);
        _systemWords = jsonList
            .map((json) => ProfanityWord.fromJson(json))
            .toList();
        debugPrint('从缓存加载 ${_systemWords!.length} 个系统脏话词汇');
      } else {
        // 从资源文件加载
        await _loadSystemWordsFromAssets();
      }
    } catch (e) {
      debugPrint('加载系统脏话词库失败: $e');
      _systemWords = [];
    }
  }

  /// 从资源文件加载系统词库
  Future<void> _loadSystemWordsFromAssets() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/data/profanity_words.json',
      );
      final Map<String, dynamic> jsonMap = json.decode(jsonString);

      final List<dynamic> wordsJson = jsonMap['words'] ?? [];
      _systemWords = wordsJson
          .map((json) => ProfanityWord.fromJson(json))
          .toList();

      // 缓存到本地
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _systemWordsKey,
        json.encode(_systemWords!.map((word) => word.toJson()).toList()),
      );
      await prefs.setInt(_lastUpdateKey, DateTime.now().millisecondsSinceEpoch);

      debugPrint('从资源文件加载 ${_systemWords!.length} 个系统脏话词汇');
    } catch (e) {
      debugPrint('从资源文件加载系统脏话词库失败: $e');
      _systemWords = [];
    }
  }

  /// 加载自定义词库
  Future<void> _loadCustomWords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_customWordsKey);

      if (jsonString != null) {
        final List<dynamic> jsonList = json.decode(jsonString);
        _customWords = jsonList
            .map((json) => ProfanityWord.fromJson(json))
            .toList();
        debugPrint('加载 ${_customWords!.length} 个自定义脏话词汇');
      } else {
        _customWords = [];
      }
    } catch (e) {
      debugPrint('加载自定义脏话词库失败: $e');
      _customWords = [];
    }
  }

  /// 加载设置
  Future<void> _loadSettings() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_settingsKey);

      if (jsonString != null) {
        final Map<String, dynamic> jsonMap = json.decode(jsonString);
        _settings = ProfanityFilterSettings.fromJson(jsonMap);
      } else {
        _settings = const ProfanityFilterSettings();
      }
    } catch (e) {
      debugPrint('加载脏话过滤设置失败: $e');
      _settings = const ProfanityFilterSettings();
    }
  }

  /// 保存设置
  Future<void> saveSettings(ProfanityFilterSettings settings) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_settingsKey, json.encode(settings.toJson()));
      _settings = settings;
      debugPrint('脏话过滤设置已保存');
    } catch (e) {
      debugPrint('保存脏话过滤设置失败: $e');
    }
  }

  /// 获取当前设置
  ProfanityFilterSettings getSettings() {
    return _settings ?? const ProfanityFilterSettings();
  }

  /// 添加自定义词汇
  Future<void> addCustomWord(ProfanityWord word) async {
    _customWords ??= [];
    _customWords!.add(word);
    await _saveCustomWords();
  }

  /// 移除自定义词汇
  Future<void> removeCustomWord(String word) async {
    _customWords?.removeWhere((w) => w.word == word);
    await _saveCustomWords();
  }

  /// 保存自定义词库
  Future<void> _saveCustomWords() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _customWords!.map((word) => word.toJson()).toList();
      await prefs.setString(_customWordsKey, json.encode(jsonList));
      debugPrint('自定义脏话词库已保存');
    } catch (e) {
      debugPrint('保存自定义脏话词库失败: $e');
    }
  }

  /// 检测和过滤文本
  Future<ProfanityDetectionResult> filterText(String text) async {
    debugPrint('🛡️ ProfanityFilterService.filterText() 开始检测文本: "$text"');

    if (_settings?.enabled != true || text.isEmpty) {
      debugPrint('🛡️ 过滤功能未启用或文本为空，跳过检测');
      debugPrint(
        '🛡️ 设置状态: enabled=${_settings?.enabled}, 文本长度=${text.length}',
      );
      return ProfanityDetectionResult(
        originalText: text,
        filteredText: text,
        detectedWords: [],
        hasViolations: false,
      );
    }

    await initialize(); // 确保数据已加载

    debugPrint(
      '🛡️ 初始化完成，设置状态: enabled=${_settings?.enabled}, filterLevel=${_settings?.filterLevel}',
    );
    debugPrint('🛡️ 系统词库数量: ${_systemWords?.length ?? 0}');
    debugPrint('🛡️ 自定义词库数量: ${_customWords?.length ?? 0}');

    final detectedWords = <DetectedProfanity>[];
    String filteredText = text;

    // 合并系统词库和自定义词库
    final allWords = <ProfanityWord>[
      ...(_systemWords ?? []),
      if (_settings!.enableCustomWords) ...(_customWords ?? []),
    ];

    debugPrint('🛡️ 总词库数量: ${allWords.length}');

    // 按级别过滤
    final wordsToCheck = allWords
        .where((word) => word.level.index >= _settings!.filterLevel.index)
        .toList();

    debugPrint('🛡️ 符合过滤级别的词汇数量: ${wordsToCheck.length}');
    debugPrint(
      '🛡️ 检查的词汇: ${wordsToCheck.map((w) => w.word).take(10).join(', ')}...',
    );

    // 检测脏话
    for (final profanityWord in wordsToCheck) {
      final matches = _findMatches(text, profanityWord);
      if (matches.isNotEmpty) {
        debugPrint(
          '🛡️ 找到匹配词汇: ${profanityWord.word} -> ${matches.length} 个匹配',
        );
      }
      detectedWords.addAll(matches);
    }

    debugPrint('🛡️ 总共检测到 ${detectedWords.length} 个违禁词');

    // 按位置排序，从后往前替换避免索引偏移
    detectedWords.sort((a, b) => b.startIndex.compareTo(a.startIndex));

    // 替换检测到的脏话
    for (final detected in detectedWords) {
      final replacement = _settings!.showReplacementText
          ? _settings!.replacementText
          : '';

      filteredText =
          filteredText.substring(0, detected.startIndex) +
          replacement +
          filteredText.substring(detected.endIndex);
    }

    return ProfanityDetectionResult(
      originalText: text,
      filteredText: filteredText,
      detectedWords: detectedWords.reversed.toList(), // 恢复原始顺序
      hasViolations: detectedWords.isNotEmpty,
    );
  }

  /// 查找匹配的脏话
  List<DetectedProfanity> _findMatches(
    String text,
    ProfanityWord profanityWord,
  ) {
    final matches = <DetectedProfanity>[];
    final lowerText = text.toLowerCase();

    // 检查主词汇
    _findWordMatches(lowerText, profanityWord.word, profanityWord, matches);

    // 检查变体
    for (final variant in profanityWord.variants) {
      _findWordMatches(lowerText, variant, profanityWord, matches);
    }

    return matches;
  }

  /// 查找单个词汇的匹配
  void _findWordMatches(
    String text,
    String word,
    ProfanityWord profanityWord,
    List<DetectedProfanity> matches,
  ) {
    if (profanityWord.isRegex) {
      // 正则表达式匹配
      try {
        final regex = RegExp(word, caseSensitive: false);
        final regexMatches = regex.allMatches(text);

        for (final match in regexMatches) {
          matches.add(
            DetectedProfanity(
              word: match.group(0)!,
              startIndex: match.start,
              endIndex: match.end,
              level: profanityWord.level,
              category: profanityWord.category,
              isCustomWord: _customWords?.contains(profanityWord) ?? false,
            ),
          );
        }
      } catch (e) {
        debugPrint('正则表达式匹配失败: $word, $e');
      }
    } else {
      // 普通字符串匹配
      final lowerWord = word.toLowerCase();
      int startIndex = 0;

      while (true) {
        final index = text.indexOf(lowerWord, startIndex);
        if (index == -1) break;

        matches.add(
          DetectedProfanity(
            word: text.substring(index, index + lowerWord.length),
            startIndex: index,
            endIndex: index + lowerWord.length,
            level: profanityWord.level,
            category: profanityWord.category,
            isCustomWord: _customWords?.contains(profanityWord) ?? false,
          ),
        );

        startIndex = index + 1;
      }
    }
  }

  /// 获取自定义词汇列表
  List<ProfanityWord> getCustomWords() {
    return List.from(_customWords ?? []);
  }

  /// 清除缓存
  Future<void> clearCache() async {
    _systemWords = null;
    _customWords = null;
    _settings = null;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_systemWordsKey);
    await prefs.remove(_lastUpdateKey);
  }
}
