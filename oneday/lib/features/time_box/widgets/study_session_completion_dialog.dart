import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/timebox_models.dart';
import '../../../services/study_session_completion_service.dart';
import '../../../services/providers/study_session_completion_provider.dart';

/// 学习会话完成对话框
/// 
/// 显示学习会话完成后的详细信息，包括：
/// - 学习时长和经验值获得
/// - 数据同步状态
/// - 今日学习统计摘要
class StudySessionCompletionDialog extends ConsumerStatefulWidget {
  final TimeBoxTask completedTask;
  final StudySessionCompletionResult completionResult;

  const StudySessionCompletionDialog({
    super.key,
    required this.completedTask,
    required this.completionResult,
  });

  @override
  ConsumerState<StudySessionCompletionDialog> createState() =>
      _StudySessionCompletionDialogState();
}

class _StudySessionCompletionDialogState
    extends ConsumerState<StudySessionCompletionDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.elasticOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final todayStudySummaryAsync = ref.watch(todayStudySummaryProvider);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: AlertDialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
              contentPadding: EdgeInsets.zero,
              content: Container(
                width: MediaQuery.of(context).size.width * 0.85,
                constraints: const BoxConstraints(maxHeight: 600),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  gradient: const LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color(0xFFF8F9FA),
                      Color(0xFFFFFFFF),
                    ],
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    _buildHeader(),
                    _buildTaskInfo(),
                    _buildDataSyncStatus(),
                    todayStudySummaryAsync.when(
                      data: (summary) => _buildTodaySummary(summary),
                      loading: () => const Padding(
                        padding: EdgeInsets.all(16),
                        child: CircularProgressIndicator(),
                      ),
                      error: (error, stack) => _buildErrorWidget(error),
                    ),
                    _buildActions(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
        gradient: LinearGradient(
          colors: [Color(0xFF2E7EED), Color(0xFF1E5FCC)],
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.celebration,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          const Expanded(
            child: Text(
              '🎉 学习会话完成！',
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaskInfo() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.task_alt, color: Color(0xFF2E7EED), size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  widget.completedTask.title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildInfoChip(
                icon: Icons.timer,
                label: '学习时长',
                value: '${widget.completedTask.plannedMinutes} 分钟',
                color: const Color(0xFF0F7B6C),
              ),
              const SizedBox(width: 12),
              _buildInfoChip(
                icon: Icons.stars,
                label: '经验值',
                value: '+${widget.completionResult.experienceGained}',
                color: const Color(0xFFE67E22),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInfoChip({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: color.withValues(alpha: 0.2)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color: color,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              value,
              style: TextStyle(
                fontSize: 14,
                color: color,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSyncStatus() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '数据同步状态',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          _buildSyncStatusItem(
            '学习时间统计',
            widget.completionResult.studyTimeUpdated,
            widget.completionResult.studyTimeError,
          ),
          _buildSyncStatusItem(
            '成就系统',
            widget.completionResult.achievementsUpdated,
            widget.completionResult.achievementError,
          ),
          _buildSyncStatusItem(
            '每日计划',
            widget.completionResult.dailyPlanUpdated,
            widget.completionResult.dailyPlanError,
          ),
        ],
      ),
    );
  }

  Widget _buildSyncStatusItem(String title, bool isSuccess, String? error) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            isSuccess ? Icons.check_circle : Icons.error,
            color: isSuccess ? const Color(0xFF0F7B6C) : const Color(0xFFE74C3C),
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              title,
              style: const TextStyle(
                fontSize: 13,
                color: Color(0xFF787774),
              ),
            ),
          ),
          if (error != null)
            Tooltip(
              message: error,
              child: const Icon(
                Icons.warning,
                color: Color(0xFFE67E22),
                size: 16,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTodaySummary(Map<String, dynamic> summary) {
    return Container(
      margin: const EdgeInsets.all(20),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFFF8F9FA),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE1E4E8)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '今日学习统计',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildSummaryItem(
                '总时长',
                '${summary['totalStudyMinutes']}分钟',
                Icons.schedule,
              ),
              _buildSummaryItem(
                '完成任务',
                '${summary['completedTasks']}个',
                Icons.task_alt,
              ),
              _buildSummaryItem(
                '连续天数',
                '${summary['studyStreak']}天',
                Icons.local_fire_department,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value, IconData icon) {
    return Expanded(
      child: Column(
        children: [
          Icon(icon, color: const Color(0xFF2E7EED), size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF787774),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget(Object error) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Text(
        '加载统计数据失败: $error',
        style: const TextStyle(
          color: Color(0xFFE74C3C),
          fontSize: 12,
        ),
      ),
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: TextButton(
              onPressed: () => Navigator.of(context).pop(),
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: const BorderSide(color: Color(0xFFE1E4E8)),
                ),
              ),
              child: const Text(
                '完成',
                style: TextStyle(
                  color: Color(0xFF37352F),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
