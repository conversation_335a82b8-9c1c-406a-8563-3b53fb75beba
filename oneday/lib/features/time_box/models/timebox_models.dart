import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'timebox_models.g.dart';

/// 任务状态枚举
@JsonEnum()
enum TaskStatus {
  @JsonValue('pending')
  pending,

  @JsonValue('inProgress')
  inProgress,

  @JsonValue('completed')
  completed;

  String get displayName {
    switch (this) {
      case TaskStatus.pending:
        return '待开始';
      case TaskStatus.inProgress:
        return '进行中';
      case TaskStatus.completed:
        return '已完成';
    }
  }

  Color get color {
    switch (this) {
      case TaskStatus.pending:
        return const Color(0xFF9B9A97);
      case TaskStatus.inProgress:
        return const Color(0xFFD9730D);
      case TaskStatus.completed:
        return const Color(0xFF0F7B6C);
    }
  }
}

/// 任务优先级枚举
@JsonEnum()
enum TaskPriority {
  @JsonValue('high')
  high,

  @JsonValue('medium')
  medium,

  @JsonValue('low')
  low;

  String get displayName {
    switch (this) {
      case TaskPriority.high:
        return '高优先级';
      case TaskPriority.medium:
        return '中优先级';
      case TaskPriority.low:
        return '低优先级';
    }
  }

  Color get color {
    switch (this) {
      case TaskPriority.high:
        return const Color(0xFFE03E3E);
      case TaskPriority.medium:
        return const Color(0xFFD9730D);
      case TaskPriority.low:
        return const Color(0xFF0F7B6C);
    }
  }
}

/// 番茄钟计时器状态枚举
@JsonEnum()
enum PomodoroTimerState {
  @JsonValue('work')
  work,

  @JsonValue('rest')
  rest,

  @JsonValue('stopped')
  stopped;

  String get displayName {
    switch (this) {
      case PomodoroTimerState.work:
        return '工作中';
      case PomodoroTimerState.rest:
        return '休息中';
      case PomodoroTimerState.stopped:
        return '已停止';
    }
  }

  Color get color {
    switch (this) {
      case PomodoroTimerState.work:
        return const Color(0xFF0F7B6C);
      case PomodoroTimerState.rest:
        return const Color(0xFF2E7EED);
      case PomodoroTimerState.stopped:
        return const Color(0xFF9B9A97);
    }
  }
}

/// 任务分类数据模型
class TaskCategory {
  /// 唯一标识符
  final String id;

  /// 分类名称
  final String name;

  /// 分类颜色
  final int colorValue;

  /// 分类图标
  final String iconName;

  /// 是否为默认分类
  final bool isDefault;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  final DateTime lastModified;

  const TaskCategory({
    required this.id,
    required this.name,
    required this.colorValue,
    required this.iconName,
    this.isDefault = false,
    required this.createdAt,
    required this.lastModified,
  });

  /// 从JSON创建实例
  factory TaskCategory.fromJson(Map<String, dynamic> json) {
    return TaskCategory(
      id: json['id'] as String,
      name: json['name'] as String,
      colorValue: json['colorValue'] as int,
      iconName: json['iconName'] as String,
      isDefault: json['isDefault'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      lastModified: DateTime.parse(json['lastModified'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'colorValue': colorValue,
      'iconName': iconName,
      'isDefault': isDefault,
      'createdAt': createdAt.toIso8601String(),
      'lastModified': lastModified.toIso8601String(),
    };
  }

  /// 获取颜色对象
  Color get color => Color(colorValue);

  /// 复制并修改
  TaskCategory copyWith({
    String? id,
    String? name,
    int? colorValue,
    Color? color,
    String? iconName,
    bool? isDefault,
    DateTime? createdAt,
    DateTime? lastModified,
  }) {
    return TaskCategory(
      id: id ?? this.id,
      name: name ?? this.name,
      colorValue: colorValue ?? (color?.toARGB32()) ?? this.colorValue,
      iconName: iconName ?? this.iconName,
      isDefault: isDefault ?? this.isDefault,
      createdAt: createdAt ?? this.createdAt,
      lastModified: lastModified ?? this.lastModified,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TaskCategory && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// 任务分类颜色工具类
class TaskCategoryColors {
  /// 默认颜色列表
  static const List<Color> defaultColors = [
    Color(0xFFE03E3E), // 红色
    Color(0xFFFFD700), // 荧光黄
    Color(0xFF0F7B6C), // 绿色
    Color(0xFF2E7EED), // 蓝色
    Color(0xFF9B9A97), // 灰色
    Color(0xFFFF6B6B), // 浅红色
    Color(0xFF4ECDC4), // 青色
    Color(0xFF45B7D1), // 天蓝色
    Color(0xFF96CEB4), // 薄荷绿
    Color(0xFFFECEA8), // 桃色
  ];

  /// 根据分类获取对应颜色
  static Color getCategoryColor(String category) {
    // 这个方法将被TaskCategoryManager替代，保留用于向后兼容
    switch (category) {
      case '计算机科学':
        return const Color(0xFFE03E3E); // 红色
      case '数学':
        return const Color(0xFFFFD700); // 荧光黄
      case '英语':
        return const Color(0xFF0F7B6C); // 绿色
      case '政治':
        return const Color(0xFF2E7EED); // 蓝色
      default:
        return const Color(0xFF9B9A97); // 默认灰色
    }
  }

  /// 获取分类的半透明颜色
  static Color getCategoryColorWithOpacity(String category, double opacity) {
    return getCategoryColor(category).withValues(alpha: opacity);
  }

  /// 根据索引获取默认颜色
  static Color getDefaultColorByIndex(int index) {
    return defaultColors[index % defaultColors.length];
  }
}

/// 时间盒子任务数据模型
@JsonSerializable()
class TimeBoxTask {
  /// 唯一标识符
  final String id;

  /// 任务标题
  final String title;

  /// 任务描述
  final String description;

  /// 计划时长（分钟）
  final int plannedMinutes;

  /// 任务状态
  final TaskStatus status;

  /// 优先级
  final TaskPriority priority;

  /// 分类
  final String category;

  /// 创建时间
  final DateTime createdAt;

  /// 开始时间
  final DateTime? startTime;

  /// 结束时间
  final DateTime? endTime;

  const TimeBoxTask({
    required this.id,
    required this.title,
    required this.description,
    required this.plannedMinutes,
    required this.status,
    required this.priority,
    required this.category,
    required this.createdAt,
    this.startTime,
    this.endTime,
  });

  /// 从JSON创建实例
  factory TimeBoxTask.fromJson(Map<String, dynamic> json) =>
      _$TimeBoxTaskFromJson(json);

  /// 转换为JSON
  Map<String, dynamic> toJson() => _$TimeBoxTaskToJson(this);

  /// 创建副本并更新指定字段
  TimeBoxTask copyWith({
    String? id,
    String? title,
    String? description,
    int? plannedMinutes,
    TaskStatus? status,
    TaskPriority? priority,
    String? category,
    DateTime? createdAt,
    DateTime? startTime,
    DateTime? endTime,
  }) {
    return TimeBoxTask(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      plannedMinutes: plannedMinutes ?? this.plannedMinutes,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
    );
  }

  /// 获取实际时长（分钟）
  int? get actualMinutes {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!).inMinutes;
    }
    return null;
  }

  /// 获取任务时长（分钟）
  int get durationMinutes {
    if (actualMinutes != null) {
      return actualMinutes!;
    }
    return plannedMinutes;
  }

  /// 获取分类颜色
  Color get categoryColor => TaskCategoryColors.getCategoryColor(category);

  /// 获取分类半透明颜色
  Color getCategoryColorWithOpacity(double opacity) {
    return TaskCategoryColors.getCategoryColorWithOpacity(category, opacity);
  }

  /// 检查是否为今天的任务
  bool get isToday {
    if (startTime == null) return false;
    final now = DateTime.now();
    return startTime!.year == now.year &&
        startTime!.month == now.month &&
        startTime!.day == now.day;
  }

  /// 检查是否为休息任务（已废弃，保留用于向后兼容）
  bool get isRestTask => false;

  /// 检查是否已完成
  bool get isCompleted => status == TaskStatus.completed;

  /// 检查是否进行中
  bool get isInProgress => status == TaskStatus.inProgress;

  /// 检查是否待开始
  bool get isPending => status == TaskStatus.pending;

  /// 计算获得的工资（基于实际时长）
  double calculateWage() {
    const double hourlyRate = 200.0; // 每小时200元
    final minutes = durationMinutes;
    return (minutes / 60.0) * hourlyRate;
  }

  /// 获取任务在日历中的开始小时
  int? get startHour => startTime?.hour;

  /// 获取任务在日历中的结束小时
  int? get endHour => endTime?.hour;

  /// 获取任务跨越的小时数
  int get spanHours {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!).inHours + 1;
    }
    return 1; // 默认跨越1小时
  }

  /// 获取任务在指定小时的显示高度比例
  double getHeightRatioForHour(int hour) {
    if (startTime == null) return 0.0;

    final taskStartHour = startTime!.hour;
    final taskStartMinute = startTime!.minute;

    if (endTime != null) {
      final taskEndHour = endTime!.hour;
      final taskEndMinute = endTime!.minute;

      // 如果任务不在这个小时内，返回0
      if (hour < taskStartHour || hour > taskEndHour) {
        return 0.0;
      }

      // 计算在这个小时内的时长比例
      if (hour == taskStartHour && hour == taskEndHour) {
        // 任务在同一小时内开始和结束
        return (taskEndMinute - taskStartMinute) / 60.0;
      } else if (hour == taskStartHour) {
        // 任务在这个小时开始
        return (60 - taskStartMinute) / 60.0;
      } else if (hour == taskEndHour) {
        // 任务在这个小时结束
        return taskEndMinute / 60.0;
      } else {
        // 任务跨越整个小时
        return 1.0;
      }
    } else {
      // 没有结束时间，使用计划时长
      if (hour == taskStartHour) {
        final remainingMinutesInHour = 60 - taskStartMinute;
        final taskRemainingMinutes = plannedMinutes;
        return (taskRemainingMinutes.clamp(0, remainingMinutesInHour)) / 60.0;
      }
      return 0.0;
    }
  }

  /// 检查任务是否在指定小时内
  bool isInHour(int hour) {
    if (startTime == null) return false;

    if (endTime != null) {
      return hour >= startTime!.hour && hour <= endTime!.hour;
    } else {
      // 没有结束时间，检查是否在开始小时
      return hour == startTime!.hour;
    }
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeBoxTask &&
        other.id == id &&
        other.title == title &&
        other.description == description &&
        other.plannedMinutes == plannedMinutes &&
        other.status == status &&
        other.priority == priority &&
        other.category == category &&
        other.createdAt == createdAt &&
        other.startTime == startTime &&
        other.endTime == endTime;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      title,
      description,
      plannedMinutes,
      status,
      priority,
      category,
      createdAt,
      startTime,
      endTime,
    );
  }

  @override
  String toString() {
    return 'TimeBoxTask(id: $id, title: $title, status: $status, category: $category)';
  }
}
