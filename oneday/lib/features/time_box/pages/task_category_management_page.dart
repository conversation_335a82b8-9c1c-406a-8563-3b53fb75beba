import 'package:flutter/material.dart';
import '../managers/task_category_manager.dart';
import '../models/timebox_models.dart';

/// 任务分类管理页面
///
/// 提供用户自定义任务分类的管理功能：
/// - 查看所有分类（默认分类和自定义分类）
/// - 添加新的自定义分类
/// - 编辑现有分类（仅限自定义分类）
/// - 删除自定义分类
/// - 分类名称验证
class TaskCategoryManagementPage extends StatefulWidget {
  const TaskCategoryManagementPage({super.key});

  @override
  State<TaskCategoryManagementPage> createState() =>
      _TaskCategoryManagementPageState();
}

class _TaskCategoryManagementPageState
    extends State<TaskCategoryManagementPage> {
  final TaskCategoryManager _categoryManager = taskCategoryManager;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    setState(() {
      _isLoading = true;
    });

    await _categoryManager.loadFromStorage();

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('任务分类管理'),
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF37352F),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: _showAddCategoryDialog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildCategoryList(),
    );
  }

  Widget _buildCategoryList() {
    final categories = _categoryManager.categories;

    if (categories.isEmpty) {
      return const Center(
        child: Text(
          '暂无任务分类\n点击右上角 + 添加新分类',
          textAlign: TextAlign.center,
          style: TextStyle(color: Color(0xFF9B9A97), fontSize: 16),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(category);
      },
    );
  }

  Widget _buildCategoryCard(TaskCategory category) {
    return Card(
      color: Colors.white,
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        leading: CircleAvatar(
          backgroundColor: category.color,
          radius: 20,
          child: Icon(
            _getIconData(category.iconName),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          category.name,
          style: const TextStyle(fontWeight: FontWeight.w500, fontSize: 16),
        ),
        subtitle: Text(
          category.isDefault ? '系统分类（可编辑）' : '自定义分类',
          style: TextStyle(
            color: category.isDefault
                ? const Color(0xFF0F7B6C)
                : const Color(0xFF9B9A97),
            fontSize: 12,
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              _showEditCategoryDialog(category);
            } else if (value == 'delete') {
              _showDeleteConfirmDialog(category);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit, size: 16),
                  SizedBox(width: 8),
                  Text('编辑'),
                ],
              ),
            ),
            // 只有非默认分类才显示删除选项
            if (!category.isDefault)
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 16, color: Colors.red),
                    SizedBox(width: 8),
                    Text('删除', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'computer':
        return Icons.computer;
      case 'calculate':
        return Icons.calculate;
      case 'translate':
        return Icons.translate;
      case 'account_balance':
        return Icons.account_balance;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'category':
        return Icons.category;
      case 'task_alt':
        return Icons.task_alt;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'music_note':
        return Icons.music_note;
      case 'palette':
        return Icons.palette;
      case 'sports':
        return Icons.sports;
      case 'restaurant':
        return Icons.restaurant;
      case 'home':
        return Icons.home;
      default:
        return Icons.task_alt;
    }
  }

  void _showAddCategoryDialog() {
    _showCategoryDialog();
  }

  void _showEditCategoryDialog(TaskCategory category) {
    _showCategoryDialog(category: category);
  }

  void _showCategoryDialog({TaskCategory? category}) {
    showDialog(
      context: context,
      builder: (context) => TaskCategoryDialog(
        category: category,
        onSaved: (name, color, iconName) async {
          bool success;
          if (category == null) {
            success = await _categoryManager.addCategory(name, color, iconName);
          } else {
            success = await _categoryManager.updateCategory(
              category.id,
              name,
              color,
              iconName,
            );
          }

          if (mounted) {
            setState(() {});
            if (context.mounted) {
              if (success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(category == null ? '分类添加成功' : '分类更新成功'),
                    backgroundColor: const Color(0xFF0F7B6C),
                    duration: const Duration(milliseconds: 300),
                  ),
                );
              } else {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('操作失败，请检查分类名称是否重复'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            }
          }
        },
      ),
    );
  }

  void _showDeleteConfirmDialog(TaskCategory category) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        title: const Text('确认删除'),
        content: Text('确定要删除分类"${category.name}"吗？\n此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await _categoryManager.deleteCategory(
                category.id,
              );
              if (mounted && success) {
                setState(() {});
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('分类删除成功'),
                      backgroundColor: Color(0xFF0F7B6C),
                      duration: Duration(milliseconds: 300),
                    ),
                  );
                }
              }
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('删除'),
          ),
        ],
      ),
    );
  }
}

/// 任务分类编辑对话框
class TaskCategoryDialog extends StatefulWidget {
  final TaskCategory? category;
  final Function(String name, Color color, String iconName) onSaved;

  const TaskCategoryDialog({super.key, this.category, required this.onSaved});

  @override
  State<TaskCategoryDialog> createState() => _TaskCategoryDialogState();
}

class _TaskCategoryDialogState extends State<TaskCategoryDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();

  Color _selectedColor = const Color(0xFFE03E3E); // 默认红色
  String _selectedIcon = 'task_alt';

  // 可用颜色列表
  static const List<Color> _availableColors = [
    Color(0xFFE03E3E), // 红色
    Color(0xFFFFD700), // 荧光黄
    Color(0xFF0F7B6C), // 绿色
    Color(0xFF2E7EED), // 蓝色
    Color(0xFF9B9A97), // 灰色
    Color(0xFFFF6B6B), // 浅红色
    Color(0xFF4ECDC4), // 青色
    Color(0xFF45B7D1), // 天蓝色
    Color(0xFF96CEB4), // 薄荷绿
    Color(0xFFFECEA8), // 桃色
  ];

  final List<String> _availableIcons = [
    'task_alt',
    'work',
    'school',
    'computer',
    'calculate',
    'translate',
    'account_balance',
    'self_improvement',
    'fitness_center',
    'music_note',
    'palette',
    'sports',
    'restaurant',
    'home',
    'category',
  ];

  @override
  void initState() {
    super.initState();
    if (widget.category != null) {
      _nameController.text = widget.category!.name;
      _selectedColor = widget.category!.color;
      _selectedIcon = widget.category!.iconName;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: Text(widget.category == null ? '添加分类' : '编辑分类'),
      content: SizedBox(
        width: double.maxFinite,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 分类名称输入
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: '分类名称',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return '请输入分类名称';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),

              // 颜色选择
              const Text('选择颜色', style: TextStyle(fontSize: 16)),
              const SizedBox(height: 8),
              _buildColorSelector(),
              const SizedBox(height: 16),

              // 图标选择
              const Text('选择图标', style: TextStyle(fontSize: 16)),
              const SizedBox(height: 8),
              _buildIconSelector(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: _handleSave,
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7EED),
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('保存'),
        ),
      ],
    );
  }

  Widget _buildColorSelector() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: _availableColors.map((color) {
        final isSelected = color == _selectedColor;
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedColor = color;
            });
          },
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: color,
              shape: BoxShape.circle,
              border: isSelected
                  ? Border.all(color: const Color(0xFF37352F), width: 3)
                  : null,
            ),
            child: isSelected
                ? const Icon(Icons.check, color: Colors.white, size: 20)
                : null,
          ),
        );
      }).toList(),
    );
  }

  Widget _buildIconSelector() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        border: Border.all(color: const Color(0xFFE3E2E0)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Wrap(
        spacing: 8,
        runSpacing: 8,
        children: _availableIcons.take(8).map((iconName) {
          final isSelected = iconName == _selectedIcon;
          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedIcon = iconName;
              });
            },
            child: Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: isSelected
                    ? _selectedColor.withValues(alpha: 0.2)
                    : Colors.transparent,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: isSelected ? _selectedColor : const Color(0xFFE3E2E0),
                  width: isSelected ? 2 : 1,
                ),
              ),
              child: Icon(
                _getIconData(iconName),
                color: isSelected ? _selectedColor : const Color(0xFF9B9A97),
                size: 20,
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'computer':
        return Icons.computer;
      case 'calculate':
        return Icons.calculate;
      case 'translate':
        return Icons.translate;
      case 'account_balance':
        return Icons.account_balance;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'category':
        return Icons.category;
      case 'task_alt':
        return Icons.task_alt;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'fitness_center':
        return Icons.fitness_center;
      case 'music_note':
        return Icons.music_note;
      case 'palette':
        return Icons.palette;
      case 'sports':
        return Icons.sports;
      case 'restaurant':
        return Icons.restaurant;
      case 'home':
        return Icons.home;
      default:
        return Icons.task_alt;
    }
  }

  void _handleSave() {
    if (_formKey.currentState!.validate()) {
      widget.onSaved(
        _nameController.text.trim(),
        _selectedColor,
        _selectedIcon,
      );
      Navigator.of(context).pop();
    }
  }
}
