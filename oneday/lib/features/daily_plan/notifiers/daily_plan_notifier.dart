import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/daily_plan.dart';

/// 每日计划状态
class DailyPlanState {
  /// 所有计划数据，使用存储键作为key
  final Map<String, DailyPlan> plans;
  
  /// 是否正在加载
  final bool isLoading;
  
  /// 错误信息
  final String? error;
  
  /// 最后更新时间
  final DateTime? lastUpdated;

  const DailyPlanState({
    this.plans = const {},
    this.isLoading = false,
    this.error,
    this.lastUpdated,
  });

  DailyPlanState copyWith({
    Map<String, DailyPlan>? plans,
    bool? isLoading,
    String? error,
    DateTime? lastUpdated,
  }) {
    return DailyPlanState(
      plans: plans ?? this.plans,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      lastUpdated: lastUpdated ?? DateTime.now(),
    );
  }

  /// 获取指定日期和类型的计划
  DailyPlan? getPlan(DateTime date, DailyPlanType type) {
    final key = '${_formatDate(date)}_${type.name}';
    return plans[key];
  }

  /// 获取指定日期的所有计划
  List<DailyPlan> getPlansForDate(DateTime date) {
    final dateStr = _formatDate(date);
    return plans.values
        .where((plan) => _formatDate(plan.date) == dateStr)
        .toList()
      ..sort((a, b) => a.type.index.compareTo(b.type.index));
  }

  /// 检查指定日期和类型是否有内容
  bool hasContent(DateTime date, DailyPlanType type) {
    final plan = getPlan(date, type);
    return plan != null && plan.isNotEmpty;
  }

  /// 格式化日期
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// 每日计划状态管理器
class DailyPlanNotifier extends StateNotifier<DailyPlanState> {
  static const String _storageKey = 'daily_plans_v1';
  
  DailyPlanNotifier() : super(const DailyPlanState()) {
    _loadFromStorage();
  }

  /// 从本地存储加载数据
  Future<void> _loadFromStorage() async {
    try {
      state = state.copyWith(isLoading: true, error: null);
      
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_storageKey);
      
      if (jsonString != null) {
        final Map<String, dynamic> jsonData = json.decode(jsonString);
        final Map<String, DailyPlan> plans = {};
        
        jsonData.forEach((key, value) {
          try {
            plans[key] = DailyPlan.fromJson(value as Map<String, dynamic>);
          } catch (e) {
            print('❌ 解析计划数据失败: $key, $e');
          }
        });
        
        state = state.copyWith(
          plans: plans,
          isLoading: false,
          lastUpdated: DateTime.now(),
        );
        
        print('✅ 已加载 ${plans.length} 个每日计划');
      } else {
        state = state.copyWith(isLoading: false);
        print('📝 首次使用，初始化空的计划数据');
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: '加载数据失败: $e',
      );
      print('❌ 加载每日计划数据失败: $e');
    }
  }

  /// 保存数据到本地存储
  Future<void> _saveToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final Map<String, dynamic> jsonData = {};
      
      state.plans.forEach((key, plan) {
        jsonData[key] = plan.toJson();
      });
      
      await prefs.setString(_storageKey, json.encode(jsonData));
      print('💾 已保存 ${state.plans.length} 个每日计划到本地存储');
    } catch (e) {
      print('❌ 保存每日计划数据失败: $e');
      state = state.copyWith(error: '保存数据失败: $e');
    }
  }

  /// 创建或更新计划
  Future<void> savePlan({
    required DateTime date,
    required DailyPlanType type,
    required String content,
    int priority = 3,
  }) async {
    try {
      final normalizedDate = DateTime(date.year, date.month, date.day);
      final storageKey = '${_formatDate(normalizedDate)}_${type.name}';
      
      // 检查是否已存在
      final existingPlan = state.plans[storageKey];
      
      DailyPlan newPlan;
      if (existingPlan != null) {
        // 更新现有计划
        newPlan = existingPlan.copyWith(
          content: content.trim(),
          updatedAt: DateTime.now(),
          priority: priority,
        );
      } else {
        // 创建新计划
        if (type == DailyPlanType.planning) {
          newPlan = DailyPlanFactory.createPlanning(
            date: normalizedDate,
            content: content,
            priority: priority,
          );
        } else {
          newPlan = DailyPlanFactory.createOptimization(
            date: normalizedDate,
            content: content,
            priority: priority,
          );
        }
      }

      // 更新状态
      final updatedPlans = Map<String, DailyPlan>.from(state.plans);
      if (content.trim().isEmpty) {
        // 如果内容为空，删除该计划
        updatedPlans.remove(storageKey);
      } else {
        updatedPlans[storageKey] = newPlan;
      }

      state = state.copyWith(
        plans: updatedPlans,
        lastUpdated: DateTime.now(),
      );

      // 保存到本地存储
      await _saveToStorage();
      
      print('✅ 已保存${type == DailyPlanType.planning ? '计划' : '优化'}: ${_formatDate(normalizedDate)}');
    } catch (e) {
      state = state.copyWith(error: '保存失败: $e');
      print('❌ 保存计划失败: $e');
    }
  }

  /// 删除计划
  Future<void> deletePlan(DateTime date, DailyPlanType type) async {
    try {
      final normalizedDate = DateTime(date.year, date.month, date.day);
      final storageKey = '${_formatDate(normalizedDate)}_${type.name}';
      
      final updatedPlans = Map<String, DailyPlan>.from(state.plans);
      updatedPlans.remove(storageKey);
      
      state = state.copyWith(
        plans: updatedPlans,
        lastUpdated: DateTime.now(),
      );
      
      await _saveToStorage();
      print('🗑️ 已删除${type == DailyPlanType.planning ? '计划' : '优化'}: ${_formatDate(normalizedDate)}');
    } catch (e) {
      state = state.copyWith(error: '删除失败: $e');
      print('❌ 删除计划失败: $e');
    }
  }

  /// 获取指定日期的计划
  DailyPlan? getPlan(DateTime date, DailyPlanType type) {
    return state.getPlan(date, type);
  }

  /// 获取指定日期的所有计划
  List<DailyPlan> getPlansForDate(DateTime date) {
    return state.getPlansForDate(date);
  }

  /// 检查是否有内容
  bool hasContent(DateTime date, DailyPlanType type) {
    return state.hasContent(date, type);
  }

  /// 获取内容
  String getContent(DateTime date, DailyPlanType type) {
    final plan = getPlan(date, type);
    return plan?.content ?? '';
  }

  /// 刷新数据
  Future<void> refresh() async {
    await _loadFromStorage();
  }

  /// 清除错误状态
  void clearError() {
    state = state.copyWith(error: null);
  }

  /// 格式化日期
  static String _formatDate(DateTime date) {
    return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
  }
}

/// 每日计划Provider
final dailyPlanProvider = StateNotifierProvider<DailyPlanNotifier, DailyPlanState>(
  (ref) => DailyPlanNotifier(),
);

/// 指定日期和类型的计划Provider
final dailyPlanContentProvider = Provider.family<String, ({DateTime date, DailyPlanType type})>((ref, params) {
  final state = ref.watch(dailyPlanProvider);
  final plan = state.getPlan(params.date, params.type);
  return plan?.content ?? '';
});

/// 指定日期和类型是否有内容Provider
final dailyPlanHasContentProvider = Provider.family<bool, ({DateTime date, DailyPlanType type})>((ref, params) {
  final state = ref.watch(dailyPlanProvider);
  return state.hasContent(params.date, params.type);
});

/// 指定日期的所有计划Provider
final dailyPlansForDateProvider = Provider.family<List<DailyPlan>, DateTime>((ref, date) {
  final state = ref.watch(dailyPlanProvider);
  return state.getPlansForDate(date);
});
