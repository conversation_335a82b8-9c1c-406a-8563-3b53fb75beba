// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'daily_plan.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DailyPlan _$DailyPlanFromJson(Map<String, dynamic> json) =>
    $checkedCreate('DailyPlan', json, ($checkedConvert) {
      final val = DailyPlan(
        id: $checkedConvert('id', (v) => v as String),
        date: $checkedConvert('date', (v) => DateTime.parse(v as String)),
        content: $checkedConvert('content', (v) => v as String),
        type: $checkedConvert(
          'type',
          (v) => $enumDecode(_$DailyPlanTypeEnumMap, v),
        ),
        createdAt: $checkedConvert(
          'createdAt',
          (v) => DateTime.parse(v as String),
        ),
        updatedAt: $checkedConvert(
          'updatedAt',
          (v) => v == null ? null : DateTime.parse(v as String),
        ),
        isCompleted: $checkedConvert('isCompleted', (v) => v as bool? ?? false),
        priority: $checkedConvert('priority', (v) => (v as num?)?.toInt() ?? 3),
      );
      return val;
    });

// ignore: unused_element
abstract class _$DailyPlanPerFieldToJson {
  // ignore: unused_element
  static Object? id(String instance) => instance;
  // ignore: unused_element
  static Object? date(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? content(String instance) => instance;
  // ignore: unused_element
  static Object? type(DailyPlanType instance) =>
      _$DailyPlanTypeEnumMap[instance]!;
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? updatedAt(DateTime? instance) => instance?.toIso8601String();
  // ignore: unused_element
  static Object? isCompleted(bool instance) => instance;
  // ignore: unused_element
  static Object? priority(int instance) => instance;
}

Map<String, dynamic> _$DailyPlanToJson(DailyPlan instance) => <String, dynamic>{
  'id': instance.id,
  'date': instance.date.toIso8601String(),
  'content': instance.content,
  'type': _$DailyPlanTypeEnumMap[instance.type]!,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt?.toIso8601String(),
  'isCompleted': instance.isCompleted,
  'priority': instance.priority,
};

const _$DailyPlanTypeEnumMap = {
  DailyPlanType.planning: 'planning',
  DailyPlanType.optimization: 'optimization',
};
