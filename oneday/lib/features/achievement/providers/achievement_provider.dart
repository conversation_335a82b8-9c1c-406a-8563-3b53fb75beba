import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter/foundation.dart';
import '../services/achievement_service.dart';
import '../services/achievement_trigger_service.dart';
import '../models/achievement.dart';
import '../models/user_level.dart';
import '../models/badge.dart';

/// 成就系统状态
class AchievementState {
  final UserLevel? userLevel;
  final Map<String, UserAchievementProgress> achievementProgress;
  final List<UserBadge> userBadges;
  final Map<SkillType, SkillLevel> skillLevels;
  final Map<String, dynamic> userStats;
  final bool isLoading;
  final String? error;

  const AchievementState({
    this.userLevel,
    this.achievementProgress = const {},
    this.userBadges = const [],
    this.skillLevels = const {},
    this.userStats = const {},
    this.isLoading = false,
    this.error,
  });

  AchievementState copyWith({
    UserLevel? userLevel,
    Map<String, UserAchievementProgress>? achievementProgress,
    List<UserBadge>? userBadges,
    Map<SkillType, SkillLevel>? skillLevels,
    Map<String, dynamic>? userStats,
    bool? isLoading,
    String? error,
  }) {
    return AchievementState(
      userLevel: userLevel ?? this.userLevel,
      achievementProgress: achievementProgress ?? this.achievementProgress,
      userBadges: userBadges ?? this.userBadges,
      skillLevels: skillLevels ?? this.skillLevels,
      userStats: userStats ?? this.userStats,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// 成就系统状态管理器
class AchievementNotifier extends StateNotifier<AchievementState> {
  final AchievementService _achievementService;
  final AchievementTriggerService _triggerService;

  /// 成就解锁通知回调
  Function(Achievement)? onAchievementUnlocked;

  AchievementNotifier(this._achievementService, this._triggerService)
      : super(const AchievementState()) {
    _init();
  }

  /// 初始化
  Future<void> _init() async {
    // 设置成就解锁回调
    _achievementService.setOnAchievementUnlocked((achievement) {
      onAchievementUnlocked?.call(achievement);
    });

    await loadData();
  }

  /// 加载数据
  Future<void> loadData() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final results = await Future.wait([
        _achievementService.getUserLevel(),
        _achievementService.getUserAchievementProgress(),
        _achievementService.getUserBadges(),
        _achievementService.getSkillLevels(),
        _achievementService.getUserStats(),
      ]);
      
      state = state.copyWith(
        userLevel: results[0] as UserLevel,
        achievementProgress: results[1] as Map<String, UserAchievementProgress>,
        userBadges: results[2] as List<UserBadge>,
        skillLevels: results[3] as Map<SkillType, SkillLevel>,
        userStats: results[4] as Map<String, dynamic>,
        isLoading: false,
      );
    } catch (e) {
      debugPrint('加载成就数据失败: $e');
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  /// 添加经验值
  Future<void> addExperience(int exp, {String? source}) async {
    try {
      final newLevel = await _achievementService.addExperience(exp, source: source);
      final newStats = await _achievementService.getUserStats();
      
      state = state.copyWith(
        userLevel: newLevel,
        userStats: newStats,
      );
    } catch (e) {
      debugPrint('添加经验值失败: $e');
    }
  }

  /// 添加技能经验
  Future<void> addSkillExperience(SkillType skillType, int exp) async {
    try {
      final newSkill = await _achievementService.addSkillExperience(skillType, exp);
      final updatedSkills = Map<SkillType, SkillLevel>.from(state.skillLevels);
      updatedSkills[skillType] = newSkill;
      
      state = state.copyWith(skillLevels: updatedSkills);
    } catch (e) {
      debugPrint('添加技能经验失败: $e');
    }
  }

  /// 解锁成就
  Future<bool> unlockAchievement(String achievementId) async {
    try {
      final unlocked = await _achievementService.unlockAchievement(achievementId);
      if (unlocked) {
        // 重新加载数据以更新UI
        await loadData();
      }
      return unlocked;
    } catch (e) {
      debugPrint('解锁成就失败: $e');
      return false;
    }
  }

  /// 更新成就进度
  Future<bool> updateAchievementProgress(String achievementId, int progress) async {
    try {
      final unlocked = await _achievementService.updateAchievementProgress(achievementId, progress);
      if (unlocked) {
        // 重新加载数据以更新UI
        await loadData();
      }
      return unlocked;
    } catch (e) {
      debugPrint('更新成就进度失败: $e');
      return false;
    }
  }

  // 学习相关触发器方法
  
  /// 开始学习会话
  Future<void> onStudySessionStart() async {
    await _triggerService.onStudySessionStart();
  }

  /// 结束学习会话
  Future<void> onStudySessionEnd(int studyMinutes) async {
    await _triggerService.onStudySessionEnd(studyMinutes);
    await loadData(); // 重新加载数据
  }

  /// 每日打卡
  Future<void> onDailyCheckIn() async {
    await _triggerService.onDailyCheckIn();
    await loadData();
  }

  /// 工资收入
  Future<void> onWageEarned(double amount) async {
    await _triggerService.onWageEarned(amount);
    await loadData();
  }

  /// 创建记忆宫殿
  Future<void> onMemoryPalaceCreated() async {
    await _triggerService.onMemoryPalaceCreated();
    await loadData();
  }

  /// 添加记忆锚点
  Future<void> onMemoryAnchorAdded(String palaceId) async {
    await _triggerService.onMemoryAnchorAdded(palaceId);
    await loadData();
  }

  /// 使用AR功能
  Future<void> onARFeatureUsed() async {
    await _triggerService.onARFeatureUsed();
    await loadData();
  }

  /// 完成PAO动作
  Future<void> onPAOActionCompleted(String letter) async {
    await _triggerService.onPAOActionCompleted(letter);
    await loadData();
  }

  /// 完成眼保健操
  Future<void> onEyeExerciseCompleted() async {
    await _triggerService.onEyeExerciseCompleted();
    await loadData();
  }

  /// 添加好友
  Future<void> onFriendAdded() async {
    await _triggerService.onFriendAdded();
    await loadData();
  }

  /// 发起挑战
  Future<void> onChallengeInitiated() async {
    await _triggerService.onChallengeInitiated();
    await loadData();
  }

  /// 写反思日志
  Future<void> onReflectionWritten(int wordCount) async {
    await _triggerService.onReflectionWritten(wordCount);
    await loadData();
  }

  /// 学习单词
  Future<void> onWordLearned() async {
    await _triggerService.onWordLearned();
    await loadData();
  }

  /// 注册当天学习
  Future<void> onFirstDayStudy(int studyMinutes) async {
    await _triggerService.onFirstDayStudy(studyMinutes);
    await loadData();
  }

  /// 使用所有功能模块
  Future<void> onAllFeaturesUsed() async {
    await _triggerService.onAllFeaturesUsed();
    await loadData();
  }

  /// 重置每日数据
  void resetDailyData() {
    _triggerService.resetDailyData();
  }

  /// 重置所有数据 (仅用于测试)
  Future<void> resetAllData() async {
    await _achievementService.resetAllData();
    await loadData();
  }
}

/// 成就服务提供者
final achievementServiceProvider = Provider<AchievementService>((ref) {
  return AchievementService();
});

/// 成就触发器服务提供者
final achievementTriggerServiceProvider = Provider<AchievementTriggerService>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  return AchievementTriggerService(achievementService);
});

/// 成就系统状态提供者
final achievementProvider = StateNotifierProvider<AchievementNotifier, AchievementState>((ref) {
  final achievementService = ref.watch(achievementServiceProvider);
  final triggerService = ref.watch(achievementTriggerServiceProvider);
  return AchievementNotifier(achievementService, triggerService);
});

/// 用户等级提供者
final userLevelProvider = Provider<UserLevel?>((ref) {
  return ref.watch(achievementProvider).userLevel;
});

/// 用户统计提供者
final userStatsProvider = Provider<Map<String, dynamic>>((ref) {
  return ref.watch(achievementProvider).userStats;
});

/// 技能等级提供者
final skillLevelsProvider = Provider<Map<SkillType, SkillLevel>>((ref) {
  return ref.watch(achievementProvider).skillLevels;
});

/// 成就进度提供者
final achievementProgressProvider = Provider<Map<String, UserAchievementProgress>>((ref) {
  return ref.watch(achievementProvider).achievementProgress;
});

/// 用户徽章提供者
final userBadgesProvider = Provider<List<UserBadge>>((ref) {
  return ref.watch(achievementProvider).userBadges;
});
