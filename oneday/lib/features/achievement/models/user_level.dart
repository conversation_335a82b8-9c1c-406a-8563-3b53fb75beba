import 'package:json_annotation/json_annotation.dart';

part 'user_level.g.dart';

/// 学者段位枚举
enum ScholarRank {
  @JsonValue('bronze')
  bronze, // 青铜学者 (1-10级)
  @JsonValue('silver')
  silver, // 白银学者 (11-20级)
  @JsonValue('gold')
  gold, // 黄金学者 (21-30级)
  @JsonValue('platinum')
  platinum, // 铂金学者 (31-40级)
  @JsonValue('diamond')
  diamond, // 钻石学者 (41-50级)
  @JsonValue('master')
  master, // 大师学者 (51-70级)
  @JsonValue('grandmaster')
  grandmaster, // 宗师学者 (71-90级)
  @JsonValue('king')
  king, // 王者学者 (91-100级)
}

/// 专业技能类型
enum SkillType {
  @JsonValue('memory')
  memory, // 记忆大师
  @JsonValue('timeManagement')
  timeManagement, // 时间管理专家
  @JsonValue('vocabulary')
  vocabulary, // 词汇达人
  @JsonValue('fitness')
  fitness, // 运动健将
  @JsonValue('reflection')
  reflection, // 反思导师
}

/// 用户等级模型
@JsonSerializable()
class UserLevel {
  /// 用户ID
  final String userId;

  /// 当前等级
  final int level;

  /// 当前经验值
  final int currentExp;

  /// 升级所需经验值
  final int requiredExp;

  /// 学者段位
  final ScholarRank rank;

  /// 段位内等级 (1-10)
  final int rankLevel;

  /// 总经验值
  final int totalExp;

  /// 创建时间
  final DateTime createdAt;

  /// 最后更新时间
  final DateTime updatedAt;

  const UserLevel({
    required this.userId,
    required this.level,
    required this.currentExp,
    required this.requiredExp,
    required this.rank,
    required this.rankLevel,
    required this.totalExp,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UserLevel.fromJson(Map<String, dynamic> json) =>
      _$UserLevelFromJson(json);
  Map<String, dynamic> toJson() => _$UserLevelToJson(this);

  /// 创建初始等级
  factory UserLevel.initial(String userId) {
    final now = DateTime.now();
    return UserLevel(
      userId: userId,
      level: 1,
      currentExp: 0,
      requiredExp: 100,
      rank: ScholarRank.bronze,
      rankLevel: 1,
      totalExp: 0,
      createdAt: now,
      updatedAt: now,
    );
  }

  /// 添加经验值
  UserLevel addExperience(int exp) {
    final newTotalExp = totalExp + exp;

    // 计算新等级
    final levelData = _calculateLevelFromExp(newTotalExp);

    return UserLevel(
      userId: userId,
      level: levelData['level'],
      currentExp: levelData['currentExp'],
      requiredExp: levelData['requiredExp'],
      rank: levelData['rank'],
      rankLevel: levelData['rankLevel'],
      totalExp: newTotalExp,
      createdAt: createdAt,
      updatedAt: DateTime.now(),
    );
  }

  /// 根据总经验值计算等级信息
  static Map<String, dynamic> _calculateLevelFromExp(int totalExp) {
    int level = 1;
    int expForCurrentLevel = 0;

    // 计算当前等级
    while (expForCurrentLevel <= totalExp) {
      final expForNextLevel = _getExpRequiredForLevel(level + 1);
      if (totalExp < expForNextLevel) break;
      level++;
      expForCurrentLevel = _getExpRequiredForLevel(level);
    }

    final currentLevelExp = _getExpRequiredForLevel(level);
    final nextLevelExp = _getExpRequiredForLevel(level + 1);
    final currentExp = totalExp - currentLevelExp;
    final requiredExp = nextLevelExp - currentLevelExp;

    // 计算段位和段位内等级
    final rank = _getRankFromLevel(level);
    final rankLevel = _getRankLevelFromLevel(level);

    return {
      'level': level,
      'currentExp': currentExp,
      'requiredExp': requiredExp,
      'rank': rank,
      'rankLevel': rankLevel,
    };
  }

  /// 获取指定等级所需的总经验值
  static int _getExpRequiredForLevel(int level) {
    if (level <= 1) return 0;

    // 经验值增长公式：基础经验 * 等级系数
    // 1-10级：100 * level
    // 11-20级：200 * (level - 10) + 1000
    // 21-30级：300 * (level - 20) + 3000
    // 以此类推...

    int totalExp = 0;
    for (int i = 2; i <= level; i++) {
      if (i <= 10) {
        totalExp += 100 * i;
      } else if (i <= 20) {
        totalExp += 200 * (i - 10) + 1000;
      } else if (i <= 30) {
        totalExp += 300 * (i - 20) + 3000;
      } else if (i <= 40) {
        totalExp += 400 * (i - 30) + 6000;
      } else if (i <= 50) {
        totalExp += 500 * (i - 40) + 10000;
      } else if (i <= 70) {
        totalExp += 600 * (i - 50) + 15000;
      } else if (i <= 90) {
        totalExp += 700 * (i - 70) + 27000;
      } else {
        totalExp += 800 * (i - 90) + 41000;
      }
    }
    return totalExp;
  }

  /// 根据等级获取段位
  static ScholarRank _getRankFromLevel(int level) {
    if (level <= 10) return ScholarRank.bronze;
    if (level <= 20) return ScholarRank.silver;
    if (level <= 30) return ScholarRank.gold;
    if (level <= 40) return ScholarRank.platinum;
    if (level <= 50) return ScholarRank.diamond;
    if (level <= 70) return ScholarRank.master;
    if (level <= 90) return ScholarRank.grandmaster;
    return ScholarRank.king;
  }

  /// 根据等级获取段位内等级
  static int _getRankLevelFromLevel(int level) {
    if (level <= 10) return level;
    if (level <= 20) return level - 10;
    if (level <= 30) return level - 20;
    if (level <= 40) return level - 30;
    if (level <= 50) return level - 40;
    if (level <= 70) return level - 50;
    if (level <= 90) return level - 70;
    return level - 90;
  }

  /// 获取段位名称
  String get rankName {
    switch (rank) {
      case ScholarRank.bronze:
        return '青铜学者';
      case ScholarRank.silver:
        return '白银学者';
      case ScholarRank.gold:
        return '黄金学者';
      case ScholarRank.platinum:
        return '铂金学者';
      case ScholarRank.diamond:
        return '钻石学者';
      case ScholarRank.master:
        return '大师学者';
      case ScholarRank.grandmaster:
        return '宗师学者';
      case ScholarRank.king:
        return '王者学者';
    }
  }

  /// 获取段位颜色
  String get rankColor {
    switch (rank) {
      case ScholarRank.bronze:
        return '#CD7F32';
      case ScholarRank.silver:
        return '#C0C0C0';
      case ScholarRank.gold:
        return '#FFD700';
      case ScholarRank.platinum:
        return '#E5E4E2';
      case ScholarRank.diamond:
        return '#B9F2FF';
      case ScholarRank.master:
        return '#7C3AED';
      case ScholarRank.grandmaster:
        return '#E03E3E';
      case ScholarRank.king:
        return '#FF6B35';
    }
  }

  /// 获取完整段位显示
  String get fullRankDisplay => '$rankName $rankLevel';

  /// 获取经验值进度百分比
  double get expProgressPercentage => currentExp / requiredExp;

  /// 是否可以升级
  bool get canLevelUp => currentExp >= requiredExp;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserLevel &&
          runtimeType == other.runtimeType &&
          userId == other.userId;

  @override
  int get hashCode => userId.hashCode;

  @override
  String toString() =>
      'UserLevel(userId: $userId, level: $level, rank: $rankName)';
}

/// 专业技能等级
@JsonSerializable()
class SkillLevel {
  /// 技能类型
  final SkillType skillType;

  /// 技能等级
  final int level;

  /// 当前经验值
  final int currentExp;

  /// 升级所需经验值
  final int requiredExp;

  /// 总经验值
  final int totalExp;

  /// 最后更新时间
  final DateTime updatedAt;

  const SkillLevel({
    required this.skillType,
    required this.level,
    required this.currentExp,
    required this.requiredExp,
    required this.totalExp,
    required this.updatedAt,
  });

  factory SkillLevel.fromJson(Map<String, dynamic> json) =>
      _$SkillLevelFromJson(json);
  Map<String, dynamic> toJson() => _$SkillLevelToJson(this);

  /// 创建初始技能等级
  factory SkillLevel.initial(SkillType skillType) {
    return SkillLevel(
      skillType: skillType,
      level: 1,
      currentExp: 0,
      requiredExp: 50,
      totalExp: 0,
      updatedAt: DateTime.now(),
    );
  }

  /// 获取技能名称
  String get skillName {
    switch (skillType) {
      case SkillType.memory:
        return '记忆大师';
      case SkillType.timeManagement:
        return '时间管理专家';
      case SkillType.vocabulary:
        return '词汇达人';
      case SkillType.fitness:
        return '运动健将';
      case SkillType.reflection:
        return '反思导师';
    }
  }

  /// 获取技能图标
  String get skillIcon {
    switch (skillType) {
      case SkillType.memory:
        return '🧠';
      case SkillType.timeManagement:
        return '⏰';
      case SkillType.vocabulary:
        return '📚';
      case SkillType.fitness:
        return '💪';
      case SkillType.reflection:
        return '🤔';
    }
  }
}
