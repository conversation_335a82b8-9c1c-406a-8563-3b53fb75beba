// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'badge.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Badge _$BadgeFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('Badge', json, ($checkedConvert) {
  final val = Badge(
    id: $checkedConvert('id', (v) => v as String),
    name: $checkedConvert('name', (v) => v as String),
    description: $checkedConvert('description', (v) => v as String),
    icon: $checkedConvert('icon', (v) => v as String),
    type: $checkedConvert('type', (v) => $enumDecode(_$BadgeTypeEnumMap, v)),
    rarity: $checkedConvert(
      'rarity',
      (v) => $enumDecode(_$AchievementRarityEnumMap, v),
    ),
    obtainCondition: $checkedConvert('obtainCondition', (v) => v as String),
    isLimited: $checkedConvert('isLimited', (v) => v as bool? ?? false),
    limitedUntil: $checkedConvert(
      'limitedUntil',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    createdAt: $checkedConvert('createdAt', (v) => DateTime.parse(v as String)),
  );
  return val;
});

// ignore: unused_element
abstract class _$BadgePerFieldToJson {
  // ignore: unused_element
  static Object? id(String instance) => instance;
  // ignore: unused_element
  static Object? name(String instance) => instance;
  // ignore: unused_element
  static Object? description(String instance) => instance;
  // ignore: unused_element
  static Object? icon(String instance) => instance;
  // ignore: unused_element
  static Object? type(BadgeType instance) => _$BadgeTypeEnumMap[instance]!;
  // ignore: unused_element
  static Object? rarity(AchievementRarity instance) =>
      _$AchievementRarityEnumMap[instance]!;
  // ignore: unused_element
  static Object? obtainCondition(String instance) => instance;
  // ignore: unused_element
  static Object? isLimited(bool instance) => instance;
  // ignore: unused_element
  static Object? limitedUntil(DateTime? instance) =>
      instance?.toIso8601String();
  // ignore: unused_element
  static Object? createdAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$BadgeToJson(Badge instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'icon': instance.icon,
  'type': _$BadgeTypeEnumMap[instance.type]!,
  'rarity': _$AchievementRarityEnumMap[instance.rarity]!,
  'obtainCondition': instance.obtainCondition,
  'isLimited': instance.isLimited,
  'limitedUntil': instance.limitedUntil?.toIso8601String(),
  'createdAt': instance.createdAt.toIso8601String(),
};

const _$BadgeTypeEnumMap = {
  BadgeType.achievement: 'achievement',
  BadgeType.rank: 'rank',
  BadgeType.seasonal: 'seasonal',
  BadgeType.special: 'special',
  BadgeType.event: 'event',
};

const _$AchievementRarityEnumMap = {
  AchievementRarity.common: 'common',
  AchievementRarity.rare: 'rare',
  AchievementRarity.epic: 'epic',
  AchievementRarity.legendary: 'legendary',
  AchievementRarity.mythic: 'mythic',
};

UserBadge _$UserBadgeFromJson(Map<String, dynamic> json) =>
    $checkedCreate('UserBadge', json, ($checkedConvert) {
      final val = UserBadge(
        badgeId: $checkedConvert('badgeId', (v) => v as String),
        obtainedAt: $checkedConvert(
          'obtainedAt',
          (v) => DateTime.parse(v as String),
        ),
        isDisplayed: $checkedConvert('isDisplayed', (v) => v as bool? ?? false),
        displayOrder: $checkedConvert(
          'displayOrder',
          (v) => (v as num?)?.toInt() ?? 0,
        ),
      );
      return val;
    });

// ignore: unused_element
abstract class _$UserBadgePerFieldToJson {
  // ignore: unused_element
  static Object? badgeId(String instance) => instance;
  // ignore: unused_element
  static Object? obtainedAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? isDisplayed(bool instance) => instance;
  // ignore: unused_element
  static Object? displayOrder(int instance) => instance;
}

Map<String, dynamic> _$UserBadgeToJson(UserBadge instance) => <String, dynamic>{
  'badgeId': instance.badgeId,
  'obtainedAt': instance.obtainedAt.toIso8601String(),
  'isDisplayed': instance.isDisplayed,
  'displayOrder': instance.displayOrder,
};

Challenge _$ChallengeFromJson(Map<String, dynamic> json) =>
    $checkedCreate('Challenge', json, ($checkedConvert) {
      final val = Challenge(
        id: $checkedConvert('id', (v) => v as String),
        name: $checkedConvert('name', (v) => v as String),
        description: $checkedConvert('description', (v) => v as String),
        icon: $checkedConvert('icon', (v) => v as String),
        type: $checkedConvert('type', (v) => v as String),
        targetValue: $checkedConvert('targetValue', (v) => (v as num).toInt()),
        experienceReward: $checkedConvert(
          'experienceReward',
          (v) => (v as num).toInt(),
        ),
        wageReward: $checkedConvert('wageReward', (v) => (v as num).toDouble()),
        rewardBadgeId: $checkedConvert('rewardBadgeId', (v) => v as String?),
        startTime: $checkedConvert(
          'startTime',
          (v) => DateTime.parse(v as String),
        ),
        endTime: $checkedConvert('endTime', (v) => DateTime.parse(v as String)),
        isActive: $checkedConvert('isActive', (v) => v as bool? ?? true),
      );
      return val;
    });

// ignore: unused_element
abstract class _$ChallengePerFieldToJson {
  // ignore: unused_element
  static Object? id(String instance) => instance;
  // ignore: unused_element
  static Object? name(String instance) => instance;
  // ignore: unused_element
  static Object? description(String instance) => instance;
  // ignore: unused_element
  static Object? icon(String instance) => instance;
  // ignore: unused_element
  static Object? type(String instance) => instance;
  // ignore: unused_element
  static Object? targetValue(int instance) => instance;
  // ignore: unused_element
  static Object? experienceReward(int instance) => instance;
  // ignore: unused_element
  static Object? wageReward(double instance) => instance;
  // ignore: unused_element
  static Object? rewardBadgeId(String? instance) => instance;
  // ignore: unused_element
  static Object? startTime(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? endTime(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? isActive(bool instance) => instance;
}

Map<String, dynamic> _$ChallengeToJson(Challenge instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'icon': instance.icon,
  'type': instance.type,
  'targetValue': instance.targetValue,
  'experienceReward': instance.experienceReward,
  'wageReward': instance.wageReward,
  'rewardBadgeId': instance.rewardBadgeId,
  'startTime': instance.startTime.toIso8601String(),
  'endTime': instance.endTime.toIso8601String(),
  'isActive': instance.isActive,
};

UserChallengeProgress _$UserChallengeProgressFromJson(
  Map<String, dynamic> json,
) => $checkedCreate('UserChallengeProgress', json, ($checkedConvert) {
  final val = UserChallengeProgress(
    challengeId: $checkedConvert('challengeId', (v) => v as String),
    currentProgress: $checkedConvert(
      'currentProgress',
      (v) => (v as num).toInt(),
    ),
    isCompleted: $checkedConvert('isCompleted', (v) => v as bool),
    completedAt: $checkedConvert(
      'completedAt',
      (v) => v == null ? null : DateTime.parse(v as String),
    ),
    startedAt: $checkedConvert('startedAt', (v) => DateTime.parse(v as String)),
    lastUpdatedAt: $checkedConvert(
      'lastUpdatedAt',
      (v) => DateTime.parse(v as String),
    ),
  );
  return val;
});

// ignore: unused_element
abstract class _$UserChallengeProgressPerFieldToJson {
  // ignore: unused_element
  static Object? challengeId(String instance) => instance;
  // ignore: unused_element
  static Object? currentProgress(int instance) => instance;
  // ignore: unused_element
  static Object? isCompleted(bool instance) => instance;
  // ignore: unused_element
  static Object? completedAt(DateTime? instance) => instance?.toIso8601String();
  // ignore: unused_element
  static Object? startedAt(DateTime instance) => instance.toIso8601String();
  // ignore: unused_element
  static Object? lastUpdatedAt(DateTime instance) => instance.toIso8601String();
}

Map<String, dynamic> _$UserChallengeProgressToJson(
  UserChallengeProgress instance,
) => <String, dynamic>{
  'challengeId': instance.challengeId,
  'currentProgress': instance.currentProgress,
  'isCompleted': instance.isCompleted,
  'completedAt': instance.completedAt?.toIso8601String(),
  'startedAt': instance.startedAt.toIso8601String(),
  'lastUpdatedAt': instance.lastUpdatedAt.toIso8601String(),
};
