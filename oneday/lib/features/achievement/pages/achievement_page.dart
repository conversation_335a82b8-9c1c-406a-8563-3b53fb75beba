import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../services/achievement_service.dart';
import '../models/achievement.dart';
import '../models/user_level.dart';
import '../models/badge.dart';
import '../widgets/user_level_card.dart';
import '../widgets/achievement_grid.dart';
import '../widgets/skill_levels_card.dart';
import '../providers/achievement_provider.dart';

/// 成就系统主页面
class AchievementPage extends ConsumerStatefulWidget {
  const AchievementPage({super.key});

  @override
  ConsumerState<AchievementPage> createState() => _AchievementPageState();
}

class _AchievementPageState extends ConsumerState<AchievementPage>
    with TickerProviderStateMixin {
  final AchievementService _achievementService = AchievementService();

  late TabController _tabController;

  UserLevel? _userLevel;
  Map<String, UserAchievementProgress> _achievementProgress = {};
  List<UserBadge> _userBadges = [];
  Map<SkillType, SkillLevel> _skillLevels = {};
  Map<String, dynamic> _userStats = {};

  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// 加载数据
  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      final results = await Future.wait([
        _achievementService.getUserLevel(),
        _achievementService.getUserAchievementProgress(),
        _achievementService.getUserBadges(),
        _achievementService.getSkillLevels(),
        _achievementService.getUserStats(),
      ]);

      setState(() {
        _userLevel = results[0] as UserLevel;
        _achievementProgress =
            results[1] as Map<String, UserAchievementProgress>;
        _userBadges = results[2] as List<UserBadge>;
        _skillLevels = results[3] as Map<SkillType, SkillLevel>;
        _userStats = results[4] as Map<String, dynamic>;
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('加载成就数据失败: $e');
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: _isLoading ? _buildLoadingView() : _buildContent(),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        '成就系统',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Color(0xFF37352F),
          size: 20,
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => context.push('/leaderboard'),
          icon: const Icon(
            Icons.leaderboard_outlined,
            color: Color(0xFF37352F),
          ),
        ),
        IconButton(
          onPressed: _showStatsDialog,
          icon: const Icon(Icons.analytics_outlined, color: Color(0xFF37352F)),
        ),
        PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, color: Color(0xFF37352F)),
          onSelected: (value) => _handleMenuAction(value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'test_achievement',
              child: Text('🧪 测试成就解锁'),
            ),
            const PopupMenuItem(value: 'add_exp', child: Text('⭐ 添加经验值')),
            const PopupMenuItem(value: 'reset_data', child: Text('🔄 重置数据')),
          ],
        ),
      ],
      bottom: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF2E7EED),
        unselectedLabelColor: const Color(0xFF9B9A97),
        indicatorColor: const Color(0xFF2E7EED),
        tabs: const [
          Tab(text: '等级'),
          Tab(text: '成就'),
          Tab(text: '徽章'),
          Tab(text: '技能'),
        ],
      ),
    );
  }

  /// 构建加载视图
  Widget _buildLoadingView() {
    return const Center(
      child: CircularProgressIndicator(color: Color(0xFF2E7EED)),
    );
  }

  /// 构建主要内容
  Widget _buildContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildLevelTab(),
        _buildAchievementTab(),
        _buildBadgeTab(),
        _buildSkillTab(),
      ],
    );
  }

  /// 等级标签页
  Widget _buildLevelTab() {
    if (_userLevel == null) return const SizedBox();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          UserLevelCard(userLevel: _userLevel!),
          const SizedBox(height: 24),
          _buildLevelProgress(),
          const SizedBox(height: 24),
          _buildRankInfo(),
        ],
      ),
    );
  }

  /// 等级进度卡片
  Widget _buildLevelProgress() {
    if (_userLevel == null) return const SizedBox();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '升级进度',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          // 经验值进度条
          Row(
            children: [
              Text(
                '${_userLevel!.currentExp}',
                style: const TextStyle(
                  color: Color(0xFF2E7EED),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 12),
                  height: 8,
                  decoration: BoxDecoration(
                    color: const Color(0xFF2E7EED).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: FractionallySizedBox(
                    alignment: Alignment.centerLeft,
                    widthFactor: _userLevel!.expProgressPercentage,
                    child: Container(
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7EED),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ),
                  ),
                ),
              ),
              Text(
                '${_userLevel!.requiredExp}',
                style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
              ),
            ],
          ),

          const SizedBox(height: 8),
          Text(
            '距离下一级还需 ${_userLevel!.requiredExp - _userLevel!.currentExp} 经验值',
            style: const TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
          ),
        ],
      ),
    );
  }

  /// 段位信息
  Widget _buildRankInfo() {
    if (_userLevel == null) return const SizedBox();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '段位信息',
            style: TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Color(
                    int.parse(_userLevel!.rankColor.substring(1), radix: 16) +
                        0xFF000000,
                  ).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: Center(
                  child: Text(
                    _userLevel!.rankLevel.toString(),
                    style: TextStyle(
                      color: Color(
                        int.parse(
                              _userLevel!.rankColor.substring(1),
                              radix: 16,
                            ) +
                            0xFF000000,
                      ),
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _userLevel!.fullRankDisplay,
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '总经验值: ${_userLevel!.totalExp}',
                      style: const TextStyle(
                        color: Color(0xFF9B9A97),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 成就标签页
  Widget _buildAchievementTab() {
    return AchievementGrid(
      achievements: _achievementService.getAllAchievements(),
      userProgress: _achievementProgress,
      onRefresh: _loadData,
    );
  }

  /// 徽章标签页
  Widget _buildBadgeTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '已获得徽章 (${_userBadges.length})',
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          if (_userBadges.isEmpty) _buildEmptyBadges() else _buildBadgeGrid(),
        ],
      ),
    );
  }

  /// 空徽章状态
  Widget _buildEmptyBadges() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: const Column(
        children: [
          Icon(Icons.emoji_events_outlined, size: 48, color: Color(0xFF9B9A97)),
          SizedBox(height: 16),
          Text(
            '还没有获得徽章',
            style: TextStyle(color: Color(0xFF9B9A97), fontSize: 16),
          ),
          SizedBox(height: 8),
          Text(
            '完成成就来解锁徽章吧！',
            style: TextStyle(color: Color(0xFF9B9A97), fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// 徽章网格
  Widget _buildBadgeGrid() {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
        childAspectRatio: 1,
      ),
      itemCount: _userBadges.length,
      itemBuilder: (context, index) {
        final userBadge = _userBadges[index];
        return _buildBadgeItem(userBadge);
      },
    );
  }

  /// 徽章项目
  Widget _buildBadgeItem(UserBadge userBadge) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFF37352F).withValues(alpha: 0.1)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Text('🏆', style: TextStyle(fontSize: 32)),
          const SizedBox(height: 8),
          Text(
            userBadge.badgeId,
            style: const TextStyle(
              color: Color(0xFF37352F),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// 技能标签页
  Widget _buildSkillTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: SkillLevelsCard(skillLevels: _skillLevels),
    );
  }

  /// 显示统计对话框
  void _showStatsDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('📊 成就统计'),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('🎯 当前等级：${_userStats['level'] ?? 0}'),
              Text('🏆 当前段位：${_userStats['rank'] ?? '未知'}'),
              Text('⭐ 总经验值：${_userStats['totalExp'] ?? 0}'),
              Text(
                '🎖️ 已解锁成就：${_userStats['unlockedAchievements'] ?? 0}/${_userStats['totalAchievements'] ?? 0}',
              ),
              Text('📈 成就完成率：${_userStats['achievementRate'] ?? 0}%'),
              Text('🏅 拥有徽章：${_userStats['badgeCount'] ?? 0}个'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 处理菜单操作
  void _handleMenuAction(String action) async {
    switch (action) {
      case 'test_achievement':
        // 测试解锁一个成就
        final achievements = _achievementService.getAllAchievements();
        if (achievements.isNotEmpty) {
          final testAchievement = achievements.first;
          await ref
              .read(achievementProvider.notifier)
              .unlockAchievement(testAchievement.id);
        }
        break;

      case 'add_exp':
        // 添加经验值
        await ref
            .read(achievementProvider.notifier)
            .addExperience(100, source: '测试');
        break;

      case 'reset_data':
        // 重置数据
        final confirmed = await _showResetConfirmDialog();
        if (confirmed) {
          await ref.read(achievementProvider.notifier).resetAllData();
        }
        break;
    }
  }

  /// 显示重置确认对话框
  Future<bool> _showResetConfirmDialog() async {
    return await showDialog<bool>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('⚠️ 确认重置'),
            content: const Text('这将清除所有成就数据，此操作不可撤销。确定要继续吗？'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('取消'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('确定'),
              ),
            ],
          ),
        ) ??
        false;
  }
}
