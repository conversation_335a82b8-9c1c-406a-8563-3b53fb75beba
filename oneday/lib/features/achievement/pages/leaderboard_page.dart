import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../models/user_level.dart';

/// 排行榜页面
class LeaderboardPage extends ConsumerStatefulWidget {
  const LeaderboardPage({super.key});

  @override
  ConsumerState<LeaderboardPage> createState() => _LeaderboardPageState();
}

class _LeaderboardPageState extends ConsumerState<LeaderboardPage>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildLevelRanking(),
          _buildWeeklyRanking(),
          _buildAchievementRanking(),
        ],
      ),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 0,
      title: const Text(
        '排行榜',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 18,
          fontWeight: FontWeight.bold,
        ),
      ),
      centerTitle: true,
      leading: IconButton(
        onPressed: () => Navigator.of(context).pop(),
        icon: const Icon(
          Icons.arrow_back_ios,
          color: Color(0xFF37352F),
          size: 20,
        ),
      ),
      bottom: TabBar(
        controller: _tabController,
        labelColor: const Color(0xFF2E7EED),
        unselectedLabelColor: const Color(0xFF9B9A97),
        indicatorColor: const Color(0xFF2E7EED),
        tabs: const [
          Tab(text: '等级榜'),
          Tab(text: '周榜'),
          Tab(text: '成就榜'),
        ],
      ),
    );
  }

  /// 等级排行榜
  Widget _buildLevelRanking() {
    return _buildRankingList([
      _createMockUser('学霸小王', 45, ScholarRank.diamond, 15420),
      _createMockUser('努力的小李', 38, ScholarRank.platinum, 12800),
      _createMockUser('勤奋小张', 32, ScholarRank.gold, 9650),
      _createMockUser('坚持小陈', 28, ScholarRank.gold, 8200),
      _createMockUser('你', 25, ScholarRank.silver, 6800),
      _createMockUser('学习小刘', 22, ScholarRank.silver, 5900),
      _createMockUser('进步小赵', 18, ScholarRank.silver, 4200),
      _createMockUser('加油小孙', 15, ScholarRank.bronze, 3100),
    ]);
  }

  /// 周榜
  Widget _buildWeeklyRanking() {
    return _buildRankingList([
      _createMockUser('本周之星', 25, ScholarRank.silver, 6800, weeklyHours: 42),
      _createMockUser('学习达人', 32, ScholarRank.gold, 9650, weeklyHours: 38),
      _createMockUser('时间管理师', 28, ScholarRank.gold, 8200, weeklyHours: 35),
      _createMockUser('专注王者', 22, ScholarRank.silver, 5900, weeklyHours: 32),
      _createMockUser('坚持不懈', 18, ScholarRank.silver, 4200, weeklyHours: 28),
    ], isWeekly: true);
  }

  /// 成就排行榜
  Widget _buildAchievementRanking() {
    return _buildRankingList([
      _createMockUser(
        '成就收集家',
        45,
        ScholarRank.diamond,
        15420,
        achievements: 28,
      ),
      _createMockUser(
        '徽章大师',
        38,
        ScholarRank.platinum,
        12800,
        achievements: 24,
      ),
      _createMockUser('荣誉学者', 32, ScholarRank.gold, 9650, achievements: 19),
      _createMockUser('进步之星', 28, ScholarRank.gold, 8200, achievements: 15),
      _createMockUser('你', 25, ScholarRank.silver, 6800, achievements: 12),
    ], isAchievement: true);
  }

  /// 构建排行榜列表
  Widget _buildRankingList(
    List<Map<String, dynamic>> users, {
    bool isWeekly = false,
    bool isAchievement = false,
  }) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: users.length,
      itemBuilder: (context, index) {
        final user = users[index];
        final isCurrentUser = user['name'] == '你';

        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isCurrentUser
                ? const Color(0xFF2E7EED).withValues(alpha: 0.1)
                : Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isCurrentUser
                  ? const Color(0xFF2E7EED).withValues(alpha: 0.3)
                  : const Color(0xFF37352F).withValues(alpha: 0.1),
              width: isCurrentUser ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // 排名
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: _getRankColor(index + 1),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Center(
                  child: Text(
                    '${index + 1}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 16),

              // 用户信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          user['name'],
                          style: TextStyle(
                            color: const Color(0xFF37352F),
                            fontSize: 16,
                            fontWeight: isCurrentUser
                                ? FontWeight.bold
                                : FontWeight.w600,
                          ),
                        ),
                        if (isCurrentUser) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: const Color(0xFF2E7EED),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              '我',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${user['rankName']} ${user['level']}级',
                      style: TextStyle(
                        color: Color(
                          int.parse(user['rankColor'].substring(1), radix: 16) +
                              0xFF000000,
                        ),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),

              // 数据显示
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (isWeekly) ...[
                    Text(
                      '${user['weeklyHours']}小时',
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '本周学习',
                      style: TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
                    ),
                  ] else if (isAchievement) ...[
                    Text(
                      '${user['achievements']}个',
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '成就解锁',
                      style: TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
                    ),
                  ] else ...[
                    Text(
                      '${user['totalExp']}',
                      style: const TextStyle(
                        color: Color(0xFF37352F),
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      '总经验',
                      style: TextStyle(color: Color(0xFF9B9A97), fontSize: 12),
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  /// 获取排名颜色
  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return const Color(0xFFFFD700); // 金色
      case 2:
        return const Color(0xFFC0C0C0); // 银色
      case 3:
        return const Color(0xFFCD7F32); // 铜色
      default:
        return const Color(0xFF9B9A97); // 灰色
    }
  }

  /// 创建模拟用户数据
  Map<String, dynamic> _createMockUser(
    String name,
    int level,
    ScholarRank rank,
    int totalExp, {
    int? weeklyHours,
    int? achievements,
  }) {
    return {
      'name': name,
      'level': level,
      'rank': rank,
      'rankName': _getRankName(rank),
      'rankColor': _getScholarRankColor(rank),
      'totalExp': totalExp,
      'weeklyHours': weeklyHours ?? 0,
      'achievements': achievements ?? 0,
    };
  }

  /// 获取段位名称
  String _getRankName(ScholarRank rank) {
    switch (rank) {
      case ScholarRank.bronze:
        return '青铜学者';
      case ScholarRank.silver:
        return '白银学者';
      case ScholarRank.gold:
        return '黄金学者';
      case ScholarRank.platinum:
        return '铂金学者';
      case ScholarRank.diamond:
        return '钻石学者';
      case ScholarRank.master:
        return '大师学者';
      case ScholarRank.grandmaster:
        return '宗师学者';
      case ScholarRank.king:
        return '王者学者';
    }
  }

  /// 获取段位颜色
  String _getScholarRankColor(ScholarRank rank) {
    switch (rank) {
      case ScholarRank.bronze:
        return '#CD7F32';
      case ScholarRank.silver:
        return '#C0C0C0';
      case ScholarRank.gold:
        return '#FFD700';
      case ScholarRank.platinum:
        return '#E5E4E2';
      case ScholarRank.diamond:
        return '#B9F2FF';
      case ScholarRank.master:
        return '#7C3AED';
      case ScholarRank.grandmaster:
        return '#E03E3E';
      case ScholarRank.king:
        return '#FF6B35';
    }
  }
}
