import '../models/achievement.dart';
import '../models/badge.dart';

/// 成就系统数据配置
class AchievementsData {
  /// 获取所有预设成就
  static List<Achievement> getAllAchievements() {
    return [
      // 学习成就类
      ..._getLearningAchievements(),
      // 记忆宫殿成就类
      ..._getMemoryAchievements(),
      // 健康运动成就类
      ..._getHealthAchievements(),
      // 社交成就类
      ..._getSocialAchievements(),
      // 里程碑成就类
      ..._getMilestoneAchievements(),
      // 特殊成就类
      ..._getSpecialAchievements(),
    ];
  }

  /// 学习成就
  static List<Achievement> _getLearningAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'learning_001',
        name: '初出茅庐',
        description: '完成第一个时间盒子学习任务',
        icon: '🎯',
        type: AchievementType.learning,
        rarity: AchievementRarity.common,
        experienceReward: 50,
        wageReward: 10.0,
        unlockCondition: '完成1个时间盒子',
        createdAt: now,
      ),
      Achievement(
        id: 'learning_002',
        name: '专注战士',
        description: '连续专注学习2小时不中断',
        icon: '⚔️',
        type: AchievementType.learning,
        rarity: AchievementRarity.rare,
        experienceReward: 100,
        wageReward: 50.0,
        unlockCondition: '连续学习2小时',
        createdAt: now,
      ),
      Achievement(
        id: 'learning_003',
        name: '时间刺客',
        description: '单日学习时长超过8小时',
        icon: '🗡️',
        type: AchievementType.learning,
        rarity: AchievementRarity.epic,
        experienceReward: 200,
        wageReward: 100.0,
        unlockCondition: '单日学习8小时',
        createdAt: now,
      ),
      Achievement(
        id: 'learning_004',
        name: '百万富翁',
        description: '累计虚拟工资达到100万',
        icon: '💰',
        type: AchievementType.learning,
        rarity: AchievementRarity.legendary,
        experienceReward: 500,
        wageReward: 1000.0,
        unlockCondition: '累计工资100万',
        createdAt: now,
      ),
      Achievement(
        id: 'learning_005',
        name: '连击王者',
        description: '连续打卡学习30天',
        icon: '🔥',
        type: AchievementType.learning,
        rarity: AchievementRarity.legendary,
        experienceReward: 300,
        wageReward: 200.0,
        unlockCondition: '连续打卡30天',
        createdAt: now,
      ),
      Achievement(
        id: 'learning_006',
        name: '深夜学者',
        description: '在23:00-01:00时段累计学习100小时',
        icon: '🌙',
        type: AchievementType.learning,
        rarity: AchievementRarity.epic,
        experienceReward: 150,
        wageReward: 80.0,
        unlockCondition: '深夜学习100小时',
        maxProgress: 100,
        createdAt: now,
      ),
      Achievement(
        id: 'learning_007',
        name: '早起鸟儿',
        description: '在06:00-08:00时段累计学习50小时',
        icon: '🐦',
        type: AchievementType.learning,
        rarity: AchievementRarity.rare,
        experienceReward: 120,
        wageReward: 60.0,
        unlockCondition: '早起学习50小时',
        maxProgress: 50,
        createdAt: now,
      ),
    ];
  }

  /// 记忆宫殿成就
  static List<Achievement> _getMemoryAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'memory_001',
        name: '建筑师',
        description: '创建第一个记忆宫殿',
        icon: '🏗️',
        type: AchievementType.memory,
        rarity: AchievementRarity.common,
        experienceReward: 30,
        wageReward: 20.0,
        unlockCondition: '创建1个记忆宫殿',
        createdAt: now,
      ),
      Achievement(
        id: 'memory_002',
        name: '宫殿领主',
        description: '拥有10个记忆宫殿',
        icon: '🏰',
        type: AchievementType.memory,
        rarity: AchievementRarity.epic,
        experienceReward: 200,
        wageReward: 100.0,
        unlockCondition: '创建10个记忆宫殿',
        maxProgress: 10,
        createdAt: now,
      ),
      Achievement(
        id: 'memory_003',
        name: '记忆帝王',
        description: '单个宫殿添加100个记忆锚点',
        icon: '👑',
        type: AchievementType.memory,
        rarity: AchievementRarity.legendary,
        experienceReward: 300,
        wageReward: 150.0,
        unlockCondition: '单宫殿100个锚点',
        maxProgress: 100,
        createdAt: now,
      ),
      Achievement(
        id: 'memory_004',
        name: '空间魔法师',
        description: '使用AR功能创建记忆场景',
        icon: '🔮',
        type: AchievementType.memory,
        rarity: AchievementRarity.mythic,
        experienceReward: 400,
        wageReward: 200.0,
        unlockCondition: '使用AR创建场景',
        isHidden: true,
        createdAt: now,
      ),
    ];
  }

  /// 健康运动成就
  static List<Achievement> _getHealthAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'health_001',
        name: '动作收集家',
        description: '解锁所有26个字母对应的PAO动作',
        icon: '🏃',
        type: AchievementType.health,
        rarity: AchievementRarity.epic,
        experienceReward: 250,
        wageReward: 120.0,
        unlockCondition: '解锁26个PAO动作',
        maxProgress: 26,
        createdAt: now,
      ),
      Achievement(
        id: 'health_002',
        name: '健身达人',
        description: '累计完成1000个运动动作',
        icon: '💪',
        type: AchievementType.health,
        rarity: AchievementRarity.legendary,
        experienceReward: 300,
        wageReward: 150.0,
        unlockCondition: '完成1000个动作',
        maxProgress: 1000,
        createdAt: now,
      ),
      Achievement(
        id: 'health_003',
        name: '护眼卫士',
        description: '完成100次眼保健操',
        icon: '👁️',
        type: AchievementType.health,
        rarity: AchievementRarity.rare,
        experienceReward: 100,
        wageReward: 50.0,
        unlockCondition: '完成100次眼保健操',
        maxProgress: 100,
        createdAt: now,
      ),
    ];
  }

  /// 社交成就
  static List<Achievement> _getSocialAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'social_001',
        name: '社交新手',
        description: '添加第一个学习好友',
        icon: '👥',
        type: AchievementType.social,
        rarity: AchievementRarity.common,
        experienceReward: 40,
        wageReward: 20.0,
        unlockCondition: '添加1个好友',
        createdAt: now,
      ),
      Achievement(
        id: 'social_002',
        name: '人气王者',
        description: '拥有50个学习好友',
        icon: '🌟',
        type: AchievementType.social,
        rarity: AchievementRarity.epic,
        experienceReward: 200,
        wageReward: 100.0,
        unlockCondition: '拥有50个好友',
        maxProgress: 50,
        createdAt: now,
      ),
      Achievement(
        id: 'social_003',
        name: '挑战者',
        description: '发起10次学习挑战',
        icon: '⚡',
        type: AchievementType.social,
        rarity: AchievementRarity.rare,
        experienceReward: 120,
        wageReward: 60.0,
        unlockCondition: '发起10次挑战',
        maxProgress: 10,
        createdAt: now,
      ),
    ];
  }

  /// 里程碑成就
  static List<Achievement> _getMilestoneAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'milestone_001',
        name: '学习新星',
        description: '达到10级',
        icon: '⭐',
        type: AchievementType.milestone,
        rarity: AchievementRarity.rare,
        experienceReward: 100,
        wageReward: 50.0,
        unlockCondition: '达到10级',
        createdAt: now,
      ),
      Achievement(
        id: 'milestone_002',
        name: '学习精英',
        description: '达到30级',
        icon: '🏅',
        type: AchievementType.milestone,
        rarity: AchievementRarity.epic,
        experienceReward: 300,
        wageReward: 150.0,
        unlockCondition: '达到30级',
        createdAt: now,
      ),
      Achievement(
        id: 'milestone_003',
        name: '学习大师',
        description: '达到50级',
        icon: '🎖️',
        type: AchievementType.milestone,
        rarity: AchievementRarity.legendary,
        experienceReward: 500,
        wageReward: 250.0,
        unlockCondition: '达到50级',
        createdAt: now,
      ),
      Achievement(
        id: 'milestone_004',
        name: '学习传说',
        description: '达到100级',
        icon: '🏆',
        type: AchievementType.milestone,
        rarity: AchievementRarity.mythic,
        experienceReward: 1000,
        wageReward: 500.0,
        unlockCondition: '达到100级',
        createdAt: now,
      ),
    ];
  }

  /// 特殊成就
  static List<Achievement> _getSpecialAchievements() {
    final now = DateTime.now();
    return [
      Achievement(
        id: 'special_001',
        name: '首日勇士',
        description: '注册当天完成5小时学习',
        icon: '🛡️',
        type: AchievementType.special,
        rarity: AchievementRarity.legendary,
        experienceReward: 200,
        wageReward: 100.0,
        unlockCondition: '注册当天学习5小时',
        isHidden: true,
        createdAt: now,
      ),
      Achievement(
        id: 'special_002',
        name: '完美主义者',
        description: '连续7天每天完成100%学习计划',
        icon: '💎',
        type: AchievementType.special,
        rarity: AchievementRarity.mythic,
        experienceReward: 500,
        wageReward: 300.0,
        unlockCondition: '连续7天100%完成计划',
        isHidden: true,
        createdAt: now,
      ),
      Achievement(
        id: 'special_003',
        name: '探索者',
        description: '使用所有功能模块',
        icon: '🧭',
        type: AchievementType.special,
        rarity: AchievementRarity.epic,
        experienceReward: 150,
        wageReward: 80.0,
        unlockCondition: '使用所有功能',
        createdAt: now,
      ),
    ];
  }

  /// 获取所有预设徽章
  static List<Badge> getAllBadges() {
    final now = DateTime.now();
    return [
      // 段位徽章
      Badge(
        id: 'rank_bronze',
        name: '青铜学者',
        description: '达到青铜段位的学习者',
        icon: '🥉',
        type: BadgeType.rank,
        rarity: AchievementRarity.common,
        obtainCondition: '达到青铜段位',
        createdAt: now,
      ),
      Badge(
        id: 'rank_silver',
        name: '白银学者',
        description: '达到白银段位的学习者',
        icon: '🥈',
        type: BadgeType.rank,
        rarity: AchievementRarity.rare,
        obtainCondition: '达到白银段位',
        createdAt: now,
      ),
      Badge(
        id: 'rank_gold',
        name: '黄金学者',
        description: '达到黄金段位的学习者',
        icon: '🥇',
        type: BadgeType.rank,
        rarity: AchievementRarity.epic,
        obtainCondition: '达到黄金段位',
        createdAt: now,
      ),
      Badge(
        id: 'rank_king',
        name: '王者学者',
        description: '达到王者段位的传奇学习者',
        icon: '👑',
        type: BadgeType.rank,
        rarity: AchievementRarity.mythic,
        obtainCondition: '达到王者段位',
        createdAt: now,
      ),
      
      // 特殊徽章
      Badge(
        id: 'special_founder',
        name: '创始用户',
        description: '应用早期的忠实用户',
        icon: '🌟',
        type: BadgeType.special,
        rarity: AchievementRarity.legendary,
        obtainCondition: '早期注册用户',
        isLimited: true,
        limitedUntil: DateTime(2024, 12, 31),
        createdAt: now,
      ),
    ];
  }

  /// 获取每日挑战模板
  static List<Challenge> getDailyChallenges() {
    final now = DateTime.now();
    final tomorrow = now.add(const Duration(days: 1));
    final endOfDay = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    
    return [
      Challenge(
        id: 'daily_study_2h',
        name: '每日专注',
        description: '今日学习2小时',
        icon: '📚',
        type: 'daily',
        targetValue: 120, // 分钟
        experienceReward: 50,
        wageReward: 20.0,
        startTime: DateTime(now.year, now.month, now.day),
        endTime: endOfDay,
      ),
      Challenge(
        id: 'daily_memory_5',
        name: '记忆训练',
        description: '在记忆宫殿中添加5个锚点',
        icon: '🧠',
        type: 'daily',
        targetValue: 5,
        experienceReward: 30,
        wageReward: 15.0,
        startTime: DateTime(now.year, now.month, now.day),
        endTime: endOfDay,
      ),
      Challenge(
        id: 'daily_exercise_10',
        name: '健康运动',
        description: '完成10个PAO动作',
        icon: '💪',
        type: 'daily',
        targetValue: 10,
        experienceReward: 25,
        wageReward: 10.0,
        startTime: DateTime(now.year, now.month, now.day),
        endTime: endOfDay,
      ),
    ];
  }
}
