import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/achievement.dart';

/// 成就解锁通知组件
class AchievementUnlockNotification extends StatefulWidget {
  final Achievement achievement;
  final VoidCallback? onComplete;

  const AchievementUnlockNotification({
    super.key,
    required this.achievement,
    this.onComplete,
  });

  @override
  State<AchievementUnlockNotification> createState() =>
      _AchievementUnlockNotificationState();

  /// 显示成就解锁通知
  static void show(
    BuildContext context,
    Achievement achievement, {
    VoidCallback? onDismiss,
  }) {
    showDialog(
      context: context,
      barrierDismissible: true, // 允许点击外部关闭
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (context) => AchievementUnlockNotification(
        achievement: achievement,
        onComplete: () {
          Navigator.of(context).pop();
          onDismiss?.call(); // 调用关闭回调
        },
      ),
    );
  }
}

class _AchievementUnlockNotificationState
    extends State<AchievementUnlockNotification>
    with TickerProviderStateMixin {
  late AnimationController _scaleController;
  late AnimationController _fadeController;
  late AnimationController _particleController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _particleAnimation;

  @override
  void initState() {
    super.initState();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeIn));

    _particleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _particleController, curve: Curves.easeOut),
    );

    _startAnimation();
  }

  @override
  void dispose() {
    _scaleController.dispose();
    _fadeController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  /// 开始动画
  void _startAnimation() async {
    // 震动反馈
    HapticFeedback.heavyImpact();

    // 同时开始所有动画
    _fadeController.forward();
    _particleController.forward();

    // 延迟一点开始缩放动画
    await Future.delayed(const Duration(milliseconds: 100));
    _scaleController.forward();

    // 5秒后自动关闭（延长显示时间）
    await Future.delayed(const Duration(seconds: 5));
    if (mounted) {
      widget.onComplete?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Center(
        child: AnimatedBuilder(
          animation: Listenable.merge([
            _scaleController,
            _fadeController,
            _particleController,
          ]),
          builder: (context, child) {
            return Stack(
              alignment: Alignment.center,
              children: [
                // 粒子效果背景
                _buildParticleEffect(),

                // 主要内容
                FadeTransition(
                  opacity: _fadeAnimation,
                  child: ScaleTransition(
                    scale: _scaleAnimation,
                    child: _buildMainContent(),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  /// 构建粒子效果
  Widget _buildParticleEffect() {
    return SizedBox(
      width: 300,
      height: 300,
      child: CustomPaint(
        painter: _ParticleEffectPainter(_particleAnimation.value),
      ),
    );
  }

  /// 构建主要内容
  Widget _buildMainContent() {
    return Container(
      width: 280,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(
              int.parse(
                    widget.achievement.rarityColor.substring(1),
                    radix: 16,
                  ) +
                  0xFF000000,
            ),
            Color(
              int.parse(
                    widget.achievement.rarityColor.substring(1),
                    radix: 16,
                  ) +
                  0xFF000000,
            ).withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(
              int.parse(
                    widget.achievement.rarityColor.substring(1),
                    radix: 16,
                  ) +
                  0xFF000000,
            ).withValues(alpha: 0.5),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 成就解锁标题
          const Text(
            '🎉 成就解锁 🎉',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // 成就图标
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(40),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 3,
              ),
            ),
            child: Center(
              child: Text(
                widget.achievement.icon,
                style: const TextStyle(fontSize: 36),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 成就名称
          Text(
            widget.achievement.name,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 8),

          // 成就描述
          Text(
            widget.achievement.description,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // 稀有度标签
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(15),
            ),
            child: Text(
              widget.achievement.rarityName,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 奖励信息
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (widget.achievement.experienceReward > 0) ...[
                const Icon(Icons.star, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  '+${widget.achievement.experienceReward} EXP',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],

              if (widget.achievement.wageReward > 0) ...[
                if (widget.achievement.experienceReward > 0)
                  const SizedBox(width: 16),
                const Icon(
                  Icons.monetization_on,
                  color: Colors.white,
                  size: 16,
                ),
                const SizedBox(width: 4),
                Text(
                  '+¥${widget.achievement.wageReward.toStringAsFixed(0)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ],
          ),

          const SizedBox(height: 24),

          // 手动关闭按钮
          GestureDetector(
            onTap: () => widget.onComplete?.call(),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: const Text(
                '点击继续',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 粒子效果绘制器
class _ParticleEffectPainter extends CustomPainter {
  final double progress;

  _ParticleEffectPainter(this.progress);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final maxRadius = size.width / 2;

    // 绘制多个粒子
    for (int i = 0; i < 20; i++) {
      final angle = (i / 20) * 2 * 3.14159;
      final radius = maxRadius * progress * (0.5 + (i % 3) * 0.25);
      final particleSize = 3.0 * (1 - progress) + 1.0;

      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      canvas.drawCircle(
        Offset(x, y),
        particleSize,
        paint..color = Colors.white.withValues(alpha: (1 - progress) * 0.8),
      );
    }

    // 绘制光环效果
    for (int i = 0; i < 3; i++) {
      final ringRadius = maxRadius * progress * (0.3 + i * 0.2);
      final ringPaint = Paint()
        ..color = Colors.white.withValues(alpha: (1 - progress) * 0.3)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0;

      canvas.drawCircle(center, ringRadius, ringPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
