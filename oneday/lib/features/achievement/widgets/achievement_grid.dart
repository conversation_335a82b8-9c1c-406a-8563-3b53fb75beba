import 'package:flutter/material.dart';
import '../models/achievement.dart';

/// 成就网格组件
class AchievementGrid extends StatefulWidget {
  final List<Achievement> achievements;
  final Map<String, UserAchievementProgress> userProgress;
  final VoidCallback? onRefresh;

  const AchievementGrid({
    super.key,
    required this.achievements,
    required this.userProgress,
    this.onRefresh,
  });

  @override
  State<AchievementGrid> createState() => _AchievementGridState();
}

class _AchievementGridState extends State<AchievementGrid> {
  AchievementType? _selectedType;
  String _searchQuery = '';

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        _buildFilterBar(),
        Expanded(
          child: _buildAchievementList(),
        ),
      ],
    );
  }

  /// 构建筛选栏
  Widget _buildFilterBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.white,
      child: Column(
        children: [
          // 搜索框
          TextField(
            onChanged: (value) => setState(() => _searchQuery = value),
            decoration: InputDecoration(
              hintText: '搜索成就...',
              prefixIcon: const Icon(Icons.search, color: Color(0xFF9B9A97)),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: const Color(0xFF37352F).withValues(alpha: 0.1),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(
                  color: const Color(0xFF37352F).withValues(alpha: 0.1),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: Color(0xFF2E7EED),
                ),
              ),
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          // 类型筛选
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildFilterChip('全部', null),
                const SizedBox(width: 8),
                ...AchievementType.values.map((type) => 
                  Padding(
                    padding: const EdgeInsets.only(right: 8),
                    child: _buildFilterChip(_getTypeDisplayName(type), type),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建筛选芯片
  Widget _buildFilterChip(String label, AchievementType? type) {
    final isSelected = _selectedType == type;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedType = selected ? type : null;
        });
      },
      backgroundColor: Colors.white,
      selectedColor: const Color(0xFF2E7EED).withValues(alpha: 0.1),
      checkmarkColor: const Color(0xFF2E7EED),
      labelStyle: TextStyle(
        color: isSelected ? const Color(0xFF2E7EED) : const Color(0xFF37352F),
        fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? const Color(0xFF2E7EED) : const Color(0xFF37352F).withValues(alpha: 0.1),
      ),
    );
  }

  /// 获取类型显示名称
  String _getTypeDisplayName(AchievementType type) {
    switch (type) {
      case AchievementType.learning:
        return '学习';
      case AchievementType.memory:
        return '记忆';
      case AchievementType.health:
        return '健康';
      case AchievementType.social:
        return '社交';
      case AchievementType.milestone:
        return '里程碑';
      case AchievementType.special:
        return '特殊';
    }
  }

  /// 构建成就列表
  Widget _buildAchievementList() {
    final filteredAchievements = _getFilteredAchievements();
    
    if (filteredAchievements.isEmpty) {
      return _buildEmptyState();
    }
    
    return RefreshIndicator(
      onRefresh: () async {
        widget.onRefresh?.call();
      },
      color: const Color(0xFF2E7EED),
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: filteredAchievements.length,
        itemBuilder: (context, index) {
          final achievement = filteredAchievements[index];
          final progress = widget.userProgress[achievement.id];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: AchievementCard(
              achievement: achievement,
              progress: progress,
            ),
          );
        },
      ),
    );
  }

  /// 获取筛选后的成就
  List<Achievement> _getFilteredAchievements() {
    return widget.achievements.where((achievement) {
      // 类型筛选
      if (_selectedType != null && achievement.type != _selectedType) {
        return false;
      }
      
      // 搜索筛选
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        return achievement.name.toLowerCase().contains(query) ||
               achievement.description.toLowerCase().contains(query);
      }
      
      // 隐藏成就筛选 (暂时显示所有成就)
      return true;
    }).toList();
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: Color(0xFF9B9A97),
          ),
          SizedBox(height: 16),
          Text(
            '没有找到匹配的成就',
            style: TextStyle(
              color: Color(0xFF9B9A97),
              fontSize: 16,
            ),
          ),
          SizedBox(height: 8),
          Text(
            '试试调整筛选条件',
            style: TextStyle(
              color: Color(0xFF9B9A97),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }
}

/// 成就卡片组件
class AchievementCard extends StatelessWidget {
  final Achievement achievement;
  final UserAchievementProgress? progress;

  const AchievementCard({
    super.key,
    required this.achievement,
    this.progress,
  });

  @override
  Widget build(BuildContext context) {
    final isUnlocked = progress?.isUnlocked ?? false;
    final currentProgress = progress?.currentProgress ?? 0;
    final progressPercentage = achievement.maxProgress > 1 
        ? (currentProgress / achievement.maxProgress).clamp(0.0, 1.0)
        : (isUnlocked ? 1.0 : 0.0);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isUnlocked 
              ? Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.3)
              : const Color(0xFF37352F).withValues(alpha: 0.1),
          width: isUnlocked ? 2 : 1,
        ),
        boxShadow: isUnlocked ? [
          BoxShadow(
            color: Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ] : null,
      ),
      child: Row(
        children: [
          // 成就图标
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: isUnlocked 
                  ? Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.1)
                  : const Color(0xFF9B9A97).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Center(
              child: Text(
                achievement.icon,
                style: TextStyle(
                  fontSize: 24,
                  color: isUnlocked ? null : const Color(0xFF9B9A97),
                ),
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // 成就信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        achievement.name,
                        style: TextStyle(
                          color: isUnlocked ? const Color(0xFF37352F) : const Color(0xFF9B9A97),
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    
                    // 稀有度标签
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(10),
                      ),
                      child: Text(
                        achievement.rarityName,
                        style: TextStyle(
                          color: Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000),
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                Text(
                  achievement.description,
                  style: TextStyle(
                    color: isUnlocked ? const Color(0xFF37352F).withValues(alpha: 0.7) : const Color(0xFF9B9A97),
                    fontSize: 14,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // 进度条 (仅进度型成就显示)
                if (achievement.isProgressAchievement) ...[
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          height: 4,
                          decoration: BoxDecoration(
                            color: const Color(0xFF9B9A97).withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(2),
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: progressPercentage,
                            child: Container(
                              decoration: BoxDecoration(
                                color: Color(int.parse(achievement.rarityColor.substring(1), radix: 16) + 0xFF000000),
                                borderRadius: BorderRadius.circular(2),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '$currentProgress/${achievement.maxProgress}',
                        style: TextStyle(
                          color: isUnlocked ? const Color(0xFF37352F) : const Color(0xFF9B9A97),
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ] else ...[
                  // 奖励信息
                  Row(
                    children: [
                      if (achievement.experienceReward > 0) ...[
                        const Icon(
                          Icons.star,
                          size: 14,
                          color: Color(0xFF2E7EED),
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '+${achievement.experienceReward} EXP',
                          style: const TextStyle(
                            color: Color(0xFF2E7EED),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                      
                      if (achievement.wageReward > 0) ...[
                        if (achievement.experienceReward > 0) const SizedBox(width: 12),
                        const Icon(
                          Icons.monetization_on,
                          size: 14,
                          color: Color(0xFF0F7B6C),
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '+¥${achievement.wageReward.toStringAsFixed(0)}',
                          style: const TextStyle(
                            color: Color(0xFF0F7B6C),
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
          
          // 解锁状态图标
          if (isUnlocked)
            const Icon(
              Icons.check_circle,
              color: Color(0xFF0F7B6C),
              size: 24,
            )
          else
            Icon(
              Icons.lock_outline,
              color: const Color(0xFF9B9A97).withValues(alpha: 0.5),
              size: 24,
            ),
        ],
      ),
    );
  }
}
