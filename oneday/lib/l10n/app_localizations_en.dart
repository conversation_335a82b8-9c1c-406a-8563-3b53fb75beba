// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'OneDay';

  @override
  String get appTagline => 'Afraid of wasting a day? Use OneDay';

  @override
  String get home => 'Home';

  @override
  String get calendar => 'Calendar';

  @override
  String get store => 'Store';

  @override
  String get community => 'Community';

  @override
  String get profile => 'Profile';

  @override
  String get settings => 'Settings';

  @override
  String get systemPreferences => 'System Preferences';

  @override
  String get language => 'Language';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get chineseSimplified => '简体中文';

  @override
  String get english => 'English';

  @override
  String get notifications => 'Notifications';

  @override
  String get notificationsDescription =>
      'Receive study reminders and important messages';

  @override
  String get vocabularyHighlight => 'Vocabulary Highlight';

  @override
  String get vocabularyHighlightDescription =>
      'Highlight graduate exam vocabulary in articles';

  @override
  String get expertOnlyHighlight => 'Expert-level Vocabulary Only';

  @override
  String get expertOnlyHighlightDescription =>
      'Only highlight expert-level graduate exam vocabulary';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get autoBackup => 'Auto Backup';

  @override
  String get autoBackupDescription => 'Regularly backup learning data to cloud';

  @override
  String get exportData => 'Export Data';

  @override
  String get exportDataDescription => 'Export learning records and wage data';

  @override
  String get clearCache => 'Clear Cache';

  @override
  String get clearCacheDescription =>
      'Clear temporary files to free storage space';

  @override
  String get resetData => 'Reset Data';

  @override
  String get resetDataDescription =>
      'Clear all data and restore to initial state';

  @override
  String get privacySecurity => 'Privacy & Security';

  @override
  String get biometricUnlock => 'Biometric Unlock';

  @override
  String get biometricUnlockDescription =>
      'Use fingerprint or face recognition to unlock app';

  @override
  String get dataAnalytics => 'Data Analytics';

  @override
  String get dataAnalyticsDescription => 'Help improve app experience';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get privacyPolicyDescription => 'Learn how we protect your privacy';

  @override
  String get aboutApp => 'About App';

  @override
  String get version => 'Version';

  @override
  String get userAgreement => 'User Agreement';

  @override
  String get userAgreementDescription =>
      'View terms of service and usage agreement';

  @override
  String get contactUs => 'Contact Us';

  @override
  String get contactUsDescription => 'Feedback suggestions or get help';

  @override
  String get rateApp => 'Rate App';

  @override
  String get rateAppDescription => 'Rate us in the app store';

  @override
  String get newLearning => 'New Learning';

  @override
  String get dataUpdated => 'Data updated';

  @override
  String refreshFailed(String error) {
    return 'Refresh failed: $error';
  }

  @override
  String pleaseClickTab(String tabName) {
    return 'Please click the \"$tabName\" tab in the bottom navigation';
  }

  @override
  String get timeBoxCreated => 'Learning time box created successfully!';

  @override
  String get cancel => 'Cancel';

  @override
  String get save => 'Save';

  @override
  String saved(String title) {
    return '$title saved';
  }

  @override
  String get settingsSaved =>
      'Settings saved, some features require app restart to take effect';

  @override
  String get understood => 'Understood';

  @override
  String get comingSoon => 'This feature is coming soon, stay tuned';

  @override
  String get vocabularyHighlightSaved => 'Vocabulary highlight settings saved';

  @override
  String saveSettingsFailed(String error) {
    return 'Failed to save settings: $error';
  }

  @override
  String get embodiedMemoryTraining => '🧠 Embodied Memory Training';

  @override
  String get selectTrainingMode => 'Select memory training mode:';

  @override
  String get focusedTraining => '🏋️ Focused Training';

  @override
  String get focusedTrainingDescription =>
      'Select words from graduate exam vocabulary for 30-60 minutes of embodied memory training';

  @override
  String get actionLibraryManagement => '📚 Action Library Management';

  @override
  String get actionLibraryDescription =>
      'Manage custom action libraries and integrate with existing PAO system';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String get privacyPolicyContent =>
      'OneDay App Privacy Policy\n\nWe are committed to protecting your privacy and personal information security.\n\n1. Information Collection\nWe only collect information necessary to provide you with services.\n\n2. Information Use\nYour data is only used to improve learning experience.\n\n3. Information Sharing\nWe do not share your personal information with third parties.\n\n4. Data Security\nWe use industry-standard security measures to protect your data.\n\nIf you have any questions, please contact us.';

  @override
  String get userAgreementTitle => 'User Agreement';

  @override
  String get userAgreementContent =>
      'OneDay App User Agreement\n\nWelcome to OneDay learning app!\n\n1. Service Content\nOneDay provides learning time management, memory training and other services.\n\n2. User Obligations\nPlease use app features reasonably and do not engage in illegal activities.\n\n3. Intellectual Property\nApp content is protected by intellectual property law.\n\n4. Disclaimer\nWe are not responsible for results from using the app.\n\n5. Agreement Changes\nWe reserve the right to modify this agreement.\n\nContinued use indicates agreement to this agreement.';

  @override
  String get contactUsTitle => 'Contact Us';

  @override
  String get contactUsContent =>
      'Thank you for using OneDay!\n\nIf you have any questions or suggestions, please contact us through:\n\n📧 Email: <EMAIL>\n💬 WeChat: OneDay_Support\n📱 QQ Group: 123456789\n\nWe will reply to your questions as soon as possible.';

  @override
  String get rateAppTitle => 'Rate App';

  @override
  String get rateAppContent =>
      'Like OneDay? ⭐\n\nYour rating is our motivation to move forward!\nPlease go to the app store to rate and comment for us.\n\nThank you for your support! 🙏';

  @override
  String get laterButton => 'Later';

  @override
  String get rateButton => 'Rate';

  @override
  String get ok => 'OK';

  @override
  String get close => 'Close';
}
