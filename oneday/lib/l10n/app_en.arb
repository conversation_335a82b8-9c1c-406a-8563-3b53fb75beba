{"@@locale": "en", "appTitle": "OneDay", "@appTitle": {"description": "The title of the application"}, "appTagline": "Afraid of wasting a day? Use OneDay", "@appTagline": {"description": "The tagline displayed below the logo"}, "home": "Home", "@home": {"description": "Home tab label"}, "calendar": "Calendar", "@calendar": {"description": "Calendar tab label"}, "store": "Store", "@store": {"description": "Store tab label"}, "community": "Community", "@community": {"description": "Community tab label"}, "profile": "Profile", "@profile": {"description": "Profile tab label"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "systemPreferences": "System Preferences", "@systemPreferences": {"description": "System preferences section title"}, "language": "Language", "@language": {"description": "Language setting label"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Language selection dialog title"}, "chineseSimplified": "简体中文", "@chineseSimplified": {"description": "Chinese Simplified language option"}, "english": "English", "@english": {"description": "English language option"}, "notifications": "Notifications", "@notifications": {"description": "Notifications setting label"}, "notificationsDescription": "Receive study reminders and important messages", "@notificationsDescription": {"description": "Notifications setting description"}, "vocabularyHighlight": "Vocabulary Highlight", "@vocabularyHighlight": {"description": "Vocabulary highlight setting label"}, "vocabularyHighlightDescription": "Highlight graduate exam vocabulary in articles", "@vocabularyHighlightDescription": {"description": "Vocabulary highlight setting description"}, "expertOnlyHighlight": "Expert-level Vocabulary Only", "@expertOnlyHighlight": {"description": "Expert-level vocabulary highlight setting label"}, "expertOnlyHighlightDescription": "Only highlight expert-level graduate exam vocabulary", "@expertOnlyHighlightDescription": {"description": "Expert-level vocabulary highlight setting description"}, "dataManagement": "Data Management", "@dataManagement": {"description": "Data management section title"}, "autoBackup": "Auto Backup", "@autoBackup": {"description": "Auto backup setting label"}, "autoBackupDescription": "Regularly backup learning data to cloud", "@autoBackupDescription": {"description": "Auto backup setting description"}, "exportData": "Export Data", "@exportData": {"description": "Export data setting label"}, "exportDataDescription": "Export learning records and wage data", "@exportDataDescription": {"description": "Export data setting description"}, "clearCache": "<PERSON>ache", "@clearCache": {"description": "Clear cache setting label"}, "clearCacheDescription": "Clear temporary files to free storage space", "@clearCacheDescription": {"description": "Clear cache setting description"}, "resetData": "Reset Data", "@resetData": {"description": "Reset data setting label"}, "resetDataDescription": "Clear all data and restore to initial state", "@resetDataDescription": {"description": "Reset data setting description"}, "privacySecurity": "Privacy & Security", "@privacySecurity": {"description": "Privacy and security section title"}, "biometricUnlock": "Biometric Unlock", "@biometricUnlock": {"description": "Biometric unlock setting label"}, "biometricUnlockDescription": "Use fingerprint or face recognition to unlock app", "@biometricUnlockDescription": {"description": "Biometric unlock setting description"}, "dataAnalytics": "Data Analytics", "@dataAnalytics": {"description": "Data analytics setting label"}, "dataAnalyticsDescription": "Help improve app experience", "@dataAnalyticsDescription": {"description": "Data analytics setting description"}, "privacyPolicy": "Privacy Policy", "@privacyPolicy": {"description": "Privacy policy setting label"}, "privacyPolicyDescription": "Learn how we protect your privacy", "@privacyPolicyDescription": {"description": "Privacy policy setting description"}, "aboutApp": "About App", "@aboutApp": {"description": "About app section title"}, "version": "Version", "@version": {"description": "Version setting label"}, "userAgreement": "User Agreement", "@userAgreement": {"description": "User agreement setting label"}, "userAgreementDescription": "View terms of service and usage agreement", "@userAgreementDescription": {"description": "User agreement setting description"}, "contactUs": "Contact Us", "@contactUs": {"description": "Contact us setting label"}, "contactUsDescription": "Feedback suggestions or get help", "@contactUsDescription": {"description": "Contact us setting description"}, "rateApp": "Rate App", "@rateApp": {"description": "Rate app setting label"}, "rateAppDescription": "Rate us in the app store", "@rateAppDescription": {"description": "Rate app setting description"}, "newLearning": "New Learning", "@newLearning": {"description": "New learning button label"}, "dataUpdated": "Data updated", "@dataUpdated": {"description": "Data updated snackbar message"}, "refreshFailed": "Refresh failed: {error}", "@refreshFailed": {"description": "Refresh failed error message", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "pleaseClickTab": "Please click the \"{tabName}\" tab in the bottom navigation", "@pleaseClickTab": {"description": "Navigation instruction message", "placeholders": {"tabName": {"type": "String", "description": "The name of the tab to click"}}}, "timeBoxCreated": "Learning time box created successfully!", "@timeBoxCreated": {"description": "Time box creation success message"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button label"}, "save": "Save", "@save": {"description": "Save button label"}, "saved": "{title} saved", "@saved": {"description": "<PERSON><PERSON> saved message", "placeholders": {"title": {"type": "String", "description": "The title of the saved item"}}}, "settingsSaved": "Settings saved, some features require app restart to take effect", "@settingsSaved": {"description": "Settings saved restart hint message"}, "understood": "Understood", "@understood": {"description": "Understood button label"}, "comingSoon": "This feature is coming soon, stay tuned", "@comingSoon": {"description": "Coming soon feature message"}, "vocabularyHighlightSaved": "Vocabulary highlight settings saved", "@vocabularyHighlightSaved": {"description": "Vocabulary highlight settings saved message"}, "saveSettingsFailed": "Failed to save settings: {error}", "@saveSettingsFailed": {"description": "Save settings failed error message", "placeholders": {"error": {"type": "String", "description": "The error message"}}}, "embodiedMemoryTraining": "🧠 Embodied Memory Training", "@embodiedMemoryTraining": {"description": "Embodied memory training title"}, "selectTrainingMode": "Select memory training mode:", "@selectTrainingMode": {"description": "Training mode selection instruction"}, "focusedTraining": "🏋️ Focused Training", "@focusedTraining": {"description": "Focused training option title"}, "focusedTrainingDescription": "Select words from graduate exam vocabulary for 30-60 minutes of embodied memory training", "@focusedTrainingDescription": {"description": "Focused training option description"}, "actionLibraryManagement": "📚 Action Library Management", "@actionLibraryManagement": {"description": "Action library management option title"}, "actionLibraryDescription": "Manage custom action libraries and integrate with existing PAO system", "@actionLibraryDescription": {"description": "Action library management option description"}, "privacyPolicyTitle": "Privacy Policy", "@privacyPolicyTitle": {"description": "Privacy policy dialog title"}, "privacyPolicyContent": "OneDay App Privacy Policy\n\nWe are committed to protecting your privacy and personal information security.\n\n1. Information Collection\nWe only collect information necessary to provide you with services.\n\n2. Information Use\nYour data is only used to improve learning experience.\n\n3. Information Sharing\nWe do not share your personal information with third parties.\n\n4. Data Security\nWe use industry-standard security measures to protect your data.\n\nIf you have any questions, please contact us.", "@privacyPolicyContent": {"description": "Privacy policy dialog content"}, "userAgreementTitle": "User Agreement", "@userAgreementTitle": {"description": "User agreement dialog title"}, "userAgreementContent": "OneDay App User Agreement\n\nWelcome to OneDay learning app!\n\n1. Service Content\nOneDay provides learning time management, memory training and other services.\n\n2. User Obligations\nPlease use app features reasonably and do not engage in illegal activities.\n\n3. Intellectual Property\nApp content is protected by intellectual property law.\n\n4. Disclaimer\nWe are not responsible for results from using the app.\n\n5. Agreement Changes\nWe reserve the right to modify this agreement.\n\nContinued use indicates agreement to this agreement.", "@userAgreementContent": {"description": "User agreement dialog content"}, "contactUsTitle": "Contact Us", "@contactUsTitle": {"description": "Contact us dialog title"}, "contactUsContent": "Thank you for using OneDay!\n\nIf you have any questions or suggestions, please contact us through:\n\n📧 Email: <EMAIL>\n💬 WeChat: OneDay_Support\n📱 QQ Group: 123456789\n\nWe will reply to your questions as soon as possible.", "@contactUsContent": {"description": "Contact us dialog content"}, "rateAppTitle": "Rate App", "@rateAppTitle": {"description": "Rate app dialog title"}, "rateAppContent": "Like OneDay? ⭐\n\nYour rating is our motivation to move forward!\nPlease go to the app store to rate and comment for us.\n\nThank you for your support! 🙏", "@rateAppContent": {"description": "Rate app dialog content"}, "laterButton": "Later", "@laterButton": {"description": "Later button label"}, "rateButton": "Rate", "@rateButton": {"description": "Rate button label"}, "ok": "OK", "@ok": {"description": "OK button label"}, "close": "Close", "@close": {"description": "Close button label"}}