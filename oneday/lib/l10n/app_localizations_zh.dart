// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'OneDay';

  @override
  String get appTagline => '怕一天浪费，就用OneDay';

  @override
  String get home => '首页';

  @override
  String get calendar => '日历';

  @override
  String get store => '商城';

  @override
  String get community => '社区';

  @override
  String get profile => '我的';

  @override
  String get settings => '设置';

  @override
  String get systemPreferences => '系统偏好';

  @override
  String get language => '语言';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get chineseSimplified => '中文简体';

  @override
  String get english => 'English';

  @override
  String get notifications => '通知';

  @override
  String get notificationsDescription => '接收学习提醒和重要消息';

  @override
  String get vocabularyHighlight => '词汇高亮';

  @override
  String get vocabularyHighlightDescription => '在文章中高亮显示考研词汇';

  @override
  String get expertOnlyHighlight => '仅高亮专家级词汇';

  @override
  String get expertOnlyHighlightDescription => '只高亮expert级别的考研词汇';

  @override
  String get dataManagement => '数据管理';

  @override
  String get autoBackup => '自动备份';

  @override
  String get autoBackupDescription => '定期备份学习数据到云端';

  @override
  String get exportData => '导出数据';

  @override
  String get exportDataDescription => '导出学习记录和工资数据';

  @override
  String get clearCache => '清理缓存';

  @override
  String get clearCacheDescription => '清理临时文件释放存储空间';

  @override
  String get resetData => '重置数据';

  @override
  String get resetDataDescription => '清除所有数据恢复初始状态';

  @override
  String get privacySecurity => '隐私安全';

  @override
  String get biometricUnlock => '生物识别解锁';

  @override
  String get biometricUnlockDescription => '使用指纹或面容解锁应用';

  @override
  String get dataAnalytics => '数据分析';

  @override
  String get dataAnalyticsDescription => '帮助改善应用体验';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get privacyPolicyDescription => '了解我们如何保护您的隐私';

  @override
  String get aboutApp => '关于应用';

  @override
  String get version => '版本';

  @override
  String get userAgreement => '用户协议';

  @override
  String get userAgreementDescription => '查看服务条款和使用协议';

  @override
  String get contactUs => '联系我们';

  @override
  String get contactUsDescription => '反馈建议或获取帮助';

  @override
  String get rateApp => '评价应用';

  @override
  String get rateAppDescription => '在应用商店为我们评分';

  @override
  String get newLearning => '新建学习';

  @override
  String get dataUpdated => '数据已更新';

  @override
  String refreshFailed(String error) {
    return '刷新失败: $error';
  }

  @override
  String pleaseClickTab(String tabName) {
    return '请点击底部导航栏的\"$tabName\"标签';
  }

  @override
  String get timeBoxCreated => '学习时间盒子创建成功！';

  @override
  String get cancel => '取消';

  @override
  String get save => '保存';

  @override
  String saved(String title) {
    return '$title已保存';
  }

  @override
  String get settingsSaved => '设置已保存，部分功能需要重启应用生效';

  @override
  String get understood => '知道了';

  @override
  String get comingSoon => '该功能即将推出，敬请期待';

  @override
  String get vocabularyHighlightSaved => '词汇高亮设置已保存';

  @override
  String saveSettingsFailed(String error) {
    return '保存设置失败: $error';
  }

  @override
  String get embodiedMemoryTraining => '🧠 具身记忆训练';

  @override
  String get selectTrainingMode => '选择记忆训练模式：';

  @override
  String get focusedTraining => '🏋️ 集中训练';

  @override
  String get focusedTrainingDescription => '从考研词汇中选择单词进行30-60分钟的具身记忆训练';

  @override
  String get actionLibraryManagement => '📚 动作库管理';

  @override
  String get actionLibraryDescription => '管理自定义动作库，与现有PAO系统无缝集成';

  @override
  String get privacyPolicyTitle => '隐私政策';

  @override
  String get privacyPolicyContent =>
      'OneDay应用隐私政策\n\n我们致力于保护您的隐私和个人信息安全。\n\n1. 信息收集\n我们仅收集为您提供服务所必需的信息。\n\n2. 信息使用\n您的数据仅用于改善学习体验。\n\n3. 信息共享\n我们不会与第三方分享您的个人信息。\n\n4. 数据安全\n我们采用业界标准的安全措施保护您的数据。\n\n如有疑问，请联系我们。';

  @override
  String get userAgreementTitle => '用户协议';

  @override
  String get userAgreementContent =>
      'OneDay应用用户协议\n\n欢迎使用OneDay学习应用！\n\n1. 服务内容\nOneDay为您提供学习时间管理、记忆训练等服务。\n\n2. 用户义务\n请合理使用应用功能，不得进行违法活动。\n\n3. 知识产权\n应用内容受知识产权法保护。\n\n4. 免责声明\n我们不对使用应用产生的结果承担责任。\n\n5. 协议变更\n我们保留修改本协议的权利。\n\n继续使用即表示同意本协议。';

  @override
  String get contactUsTitle => '联系我们';

  @override
  String get contactUsContent =>
      '感谢您使用OneDay！\n\n如果您有任何问题或建议，请通过以下方式联系我们：\n\n📧 邮箱：<EMAIL>\n💬 微信：OneDay_Support\n📱 QQ群：123456789\n\n我们会尽快回复您的问题。';

  @override
  String get rateAppTitle => '评价应用';

  @override
  String get rateAppContent =>
      '喜欢OneDay吗？ ⭐\n\n您的评价是我们前进的动力！\n请前往应用商店为我们评分和留言。\n\n谢谢您的支持！ 🙏';

  @override
  String get laterButton => '稍后再说';

  @override
  String get rateButton => '去评价';

  @override
  String get ok => '好的';

  @override
  String get close => '关闭';
}
