import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_zh.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('zh'),
  ];

  /// The title of the application
  ///
  /// In en, this message translates to:
  /// **'OneDay'**
  String get appTitle;

  /// The tagline displayed below the logo
  ///
  /// In en, this message translates to:
  /// **'Afraid of wasting a day? Use OneDay'**
  String get appTagline;

  /// Home tab label
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// Calendar tab label
  ///
  /// In en, this message translates to:
  /// **'Calendar'**
  String get calendar;

  /// Store tab label
  ///
  /// In en, this message translates to:
  /// **'Store'**
  String get store;

  /// Community tab label
  ///
  /// In en, this message translates to:
  /// **'Community'**
  String get community;

  /// Profile tab label
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// Settings page title
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// System preferences section title
  ///
  /// In en, this message translates to:
  /// **'System Preferences'**
  String get systemPreferences;

  /// Language setting label
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// Language selection dialog title
  ///
  /// In en, this message translates to:
  /// **'Select Language'**
  String get selectLanguage;

  /// Chinese Simplified language option
  ///
  /// In en, this message translates to:
  /// **'简体中文'**
  String get chineseSimplified;

  /// English language option
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// Notifications setting label
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// Notifications setting description
  ///
  /// In en, this message translates to:
  /// **'Receive study reminders and important messages'**
  String get notificationsDescription;

  /// Vocabulary highlight setting label
  ///
  /// In en, this message translates to:
  /// **'Vocabulary Highlight'**
  String get vocabularyHighlight;

  /// Vocabulary highlight setting description
  ///
  /// In en, this message translates to:
  /// **'Highlight graduate exam vocabulary in articles'**
  String get vocabularyHighlightDescription;

  /// Expert-level vocabulary highlight setting label
  ///
  /// In en, this message translates to:
  /// **'Expert-level Vocabulary Only'**
  String get expertOnlyHighlight;

  /// Expert-level vocabulary highlight setting description
  ///
  /// In en, this message translates to:
  /// **'Only highlight expert-level graduate exam vocabulary'**
  String get expertOnlyHighlightDescription;

  /// Data management section title
  ///
  /// In en, this message translates to:
  /// **'Data Management'**
  String get dataManagement;

  /// Auto backup setting label
  ///
  /// In en, this message translates to:
  /// **'Auto Backup'**
  String get autoBackup;

  /// Auto backup setting description
  ///
  /// In en, this message translates to:
  /// **'Regularly backup learning data to cloud'**
  String get autoBackupDescription;

  /// Export data setting label
  ///
  /// In en, this message translates to:
  /// **'Export Data'**
  String get exportData;

  /// Export data setting description
  ///
  /// In en, this message translates to:
  /// **'Export learning records and wage data'**
  String get exportDataDescription;

  /// Clear cache setting label
  ///
  /// In en, this message translates to:
  /// **'Clear Cache'**
  String get clearCache;

  /// Clear cache setting description
  ///
  /// In en, this message translates to:
  /// **'Clear temporary files to free storage space'**
  String get clearCacheDescription;

  /// Reset data setting label
  ///
  /// In en, this message translates to:
  /// **'Reset Data'**
  String get resetData;

  /// Reset data setting description
  ///
  /// In en, this message translates to:
  /// **'Clear all data and restore to initial state'**
  String get resetDataDescription;

  /// Privacy and security section title
  ///
  /// In en, this message translates to:
  /// **'Privacy & Security'**
  String get privacySecurity;

  /// Biometric unlock setting label
  ///
  /// In en, this message translates to:
  /// **'Biometric Unlock'**
  String get biometricUnlock;

  /// Biometric unlock setting description
  ///
  /// In en, this message translates to:
  /// **'Use fingerprint or face recognition to unlock app'**
  String get biometricUnlockDescription;

  /// Data analytics setting label
  ///
  /// In en, this message translates to:
  /// **'Data Analytics'**
  String get dataAnalytics;

  /// Data analytics setting description
  ///
  /// In en, this message translates to:
  /// **'Help improve app experience'**
  String get dataAnalyticsDescription;

  /// Privacy policy setting label
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Privacy policy setting description
  ///
  /// In en, this message translates to:
  /// **'Learn how we protect your privacy'**
  String get privacyPolicyDescription;

  /// About app section title
  ///
  /// In en, this message translates to:
  /// **'About App'**
  String get aboutApp;

  /// Version setting label
  ///
  /// In en, this message translates to:
  /// **'Version'**
  String get version;

  /// User agreement setting label
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get userAgreement;

  /// User agreement setting description
  ///
  /// In en, this message translates to:
  /// **'View terms of service and usage agreement'**
  String get userAgreementDescription;

  /// Contact us setting label
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUs;

  /// Contact us setting description
  ///
  /// In en, this message translates to:
  /// **'Feedback suggestions or get help'**
  String get contactUsDescription;

  /// Rate app setting label
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rateApp;

  /// Rate app setting description
  ///
  /// In en, this message translates to:
  /// **'Rate us in the app store'**
  String get rateAppDescription;

  /// New learning button label
  ///
  /// In en, this message translates to:
  /// **'New Learning'**
  String get newLearning;

  /// Data updated snackbar message
  ///
  /// In en, this message translates to:
  /// **'Data updated'**
  String get dataUpdated;

  /// Refresh failed error message
  ///
  /// In en, this message translates to:
  /// **'Refresh failed: {error}'**
  String refreshFailed(String error);

  /// Navigation instruction message
  ///
  /// In en, this message translates to:
  /// **'Please click the \"{tabName}\" tab in the bottom navigation'**
  String pleaseClickTab(String tabName);

  /// Time box creation success message
  ///
  /// In en, this message translates to:
  /// **'Learning time box created successfully!'**
  String get timeBoxCreated;

  /// Cancel button label
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// Save button label
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// Item saved message
  ///
  /// In en, this message translates to:
  /// **'{title} saved'**
  String saved(String title);

  /// Settings saved restart hint message
  ///
  /// In en, this message translates to:
  /// **'Settings saved, some features require app restart to take effect'**
  String get settingsSaved;

  /// Understood button label
  ///
  /// In en, this message translates to:
  /// **'Understood'**
  String get understood;

  /// Coming soon feature message
  ///
  /// In en, this message translates to:
  /// **'This feature is coming soon, stay tuned'**
  String get comingSoon;

  /// Vocabulary highlight settings saved message
  ///
  /// In en, this message translates to:
  /// **'Vocabulary highlight settings saved'**
  String get vocabularyHighlightSaved;

  /// Save settings failed error message
  ///
  /// In en, this message translates to:
  /// **'Failed to save settings: {error}'**
  String saveSettingsFailed(String error);

  /// Embodied memory training title
  ///
  /// In en, this message translates to:
  /// **'🧠 Embodied Memory Training'**
  String get embodiedMemoryTraining;

  /// Training mode selection instruction
  ///
  /// In en, this message translates to:
  /// **'Select memory training mode:'**
  String get selectTrainingMode;

  /// Focused training option title
  ///
  /// In en, this message translates to:
  /// **'🏋️ Focused Training'**
  String get focusedTraining;

  /// Focused training option description
  ///
  /// In en, this message translates to:
  /// **'Select words from graduate exam vocabulary for 30-60 minutes of embodied memory training'**
  String get focusedTrainingDescription;

  /// Action library management option title
  ///
  /// In en, this message translates to:
  /// **'📚 Action Library Management'**
  String get actionLibraryManagement;

  /// Action library management option description
  ///
  /// In en, this message translates to:
  /// **'Manage custom action libraries and integrate with existing PAO system'**
  String get actionLibraryDescription;

  /// Privacy policy dialog title
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicyTitle;

  /// Privacy policy dialog content
  ///
  /// In en, this message translates to:
  /// **'OneDay App Privacy Policy\n\nWe are committed to protecting your privacy and personal information security.\n\n1. Information Collection\nWe only collect information necessary to provide you with services.\n\n2. Information Use\nYour data is only used to improve learning experience.\n\n3. Information Sharing\nWe do not share your personal information with third parties.\n\n4. Data Security\nWe use industry-standard security measures to protect your data.\n\nIf you have any questions, please contact us.'**
  String get privacyPolicyContent;

  /// User agreement dialog title
  ///
  /// In en, this message translates to:
  /// **'User Agreement'**
  String get userAgreementTitle;

  /// User agreement dialog content
  ///
  /// In en, this message translates to:
  /// **'OneDay App User Agreement\n\nWelcome to OneDay learning app!\n\n1. Service Content\nOneDay provides learning time management, memory training and other services.\n\n2. User Obligations\nPlease use app features reasonably and do not engage in illegal activities.\n\n3. Intellectual Property\nApp content is protected by intellectual property law.\n\n4. Disclaimer\nWe are not responsible for results from using the app.\n\n5. Agreement Changes\nWe reserve the right to modify this agreement.\n\nContinued use indicates agreement to this agreement.'**
  String get userAgreementContent;

  /// Contact us dialog title
  ///
  /// In en, this message translates to:
  /// **'Contact Us'**
  String get contactUsTitle;

  /// Contact us dialog content
  ///
  /// In en, this message translates to:
  /// **'Thank you for using OneDay!\n\nIf you have any questions or suggestions, please contact us through:\n\n📧 Email: <EMAIL>\n💬 WeChat: OneDay_Support\n📱 QQ Group: 123456789\n\nWe will reply to your questions as soon as possible.'**
  String get contactUsContent;

  /// Rate app dialog title
  ///
  /// In en, this message translates to:
  /// **'Rate App'**
  String get rateAppTitle;

  /// Rate app dialog content
  ///
  /// In en, this message translates to:
  /// **'Like OneDay? ⭐\n\nYour rating is our motivation to move forward!\nPlease go to the app store to rate and comment for us.\n\nThank you for your support! 🙏'**
  String get rateAppContent;

  /// Later button label
  ///
  /// In en, this message translates to:
  /// **'Later'**
  String get laterButton;

  /// Rate button label
  ///
  /// In en, this message translates to:
  /// **'Rate'**
  String get rateButton;

  /// OK button label
  ///
  /// In en, this message translates to:
  /// **'OK'**
  String get ok;

  /// Close button label
  ///
  /// In en, this message translates to:
  /// **'Close'**
  String get close;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['en', 'zh'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en':
      return AppLocalizationsEn();
    case 'zh':
      return AppLocalizationsZh();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
