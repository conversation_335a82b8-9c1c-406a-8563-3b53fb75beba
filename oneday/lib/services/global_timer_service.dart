import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../features/time_box/models/timebox_models.dart';
import '../features/study_time/services/study_time_statistics_service.dart';
import 'floating_timer_service.dart';
import '../features/achievement/services/achievement_trigger_service.dart';
import 'study_session_completion_service.dart';

/// 全局计时器状态管理服务
///
/// 负责管理应用级别的计时器状态，确保：
/// 1. 页面导航时的计时器状态一致性
/// 2. 主计时器与浮动窗口的时间同步
/// 3. 计时器状态的持久化和恢复
class GlobalTimerService extends ChangeNotifier {
  static final GlobalTimerService _instance = GlobalTimerService._internal();
  factory GlobalTimerService() => _instance;
  GlobalTimerService._internal();

  // 计时器状态
  TimeBoxTask? _currentTask;
  int _totalDurationSeconds = 0;
  int _remainingSeconds = 0;
  bool _isTimerRunning = false;
  DateTime? _timerStartTime;
  Timer? _masterTimer;

  // 番茄钟状态
  PomodoroTimerState _pomodoroState = PomodoroTimerState.stopped;
  static const int _restDurationSeconds = 5 * 60; // 5分钟休息时间

  // 浮动计时器服务
  final FloatingTimerService _floatingTimerService = FloatingTimerService();

  // 学习时间统计服务
  StudyTimeStatisticsService? _studyTimeStatisticsService;

  // 成就触发器服务
  AchievementTriggerService? _achievementTriggerService;

  // 学习会话完成服务
  StudySessionCompletionService? _studySessionCompletionService;

  // 页面导航回调
  VoidCallback? _onNavigateToTimer;

  // 任务更新回调
  Function(TimeBoxTask)? _onTaskUpdated;

  // 休息完成回调
  VoidCallback? _onRestCompleted;

  // 最后完成的任务（用于休息完成后显示任务完成对话框）
  TimeBoxTask? _lastCompletedTask;

  // 监听器列表（用于安全管理）(暂未使用)
  // final List<VoidCallback> _listeners = [];

  // Getters
  TimeBoxTask? get currentTask => _currentTask;
  TimeBoxTask? get lastCompletedTask => _lastCompletedTask;
  int get totalDurationSeconds => _totalDurationSeconds;
  int get remainingSeconds => _remainingSeconds;
  bool get isTimerRunning => _isTimerRunning;
  DateTime? get timerStartTime => _timerStartTime;
  bool get hasActiveTimer => _currentTask != null && _remainingSeconds > 0;
  PomodoroTimerState get pomodoroState => _pomodoroState;
  bool get isWorkTime => _pomodoroState == PomodoroTimerState.work;
  bool get isRestTime => _pomodoroState == PomodoroTimerState.rest;

  /// 设置页面导航回调
  void setNavigationCallback(VoidCallback callback) {
    _onNavigateToTimer = callback;
  }

  /// 设置任务更新回调
  void setTaskUpdateCallback(Function(TimeBoxTask) callback) {
    _onTaskUpdated = callback;
  }

  /// 设置休息完成回调
  void setRestCompletedCallback(VoidCallback callback) {
    _onRestCompleted = callback;
  }

  /// 设置学习时间统计服务
  void setStudyTimeStatisticsService(StudyTimeStatisticsService service) {
    _studyTimeStatisticsService = service;
  }

  /// 设置成就触发器服务
  void setAchievementTriggerService(AchievementTriggerService service) {
    _achievementTriggerService = service;
  }

  /// 设置学习会话完成服务
  void setStudySessionCompletionService(StudySessionCompletionService service) {
    _studySessionCompletionService = service;
  }

  /// 启动计时器
  Future<void> startTimer(TimeBoxTask task) async {
    print('🚀 全局计时器服务：启动计时器 - ${task.title}');

    try {
      // 停止现有计时器
      print('🛑 停止现有计时器...');
      await stopTimer();
      print('✅ 现有计时器已停止');

      // 设置新的计时器状态
      print('⚙️ 设置新的计时器状态...');
      _currentTask = task;
      _totalDurationSeconds = task.plannedMinutes * 60;
      _remainingSeconds = _totalDurationSeconds;
      _isTimerRunning = true;
      _timerStartTime = DateTime.now();
      _pomodoroState = PomodoroTimerState.work; // 开始工作时间
      print('✅ 计时器状态设置完成');

      // 更新任务状态（创建新的不可变实例）
      print('📝 更新任务状态...');
      final updatedTask = task.copyWith(
        status: TaskStatus.inProgress,
        startTime: task.startTime ?? _timerStartTime,
      );
      _currentTask = updatedTask;
      print('✅ 任务状态更新完成');

      // 通知任务更新
      print('📢 通知任务更新...');
      try {
        _onTaskUpdated?.call(updatedTask);
        print('✅ 任务更新通知完成');
      } catch (e) {
        print('⚠️ 任务更新通知失败: $e');
      }

      // 触发学习会话开始成就
      print('🏆 触发学习会话开始成就...');
      try {
        _achievementTriggerService?.onStudySessionStart();
        print('✅ 学习会话开始成就触发完成');
      } catch (e) {
        print('⚠️ 学习会话开始成就触发失败: $e');
      }

      // 启动主计时器
      print('⏰ 启动主计时器...');
      _startMasterTimer();
      print('✅ 主计时器启动完成');

      // 通知监听器
      print('📢 通知监听器...');
      try {
        notifyListeners();
        print('✅ 监听器通知完成');
      } catch (e) {
        print('⚠️ 监听器通知失败: $e');
      }

      // 触觉反馈
      try {
        HapticFeedback.lightImpact();
      } catch (e) {
        print('⚠️ 触觉反馈失败: $e');
      }

      print('🎉 计时器启动完全成功: ${task.title}');
    } catch (e, stackTrace) {
      print('❌ 启动计时器过程中发生异常: $e');
      print('Stack trace: $stackTrace');

      // 清理状态以防止不一致
      _isTimerRunning = false;
      _stopMasterTimer();
      _pomodoroState = PomodoroTimerState.stopped;

      // 重新抛出异常让调用者处理
      rethrow;
    }
  }

  /// 启动休息时间
  Future<void> startRestTimer() async {
    print('🧘 全局计时器服务：启动休息时间');

    // 设置休息时间状态
    _totalDurationSeconds = _restDurationSeconds;
    _remainingSeconds = _restDurationSeconds;
    _isTimerRunning = true;
    _timerStartTime = DateTime.now();
    _pomodoroState = PomodoroTimerState.rest;

    // 启动主计时器
    _startMasterTimer();

    // 通知监听器
    notifyListeners();

    HapticFeedback.lightImpact();
  }

  /// 启动主计时器（统一时间源）
  void _startMasterTimer() {
    _stopMasterTimer();

    print('⏰ 启动主计时器 - 总时长: $_totalDurationSeconds 秒');

    _masterTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!_isTimerRunning || _timerStartTime == null) {
        return;
      }

      final now = DateTime.now();
      final elapsedSeconds = now.difference(_timerStartTime!).inSeconds;
      final newRemainingSeconds = (_totalDurationSeconds - elapsedSeconds)
          .clamp(0, _totalDurationSeconds);

      if (newRemainingSeconds != _remainingSeconds) {
        _remainingSeconds = newRemainingSeconds;
        print('⏰ 主计时器更新: 剩余 $_remainingSeconds 秒');

        // 同步更新浮动计时器（使用相同的时间源）
        if (_floatingTimerService.isVisible) {
          String displayTitle;
          if (_pomodoroState == PomodoroTimerState.work &&
              _currentTask != null) {
            displayTitle = _currentTask!.title;
          } else if (_pomodoroState == PomodoroTimerState.rest) {
            // 休息时间只传递状态标识，让浮动计时器服务处理具体显示格式
            displayTitle = '休息中';
          } else {
            displayTitle = '计时器';
          }

          _floatingTimerService.syncWithMasterTimer(
            taskTitle: displayTitle,
            remainingSeconds: _remainingSeconds,
            totalDurationSeconds: _totalDurationSeconds,
            isTimerRunning: _isTimerRunning,
            timerStartTime: _timerStartTime!,
            isRestMode: _pomodoroState == PomodoroTimerState.rest,
          );
        }

        // 安全通知监听器
        _safeNotifyListeners();

        // 检查是否完成
        if (_remainingSeconds <= 0) {
          print('✅ 主计时器完成');
          _completeTimer();
        }
      }
    });
  }

  /// 停止主计时器
  void _stopMasterTimer() {
    _masterTimer?.cancel();
    _masterTimer = null;
  }

  /// 暂停计时器
  void pauseTimer() {
    if (!_isTimerRunning) return;

    print('⏸️ 全局计时器服务：暂停计时器');
    _isTimerRunning = false;
    _stopMasterTimer();

    // 同步暂停浮动计时器
    _floatingTimerService.pauseTimer();

    notifyListeners();
    HapticFeedback.lightImpact();
  }

  /// 恢复计时器
  void resumeTimer() {
    if (_currentTask == null || _remainingSeconds <= 0 || _isTimerRunning) {
      return;
    }

    print('▶️ 全局计时器服务：恢复计时器');
    _isTimerRunning = true;

    // 重新计算开始时间，基于当前剩余时间
    _timerStartTime = DateTime.now().subtract(
      Duration(seconds: _totalDurationSeconds - _remainingSeconds),
    );

    // 启动主计时器
    _startMasterTimer();

    // 同步恢复浮动计时器
    _floatingTimerService.resumeTimer();

    notifyListeners();
    HapticFeedback.lightImpact();
  }

  /// 停止计时器
  Future<void> stopTimer() async {
    if (_currentTask == null && _pomodoroState == PomodoroTimerState.stopped) {
      return;
    }

    print('🛑 全局计时器服务：停止计时器');

    _isTimerRunning = false;
    _stopMasterTimer();
    _pomodoroState = PomodoroTimerState.stopped;

    // 关闭浮动窗口
    _floatingTimerService.hideFloatingTimer();

    // 清理状态
    _currentTask = null;
    _remainingSeconds = 0;
    _totalDurationSeconds = 0;
    _timerStartTime = null;

    notifyListeners();
    HapticFeedback.lightImpact();
  }

  /// 提前结束休息时间
  Future<void> skipRestTime() async {
    if (_pomodoroState != PomodoroTimerState.rest) {
      print('⚠️ 当前不在休息状态，无法跳过休息时间');
      return;
    }

    print('⏭️ 全局计时器服务：提前结束休息时间');

    _isTimerRunning = false;
    _stopMasterTimer();
    _pomodoroState = PomodoroTimerState.stopped;

    // 关闭浮动窗口
    _floatingTimerService.hideFloatingTimer();

    // 触发休息完成回调（在清理状态之前调用）
    _onRestCompleted?.call();

    // 清理状态
    _currentTask = null;
    _remainingSeconds = 0;
    _totalDurationSeconds = 0;
    _timerStartTime = null;

    notifyListeners();
    HapticFeedback.lightImpact();
  }

  /// 完成计时器
  void _completeTimer() {
    print('🔍 _completeTimer 被调用，当前状态: $_pomodoroState');

    if (_pomodoroState == PomodoroTimerState.work) {
      // 工作时间结束，自动进入休息时间
      print('✅ 工作时间完成，自动进入休息时间');

      if (_currentTask != null) {
        final completedTask = _currentTask!.copyWith(
          status: TaskStatus.completed,
          endTime: DateTime.now(),
        );
        _currentTask = completedTask;
        _lastCompletedTask = completedTask; // 保存最后完成的任务

        // 通知任务更新
        _onTaskUpdated?.call(completedTask);

        // 使用统一的学习会话完成服务处理所有数据同步
        _studySessionCompletionService
            ?.handleSessionCompletion(completedTask)
            .then((result) {
              if (result.isSuccess) {
                print('✅ 学习会话完成处理成功: ${result.updateSummary}');
              } else {
                print('❌ 学习会话完成处理失败: ${result.error}');
              }
            })
            .catchError((error) {
              print('❌ 学习会话完成处理异常: $error');
            });

        // 保持向后兼容的单独调用（作为备用）
        _studyTimeStatisticsService?.onTaskCompleted(completedTask);
        _achievementTriggerService?.onStudySessionEnd(
          completedTask.plannedMinutes,
        );
        print('📊 已处理学习会话完成: ${completedTask.title}');
      }

      // 停止当前计时器
      _isTimerRunning = false;
      _stopMasterTimer();

      // 自动启动休息时间
      print('🔄 准备启动休息时间...');
      _startRestTimerInternal();
      print('🔄 休息时间启动调用完成');
    } else if (_pomodoroState == PomodoroTimerState.rest) {
      // 休息时间结束
      print('✅ 休息时间完成');

      _isTimerRunning = false;
      _stopMasterTimer();
      _pomodoroState = PomodoroTimerState.stopped;

      // 关闭浮动窗口
      _floatingTimerService.hideFloatingTimer();

      // 清理状态
      _currentTask = null;
      _remainingSeconds = 0;
      _totalDurationSeconds = 0;
      _timerStartTime = null;

      // 通知休息完成
      _onRestCompleted?.call();

      notifyListeners();
      HapticFeedback.heavyImpact();
    } else {
      // 普通停止
      _isTimerRunning = false;
      _stopMasterTimer();
      _pomodoroState = PomodoroTimerState.stopped;

      notifyListeners();
      HapticFeedback.heavyImpact();
    }
  }

  /// 内部启动休息时间（不重复调用异步方法）
  void _startRestTimerInternal() {
    print('🧘 全局计时器服务：启动休息时间（内部）');

    // 设置休息时间状态
    _totalDurationSeconds = _restDurationSeconds;
    _remainingSeconds = _restDurationSeconds;
    _isTimerRunning = true;
    _timerStartTime = DateTime.now();
    _pomodoroState = PomodoroTimerState.rest;

    // 如果浮动窗口正在显示，立即更新它
    if (_floatingTimerService.isVisible) {
      print('🔄 更新浮动窗口为休息状态');
      // 休息时间只传递状态标识，让浮动计时器服务处理具体显示格式
      _floatingTimerService.syncWithMasterTimer(
        taskTitle: '休息中',
        remainingSeconds: _remainingSeconds,
        totalDurationSeconds: _totalDurationSeconds,
        isTimerRunning: _isTimerRunning,
        timerStartTime: _timerStartTime!,
        isRestMode: true,
      );
    } else {
      print('⚠️ 浮动窗口未显示，休息时间无法在浮动窗口中显示');
    }

    // 启动主计时器
    _startMasterTimer();

    // 安全通知监听器
    _safeNotifyListeners();

    HapticFeedback.lightImpact();
  }

  /// 显示浮动窗口
  Future<void> showFloatingTimer(BuildContext context) async {
    if (_currentTask == null && _pomodoroState == PomodoroTimerState.stopped) {
      return;
    }

    String displayTitle;
    bool isRestMode = false;
    if (_pomodoroState == PomodoroTimerState.work && _currentTask != null) {
      displayTitle = _currentTask!.title;
    } else if (_pomodoroState == PomodoroTimerState.rest) {
      // 休息时间只传递状态标识，让浮动计时器服务处理具体显示格式
      displayTitle = '休息中';
      isRestMode = true;
    } else {
      displayTitle = '计时器';
    }

    await _floatingTimerService.showFloatingTimer(
      context: context,
      taskTitle: displayTitle,
      remainingSeconds: _remainingSeconds,
      totalDurationSeconds: _totalDurationSeconds,
      isTimerRunning: _isTimerRunning,
      isRestMode: isRestMode,
      onClose: () {
        // 浮动窗口关闭时不停止主计时器，只是隐藏窗口
        print('🔔 浮动窗口已关闭');
      },
    );
  }

  /// 隐藏浮动窗口
  void hideFloatingTimer() {
    _floatingTimerService.hideFloatingTimer();
  }

  /// 检查并恢复计时器状态（用于页面导航）
  void checkAndRestoreTimerState() {
    if (hasActiveTimer) {
      print('🔄 检测到活跃计时器，准备导航到计时页面');
      _onNavigateToTimer?.call();
    }
  }

  /// 格式化时间显示
  String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final secs = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
  }

  /// 安全通知监听器（避免已销毁组件的setState错误）
  void _safeNotifyListeners() {
    try {
      notifyListeners();
    } catch (e) {
      print('⚠️ 通知监听器时出错: $e');
      // 清理可能已经销毁的监听器
      _cleanupInvalidListeners();
    }
  }

  /// 清理无效的监听器
  void _cleanupInvalidListeners() {
    // 这里可以添加更复杂的监听器清理逻辑
    // 目前只是记录错误，让Flutter自动处理
    print('🧹 清理无效监听器');
  }

  /// 清理资源
  @override
  void dispose() {
    _stopMasterTimer();
    super.dispose();
  }
}
