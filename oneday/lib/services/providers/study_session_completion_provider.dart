import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../study_session_completion_service.dart';
import '../../features/study_time/providers/study_time_providers.dart';
import '../../features/achievement/providers/achievement_provider.dart';
import '../../features/daily_plan/notifiers/daily_plan_notifier.dart';

/// 学习会话完成服务Provider
final studySessionCompletionServiceProvider = Provider<StudySessionCompletionService>((ref) {
  final studyTimeService = ref.watch(studyTimeStatisticsServiceProvider);
  final achievementTriggerService = ref.watch(achievementTriggerServiceProvider);
  final dailyPlanNotifier = ref.watch(dailyPlanProvider.notifier);
  
  return StudySessionCompletionService(
    studyTimeService: studyTimeService,
    achievementService: achievementTriggerService,
    dailyPlanNotifier: dailyPlanNotifier,
  );
});

/// 今日学习统计摘要Provider
final todayStudySummaryProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final service = ref.watch(studySessionCompletionServiceProvider);
  return await service.getTodayStudySummary();
});
