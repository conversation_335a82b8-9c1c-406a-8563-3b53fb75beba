#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to transform the vocabulary.json file from Chinese field names to optimized English structure
for the OneDay Flutter application.
"""

import json
import re
from typing import Dict, List, Any

def categorize_word_by_frequency(frequency: int) -> str:
    """Categorize word based on frequency."""
    if frequency >= 10000:
        return "basic"
    elif frequency >= 1000:
        return "intermediate"
    elif frequency >= 100:
        return "advanced"
    else:
        return "academic"

def determine_difficulty(frequency: int, word_length: int) -> str:
    """Determine difficulty level based on frequency and word length."""
    if frequency >= 5000:
        return "beginner"
    elif frequency >= 1000:
        return "intermediate"
    elif frequency >= 200:
        return "advanced"
    else:
        return "expert"

def determine_priority(frequency: int) -> str:
    """Determine priority based on frequency."""
    if frequency >= 5000:
        return "high"
    elif frequency >= 1000:
        return "medium"
    else:
        return "low"

def guess_part_of_speech(word: str, definition: str) -> str:
    """Basic part of speech detection based on word patterns and definition."""
    word_lower = word.lower()
    
    # Common patterns
    if word_lower in ['the', 'a', 'an']:
        return "article"
    elif word_lower in ['and', 'or', 'but', 'because', 'since', 'however', 'although']:
        return "conjunction"
    elif word_lower in ['in', 'on', 'at', 'by', 'for', 'with', 'from', 'to', 'of', 'about', 'through', 'between', 'among', 'against']:
        return "preposition"
    elif word_lower in ['i', 'you', 'he', 'she', 'it', 'we', 'they', 'this', 'that', 'these', 'those', 'what', 'who', 'which']:
        return "pronoun"
    elif word.endswith('ly'):
        return "adverb"
    elif word.endswith(('ing', 'ed', 'en')) or word_lower in ['be', 'have', 'do', 'will', 'can', 'should', 'would', 'could', 'may', 'might', 'must']:
        return "verb"
    elif word.endswith(('tion', 'sion', 'ness', 'ment', 'ity', 'ty', 'er', 'or', 'ist', 'ism')):
        return "noun"
    elif word.endswith(('ful', 'less', 'ous', 'ive', 'able', 'ible', 'al', 'ic', 'ed', 'ing')):
        return "adjective"
    else:
        # Default based on definition patterns
        if any(keyword in definition for keyword in ['v.', '动词', '做', '进行']):
            return "verb"
        elif any(keyword in definition for keyword in ['n.', '名词', 'adj.', '形容词']):
            return "noun" if 'n.' in definition else "adjective"
        elif any(keyword in definition for keyword in ['adv.', '副词']):
            return "adverb"
        else:
            return "noun"  # Default fallback

def generate_tags(word: str, definition: str, frequency: int, part_of_speech: str) -> List[str]:
    """Generate relevant tags for the word."""
    tags = []
    
    # Frequency-based tags
    if frequency >= 10000:
        tags.append("common")
    elif frequency >= 1000:
        tags.append("frequent")
    else:
        tags.append("rare")
    
    # Part of speech tag
    tags.append(part_of_speech)
    
    # Special tags based on word characteristics
    if len(word) <= 3:
        tags.append("short")
    elif len(word) >= 10:
        tags.append("long")
    
    # Academic/technical words
    if any(suffix in word for suffix in ['tion', 'sion', 'ology', 'ism', 'ity']):
        tags.append("academic")
    
    # Basic function words
    if part_of_speech in ['article', 'preposition', 'conjunction', 'pronoun']:
        tags.append("function-word")
    
    return tags

def transform_vocabulary_entry(entry: Dict[str, Any], index: int) -> Dict[str, Any]:
    """Transform a single vocabulary entry from Chinese to English structure."""
    word = entry.get("单词", "")
    definition = entry.get("释义", "")
    frequency = entry.get("词频", 0)
    alternative_spelling = entry.get("其他拼写")
    
    # Determine various attributes
    category = categorize_word_by_frequency(frequency)
    difficulty = determine_difficulty(frequency, len(word))
    priority = determine_priority(frequency)
    part_of_speech = guess_part_of_speech(word, definition)
    tags = generate_tags(word, definition, frequency, part_of_speech)
    
    # Handle alternative spellings
    alternative_spellings = []
    if alternative_spelling and alternative_spelling != "null":
        alternative_spellings = [alternative_spelling]
    
    return {
        "id": index,
        "word": word,
        "definition": definition,
        "frequency": frequency,
        "difficulty": difficulty,
        "category": category,
        "alternativeSpellings": alternative_spellings,
        "partOfSpeech": part_of_speech,
        "examples": [],  # Can be populated later
        "tags": tags,
        "priority": priority
    }

def transform_vocabulary_file(input_file: str, output_file: str):
    """Transform the entire vocabulary file."""
    print(f"Loading vocabulary from {input_file}...")
    
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Extract the vocabulary list
    vocab_list = data.get("5530考研词汇词频排序表", [])
    print(f"Found {len(vocab_list)} vocabulary entries")
    
    # Transform each entry
    transformed_vocab_dict = {}
    all_pos = set()
    for i, entry in enumerate(vocab_list, 1):
        transformed_entry = transform_vocabulary_entry(entry, i)
        word = transformed_entry.pop("word") # Use word as key, and remove it from the value
        transformed_vocab_dict[word] = transformed_entry
        all_pos.add(transformed_entry["partOfSpeech"])
        
        if i % 500 == 0:
            print(f"Processed {i}/{len(vocab_list)} entries...")
    
    # Create the new structure
    new_structure = {
        "metadata": {
            "version": "2.0",
            "source": "Graduate Entrance Exam Vocabulary",
            "totalWords": len(transformed_vocab_dict),
            "description": "Optimized vocabulary data for OneDay Flutter application",
            "lastUpdated": "2025-07-07",
            "categories": ["basic", "intermediate", "advanced", "academic"],
            "difficultyLevels": ["beginner", "intermediate", "advanced", "expert"],
            "priorityLevels": ["high", "medium", "low"],
            "partsOfSpeech": sorted(list(all_pos))
        },
        "words": transformed_vocab_dict
    }
    
    # Save the transformed data
    print(f"Saving transformed vocabulary to {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(new_structure, f, ensure_ascii=False, indent=2)
    
    print(f"✅ Successfully transformed {len(transformed_vocab_dict)} vocabulary entries!")
    print(f"📊 Statistics:")
    print(f"   - Categories: {len(new_structure['metadata']['categories'])}")
    print(f"   - Difficulty levels: {len(new_structure['metadata']['difficultyLevels'])}")
    print(f"   - Parts of speech: {len(new_structure['metadata']['partsOfSpeech'])}")

if __name__ == "__main__":
    import sys
    import os
    
    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    project_dir = os.path.dirname(script_dir)
    
    input_file = os.path.join(project_dir, "assets", "data", "vocabulary_original.json")
    output_file = os.path.join(project_dir, "assets", "data", "vocabulary.json")
    
    if len(sys.argv) > 1:
        input_file = sys.argv[1]
    if len(sys.argv) > 2:
        output_file = sys.argv[2]
    
    if not os.path.exists(input_file):
        print(f"❌ Input file not found: {input_file}")
        print("Please ensure the original vocabulary file exists.")
        sys.exit(1)
    
    transform_vocabulary_file(input_file, output_file)
