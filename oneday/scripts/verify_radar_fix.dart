#!/usr/bin/env dart

import 'dart:io';

/// 验证雷达图修复效果的脚本
void main() {
  print('🎯 OneDay应用雷达图修复验证');
  print('=' * 50);
  
  _checkRadarChartFile();
  _checkRouterConfiguration();
  _checkTestButton();
  _printVerificationSteps();
  _printSummary();
}

/// 检查雷达图文件的修复
void _checkRadarChartFile() {
  print('\n📋 检查雷达图修复...');
  
  final radarFile = File('lib/features/ability_radar/widgets/ability_radar_chart.dart');
  if (!radarFile.existsSync()) {
    print('❌ 雷达图文件不存在');
    return;
  }
  
  final content = radarFile.readAsStringSync();
  
  // 检查关键修复点
  final fixes = [
    {
      'name': '轴线长度控制',
      'pattern': 'titlePositionPercentageOffset:\\s*0\\.05',
      'description': '标题位置偏移调整为0.05，确保轴线端点位于圆周边界'
    },
    {
      'name': '透明边框配置',
      'pattern': 'borderColor: Colors\\.transparent',
      'description': '最大值数据集边框设为透明，仅用于范围控制'
    },
    {
      'name': '100分映射注释',
      'pattern': '100分精确映射到最外层圆周边界',
      'description': '包含详细的修复说明注释'
    },
  ];
  
  for (final fix in fixes) {
    final regex = RegExp(fix['pattern']!);
    if (regex.hasMatch(content)) {
      print('  ✅ ${fix['name']}: ${fix['description']}');
    } else {
      print('  ❌ ${fix['name']}: 修复未找到');
    }
  }
}

/// 检查路由配置
void _checkRouterConfiguration() {
  print('\n📋 检查路由配置...');
  
  final routerFile = File('lib/router/app_router.dart');
  if (!routerFile.existsSync()) {
    print('❌ 路由配置文件不存在');
    return;
  }
  
  final content = routerFile.readAsStringSync();
  
  // 检查雷达图路由
  if (content.contains('abilityRadar = \'/ability-radar\'')) {
    print('  ✅ 雷达图路由常量已配置');
  } else {
    print('  ❌ 缺少雷达图路由常量');
  }
  
  if (content.contains('path: abilityRadar')) {
    print('  ✅ 雷达图路由已配置');
  } else {
    print('  ❌ 缺少雷达图路由配置');
  }
  
  if (content.contains('AbilityRadarPage')) {
    print('  ✅ 雷达图页面已导入');
  } else {
    print('  ❌ 缺少雷达图页面导入');
  }
}

/// 检查测试按钮
void _checkTestButton() {
  print('\n📋 检查测试按钮...');
  
  final homeFile = File('lib/features/home/<USER>');
  if (!homeFile.existsSync()) {
    print('❌ 首页文件不存在');
    return;
  }
  
  final content = homeFile.readAsStringSync();
  
  if (content.contains('🎯 测试雷达图修复效果')) {
    print('  ✅ 调试测试按钮已添加');
  } else {
    print('  ❌ 缺少调试测试按钮');
  }
  
  if (content.contains('context.push(\'/ability-radar\')')) {
    print('  ✅ 测试按钮导航已配置');
  } else {
    print('  ❌ 测试按钮导航未配置');
  }
}

/// 打印验证步骤
void _printVerificationSteps() {
  print('\n🧪 手动验证步骤:');
  print('  1. 启动OneDay应用: flutter run');
  print('  2. 在首页向下滚动到"🔧 调试工具"区域');
  print('  3. 点击"🎯 测试雷达图修复效果"按钮');
  print('  4. 验证以下修复效果:');
  print('     • 轴线端点位于最外层圆周边界（不超出）');
  print('     • 100分数据点精确显示在圆周边界上');
  print('     • 五边形顶点与轴线端点完美对齐');
  print('     • 整体视觉效果精确专业');
  
  print('\n🔄 替代验证方法:');
  print('  • 通过个人资料页面 → 能力雷达图');
  print('  • 直接路由访问: /ability-radar');
}

/// 打印修复总结
void _printSummary() {
  print('\n📊 修复总结:');
  print('  🎯 问题: 雷达图轴线、数据映射、五边形对齐问题');
  print('  🔧 方案: 调整titlePositionPercentageOffset和数据集配置');
  print('  ✨ 效果: 轴线、数据点、五边形完美对齐');
  print('  🧪 验证: 通过调试按钮或正常导航验证');
  print('  📈 结果: 视觉效果更加精确和专业');
  
  print('\n🎉 修复完成！请按照验证步骤测试效果。');
}
