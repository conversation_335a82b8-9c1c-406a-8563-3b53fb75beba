# OneDay 功能实现文档

本文档记录 OneDay 应用的核心功能实现和技术方案。

## 📋 目录

- [拖拽交互系统](#拖拽交互系统)
- [TimeBox 计时功能](#timebox-计时功能)
- [图表可视化系统](#图表可视化系统)
- [成就系统](#成就系统)
- [记忆宫殿功能](#记忆宫殿功能)
- [学习统计功能](#学习统计功能)

---

## 🎯 拖拽交互系统

### 功能概述
实现了记忆宫殿中知识点气泡的精确拖拽定位系统，支持横屏/竖屏自适应。

### 核心技术实现

#### 1. 坐标系统设计
- **图片坐标系**：基于图片原始尺寸的相对坐标 (0.0-1.0)
- **屏幕坐标系**：设备屏幕的绝对像素坐标
- **变换矩阵**：处理缩放、平移、旋转的坐标转换

#### 2. 拖拽定位算法
```dart
// 核心定位逻辑
final dotGlobalPosition = Offset(
  correctedX,
  correctedY + connectionLineLength,
);

// FractionalTranslation 精确偏移
return FractionalTranslation(
  translation: Offset(-0.5, yOffset),
  child: Transform.scale(scale: inverseScale, child: child),
);
```

#### 3. 关键修复点
- **横屏适配**：解决横屏模式下的坐标偏移问题
- **定位圆点对齐**：确保拖拽圆点与显示圆点像素级重叠
- **连接线长度调整**：拖拽时连接线从8px延长到70px，避免手指遮挡
- **边界检测**：防止拖拽超出图片范围
- **偏移计算统一**：修复拖拽更新逻辑，使用与显示逻辑一致的计算公式
- **文本框拖拽设计**：将拖拽检测区域从定位圆点移动到文本框，解决手指遮挡问题

#### 4. 核心算法修复
```dart
// 统一偏移计算公式（关键修复）
yOffsetRatio = -1.0 - adjustment; // 与显示逻辑完全一致

// 文本框拖拽坐标转换
void _onTempBubbleDragUpdate(Offset textBoxGlobalPosition) {
  final connectionLineLength = 70.0 * inverseScale;
  final dotGlobalPosition = Offset(
    textBoxGlobalPosition.dx,
    textBoxGlobalPosition.dy + connectionLineLength,
  );
  final imagePosition = _convertGlobalToImagePosition(dotGlobalPosition);
  _tapPosition = Vector3(imagePosition.dx, imagePosition.dy, 0);
}
```

### 测试验证
- 支持多种图片比例（1:1, 16:9, 4:3等）
- 横屏/竖屏切换测试通过
- 缩放状态下拖拽精度验证

---

## ⏰ TimeBox 计时功能

### 功能概述
番茄钟计时系统，支持工作/休息周期管理，浮动计时器，休息时间学习功能。

### 核心功能模块

#### 1. 浮动计时器
- **尺寸**：100x40px 紧凑设计
- **透明度**：60-70% 半透明效果
- **系统级浮动**：支持跨应用显示
- **状态同步**：与主界面实时同步

#### 2. 休息时间学习
- **词汇学习**：从个人词汇库随机选择单词
- **动觉记忆训练**：结合PAO记忆法的动作训练
- **提前结束**：支持用户主动跳过休息时间

#### 3. 任务管理
- **左滑手势**：WeChat风格的编辑/删除操作
- **任务分类**：用户自定义分类系统
- **进度追踪**：任务完成状态和时间统计

### 技术实现
```dart
// 浮动计时器组件
Container(
  width: 100,
  height: 40,
  decoration: BoxDecoration(
    color: Colors.black.withOpacity(0.7),
    borderRadius: BorderRadius.circular(20),
  ),
  child: Center(
    child: Text(
      _formatTime(_remainingTime),
      style: TextStyle(color: Colors.white, fontSize: 14),
    ),
  ),
)
```

---

## 📊 图表可视化系统

### 功能概述
学习数据的可视化展示，包括雷达图、ROI图表等多种图表类型。

### 核心图表组件

#### 1. 能力雷达图
- **五边形设计**：覆盖5个学习维度
- **网格系统**：Mondrian风格的几何网格
- **数据映射**：学习数据到视觉元素的映射

#### 2. ROI学习效率图
- **Y轴修复**：解决数据显示范围问题
- **动态缩放**：根据数据范围自动调整
- **趋势分析**：学习效率变化趋势可视化

#### 3. 日历热力图
- **时间块显示**：基于任务时长的动态高度
- **颜色编码**：四色分类系统
- **完成状态**：透明度表示任务完成情况

### 设计规范
- **配色方案**：OneDay原始配色（红#E03E3E, 黄#FFD700, 绿#0F7B6C, 蓝#2E7EED）
- **网格线条**：2-3px黑色线条，Mondrian几何风格
- **响应式设计**：支持不同屏幕尺寸自适应

---

## 🏆 成就系统

### 功能概述
用户学习成就的解锁和展示系统，提供学习动机和成就感。

### 核心功能
- **成就解锁**：基于学习数据的自动解锁机制
- **进度追踪**：成就完成进度的可视化
- **分类管理**：按学习类型分类的成就体系

### 导航集成
- 解决成就页面的导航问题
- 与底部导航栏的集成
- 页面状态管理优化

---

## 🏰 记忆宫殿功能

### 功能概述
基于空间记忆的学习方法，支持图片标注、知识点管理。

### 核心功能
- **图片压缩**：智能压缩算法，保持文字清晰度
- **知识点标注**：精确的点击定位和气泡显示
- **拖拽编辑**：支持知识点位置的拖拽调整
- **知识点删除**：支持知识点的删除操作，包含确认对话框和成功反馈
- **分类管理**：记忆宫殿的层级分类系统

### 坐标系统
- **标准化坐标**：解决不同图片尺寸的兼容性
- **压缩图片适配**：用户导入图片的坐标转换
- **横屏支持**：横屏模式下的坐标系统适配

---

## 📈 学习统计功能

### 功能概述
学习数据的统计分析和效率评估系统。

### 核心指标
- **学习ROI**：学习效果得分 ÷ 时间投入
- **并行时间统计**：多任务活动节省的时间
- **专注信噪比**：专注时间 ÷ 无关时间

### 数据同步
- **实时更新**：TimeBox任务完成时触发数据更新
- **学习会话完成**：原子性数据同步，包含学习时长、成就解锁、连续天数统计
- **状态管理**：使用Riverpod进行状态管理
- **持久化存储**：本地数据存储和恢复

### 会话完成处理
- **StudySessionCompletionService**：统一处理学习会话完成后的所有数据更新
- **原子性操作**：确保数据一致性，避免部分更新失败
- **成就触发**：自动检测并解锁相关学习成就

---

## 🔧 技术栈总结

### 核心技术
- **Flutter 3.x**：跨平台UI框架
- **Riverpod**：状态管理
- **SharedPreferences**：本地数据存储
- **Transform & Matrix4**：坐标变换
- **CustomPainter**：自定义图表绘制

### 设计模式
- **MVVM架构**：视图与逻辑分离
- **Provider模式**：状态管理
- **组件化设计**：可复用UI组件
- **响应式编程**：数据流管理

---

**文档维护**: 本文档记录核心功能的技术实现，新功能开发时应及时更新相应章节。
