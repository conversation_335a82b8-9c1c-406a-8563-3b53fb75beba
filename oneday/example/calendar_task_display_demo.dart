import 'package:flutter/material.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

/// TimeBox 日历界面任务显示修复演示
///
/// 展示修复后的效果：
/// 1. <PERSON><PERSON><PERSON> 风格颜色分类系统
/// 2. 任务名称文本优化显示
/// 3. 响应式时间块布局
/// 4. 智能文字颜色适配
class CalendarTaskDisplayDemo extends StatefulWidget {
  const CalendarTaskDisplayDemo({super.key});

  @override
  State<CalendarTaskDisplayDemo> createState() =>
      _CalendarTaskDisplayDemoState();
}

class _CalendarTaskDisplayDemoState extends State<CalendarTaskDisplayDemo> {
  double _blockHeight = 50.0;
  bool _showCompleted = false;

  // 演示任务数据
  final List<TimeBoxTask> _demoTasks = [
    TimeBoxTask(
      id: 'demo-1',
      title: '算法与数据结构',
      description: '学习二叉树遍历算法',
      plannedMinutes: 45,
      status: TaskStatus.pending,
      priority: TaskPriority.high,
      category: '计算机科学',
      createdAt: DateTime.now(),
    ),
    TimeBoxTask(
      id: 'demo-2',
      title: '马克思主义基本原理',
      description: '复习唯物辩证法',
      plannedMinutes: 30,
      status: TaskStatus.completed,
      priority: TaskPriority.medium,
      category: '政治',
      createdAt: DateTime.now(),
      endTime: DateTime.now(),
    ),
    TimeBoxTask(
      id: 'demo-3',
      title: '高等数学微积分',
      description: '练习定积分计算',
      plannedMinutes: 60,
      status: TaskStatus.pending,
      priority: TaskPriority.high,
      category: '数学',
      createdAt: DateTime.now(),
    ),
    TimeBoxTask(
      id: 'demo-4',
      title: 'TOEFL Reading Comprehension',
      description: '阅读理解练习',
      plannedMinutes: 40,
      status: TaskStatus.completed,
      priority: TaskPriority.medium,
      category: '英语',
      createdAt: DateTime.now(),
      endTime: DateTime.now(),
    ),
    TimeBoxTask(
      id: 'demo-5',
      title: '具身记忆训练',
      description: '单词记忆动作练习',
      plannedMinutes: 15,
      status: TaskStatus.completed,
      priority: TaskPriority.low,
      category: '休息',
      createdAt: DateTime.now(),
      endTime: DateTime.now(),
    ),
    TimeBoxTask(
      id: 'demo-6',
      title: '这是一个非常长的任务名称用来测试文本截断和换行功能的效果',
      description: '测试长文本显示',
      plannedMinutes: 25,
      status: TaskStatus.pending,
      priority: TaskPriority.low,
      category: '计算机科学',
      createdAt: DateTime.now(),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TimeBox 日历任务显示演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '🎨 Mondrian 风格演示控制',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 12),

                // 时间块高度控制
                Row(
                  children: [
                    const Text('时间块高度: '),
                    Expanded(
                      child: Slider(
                        value: _blockHeight,
                        min: 15,
                        max: 100,
                        divisions: 17,
                        activeColor: const Color(0xFF2E7EED),
                        label: '${_blockHeight.toInt()}px',
                        onChanged: (value) {
                          setState(() {
                            _blockHeight = value;
                          });
                        },
                      ),
                    ),
                    Text('${_blockHeight.toInt()}px'),
                  ],
                ),

                // 完成状态切换
                Row(
                  children: [
                    const Text('显示完成状态: '),
                    Switch(
                      value: _showCompleted,
                      activeColor: const Color(0xFF0F7B6C),
                      onChanged: (value) {
                        setState(() {
                          _showCompleted = value;
                        });
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),

          // Mondrian 颜色说明
          Container(
            padding: const EdgeInsets.all(16),
            color: Colors.white,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '🎯 原始颜色分类系统',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 12),
                Wrap(
                  spacing: 12,
                  runSpacing: 8,
                  children: [
                    _buildColorLegend(
                      '计算机科学',
                      const Color(0xFFE03E3E),
                      '红色 - 计算机科学',
                    ),
                    _buildColorLegend(
                      '数学',
                      const Color(0xFFFFD700),
                      '荧光黄 - 数学学习',
                    ),
                    _buildColorLegend(
                      '英语',
                      const Color(0xFF0F7B6C),
                      '绿色 - 英语学习',
                    ),
                    _buildColorLegend(
                      '政治',
                      const Color(0xFF2E7EED),
                      '蓝色 - 政治学习',
                    ),
                    _buildColorLegend(
                      '休息',
                      const Color(0xFF9B9A97),
                      '灰色 - 休息活动',
                    ),
                  ],
                ),
              ],
            ),
          ),

          // 任务展示区域
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📅 任务时间块展示',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 12),

                  // 任务网格
                  Expanded(
                    child: GridView.builder(
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            crossAxisSpacing: 8,
                            mainAxisSpacing: 8,
                            childAspectRatio: 2.5,
                          ),
                      itemCount: _demoTasks.length,
                      itemBuilder: (context, index) {
                        final task = _demoTasks[index];
                        final displayTask = _showCompleted
                            ? task.copyWith(
                                status: TaskStatus.completed,
                                endTime: DateTime.now(),
                              )
                            : task;

                        return _DemoTimeBoxBlock(
                          task: displayTask,
                          blockHeight: _blockHeight,
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),

          // 修复说明
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '✅ 修复效果展示',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 8),
                const Text(
                  '• 任务名称文本不再重叠，根据时间块高度自动调整字体大小\n'
                  '• 移除了分类文字显示，通过原始颜色直接识别分类\n'
                  '• 智能文字颜色：深色背景使用白色文字，浅色背景使用深色文字\n'
                  '• 保持原始设计风格：圆角设计，经典配色，简洁边框',
                  style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF787774),
                    height: 1.4,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorLegend(String category, Color color, String description) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2), // 原始圆角
            ),
          ),
          const SizedBox(width: 6),
          Text(
            description,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
    );
  }
}

/// 演示用的时间盒子块组件
class _DemoTimeBoxBlock extends StatelessWidget {
  final TimeBoxTask task;
  final double blockHeight;

  const _DemoTimeBoxBlock({required this.task, required this.blockHeight});

  @override
  Widget build(BuildContext context) {
    final isCompleted = task.isCompleted;
    final isRestTask = task.category == '休息' || task.category == '具身记忆';

    // 原始风格的颜色和边框处理
    Color backgroundColor;
    Border? border;

    if (isCompleted) {
      backgroundColor = task.categoryColor;
      border = null; // 已完成任务不需要边框
    } else {
      backgroundColor = task.getCategoryColorWithOpacity(0.3);
      border = Border.all(color: task.categoryColor, width: 1);
    }

    return Container(
      height: blockHeight,
      margin: const EdgeInsets.symmetric(horizontal: 0.5, vertical: 0.5),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(3), // 原始圆角风格
        border: border,
      ),
      child: _buildTimeBoxContent(task, isRestTask, blockHeight),
    );
  }

  Widget _buildTimeBoxContent(
    TimeBoxTask task,
    bool isRestTask,
    double blockHeight,
  ) {
    if (isRestTask) {
      return Center(
        child: Container(
          width: 6,
          height: 6,
          decoration: BoxDecoration(
            color: task.isCompleted ? Colors.white : task.categoryColor,
            shape: BoxShape.circle,
          ),
        ),
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // 动态计算字体大小和行数
        double fontSize;
        int maxLines;

        if (blockHeight <= 15) {
          return const SizedBox.shrink();
        } else if (blockHeight <= 25) {
          fontSize = 6;
          maxLines = 1;
        } else if (blockHeight <= 40) {
          fontSize = 7;
          maxLines = 1;
        } else if (blockHeight <= 60) {
          fontSize = 8;
          maxLines = 2;
        } else {
          fontSize = 9;
          maxLines = 3;
        }

        // 根据原始颜色方案确定文字颜色
        Color textColor;
        if (task.isCompleted) {
          textColor = Colors.white;
        } else {
          final bgColor = task.categoryColor;
          if (bgColor == const Color(0xFFE03E3E) || // 红色
              bgColor == const Color(0xFF2E7EED) || // 蓝色
              bgColor == const Color(0xFF0F7B6C)) {
            // 绿色
            textColor = Colors.white;
          } else {
            textColor = const Color(0xFF37352F); // 深灰色文字（适用于荧光黄和灰色背景）
          }
        }

        return Padding(
          padding: const EdgeInsets.all(2),
          child: Center(
            child: Text(
              task.title,
              style: TextStyle(
                fontSize: fontSize,
                fontWeight: FontWeight.w600,
                color: textColor,
                height: 1.1,
              ),
              maxLines: maxLines,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.center,
            ),
          ),
        );
      },
    );
  }
}
