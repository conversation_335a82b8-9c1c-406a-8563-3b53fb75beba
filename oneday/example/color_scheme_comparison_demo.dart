import 'package:flutter/material.dart';
import 'package:oneday/features/time_box/timebox_list_page.dart';

/// 颜色方案对比演示
///
/// 展示原始颜色方案与 Mondrian 艺术风格的对比效果
class ColorSchemeComparisonDemo extends StatefulWidget {
  const ColorSchemeComparisonDemo({super.key});

  @override
  State<ColorSchemeComparisonDemo> createState() =>
      _ColorSchemeComparisonDemoState();
}

class _ColorSchemeComparisonDemoState extends State<ColorSchemeComparisonDemo> {
  bool _showCompleted = false;

  // 演示任务数据
  final List<Map<String, dynamic>> _demoTasks = [
    {'title': '算法与数据结构', 'category': '计算机科学', 'description': '学习二叉树遍历算法'},
    {'title': '高等数学微积分', 'category': '数学', 'description': '练习定积分计算'},
    {'title': 'TOEFL Reading', 'category': '英语', 'description': '阅读理解练习'},
    {'title': '马克思主义基本原理', 'category': '政治', 'description': '复习唯物辩证法'},
    {'title': '具身记忆训练', 'category': '休息', 'description': '单词记忆动作练习'},
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('颜色方案对比演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 控制面板
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: Row(
              children: [
                const Text('显示完成状态: '),
                Switch(
                  value: _showCompleted,
                  activeColor: const Color(0xFF0F7B6C),
                  onChanged: (value) {
                    setState(() {
                      _showCompleted = value;
                    });
                  },
                ),
              ],
            ),
          ),

          // 对比展示区域
          Expanded(
            child: Row(
              children: [
                // 原始颜色方案
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🎨 原始颜色方案（当前使用）',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // 颜色图例
                        _buildColorLegendSection(_getOriginalColors()),

                        const SizedBox(height: 16),

                        // 任务展示
                        const Text(
                          '任务时间块展示:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const SizedBox(height: 8),

                        Expanded(
                          child: ListView.builder(
                            itemCount: _demoTasks.length,
                            itemBuilder: (context, index) {
                              final taskData = _demoTasks[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _OriginalColorTimeBlock(
                                  title: taskData['title'],
                                  category: taskData['category'],
                                  isCompleted: _showCompleted,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // 分隔线
                Container(width: 1, color: const Color(0xFFE3E2DE)),

                // Mondrian 颜色方案
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          '🎭 Mondrian 艺术风格',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const SizedBox(height: 12),

                        // 颜色图例
                        _buildColorLegendSection(_getMondrianColors()),

                        const SizedBox(height: 16),

                        // 任务展示
                        const Text(
                          '任务时间块展示:',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        const SizedBox(height: 8),

                        Expanded(
                          child: ListView.builder(
                            itemCount: _demoTasks.length,
                            itemBuilder: (context, index) {
                              final taskData = _demoTasks[index];
                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8),
                                child: _MondrianColorTimeBlock(
                                  title: taskData['title'],
                                  category: taskData['category'],
                                  isCompleted: _showCompleted,
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 对比说明
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '📊 颜色方案对比分析',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 8),
                const Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '原始颜色方案优势:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF0F7B6C),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '• 明亮活跃，荧光黄醒目\n'
                            '• 自然舒适，绿色温和\n'
                            '• 传统经典，认知习惯\n'
                            '• 对比度高，区分明显',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF787774),
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Mondrian 风格优势:',
                            style: TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFFD40000),
                            ),
                          ),
                          SizedBox(height: 4),
                          Text(
                            '• 艺术美感，视觉统一\n'
                            '• 几何简洁，现代感强\n'
                            '• 色彩理论，和谐统一\n'
                            '• 专业感强，极简设计',
                            style: TextStyle(
                              fontSize: 12,
                              color: Color(0xFF787774),
                              height: 1.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildColorLegendSection(List<Map<String, dynamic>> colors) {
    return Column(
      children: colors
          .map(
            (colorData) => Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: _buildColorLegend(
                colorData['category'],
                colorData['color'],
                colorData['description'],
              ),
            ),
          )
          .toList(),
    );
  }

  Widget _buildColorLegend(String category, Color color, String description) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: color, width: 1),
      ),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              description,
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getOriginalColors() {
    return [
      {
        'category': '计算机科学',
        'color': const Color(0xFFE03E3E),
        'description': '红色 - 计算机科学',
      },
      {
        'category': '数学',
        'color': const Color(0xFFFFD700),
        'description': '荧光黄 - 数学学习',
      },
      {
        'category': '英语',
        'color': const Color(0xFF0F7B6C),
        'description': '绿色 - 英语学习',
      },
      {
        'category': '政治',
        'color': const Color(0xFF2E7EED),
        'description': '蓝色 - 政治学习',
      },
      {
        'category': '休息',
        'color': const Color(0xFF9B9A97),
        'description': '灰色 - 休息活动',
      },
    ];
  }

  List<Map<String, dynamic>> _getMondrianColors() {
    return [
      {
        'category': '计算机科学',
        'color': const Color(0xFFD40000),
        'description': 'Mondrian 红色 - 紧急重要',
      },
      {
        'category': '数学',
        'color': const Color(0xFF004595),
        'description': 'Mondrian 蓝色 - 数学学习',
      },
      {
        'category': '英语',
        'color': const Color(0xFF000000),
        'description': 'Mondrian 黑色 - 英语学习',
      },
      {
        'category': '政治',
        'color': const Color(0xFFF7D000),
        'description': 'Mondrian 黄色 - 政治学习',
      },
      {
        'category': '休息',
        'color': const Color(0xFFD3D3D3),
        'description': 'Mondrian 灰色 - 已完成活动',
      },
    ];
  }
}

/// 原始颜色方案时间块
class _OriginalColorTimeBlock extends StatelessWidget {
  final String title;
  final String category;
  final bool isCompleted;

  const _OriginalColorTimeBlock({
    required this.title,
    required this.category,
    required this.isCompleted,
  });

  @override
  Widget build(BuildContext context) {
    final color = SubjectColors.getCategoryColor(category);

    return Container(
      height: 50,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isCompleted ? color : color.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(3),
        border: isCompleted ? null : Border.all(color: color, width: 1),
      ),
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: isCompleted
                ? Colors.white
                : (color == const Color(0xFFFFD700) ||
                      color == const Color(0xFF9B9A97))
                ? const Color(0xFF37352F)
                : Colors.white,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}

/// Mondrian 颜色方案时间块
class _MondrianColorTimeBlock extends StatelessWidget {
  final String title;
  final String category;
  final bool isCompleted;

  const _MondrianColorTimeBlock({
    required this.title,
    required this.category,
    required this.isCompleted,
  });

  Color _getMondrianColor(String category) {
    switch (category) {
      case '计算机科学':
        return const Color(0xFFD40000); // Mondrian 红色
      case '政治':
        return const Color(0xFFF7D000); // Mondrian 黄色
      case '数学':
        return const Color(0xFF004595); // Mondrian 蓝色
      case '英语':
        return const Color(0xFF000000); // Mondrian 黑色
      case '休息':
        return const Color(0xFFD3D3D3); // Mondrian 灰色
      default:
        return const Color(0xFF9B9A97);
    }
  }

  @override
  Widget build(BuildContext context) {
    final color = _getMondrianColor(category);

    return Container(
      height: 50,
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: isCompleted ? color : color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(0), // Mondrian 直角
        border: isCompleted
            ? Border.all(color: const Color(0xFF000000), width: 2)
            : Border.all(color: color, width: 1.5),
      ),
      child: Center(
        child: Text(
          title,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: isCompleted
                ? Colors.white
                : (color == const Color(0xFFF7D000) ||
                      color == const Color(0xFFD3D3D3))
                ? const Color(0xFF37352F)
                : Colors.white,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
