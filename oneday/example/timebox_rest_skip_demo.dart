import 'package:flutter/material.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/services/global_timer_service.dart';

/// TimeBox 休息时间提前结束功能演示
///
/// 这个演示展示了修复后的休息时间提前结束功能：
/// 1. 正确的导航逻辑
/// 2. 休息完成回调的触发
/// 3. 任务完成对话框的显示
class TimeBoxRestSkipDemo extends StatefulWidget {
  const TimeBoxRestSkipDemo({super.key});

  @override
  State<TimeBoxRestSkipDemo> createState() => _TimeBoxRestSkipDemoState();
}

class _TimeBoxRestSkipDemoState extends State<TimeBoxRestSkipDemo> {
  final GlobalTimerService _globalTimerService = GlobalTimerService();
  bool _isKinestheticLearningShowing = false;
  String _statusMessage = '准备开始演示';

  // 演示用的测试任务
  late TimeBoxTask _demoTask;

  @override
  void initState() {
    super.initState();

    // 创建演示任务
    _demoTask = TimeBoxTask(
      id: 'demo-task-${DateTime.now().millisecondsSinceEpoch}',
      title: '演示任务：学习Flutter',
      description: '演示TimeBox休息时间提前结束功能',
      plannedMinutes: 1, // 1分钟用于演示
      status: TaskStatus.pending,
      priority: TaskPriority.medium,
      category: '学习',
      createdAt: DateTime.now(),
    );

    // 设置休息完成回调
    _globalTimerService.setRestCompletedCallback(() {
      _onRestCompleted();
    });

    // 监听全局计时器状态变化
    _globalTimerService.addListener(_onTimerStateChanged);
  }

  @override
  void dispose() {
    _globalTimerService.removeListener(_onTimerStateChanged);
    super.dispose();
  }

  void _onTimerStateChanged() {
    if (mounted) {
      setState(() {
        // 更新UI状态
      });

      // 检查是否进入休息时间
      if (_globalTimerService.pomodoroState == PomodoroTimerState.rest &&
          _globalTimerService.isTimerRunning &&
          !_isKinestheticLearningShowing) {
        _showKinestheticLearningInterface();
      }
    }
  }

  void _onRestCompleted() {
    if (mounted) {
      setState(() {
        _statusMessage = '✅ 休息完成回调被触发';
      });

      // 显示任务完成对话框
      _showTaskCompletionDialog();
    }
  }

  void _showTaskCompletionDialog() {
    final task =
        _globalTimerService.currentTask ??
        _globalTimerService.lastCompletedTask;
    if (task == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('🎉 任务完成'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('任务："${task.title}"'),
            const SizedBox(height: 8),
            Text('学习时长：${task.plannedMinutes} 分钟'),
            Text('获得工资：¥${task.calculateWage().toStringAsFixed(1)}'),
            const SizedBox(height: 12),
            const Text(
              '✅ 修复验证：休息时间提前结束功能正常工作！',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF0F7B6C),
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              setState(() {
                _statusMessage = '演示完成，可以重新开始';
              });
            },
            child: const Text('完成'),
          ),
        ],
      ),
    );
  }

  void _showKinestheticLearningInterface() {
    if (_isKinestheticLearningShowing) return;

    _isKinestheticLearningShowing = true;
    setState(() {
      _statusMessage = '显示动觉记忆法学习页面';
    });

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DemoKinestheticLearningPage(
          onComplete: () {
            _isKinestheticLearningShowing = false;
            Navigator.of(context).pop();
            setState(() {
              _statusMessage = '休息时间自然结束';
            });
          },
          onSkipRest: () {
            _isKinestheticLearningShowing = false;
            setState(() {
              _statusMessage = '用户选择提前结束休息';
            });
            // 关键修复：使用 skipRestTime() 而不是 stopTimer()
            _globalTimerService.skipRestTime();
            Navigator.of(context).pop();
          },
        ),
      ),
    ).then((_) {
      _isKinestheticLearningShowing = false;
    });
  }

  void _startDemo() async {
    setState(() {
      _statusMessage = '启动工作计时器...';
    });

    // 启动工作计时器
    await _globalTimerService.startTimer(_demoTask);

    setState(() {
      _statusMessage = '工作时间开始（${_demoTask.plannedMinutes}分钟）';
    });

    // 为了演示，我们快速进入休息时间
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _statusMessage = '模拟工作完成，进入休息时间...';
    });

    // 启动休息时间
    await _globalTimerService.startRestTimer();
  }

  void _resetDemo() {
    _globalTimerService.stopTimer();
    setState(() {
      _statusMessage = '准备开始演示';
      _isKinestheticLearningShowing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('TimeBox 休息跳过功能演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 演示说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2DE)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '🎯 演示说明',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '这个演示展示了修复后的休息时间提前结束功能：\n'
                    '1. 启动工作计时器\n'
                    '2. 自动进入休息时间\n'
                    '3. 显示动觉记忆法学习页面\n'
                    '4. 点击"提前结束休息"按钮\n'
                    '5. 正确返回并显示任务完成对话框',
                    style: TextStyle(fontSize: 14, color: Color(0xFF787774)),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 当前状态显示
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.04),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📊 当前状态',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildStatusRow(
                    '计时器状态',
                    _globalTimerService.pomodoroState.toString(),
                  ),
                  _buildStatusRow(
                    '是否运行',
                    _globalTimerService.isTimerRunning ? '是' : '否',
                  ),
                  _buildStatusRow(
                    '当前任务',
                    _globalTimerService.currentTask?.title ?? '无',
                  ),
                  _buildStatusRow(
                    '剩余时间',
                    _globalTimerService.formatTime(
                      _globalTimerService.remainingSeconds,
                    ),
                  ),
                  _buildStatusRow('状态消息', _statusMessage),
                ],
              ),
            ),

            const SizedBox(height: 24),

            // 操作按钮
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: _globalTimerService.isTimerRunning
                        ? null
                        : _startDemo,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF2E7EED),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      '开始演示',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _resetDemo,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: const Color(0xFF37352F),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                        side: const BorderSide(color: Color(0xFFE3E2DE)),
                      ),
                    ),
                    child: const Text(
                      '重置演示',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(fontSize: 14, color: Color(0xFF787774)),
            ),
          ),
          const Text(': ', style: TextStyle(color: Color(0xFF787774))),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// 演示用的动觉记忆法学习页面
class DemoKinestheticLearningPage extends StatelessWidget {
  final VoidCallback onComplete;
  final VoidCallback? onSkipRest;

  const DemoKinestheticLearningPage({
    super.key,
    required this.onComplete,
    this.onSkipRest,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('动觉记忆法学习'),
        backgroundColor: const Color(0xFF0F7B6C),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: const Color(0xFFE3E2DE)),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.fitness_center,
                    size: 64,
                    color: Color(0xFF0F7B6C),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    '🧘 休息时间',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '进行动觉记忆法学习，结合身体动作记忆单词',
                    style: TextStyle(fontSize: 16, color: Color(0xFF787774)),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),

                  // 演示单词
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Column(
                      children: [
                        Text(
                          'EXAMPLE',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                            color: Color(0xFF37352F),
                          ),
                        ),
                        SizedBox(height: 4),
                        Text(
                          '例子，示例',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF787774),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 24),

                  // 操作按钮
                  Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: onComplete,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF0F7B6C),
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(6),
                            ),
                          ),
                          child: const Text(
                            '完成休息',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(height: 8),

                      // 提前结束休息按钮
                      if (onSkipRest != null)
                        SizedBox(
                          width: double.infinity,
                          child: TextButton(
                            onPressed: onSkipRest,
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(6),
                              ),
                            ),
                            child: const Text(
                              '⏭️ 提前结束休息，立即工作',
                              style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF9B9A97),
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
