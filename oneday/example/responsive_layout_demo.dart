import 'package:flutter/material.dart';

/// iPad 台前调度模式响应式布局演示
/// 
/// 这个演示展示了 OneDay 应用主页快捷入口组件
/// 在不同窗口尺寸下的响应式布局效果
class ResponsiveLayoutDemo extends StatefulWidget {
  const ResponsiveLayoutDemo({super.key});

  @override
  State<ResponsiveLayoutDemo> createState() => _ResponsiveLayoutDemoState();
}

class _ResponsiveLayoutDemoState extends State<ResponsiveLayoutDemo> {
  double _windowWidth = 375; // 默认手机宽度

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('iPad 台前调度响应式布局演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Column(
        children: [
          // 窗口宽度控制器
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '模拟窗口宽度: ${_windowWidth.toInt()}px',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 8),
                Slider(
                  value: _windowWidth,
                  min: 300,
                  max: 1200,
                  divisions: 18,
                  activeColor: const Color(0xFF2E7EED),
                  label: '${_windowWidth.toInt()}px',
                  onChanged: (value) {
                    setState(() {
                      _windowWidth = value;
                    });
                  },
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildPresetButton('窄屏', 350),
                    _buildPresetButton('手机', 375),
                    _buildPresetButton('平板竖屏', 600),
                    _buildPresetButton('平板横屏', 900),
                    _buildPresetButton('宽屏', 1100),
                  ],
                ),
              ],
            ),
          ),
          
          // 响应式布局演示区域
          Expanded(
            child: Center(
              child: Container(
                width: _windowWidth,
                height: 600,
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border.all(color: const Color(0xFFE3E2DE), width: 2),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 快捷入口演示
                      _buildQuickActionsDemo(),
                      const SizedBox(height: 24),
                      // 统计卡片演示
                      _buildStatsDemo(),
                    ],
                  ),
                ),
              ),
            ),
          ),
          
          // 布局信息显示
          Container(
            padding: const EdgeInsets.all(16),
            color: const Color(0xFFF7F6F3),
            child: _buildLayoutInfo(),
          ),
        ],
      ),
    );
  }

  Widget _buildPresetButton(String label, double width) {
    return ElevatedButton(
      onPressed: () {
        setState(() {
          _windowWidth = width;
        });
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: _windowWidth == width 
            ? const Color(0xFF2E7EED) 
            : Colors.white,
        foregroundColor: _windowWidth == width 
            ? Colors.white 
            : const Color(0xFF37352F),
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(
            color: _windowWidth == width 
                ? const Color(0xFF2E7EED) 
                : const Color(0xFFE3E2DE),
          ),
        ),
      ),
      child: Text(label, style: const TextStyle(fontSize: 12)),
    );
  }

  Widget _buildQuickActionsDemo() {
    final actions = [
      {
        'title': '具身记忆',
        'subtitle': '五感记忆',
        'icon': Icons.fitness_center,
        'color': const Color(0xFFE03E3E),
      },
      {
        'title': '时间盒子',
        'subtitle': '专注学习',
        'icon': Icons.timer_outlined,
        'color': const Color(0xFF0F7B6C),
      },
      {
        'title': '知忆相册',
        'subtitle': '场景记忆',
        'icon': Icons.account_balance,
        'color': const Color(0xFF7C3AED),
      },
      {
        'title': '词汇管理',
        'subtitle': '考研词汇',
        'icon': Icons.library_books,
        'color': const Color(0xFF2E7EED),
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快捷入口',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        LayoutBuilder(
          builder: (context, constraints) {
            int columns = 2;
            double aspectRatio = 1.2;
            double spacing = 12;

            if (constraints.maxWidth > 900) {
              columns = 4;
              aspectRatio = 1.0;
              spacing = 16;
            } else if (constraints.maxWidth > 600) {
              columns = 3;
              aspectRatio = 1.1;
              spacing = 14;
            } else if (constraints.maxWidth > 400) {
              columns = 2;
              aspectRatio = 1.2;
              spacing = 12;
            } else {
              columns = 2;
              aspectRatio = 1.3;
              spacing = 8;
            }

            return GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: columns,
                crossAxisSpacing: spacing,
                mainAxisSpacing: spacing,
                childAspectRatio: aspectRatio,
              ),
              itemCount: actions.length,
              itemBuilder: (context, index) {
                final action = actions[index];
                return _DemoQuickActionCard(
                  title: action['title'] as String,
                  subtitle: action['subtitle'] as String,
                  icon: action['icon'] as IconData,
                  color: action['color'] as Color,
                );
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildStatsDemo() {
    final statsData = [
      {
        'title': '学习时长',
        'value': '3h 45m',
        'subtitle': '今日已学习',
        'color': const Color(0xFF2E7EED),
        'icon': Icons.schedule,
      },
      {
        'title': '虚拟工资',
        'value': '¥750',
        'subtitle': '今日收入',
        'color': const Color(0xFF0F7B6C),
        'icon': Icons.attach_money,
      },
      {
        'title': '完成任务',
        'value': '6/8',
        'subtitle': '时间盒子',
        'color': const Color(0xFF7C3AED),
        'icon': Icons.task_alt,
      },
      {
        'title': '连续天数',
        'value': '12天',
        'subtitle': '学习打卡',
        'color': const Color(0xFFD9730D),
        'icon': Icons.local_fire_department,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '今日概览',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 800) {
              // 宽屏：4列布局
              return Row(
                children: statsData.map((stat) => 
                  Expanded(
                    child: Padding(
                      padding: EdgeInsets.only(
                        right: stat == statsData.last ? 0 : 12,
                      ),
                      child: _DemoStatsCard(
                        title: stat['title'] as String,
                        value: stat['value'] as String,
                        subtitle: stat['subtitle'] as String,
                        color: stat['color'] as Color,
                        icon: stat['icon'] as IconData,
                      ),
                    ),
                  ),
                ).toList(),
              );
            } else {
              // 标准布局：2x2 网格
              return Column(
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: _DemoStatsCard(
                          title: statsData[0]['title'] as String,
                          value: statsData[0]['value'] as String,
                          subtitle: statsData[0]['subtitle'] as String,
                          color: statsData[0]['color'] as Color,
                          icon: statsData[0]['icon'] as IconData,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _DemoStatsCard(
                          title: statsData[1]['title'] as String,
                          value: statsData[1]['value'] as String,
                          subtitle: statsData[1]['subtitle'] as String,
                          color: statsData[1]['color'] as Color,
                          icon: statsData[1]['icon'] as IconData,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _DemoStatsCard(
                          title: statsData[2]['title'] as String,
                          value: statsData[2]['value'] as String,
                          subtitle: statsData[2]['subtitle'] as String,
                          color: statsData[2]['color'] as Color,
                          icon: statsData[2]['icon'] as IconData,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _DemoStatsCard(
                          title: statsData[3]['title'] as String,
                          value: statsData[3]['value'] as String,
                          subtitle: statsData[3]['subtitle'] as String,
                          color: statsData[3]['color'] as Color,
                          icon: statsData[3]['icon'] as IconData,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }
          },
        ),
      ],
    );
  }

  Widget _buildLayoutInfo() {
    String layoutType;
    int columns;
    
    if (_windowWidth > 900) {
      layoutType = '宽屏布局';
      columns = 4;
    } else if (_windowWidth > 600) {
      layoutType = '中等屏幕布局';
      columns = 3;
    } else if (_windowWidth > 400) {
      layoutType = '标准布局';
      columns = 2;
    } else {
      layoutType = '窄屏布局';
      columns = 2;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: [
        Column(
          children: [
            const Text('布局类型', style: TextStyle(fontSize: 12, color: Color(0xFF787774))),
            Text(layoutType, style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
        Column(
          children: [
            const Text('网格列数', style: TextStyle(fontSize: 12, color: Color(0xFF787774))),
            Text('$columns 列', style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
        Column(
          children: [
            const Text('窗口宽度', style: TextStyle(fontSize: 12, color: Color(0xFF787774))),
            Text('${_windowWidth.toInt()}px', style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600)),
          ],
        ),
      ],
    );
  }
}

// 演示用的快捷操作卡片
class _DemoQuickActionCard extends StatelessWidget {
  final String title;
  final String subtitle;
  final IconData icon;
  final Color color;

  const _DemoQuickActionCard({
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final cardWidth = constraints.maxWidth;
        
        double iconSize = 24;
        double titleFontSize = 14;
        double subtitleFontSize = 12;
        double iconContainerSize = 48;
        double padding = 16;
        double spacing = 12;
        
        if (cardWidth < 120) {
          iconSize = 20;
          titleFontSize = 12;
          subtitleFontSize = 10;
          iconContainerSize = 40;
          padding = 12;
          spacing = 8;
        } else if (cardWidth > 180) {
          iconSize = 28;
          titleFontSize = 16;
          subtitleFontSize = 14;
          iconContainerSize = 56;
          padding = 20;
          spacing = 16;
        }

        return Container(
          padding: EdgeInsets.all(padding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.04),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                flex: 2,
                child: Container(
                  width: iconContainerSize,
                  height: iconContainerSize,
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(icon, size: iconSize, color: color),
                ),
              ),
              SizedBox(height: spacing),
              Flexible(
                flex: 1,
                child: Text(
                  title,
                  style: TextStyle(
                    fontSize: titleFontSize,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF37352F),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: spacing * 0.3),
              Flexible(
                flex: 1,
                child: Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: subtitleFontSize,
                    color: const Color(0xFF787774),
                  ),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}

// 演示用的统计卡片
class _DemoStatsCard extends StatelessWidget {
  final String title;
  final String value;
  final String subtitle;
  final Color color;
  final IconData icon;

  const _DemoStatsCard({
    required this.title,
    required this.value,
    required this.subtitle,
    required this.color,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.04),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, size: 18, color: color),
              ),
              const Spacer(),
              Flexible(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 12,
                    color: Color(0xFF787774),
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: 12,
              color: Color(0xFF9B9A97),
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }
}
