import 'package:oneday/features/achievement/services/achievement_service.dart';
import 'package:oneday/features/wage_system/services/wage_service.dart';

/// 成就和工资系统集成示例
///
/// 这个示例展示了如何使用成就系统和工资系统的集成功能
class AchievementWageExample {
  final AchievementService _achievementService = AchievementService();
  final WageService _wageService = WageService();

  /// 示例：解锁成就并获得工资奖励
  Future<void> demonstrateAchievementUnlock() async {
    print('🎯 开始演示成就解锁和工资奖励...\n');

    // 1. 查看初始状态
    final initialBalance = await _wageService.getUserBalance();
    print('💰 初始余额: ¥$initialBalance');

    final initialLevel = await _achievementService.getUserLevel();
    print(
      '⭐ 初始等级: ${initialLevel.level} (经验: ${initialLevel.currentExp}/${initialLevel.requiredExp})',
    );

    // 2. 模拟解锁一个成就
    print('\n🏆 尝试解锁成就: learning_001 (初学者)');
    final unlocked = await _achievementService.unlockAchievement(
      'learning_001',
    );

    if (unlocked) {
      print('✅ 成就解锁成功！');
    } else {
      print('❌ 成就解锁失败或已解锁');
    }

    // 3. 查看更新后的状态
    final newBalance = await _wageService.getUserBalance();
    print('💰 新余额: ¥$newBalance (增加: ¥${newBalance - initialBalance})');

    final newLevel = await _achievementService.getUserLevel();
    print(
      '⭐ 新等级: ${newLevel.level} (经验: ${newLevel.currentExp}/${newLevel.requiredExp})',
    );

    // 4. 查看交易记录
    final transactions = await _wageService.getTransactions();
    print('\n📊 最近交易记录:');
    for (final transaction in transactions.take(3)) {
      print(
        '  ${transaction.timestamp.toString().substring(0, 19)} | '
        '${transaction.amount >= 0 ? '+' : ''}¥${transaction.amount} | '
        '${transaction.description}',
      );
    }
  }

  /// 示例：添加学习经验并检查成就
  Future<void> demonstrateStudySession() async {
    print('\n📚 开始演示学习会话...\n');

    // 模拟学习会话
    const studyMinutes = 60;
    print('⏰ 学习时长: $studyMinutes 分钟');

    // 添加经验值
    final newLevel = await _achievementService.addExperience(
      studyMinutes,
      source: '学习会话',
    );
    print('⭐ 获得经验: $studyMinutes 点，当前等级: ${newLevel.level}');

    // 添加学习收入
    const hourlyRate = 200.0;
    final income = (studyMinutes / 60.0) * hourlyRate;
    final success = await _wageService.addStudyIncome(
      income,
      description: '学习收入 - $studyMinutes分钟',
    );

    if (success) {
      print('💰 获得学习收入: ¥$income');
    }

    // 查看统计数据
    final stats = await _wageService.getStatistics();
    print('\n📈 工资统计:');
    print('  总余额: ¥${stats['balance']}');
    print('  今日收入: ¥${stats['todayIncome']}');
    print('  总收入: ¥${stats['totalIncome']}');
    print('  交易次数: ${stats['transactionCount']}');
  }

  /// 示例：购买道具
  Future<void> demonstratePurchase() async {
    print('\n🛒 开始演示道具购买...\n');

    const itemPrice = 50.0;
    const itemName = '专注药水';

    final currentBalance = await _wageService.getUserBalance();
    print('💰 当前余额: ¥$currentBalance');
    print('🛍️ 尝试购买: $itemName (¥$itemPrice)');

    final success = await _wageService.deductAmount(
      itemPrice,
      description: '购买道具: $itemName',
    );

    if (success) {
      print('✅ 购买成功！');
      final newBalance = await _wageService.getUserBalance();
      print('💰 新余额: ¥$newBalance');
    } else {
      print('❌ 购买失败，余额不足');
    }
  }

  /// 运行完整示例
  Future<void> runFullExample() async {
    print('🚀 OneDay 成就和工资系统集成示例\n');
    print('=' * 50);

    try {
      await demonstrateAchievementUnlock();
      await demonstrateStudySession();
      await demonstratePurchase();

      print('\n${'=' * 50}');
      print('✨ 示例运行完成！');
    } catch (e) {
      print('❌ 示例运行出错: $e');
    }
  }

  /// 重置所有数据
  Future<void> resetData() async {
    await _achievementService.resetAllData();
    await _wageService.resetAllData();
    print('🔄 所有数据已重置');
  }
}

/// 运行示例的主函数
void main() async {
  final example = AchievementWageExample();

  // 重置数据以确保干净的开始
  await example.resetData();

  // 运行示例
  await example.runFullExample();
}
