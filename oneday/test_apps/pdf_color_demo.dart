import 'package:flutter/material.dart';
import 'package:oneday/features/learning_report/services/learning_report_export_service.dart';
import 'package:oneday/features/learning_report/models/learning_report_models.dart';

/// PDF颜色导出演示应用
/// 
/// 这个演示应用展示了修复后的PDF导出功能，包括：
/// 1. 彩色标题和渐变背景
/// 2. 彩色指标卡片
/// 3. 彩色学科分布图表
/// 4. OneDay品牌色彩方案
class PDFColorDemoApp extends StatelessWidget {
  const PDFColorDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PDF颜色导出演示',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        primaryColor: const Color(0xFF2E7EED),
      ),
      home: const PDFColorDemoPage(),
    );
  }
}

class PDFColorDemoPage extends StatefulWidget {
  const PDFColorDemoPage({super.key});

  @override
  State<PDFColorDemoPage> createState() => _PDFColorDemoPageState();
}

class _PDFColorDemoPageState extends State<PDFColorDemoPage> {
  final LearningReportExportService _exportService = LearningReportExportService();
  bool _isExporting = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('PDF颜色导出演示'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [
                    Color(0xFF2E7EED), // OneDay蓝
                    Color(0xFF1E5FCC), // 深蓝
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'OneDay Learning Report',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    'PDF导出现在支持完整的彩色显示',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.white70,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // 颜色方案展示
            const Text(
              '颜色方案展示',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),

            // 学科颜色
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildColorCard('计算机科学', const Color(0xFFE03E3E)),
                _buildColorCard('数学', const Color(0xFFFFD700)),
                _buildColorCard('英语', const Color(0xFF0F7B6C)),
                _buildColorCard('政治', const Color(0xFF2E7EED)),
                _buildColorCard('休息', const Color(0xFF9B9A97)),
              ],
            ),
            const SizedBox(height: 24),

            // 功能说明
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF0F7B6C).withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFF0F7B6C).withOpacity(0.3),
                ),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '✅ 修复完成的功能',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF0F7B6C),
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 彩色标题和渐变背景\n'
                    '• 彩色指标卡片\n'
                    '• 彩色学科分布图表\n'
                    '• OneDay品牌色彩方案\n'
                    '• 英文降级策略保持稳定性',
                    style: TextStyle(
                      fontSize: 14,
                      color: Color(0xFF37352F),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // 导出按钮
            Center(
              child: ElevatedButton.icon(
                onPressed: _isExporting ? null : _exportTestPDF,
                icon: _isExporting 
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.picture_as_pdf),
                label: Text(_isExporting ? '正在导出...' : '导出彩色PDF测试'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2E7EED),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildColorCard(String subject, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 16,
            height: 16,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            subject,
            style: TextStyle(
              fontSize: 12,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _exportTestPDF() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // 创建测试数据
      final testData = LearningReportData(
        timeRange: TimeRange.today,
        startDate: DateTime.now().subtract(const Duration(days: 7)),
        endDate: DateTime.now(),
        totalStudyTime: const Duration(hours: 12, minutes: 30),
        totalCompletedTasks: 15,
        averageLearningROI: 85.5,
        averageFocusSignalToNoiseRatio: 78.2,
        streakDays: 7,
        subjectDistribution: [
          SubjectDistribution(subject: '计算机科学', studyMinutes: 180),
          SubjectDistribution(subject: '数学', studyMinutes: 120),
          SubjectDistribution(subject: '英语', studyMinutes: 90),
          SubjectDistribution(subject: '政治', studyMinutes: 60),
        ],
        dailyStats: [],
        efficiencyLevel: 'High',
      );

      // 导出PDF
      await _exportService.exportToPDF(
        context: context,
        reportData: testData,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('✅ PDF导出成功！请检查彩色显示效果'),
            backgroundColor: Color(0xFF0F7B6C),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ PDF导出失败: $e'),
            backgroundColor: const Color(0xFFE03E3E),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isExporting = false;
        });
      }
    }
  }
}

void main() {
  runApp(const PDFColorDemoApp());
}
