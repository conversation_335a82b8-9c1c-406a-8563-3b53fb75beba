import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';
import 'package:oneday/features/time_box/managers/task_category_manager.dart';

void main() {
  runApp(const CategoryEditDemoApp());
}

class CategoryEditDemoApp extends StatelessWidget {
  const CategoryEditDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Category Edit Demo',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const CategoryEditDemoPage(),
    );
  }
}

class CategoryEditDemoPage extends StatefulWidget {
  const CategoryEditDemoPage({super.key});

  @override
  State<CategoryEditDemoPage> createState() => _CategoryEditDemoPageState();
}

class _CategoryEditDemoPageState extends State<CategoryEditDemoPage> {
  final TaskCategoryManager _categoryManager = TaskCategoryManager();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    await _categoryManager.loadFromStorage();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类编辑功能演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 功能说明
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '功能说明',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          const Text('✅ 默认分类现在可以编辑名称和图标'),
                          const Text('✅ 所有分类都显示编辑菜单'),
                          const Text('✅ 默认分类仍然不能删除'),
                          const Text('✅ 自定义分类可以编辑和删除'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 分类统计
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '分类统计',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text('总分类数: ${_categoryManager.categories.length}'),
                          Text('默认分类数: ${_categoryManager.categories.where((cat) => cat.isDefault).length}'),
                          Text('自定义分类数: ${_categoryManager.categories.where((cat) => !cat.isDefault).length}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 分类列表预览
                  Text(
                    '分类列表预览',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: _categoryManager.categories.length,
                      itemBuilder: (context, index) {
                        final category = _categoryManager.categories[index];
                        return Card(
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: category.color,
                              child: Icon(
                                _getIconData(category.iconName),
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                            title: Text(category.name),
                            subtitle: Text(
                              category.isDefault ? '系统分类（可编辑）' : '自定义分类',
                              style: TextStyle(
                                color: category.isDefault
                                    ? const Color(0xFF0F7B6C)
                                    : const Color(0xFF9B9A97),
                              ),
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.edit,
                                  color: Colors.blue,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                if (!category.isDefault) ...[
                                  Icon(
                                    Icons.delete,
                                    color: Colors.red,
                                    size: 16,
                                  ),
                                ],
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),

                  // 操作按钮
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _openCategoryManagementPage(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('打开分类管理页面'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _testEditDefaultCategory(),
                          child: const Text('测试编辑默认分类'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _resetCategories(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('重置分类'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _refreshData(),
                          child: const Text('刷新数据'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'computer':
        return Icons.computer;
      case 'calculate':
        return Icons.calculate;
      case 'translate':
        return Icons.translate;
      case 'account_balance':
        return Icons.account_balance;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'more_horiz':
        return Icons.more_horiz;
      case 'school':
        return Icons.school;
      case 'work':
        return Icons.work;
      default:
        return Icons.category;
    }
  }

  void _openCategoryManagementPage() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const TaskCategoryManagementPage(),
      ),
    ).then((_) {
      // 页面返回后刷新数据
      _refreshData();
    });
  }

  void _testEditDefaultCategory() async {
    // 获取第一个默认分类
    final defaultCategory = _categoryManager.categories.firstWhere(
      (cat) => cat.isDefault,
    );

    // 测试更新默认分类
    final success = await _categoryManager.updateCategory(
      defaultCategory.id,
      '${defaultCategory.name}（已编辑）',
      Colors.purple,
      'school',
    );

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(success ? '默认分类编辑成功！' : '编辑失败'),
          backgroundColor: success ? Colors.green : Colors.red,
        ),
      );

      if (success) {
        _refreshData();
      }
    }
  }

  void _resetCategories() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('task_categories_v1');
    await _categoryManager.loadFromStorage();

    if (mounted) {
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分类已重置为默认状态'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    await _categoryManager.loadFromStorage();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
