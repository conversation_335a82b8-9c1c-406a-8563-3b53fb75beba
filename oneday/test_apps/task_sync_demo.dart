import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/providers/timebox_provider.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';
import 'package:oneday/features/time_box/timebox_list_page.dart';

void main() {
  runApp(const ProviderScope(child: TaskSyncDemoApp()));
}

class TaskSyncDemoApp extends StatelessWidget {
  const TaskSyncDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Task Sync Demo',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const TaskSyncDemoPage(),
    );
  }
}

class TaskSyncDemoPage extends ConsumerStatefulWidget {
  const TaskSyncDemoPage({super.key});

  @override
  ConsumerState<TaskSyncDemoPage> createState() => _TaskSyncDemoPageState();
}

class _TaskSyncDemoPageState extends ConsumerState<TaskSyncDemoPage> {
  final _titleController = TextEditingController();

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final timeBoxState = ref.watch(timeBoxProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('任务同步演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 状态信息
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '当前状态',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Text('任务总数: ${timeBoxState.tasks.length}'),
                    Text('加载状态: ${timeBoxState.isLoading ? "加载中" : "已加载"}'),
                    if (timeBoxState.error != null)
                      Text(
                        '错误: ${timeBoxState.error}',
                        style: const TextStyle(color: Colors.red),
                      ),
                    Text(
                      '最后更新: ${timeBoxState.lastUpdated?.toString() ?? "未知"}',
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 创建任务区域
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '创建新任务',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _titleController,
                      decoration: const InputDecoration(
                        labelText: '任务标题',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        ElevatedButton(
                          onPressed: () => _createTask(),
                          child: const Text('创建任务'),
                        ),
                        const SizedBox(width: 8),
                        ElevatedButton(
                          onPressed: () => _clearAllTasks(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('清空所有任务'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // 任务列表
            Text('任务列表', style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 8),
            Expanded(
              child: timeBoxState.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : timeBoxState.tasks.isEmpty
                  ? const Center(
                      child: Text(
                        '暂无任务\n点击上方按钮创建新任务',
                        textAlign: TextAlign.center,
                        style: TextStyle(color: Colors.grey),
                      ),
                    )
                  : ListView.builder(
                      itemCount: timeBoxState.tasks.length,
                      itemBuilder: (context, index) {
                        final task = timeBoxState.tasks[index];
                        return Card(
                          child: ListTile(
                            title: Text(task.title),
                            subtitle: Text(
                              '${task.description}\n'
                              '分类: ${task.category} | '
                              '状态: ${task.status.displayName} | '
                              '时长: ${task.plannedMinutes}分钟',
                            ),
                            trailing: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () => _updateTaskStatus(task),
                                ),
                                IconButton(
                                  icon: const Icon(Icons.delete),
                                  onPressed: () => _deleteTask(task.id),
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
            ),

            // 操作按钮
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _navigateToTimeBoxPage(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('打开TimeBox页面'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () => _refreshData(),
                    child: const Text('刷新数据'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _createTask() async {
    final title = _titleController.text.trim();
    if (title.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('请输入任务标题')));
      return;
    }

    final task = TimeBoxTask(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title,
      description: '通过演示应用创建的任务',
      plannedMinutes: 60,
      status: TaskStatus.pending,
      priority: TaskPriority.medium,
      category: '测试',
      createdAt: DateTime.now(),
    );

    await ref.read(timeBoxProvider.notifier).addTask(task);
    _titleController.clear();

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text('任务 "$title" 创建成功！')));
    }
  }

  void _updateTaskStatus(TimeBoxTask task) async {
    final newStatus = task.status == TaskStatus.pending
        ? TaskStatus.inProgress
        : task.status == TaskStatus.inProgress
        ? TaskStatus.completed
        : TaskStatus.pending;

    final updatedTask = task.copyWith(status: newStatus);
    await ref.read(timeBoxProvider.notifier).updateTask(updatedTask);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('任务状态已更新为: ${newStatus.displayName}')),
      );
    }
  }

  void _deleteTask(String taskId) async {
    await ref.read(timeBoxProvider.notifier).deleteTask(taskId);

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('任务已删除')));
    }
  }

  void _clearAllTasks() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('timebox_tasks_v1');
    await ref.read(timeBoxProvider.notifier).refresh();

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('所有任务已清空')));
    }
  }

  void _refreshData() async {
    await ref.read(timeBoxProvider.notifier).refresh();

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(const SnackBar(content: Text('数据已刷新')));
    }
  }

  void _navigateToTimeBoxPage() {
    Navigator.of(
      context,
    ).push(MaterialPageRoute(builder: (context) => const TimeBoxListPage()));
  }
}
