import 'package:flutter/material.dart';
import 'package:oneday/utils/category_cleanup_helper.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  runApp(const SimpleCleanupTestApp());
}

class SimpleCleanupTestApp extends StatelessWidget {
  const SimpleCleanupTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple Cleanup Test',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const SimpleCleanupTestPage(),
    );
  }
}

class SimpleCleanupTestPage extends StatefulWidget {
  const SimpleCleanupTestPage({super.key});

  @override
  State<SimpleCleanupTestPage> createState() => _SimpleCleanupTestPageState();
}

class _SimpleCleanupTestPageState extends State<SimpleCleanupTestPage> {
  String _statusText = '点击"检查状态"开始诊断';
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkStatus();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类清理测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '状态信息',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.grey.shade100,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Text(
                        _statusText,
                        style: const TextStyle(
                          fontFamily: 'monospace',
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            const SizedBox(height: 16),

            if (_isLoading)
              const Center(child: CircularProgressIndicator())
            else
              Column(
                children: [
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _checkStatus,
                      icon: const Icon(Icons.refresh),
                      label: const Text('检查状态'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _forceCleanup,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.cleaning_services),
                      label: const Text('强制清理为4个系统分类'),
                    ),
                  ),
                  const SizedBox(height: 8),
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _openCategoryPage,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                      ),
                      icon: const Icon(Icons.settings),
                      label: const Text('打开分类管理页面'),
                    ),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _checkStatus() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在检查状态...';
    });

    try {
      // 打印详细诊断信息到控制台
      await CategoryCleanupHelper.printDiagnostics();

      // 获取状态信息显示在UI上
      final status = await CategoryCleanupHelper.checkCategoryStatus();
      final needsCleanup = await CategoryCleanupHelper.needsCleanup();

      String statusText = '';
      if (status['success']) {
        statusText += '📊 总分类数: ${status['totalCategories']}\n';
        statusText += '🏛️ 系统分类数: ${status['systemCategories']}\n';
        statusText += '📝 自定义分类数: ${status['customCategories']}\n';
        statusText += '🔢 数据版本: ${status['version']}\n';
        statusText += '💾 有旧数据: ${status['hasOldData']}\n';
        statusText += '🧹 需要清理: $needsCleanup\n\n';
        statusText +=
            '📋 所有分类:\n${(status['categoryNames'] as List).join(", ")}\n\n';
        statusText +=
            '🏛️ 系统分类:\n${(status['systemCategoryNames'] as List).join(", ")}';

        if (needsCleanup) {
          statusText += '\n\n⚠️ 检测到问题，建议执行清理操作';
        } else {
          statusText += '\n\n✅ 分类状态正常';
        }
      } else {
        statusText = '❌ 检查失败: ${status['error']}';
      }

      setState(() {
        _statusText = statusText;
      });
    } catch (e) {
      setState(() {
        _statusText = '❌ 检查状态失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _forceCleanup() async {
    setState(() {
      _isLoading = true;
      _statusText = '正在执行强制清理...';
    });

    try {
      final success = await CategoryCleanupHelper.forceResetToNewCategories();

      if (success) {
        setState(() {
          _statusText = '✅ 强制清理完成！\n\n正在重新检查状态...';
        });

        // 等待一下然后重新检查状态
        await Future.delayed(const Duration(milliseconds: 500));
        await _checkStatus();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('清理完成！现在应该只有4个系统分类'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        setState(() {
          _statusText = '❌ 强制清理失败';
        });
      }
    } catch (e) {
      setState(() {
        _statusText = '❌ 清理过程中出错: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _openCategoryPage() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const TaskCategoryManagementPage(),
          ),
        )
        .then((_) {
          // 页面返回后重新检查状态
          _checkStatus();
        });
  }
}
