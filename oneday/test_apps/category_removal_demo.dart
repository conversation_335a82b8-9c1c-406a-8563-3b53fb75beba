import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/managers/task_category_manager.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  runApp(const CategoryRemovalDemoApp());
}

class CategoryRemovalDemoApp extends StatelessWidget {
  const CategoryRemovalDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Category Removal Demo',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const CategoryRemovalDemoPage(),
    );
  }
}

class CategoryRemovalDemoPage extends StatefulWidget {
  const CategoryRemovalDemoPage({super.key});

  @override
  State<CategoryRemovalDemoPage> createState() =>
      _CategoryRemovalDemoPageState();
}

class _CategoryRemovalDemoPageState extends State<CategoryRemovalDemoPage> {
  final TaskCategoryManager _categoryManager = TaskCategoryManager();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadCategories();
  }

  Future<void> _loadCategories() async {
    await _categoryManager.loadFromStorage();
    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类删除功能演示'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 功能说明
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '删除功能说明',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          const Text('✅ 已删除"休息"和"其他"系统分类'),
                          const Text('✅ 保留"计算机科学"、"数学"、"英语"、"政治"'),
                          const Text('✅ 现有任务的分类会自动迁移为自定义分类'),
                          const Text('✅ 所有功能仍然正常工作'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 分类统计
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '分类统计',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Text('总分类数: ${_categoryManager.categories.length}'),
                          Text(
                            '系统分类数: ${_categoryManager.getDefaultCategories().length}',
                          ),
                          Text(
                            '自定义分类数: ${_categoryManager.getCustomCategories().length}',
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 系统分类列表
                  Text(
                    '系统分类（${_categoryManager.getDefaultCategories().length}个）',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  ...(_categoryManager.getDefaultCategories().map(
                    (category) => Card(
                      child: ListTile(
                        leading: CircleAvatar(
                          backgroundColor: category.color,
                          child: Icon(
                            _getIconData(category.iconName),
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                        title: Text(category.name),
                        subtitle: const Text('系统分类（可编辑）'),
                        trailing: const Icon(Icons.edit, color: Colors.blue),
                      ),
                    ),
                  )),

                  const SizedBox(height: 16),

                  // 自定义分类列表
                  if (_categoryManager.getCustomCategories().isNotEmpty) ...[
                    Text(
                      '自定义分类（${_categoryManager.getCustomCategories().length}个）',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    ...(_categoryManager.getCustomCategories().map(
                      (category) => Card(
                        child: ListTile(
                          leading: CircleAvatar(
                            backgroundColor: category.color,
                            child: Icon(
                              _getIconData(category.iconName),
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          title: Text(category.name),
                          subtitle: const Text('自定义分类'),
                          trailing: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.edit, color: Colors.blue, size: 16),
                              const SizedBox(width: 4),
                              Icon(Icons.delete, color: Colors.red, size: 16),
                            ],
                          ),
                        ),
                      ),
                    )),
                  ],

                  const Spacer(),

                  // 操作按钮
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _openCategoryManagementPage(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('打开分类管理页面'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _simulateMigration(),
                          child: const Text('模拟数据迁移'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _resetCategories(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('重置分类'),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _refreshData(),
                          child: const Text('刷新数据'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'computer':
        return Icons.computer;
      case 'calculate':
        return Icons.calculate;
      case 'translate':
        return Icons.translate;
      case 'account_balance':
        return Icons.account_balance;
      case 'self_improvement':
        return Icons.self_improvement;
      case 'category':
        return Icons.category;
      case 'school':
        return Icons.school;
      case 'work':
        return Icons.work;
      default:
        return Icons.task_alt;
    }
  }

  void _openCategoryManagementPage() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const TaskCategoryManagementPage(),
          ),
        )
        .then((_) {
          // 页面返回后刷新数据
          _refreshData();
        });
  }

  void _simulateMigration() async {
    // 模拟添加使用删除分类的任务数据
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('timebox_tasks_v1', '''[
      {
        "id": "demo_task_1",
        "title": "休息任务",
        "description": "使用已删除的休息分类",
        "plannedMinutes": 30,
        "status": "pending",
        "priority": "medium",
        "category": "休息",
        "createdAt": "2024-01-01T00:00:00.000Z"
      },
      {
        "id": "demo_task_2",
        "title": "其他任务",
        "description": "使用已删除的其他分类",
        "plannedMinutes": 45,
        "status": "pending",
        "priority": "low",
        "category": "其他",
        "createdAt": "2024-01-01T00:00:00.000Z"
      }
    ]''');

    // 重新加载分类以触发迁移
    await _categoryManager.loadFromStorage();

    if (mounted) {
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('已模拟数据迁移，删除的分类已转为自定义分类'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _resetCategories() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('task_categories_v1');
    await prefs.remove('timebox_tasks_v1');
    await _categoryManager.loadFromStorage();

    if (mounted) {
      setState(() {});
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('分类已重置，只保留4个系统分类'),
          backgroundColor: Colors.blue,
        ),
      );
    }
  }

  void _refreshData() async {
    setState(() {
      _isLoading = true;
    });

    await _categoryManager.loadFromStorage();

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
