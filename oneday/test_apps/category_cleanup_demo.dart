import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:oneday/features/time_box/managers/task_category_manager.dart';
import 'package:oneday/features/time_box/pages/task_category_management_page.dart';

void main() {
  runApp(const CategoryCleanupDemoApp());
}

class CategoryCleanupDemoApp extends StatelessWidget {
  const CategoryCleanupDemoApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Category Cleanup Demo',
      theme: ThemeData(primarySwatch: Colors.blue, useMaterial3: true),
      home: const CategoryCleanupDemoPage(),
    );
  }
}

class CategoryCleanupDemoPage extends StatefulWidget {
  const CategoryCleanupDemoPage({super.key});

  @override
  State<CategoryCleanupDemoPage> createState() =>
      _CategoryCleanupDemoPageState();
}

class _CategoryCleanupDemoPageState extends State<CategoryCleanupDemoPage> {
  final TaskCategoryManager _categoryManager = TaskCategoryManager();
  bool _isLoading = true;
  String _debugInfo = '';

  @override
  void initState() {
    super.initState();
    _loadAndAnalyze();
  }

  Future<void> _loadAndAnalyze() async {
    setState(() {
      _isLoading = true;
      _debugInfo = '';
    });

    try {
      // 获取当前存储信息
      final prefs = await SharedPreferences.getInstance();
      final version = prefs.getInt('task_categories_version') ?? 0;
      final hasData = prefs.getString('task_categories_v1') != null;

      _debugInfo += '📊 存储分析:\n';
      _debugInfo += '- 版本: $version\n';
      _debugInfo += '- 有数据: $hasData\n\n';

      // 加载分类
      await _categoryManager.loadFromStorage();

      _debugInfo += '📋 分类信息:\n';
      _debugInfo += '- 总数量: ${_categoryManager.categories.length}\n';
      _debugInfo +=
          '- 系统分类: ${_categoryManager.getDefaultCategories().length}\n';
      _debugInfo +=
          '- 自定义分类: ${_categoryManager.getCustomCategories().length}\n\n';

      _debugInfo += '📝 分类列表:\n';
      for (final category in _categoryManager.categories) {
        _debugInfo +=
            '- ${category.name} (${category.isDefault ? "系统" : "自定义"})\n';
      }
    } catch (e) {
      _debugInfo += '❌ 错误: $e\n';
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('分类数据清理工具'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 问题说明
                  Card(
                    color: Colors.red.shade50,
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.warning, color: Colors.red),
                              const SizedBox(width: 8),
                              Text(
                                '问题诊断',
                                style: Theme.of(context).textTheme.titleMedium
                                    ?.copyWith(
                                      color: Colors.red,
                                      fontWeight: FontWeight.bold,
                                    ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          const Text('界面仍显示"休息"和"其他"分类，可能原因：'),
                          const Text('• 本地存储中有旧的分类数据'),
                          const Text('• 版本控制未生效'),
                          const Text('• 缓存数据未清理'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  // 调试信息
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '调试信息',
                            style: Theme.of(context).textTheme.titleMedium,
                          ),
                          const SizedBox(height: 8),
                          Container(
                            width: double.infinity,
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.grey.shade100,
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(color: Colors.grey.shade300),
                            ),
                            child: Text(
                              _debugInfo.isEmpty ? '加载中...' : _debugInfo,
                              style: const TextStyle(
                                fontFamily: 'monospace',
                                fontSize: 12,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const Spacer(),

                  // 操作按钮
                  Column(
                    children: [
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton.icon(
                          onPressed: () => _forceCleanupToNewCategories(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          icon: const Icon(Icons.cleaning_services),
                          label: const Text('强制重置为4个系统分类'),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () => _loadAndAnalyze(),
                              child: const Text('重新分析'),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: ElevatedButton(
                              onPressed: () => _openCategoryPage(),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.green,
                                foregroundColor: Colors.white,
                              ),
                              child: const Text('打开分类页面'),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => _clearAllData(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.orange,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('清空所有SharedPreferences'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Future<void> _forceCleanupToNewCategories() async {
    try {
      await _categoryManager.forceResetToNewCategories();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('已重置为4个系统分类！'),
            backgroundColor: Colors.green,
          ),
        );

        // 重新分析
        await _loadAndAnalyze();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('重置失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  Future<void> _clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.clear();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('所有数据已清空！请重启应用'),
            backgroundColor: Colors.orange,
          ),
        );

        // 重新分析
        await _loadAndAnalyze();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('清空失败: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  void _openCategoryPage() {
    Navigator.of(context)
        .push(
          MaterialPageRoute(
            builder: (context) => const TaskCategoryManagementPage(),
          ),
        )
        .then((_) {
          // 页面返回后重新分析
          _loadAndAnalyze();
        });
  }
}
