import 'package:flutter/material.dart';
import 'package:oneday/features/time_box/models/timebox_models.dart';

/// 工资计算测试页面
///
/// 用于验证工资计算逻辑是否正确
class WageCalculationTestPage extends StatefulWidget {
  const WageCalculationTestPage({super.key});

  @override
  State<WageCalculationTestPage> createState() =>
      _WageCalculationTestPageState();
}

class _WageCalculationTestPageState extends State<WageCalculationTestPage> {
  final List<Map<String, dynamic>> _testCases = [];

  @override
  void initState() {
    super.initState();
    _runTests();
  }

  void _runTests() {
    print('🧪 开始工资计算测试...');

    // 测试用例1: 120分钟任务
    final task1 = TimeBoxTask(
      id: 'test1',
      title: '算法复习：动态规划',
      description: '复习背包问题，最长公共子序列等经典算法',
      category: '计算机科学',
      priority: TaskPriority.high,
      plannedMinutes: 120,
      status: TaskStatus.completed,
      startTime: DateTime.now().subtract(const Duration(minutes: 120)),
      endTime: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(minutes: 120)),
    );

    final wage1 = task1.calculateWage();
    final expected1 = (120 / 60.0) * 200.0; // 2小时 * 200元/小时 = 400元

    _testCases.add({
      'title': '120分钟任务',
      'task': task1,
      'actualWage': wage1,
      'expectedWage': expected1,
      'passed': (wage1 - expected1).abs() < 0.01,
    });

    // 测试用例2: 60分钟任务
    final task2 = TimeBoxTask(
      id: 'test2',
      title: '英语单词记忆',
      description: '使用记忆宫殿法记忆GRE词汇100个',
      category: '英语',
      priority: TaskPriority.medium,
      plannedMinutes: 60,
      status: TaskStatus.completed,
      startTime: DateTime.now().subtract(const Duration(minutes: 60)),
      endTime: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(minutes: 60)),
    );

    final wage2 = task2.calculateWage();
    final expected2 = (60 / 60.0) * 200.0; // 1小时 * 200元/小时 = 200元

    _testCases.add({
      'title': '60分钟任务',
      'task': task2,
      'actualWage': wage2,
      'expectedWage': expected2,
      'passed': (wage2 - expected2).abs() < 0.01,
    });

    // 测试用例3: 90分钟任务
    final task3 = TimeBoxTask(
      id: 'test3',
      title: '数学练习：微积分',
      description: '完成教材第5章练习题',
      category: '数学',
      priority: TaskPriority.medium,
      plannedMinutes: 90,
      status: TaskStatus.completed,
      startTime: DateTime.now().subtract(const Duration(minutes: 90)),
      endTime: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(minutes: 90)),
    );

    final wage3 = task3.calculateWage();
    final expected3 = (90 / 60.0) * 200.0; // 1.5小时 * 200元/小时 = 300元

    _testCases.add({
      'title': '90分钟任务',
      'task': task3,
      'actualWage': wage3,
      'expectedWage': expected3,
      'passed': (wage3 - expected3).abs() < 0.01,
    });

    // 测试用例4: 总计406分钟（用户报告的情况）
    final totalMinutes = 120 + 60 + 90 + 136; // 406分钟
    final totalExpectedWage =
        (totalMinutes / 60.0) * 200.0; // 6.77小时 * 200元/小时 = 1354元

    // 添加第4个任务来达到406分钟
    final task4 = TimeBoxTask(
      id: 'test4',
      title: '政治理论学习',
      description: '马克思主义基本原理重点章节复习',
      category: '政治',
      priority: TaskPriority.low,
      plannedMinutes: 136, // 使总计达到406分钟
      status: TaskStatus.completed,
      startTime: DateTime.now().subtract(const Duration(minutes: 136)),
      endTime: DateTime.now(),
      createdAt: DateTime.now().subtract(const Duration(minutes: 136)),
    );

    final wage4 = task4.calculateWage();
    final totalWageWith4Tasks = wage1 + wage2 + wage3 + wage4;

    _testCases.add({
      'title': '136分钟任务',
      'task': task4,
      'actualWage': wage4,
      'expectedWage': (136 / 60.0) * 200.0,
      'passed': (wage4 - (136 / 60.0) * 200.0).abs() < 0.01,
    });

    _testCases.add({
      'title': '总计406分钟（用户情况）',
      'task': null,
      'actualWage': totalWageWith4Tasks,
      'expectedWage': totalExpectedWage,
      'passed': (totalWageWith4Tasks - totalExpectedWage).abs() < 0.01,
    });

    setState(() {});

    // 输出测试结果
    print('📊 工资计算测试结果:');
    for (final testCase in _testCases) {
      final status = testCase['passed'] ? '✅' : '❌';
      print(
        '$status ${testCase['title']}: 实际=¥${testCase['actualWage'].toStringAsFixed(2)}, 期望=¥${testCase['expectedWage'].toStringAsFixed(2)}',
      );
    }

    final allPassed = _testCases.every((test) => test['passed']);
    print(allPassed ? '🎉 所有测试通过！' : '⚠️ 有测试失败！');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('工资计算测试'),
        backgroundColor: const Color(0xFF2E7EED),
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '工资计算测试结果',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            const Text(
              '基准: 每小时 ¥200',
              style: TextStyle(fontSize: 16, color: Colors.grey),
            ),
            const SizedBox(height: 16),
            Expanded(
              child: ListView.builder(
                itemCount: _testCases.length,
                itemBuilder: (context, index) {
                  final testCase = _testCases[index];
                  final passed = testCase['passed'] as bool;

                  return Card(
                    margin: const EdgeInsets.only(bottom: 8),
                    child: ListTile(
                      leading: Icon(
                        passed ? Icons.check_circle : Icons.error,
                        color: passed ? Colors.green : Colors.red,
                      ),
                      title: Text(testCase['title']),
                      subtitle: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '实际工资: ¥${testCase['actualWage'].toStringAsFixed(2)}',
                          ),
                          Text(
                            '期望工资: ¥${testCase['expectedWage'].toStringAsFixed(2)}',
                          ),
                          if (testCase['task'] != null)
                            Text(
                              '时长: ${(testCase['task'] as TimeBoxTask).durationMinutes}分钟',
                            ),
                        ],
                      ),
                      trailing: Text(
                        passed ? '通过' : '失败',
                        style: TextStyle(
                          color: passed ? Colors.green : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF7F6F3),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '📝 测试说明',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  const Text('• 工资计算公式: (实际时长分钟 ÷ 60) × 200元/小时'),
                  const Text('• 406分钟 = 6.77小时 = ¥1,354'),
                  const Text('• 只有已完成的任务才计算工资'),
                  const SizedBox(height: 8),
                  Text(
                    '总测试数: ${_testCases.length}',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                  Text(
                    '通过数: ${_testCases.where((test) => test['passed']).length}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: Colors.green,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

void main() {
  runApp(MaterialApp(home: const WageCalculationTestPage(), title: '工资计算测试'));
}
