import 'package:flutter/material.dart';

void main() {
  runApp(const SimpleDialogTestApp());
}

class SimpleDialogTestApp extends StatelessWidget {
  const SimpleDialogTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Simple Dialog Test',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const TestHomePage(),
    );
  }
}

class TestHomePage extends StatelessWidget {
  const TestHomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('对话框测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _showTestDialog(context),
          ),
        ],
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              '点击右上角的 + 按钮测试对话框',
              style: TextStyle(fontSize: 18),
            ),
            SizedBox(height: 20),
            Text(
              '这是一个简化的测试版本',
              style: TextStyle(fontSize: 14, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  void _showTestDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const SimpleTestDialog(),
    );
  }
}

class SimpleTestDialog extends StatefulWidget {
  const SimpleTestDialog({super.key});

  @override
  State<SimpleTestDialog> createState() => _SimpleTestDialogState();
}

class _SimpleTestDialogState extends State<SimpleTestDialog> {
  final _nameController = TextEditingController();
  Color _selectedColor = const Color(0xFFE03E3E);
  String _selectedIcon = 'task_alt';

  static const List<Color> _colors = [
    Color(0xFFE03E3E), // 红色
    Color(0xFFFFD700), // 黄色
    Color(0xFF0F7B6C), // 绿色
    Color(0xFF2E7EED), // 蓝色
    Color(0xFF9B9A97), // 灰色
  ];

  static const List<String> _icons = [
    'task_alt',
    'work',
    'school',
    'computer',
    'calculate',
  ];

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      title: const Text('添加分类'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 名称输入
            TextField(
              controller: _nameController,
              decoration: const InputDecoration(
                labelText: '分类名称',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 16),

            // 颜色选择
            const Text('选择颜色', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _colors.map((color) {
                final isSelected = color == _selectedColor;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedColor = color;
                    });
                  },
                  child: Container(
                    width: 32,
                    height: 32,
                    decoration: BoxDecoration(
                      color: color,
                      shape: BoxShape.circle,
                      border: isSelected
                          ? Border.all(color: Colors.black, width: 3)
                          : null,
                    ),
                  ),
                );
              }).toList(),
            ),
            const SizedBox(height: 16),

            // 图标选择
            const Text('选择图标', style: TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            Wrap(
              spacing: 8,
              children: _icons.map((iconName) {
                final isSelected = iconName == _selectedIcon;
                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedIcon = iconName;
                    });
                  },
                  child: Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: isSelected
                          ? _selectedColor.withValues(alpha: 0.2)
                          : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: isSelected ? _selectedColor : Colors.grey,
                        width: isSelected ? 2 : 1,
                      ),
                    ),
                    child: Icon(
                      _getIconData(iconName),
                      color: isSelected ? _selectedColor : Colors.grey,
                      size: 20,
                    ),
                  ),
                );
              }).toList(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('取消'),
        ),
        ElevatedButton(
          onPressed: () {
            final name = _nameController.text.trim();
            if (name.isNotEmpty) {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('创建分类: $name'),
                  backgroundColor: Colors.green,
                ),
              );
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: const Color(0xFF2E7EED),
            foregroundColor: Colors.white,
          ),
          child: const Text('保存'),
        ),
      ],
    );
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'task_alt':
        return Icons.task_alt;
      case 'work':
        return Icons.work;
      case 'school':
        return Icons.school;
      case 'computer':
        return Icons.computer;
      case 'calculate':
        return Icons.calculate;
      default:
        return Icons.task_alt;
    }
  }
}
