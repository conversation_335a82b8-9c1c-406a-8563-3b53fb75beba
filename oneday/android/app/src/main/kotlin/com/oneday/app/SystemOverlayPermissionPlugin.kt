package com.oneday.app

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.PixelFormat
import android.graphics.drawable.GradientDrawable
import android.net.Uri
import android.os.Build
import android.provider.Settings
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import android.widget.LinearLayout
import android.widget.TextView
import androidx.annotation.NonNull
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.embedding.engine.plugins.activity.ActivityAware
import io.flutter.embedding.engine.plugins.activity.ActivityPluginBinding
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

/** SystemOverlayPermissionPlugin */
class SystemOverlayPermissionPlugin: FlutterPlugin, MethodCallHandler, ActivityAware {
  private lateinit var channel: MethodChannel
  private var activity: Activity? = null
  private var context: Context? = null
  private var windowManager: WindowManager? = null
  private var overlayView: View? = null
  private var isOverlayShowing = false

  override fun onAttachedToEngine(@NonNull flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
    channel = MethodChannel(flutterPluginBinding.binaryMessenger, "system_overlay_permission")
    channel.setMethodCallHandler(this)
    context = flutterPluginBinding.applicationContext
    windowManager = context?.getSystemService(Context.WINDOW_SERVICE) as WindowManager?
  }

  override fun onMethodCall(@NonNull call: MethodCall, @NonNull result: Result) {
    when (call.method) {
      "hasPermission" -> {
        result.success(hasOverlayPermission())
      }
      "requestPermission" -> {
        requestOverlayPermission()
        result.success(true)
      }
      "openSettings" -> {
        openOverlaySettings()
        result.success(null)
      }
      "showSystemOverlay" -> {
        val taskTitle = call.argument<String>("taskTitle") ?: ""
        val remainingTime = call.argument<String>("remainingTime") ?: "00:00"
        showSystemOverlay(taskTitle, remainingTime)
        result.success(true)
      }
      "hideSystemOverlay" -> {
        hideSystemOverlay()
        result.success(true)
      }
      "updateSystemOverlay" -> {
        val taskTitle = call.argument<String>("taskTitle") ?: ""
        val remainingTime = call.argument<String>("remainingTime") ?: "00:00"
        updateSystemOverlay(taskTitle, remainingTime)
        result.success(true)
      }
      else -> {
        result.notImplemented()
      }
    }
  }

  private fun hasOverlayPermission(): Boolean {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
      Settings.canDrawOverlays(context)
    } else {
      true
    }
  }

  private fun requestOverlayPermission() {
    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context)) {
      val intent = Intent(
        Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
        Uri.parse("package:${context?.packageName}")
      )
      intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
      activity?.startActivity(intent)
    }
  }

  private fun openOverlaySettings() {
    val intent = Intent(
      Settings.ACTION_MANAGE_OVERLAY_PERMISSION,
      Uri.parse("package:${context?.packageName}")
    )
    intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
    activity?.startActivity(intent)
  }

  private fun showSystemOverlay(taskTitle: String, remainingTime: String) {
    if (!hasOverlayPermission() || context == null || windowManager == null) {
      return
    }

    // 如果已经显示，先隐藏
    if (isOverlayShowing) {
      hideSystemOverlay()
    }

    try {
      // 创建悬浮窗布局
      overlayView = createOverlayView(taskTitle, remainingTime)

      // 设置悬浮窗参数
      val params = WindowManager.LayoutParams(
        dpToPx(160), // 宽度160dp
        dpToPx(60),  // 高度60dp
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
          WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY
        } else {
          WindowManager.LayoutParams.TYPE_PHONE
        },
        WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or
        WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL or
        WindowManager.LayoutParams.FLAG_WATCH_OUTSIDE_TOUCH,
        PixelFormat.TRANSLUCENT
      )

      // 设置初始位置
      params.gravity = Gravity.TOP or Gravity.START
      params.x = dpToPx(20)
      params.y = dpToPx(100)

      // 添加到WindowManager
      windowManager?.addView(overlayView, params)
      isOverlayShowing = true

      // 设置拖拽功能
      setupDragListener(params)

    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun hideSystemOverlay() {
    try {
      if (overlayView != null && windowManager != null && isOverlayShowing) {
        windowManager?.removeView(overlayView)
        overlayView = null
        isOverlayShowing = false
      }
    } catch (e: Exception) {
      e.printStackTrace()
    }
  }

  private fun updateSystemOverlay(taskTitle: String, remainingTime: String) {
    if (overlayView != null && isOverlayShowing) {
      try {
        val titleView = overlayView?.findViewById<TextView>(android.R.id.text1)
        val timeView = overlayView?.findViewById<TextView>(android.R.id.text2)

        titleView?.text = taskTitle
        timeView?.text = remainingTime

        // 强制重绘视图
        overlayView?.invalidate()
        overlayView?.requestLayout()

        // 调试日志
        android.util.Log.d("SystemOverlay", "更新悬浮窗: $taskTitle - $remainingTime")
      } catch (e: Exception) {
        android.util.Log.e("SystemOverlay", "更新悬浮窗失败: ${e.message}")
      }
    } else {
      android.util.Log.w("SystemOverlay", "悬浮窗不存在或未显示，无法更新")
    }
  }

  private fun createOverlayView(taskTitle: String, remainingTime: String): View {
    val ctx = context ?: return TextView(context).apply { text = "Error: No context" }

    // 创建主容器
    val mainContainer = FrameLayout(ctx)
    mainContainer.layoutParams = FrameLayout.LayoutParams(
      dpToPx(160),
      dpToPx(60)
    )

    // 设置圆角背景
    val background = GradientDrawable()
    background.setColor(Color.parseColor("#E6FFFFFF")) // 90% 透明度白色
    background.cornerRadius = dpToPx(8).toFloat()
    background.setStroke(dpToPx(1), Color.parseColor("#4D2E7EED")) // 30% 透明度蓝色边框
    mainContainer.background = background

    // 创建内容容器
    val contentContainer = LinearLayout(ctx)
    contentContainer.orientation = LinearLayout.VERTICAL
    contentContainer.setPadding(dpToPx(6), dpToPx(6), dpToPx(6), dpToPx(6))

    // 第一行：标题和关闭按钮的容器
    val firstRowContainer = FrameLayout(ctx)
    val firstRowParams = LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    )
    firstRowContainer.layoutParams = firstRowParams

    // 标题TextView（居中）
    val titleTextView = TextView(ctx)
    titleTextView.id = android.R.id.text1
    titleTextView.text = taskTitle
    titleTextView.textSize = 10f
    titleTextView.setTextColor(Color.parseColor("#FF37352F"))
    titleTextView.gravity = Gravity.CENTER
    titleTextView.maxLines = 1
    titleTextView.ellipsize = android.text.TextUtils.TruncateAt.END

    val titleParams = FrameLayout.LayoutParams(
      FrameLayout.LayoutParams.MATCH_PARENT,
      FrameLayout.LayoutParams.WRAP_CONTENT
    )
    titleParams.leftMargin = dpToPx(16)
    titleParams.rightMargin = dpToPx(16)
    titleTextView.layoutParams = titleParams

    // 关闭按钮
    val closeButton = TextView(ctx)
    closeButton.id = android.R.id.button1
    closeButton.text = "×"
    closeButton.textSize = 8f
    closeButton.setTextColor(Color.WHITE)
    closeButton.gravity = Gravity.CENTER
    closeButton.width = dpToPx(12)
    closeButton.height = dpToPx(12)

    // 关闭按钮背景
    val closeBackground = GradientDrawable()
    closeBackground.setColor(Color.parseColor("#FF9E9E9E")) // 灰色背景
    closeBackground.shape = GradientDrawable.OVAL
    closeButton.background = closeBackground

    val closeParams = FrameLayout.LayoutParams(
      dpToPx(12),
      dpToPx(12)
    )
    closeParams.gravity = Gravity.TOP or Gravity.END
    closeButton.layoutParams = closeParams

    // 设置关闭按钮点击事件
    closeButton.setOnClickListener {
      hideSystemOverlay()
      // 通知Flutter层窗口已关闭
      channel.invokeMethod("onOverlayClosed", null)
    }

    // 添加标题和关闭按钮到第一行容器
    firstRowContainer.addView(titleTextView)
    firstRowContainer.addView(closeButton)

    // 第二行：时间显示
    val timeTextView = TextView(ctx)
    timeTextView.id = android.R.id.text2
    timeTextView.text = remainingTime
    timeTextView.textSize = 14f
    timeTextView.setTextColor(Color.parseColor("#FF37352F"))
    timeTextView.gravity = Gravity.CENTER
    timeTextView.typeface = android.graphics.Typeface.MONOSPACE
    timeTextView.setTypeface(timeTextView.typeface, android.graphics.Typeface.BOLD)

    val timeParams = LinearLayout.LayoutParams(
      LinearLayout.LayoutParams.MATCH_PARENT,
      LinearLayout.LayoutParams.WRAP_CONTENT
    )
    timeParams.topMargin = dpToPx(4)
    timeTextView.layoutParams = timeParams

    // 添加到内容容器
    contentContainer.addView(firstRowContainer)
    contentContainer.addView(timeTextView)

    // 添加到主容器
    mainContainer.addView(contentContainer)

    return mainContainer
  }

  private fun setupDragListener(params: WindowManager.LayoutParams) {
    var initialX = 0
    var initialY = 0
    var initialTouchX = 0f
    var initialTouchY = 0f

    overlayView?.setOnTouchListener { _, event ->
      when (event.action) {
        MotionEvent.ACTION_DOWN -> {
          initialX = params.x
          initialY = params.y
          initialTouchX = event.rawX
          initialTouchY = event.rawY
          true
        }
        MotionEvent.ACTION_MOVE -> {
          params.x = initialX + (event.rawX - initialTouchX).toInt()
          params.y = initialY + (event.rawY - initialTouchY).toInt()
          windowManager?.updateViewLayout(overlayView, params)
          true
        }
        else -> false
      }
    }
  }

  private fun dpToPx(dp: Int): Int {
    val density = context?.resources?.displayMetrics?.density ?: 1f
    return (dp * density).toInt()
  }

  override fun onDetachedFromEngine(@NonNull binding: FlutterPlugin.FlutterPluginBinding) {
    channel.setMethodCallHandler(null)
  }

  override fun onAttachedToActivity(binding: ActivityPluginBinding) {
    activity = binding.activity
  }

  override fun onDetachedFromActivityForConfigChanges() {
    activity = null
  }

  override fun onReattachedToActivityForConfigChanges(binding: ActivityPluginBinding) {
    activity = binding.activity
  }

  override fun onDetachedFromActivity() {
    activity = null
  }
}
