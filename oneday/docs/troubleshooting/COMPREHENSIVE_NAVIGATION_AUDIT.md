# OneDay 应用导航问题全面审计报告

## 🔍 审计概述

基于对 OneDay 应用的全面检查，发现了多个页面仍在使用 `MaterialPageRoute` 进行导航，这些页面可能存在底部导航栏没有正确隐藏的问题。

## 📊 发现的问题页面

### 🚨 高优先级问题（需要立即修复）

#### 1. 具身记忆相关页面
**文件**: `lib/features/exercise/exercise_library_page.dart`

**问题导航**:
- **自定义动作库编辑器** (行 761-767)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => CustomLibraryEditorPage(...)
    ),
  );
  ```

- **运动会话页面** (行 1016-1027, 1285-1294, 1333-1342)
  ```dart
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => ExerciseSessionPage(...)
    ),
  );
  ```

**影响**: 从具身记忆页面进入编辑器和运动会话时，底部导航栏仍然显示

#### 2. 记忆宫殿相关页面
**文件**: `lib/features/memory_palace/palace_manager_page.dart`

**问题导航**:
- **场景详情页面** (行 966-978)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => SceneDetailPage(...)
    ),
  );
  ```

- **相册创建页面** (行 2771-2776)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => PhotoAlbumCreatorPage(...)
    ),
  );
  ```

**影响**: 从记忆宫殿进入场景详情和相册创建时，底部导航栏仍然显示

#### 3. 词汇管理相关页面
**文件**: `lib/features/vocabulary/vocabulary_manager_page.dart`

**问题导航**:
- **词汇分类页面** (行 298-302)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => VocabularyCategoryPage(...)
    ),
  );
  ```

- **创建词库页面** (行 312-316)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => const CreateVocabularyPage()
    ),
  );
  ```

- **词汇详情页面** (行 693-697)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => VocabularyDetailPage(...)
    ),
  );
  ```

**影响**: 从词汇管理进入分类、创建、详情页面时，底部导航栏仍然显示

#### 4. 时间盒子相关页面
**文件**: `lib/features/time_box/timebox_list_page.dart`

**问题导航**:
- **动觉学习页面** (行 450-470, 934-950)
  ```dart
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => KinestheticLearningPage(...)
    ),
  );
  ```

**影响**: 从时间盒子进入动觉学习时，底部导航栏仍然显示

### ⚠️ 中优先级问题

#### 5. 相册创建页面
**文件**: `lib/features/photo_album/photo_album_creator_page.dart`

**问题导航**:
- **图片预览页面** (行 837-842)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => PhotoPreviewPage(...)
    ),
  );
  ```

#### 6. 自定义动作库管理
**文件**: `lib/features/exercise/manage_custom_libraries_dialog.dart`

**问题导航**:
- **自定义动作库编辑器** (行 390-395)
  ```dart
  Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => CustomLibraryEditorPage(...)
    ),
  );
  ```

#### 7. 词汇学习页面
**文件**: `lib/features/vocabulary/vocabulary_category_page.dart`

**问题导航**:
- **运动会话页面** (行 558-569)
  ```dart
  final result = await Navigator.of(context).push(
    MaterialPageRoute(
      builder: (context) => ExerciseSessionPage(...)
    ),
  );
  ```

## 🎯 路由分类分析

### 当前正确的独立路由（已修复）
- `/inventory` - 背包页面 ✅
- `/community-post-editor` - 编辑文章页面 ✅
- `/wage-wallet` - 工资钱包页面 ✅
- `/settings` - 设置页面 ✅
- `/exercise` - 具身记忆页面 ✅
- `/timebox` - 时间盒子页面 ✅
- `/memory-palace` - 知忆相册页面 ✅

### 需要添加的独立路由
- `/custom-library-editor` - 自定义动作库编辑器
- `/exercise-session` - 运动会话页面
- `/scene-detail` - 场景详情页面
- `/photo-album-creator` - 相册创建页面
- `/vocabulary-category` - 词汇分类页面
- `/create-vocabulary` - 创建词库页面
- `/vocabulary-detail` - 词汇详情页面
- `/kinesthetic-learning` - 动觉学习页面
- `/photo-preview` - 图片预览页面

## 🔧 修复建议

### 优先级排序

**P0 - 立即修复**:
1. **运动会话页面** - 影响多个功能模块
2. **动觉学习页面** - 时间盒子核心功能
3. **场景详情页面** - 记忆宫殿核心功能

**P1 - 高优先级**:
4. **自定义动作库编辑器** - 具身记忆功能
5. **词汇分类页面** - 词汇管理功能
6. **相册创建页面** - 记忆宫殿功能

**P2 - 中优先级**:
7. **创建词库页面** - 词汇管理功能
8. **词汇详情页面** - 词汇管理功能
9. **图片预览页面** - 相册功能

### 修复策略

1. **路由配置扩展** - 在 `app_router.dart` 中添加新的独立路由
2. **导航方式统一** - 将所有 `MaterialPageRoute` 改为 `context.push()`
3. **参数传递优化** - 使用 `extra` 参数传递复杂数据
4. **分批修复** - 按优先级分批进行修复，避免一次性改动过大

## 📋 修复检查清单

### 需要修复的文件
- [ ] `lib/features/exercise/exercise_library_page.dart`
- [ ] `lib/features/memory_palace/palace_manager_page.dart`
- [ ] `lib/features/vocabulary/vocabulary_manager_page.dart`
- [ ] `lib/features/time_box/timebox_list_page.dart`
- [ ] `lib/features/photo_album/photo_album_creator_page.dart`
- [ ] `lib/features/exercise/manage_custom_libraries_dialog.dart`
- [ ] `lib/features/vocabulary/vocabulary_category_page.dart`

### 需要添加的路由
- [ ] `/custom-library-editor`
- [ ] `/exercise-session`
- [ ] `/scene-detail`
- [ ] `/photo-album-creator`
- [ ] `/vocabulary-category`
- [ ] `/create-vocabulary`
- [ ] `/vocabulary-detail`
- [ ] `/kinesthetic-learning`
- [ ] `/photo-preview`

## 🚀 预期效果

修复完成后，所有子页面都将：
- ✅ 正确隐藏底部导航栏
- ✅ 提供一致的全屏体验
- ✅ 使用统一的导航方式
- ✅ 支持正确的返回导航

## 📊 影响评估

**用户体验影响**: 高
- 当前多个核心功能的子页面显示底部导航栏，影响沉浸式体验

**修复复杂度**: 中等
- 需要修改多个文件，但修复模式已经确立

**测试需求**: 高
- 需要全面测试所有修复的导航路径

这个审计报告为后续的修复工作提供了清晰的路线图和优先级指导。
