# 如何测试底部导航栏隐藏修复

## 🎯 测试目标

验证以下页面的底部导航栏是否正确隐藏：
1. **商城背包页面**
2. **社区编辑文章页面**
3. **工资钱包页面**

## 📱 测试步骤

### 方法一：使用路由调试工具（推荐）

1. **启动应用**
   ```bash
   flutter run --debug
   ```

2. **进入路由调试页面**
   - 导航到 "我的" 页面（底部导航栏最右侧）
   - 点击右上角设置图标
   - 在开发者工具中选择 "路由调试"

3. **测试各个页面**
   - 点击 "背包页面" 按钮
   - 点击 "编辑文章页面" 按钮
   - 点击 "工资钱包页面" 按钮
   - 验证这些页面都没有底部导航栏

### 方法二：手动测试

#### 测试商城背包功能

1. **进入商城页面**
   - 点击底部导航栏的 "商城" 标签
   - 确认底部导航栏正常显示

2. **进入背包页面**
   - 点击商城页面右上角的背包图标 📦
   - **验证点**：背包页面应该没有底部导航栏

3. **返回商城页面**
   - 点击背包页面左上角的返回按钮
   - **验证点**：返回商城页面后底部导航栏重新显示

#### 测试社区编辑文章功能

1. **进入社区页面**
   - 点击底部导航栏的 "社区" 标签
   - 确认底部导航栏正常显示

2. **进入编辑文章页面**
   - 点击社区页面右下角的编辑按钮 ✏️
   - **验证点**：编辑文章页面应该没有底部导航栏

3. **返回社区页面**
   - 点击编辑页面的关闭按钮
   - **验证点**：返回社区页面后底部导航栏重新显示

#### 测试工资钱包页面

1. **进入工资钱包页面**
   - 从任意主页面导航到工资钱包
   - **验证点**：工资钱包页面应该没有底部导航栏

2. **返回上一页面**
   - 点击工资钱包页面的返回按钮
   - **验证点**：返回后底部导航栏重新显示

## ✅ 预期结果

### 修复前的问题
- ❌ 背包页面显示底部导航栏
- ❌ 编辑文章页面显示底部导航栏
- ❌ 用户体验不一致

### 修复后的效果
- ✅ **背包页面** - 全屏显示，无底部导航栏
- ✅ **编辑文章页面** - 全屏显示，无底部导航栏
- ✅ **工资钱包页面** - 全屏显示，无底部导航栏
- ✅ **返回导航** - 正确恢复底部导航栏
- ✅ **用户体验** - 一致的全屏页面体验

## 🔧 技术验证

### 路由层级检查

**ShellRoute 页面（显示底部导航栏）**：
- `/home` - 首页
- `/calendar` - 日历
- `/store` - 商城
- `/community` - 社区
- `/profile` - 个人中心

**独立路由页面（隐藏底部导航栏）**：
- `/inventory` - 背包页面
- `/community-post-editor` - 编辑文章页面
- `/wage-wallet` - 工资钱包页面

### 导航方式验证

**正确的导航方式**：
```dart
// ✅ 使用 GoRouter
context.push('/inventory');
context.push('/community-post-editor');
context.push('/wage-wallet');
```

**错误的导航方式**：
```dart
// ❌ 使用 MaterialPageRoute（已修复）
Navigator.push(context, MaterialPageRoute(...));
```

## 🐛 故障排除

### 如果底部导航栏仍然显示

1. **热重载应用**
   ```bash
   # 在 Flutter 控制台中按 'r'
   r
   ```

2. **热重启应用**
   ```bash
   # 在 Flutter 控制台中按 'R'
   R
   ```

3. **完全重启应用**
   ```bash
   # 停止应用
   q
   # 重新启动
   flutter run --debug
   ```

4. **清理并重新构建**
   ```bash
   flutter clean
   flutter pub get
   flutter run --debug
   ```

### 如果路由调试工具不可用

确保应用在调试模式下运行：
```bash
flutter run --debug
```

路由调试工具只在调试模式下的开发者工具中可用。

## 📊 测试报告模板

测试完成后，可以使用以下模板记录结果：

```
## 测试结果

### 商城背包功能
- [ ] 商城页面显示底部导航栏
- [ ] 背包页面隐藏底部导航栏
- [ ] 返回商城页面恢复底部导航栏

### 社区编辑文章功能
- [ ] 社区页面显示底部导航栏
- [ ] 编辑文章页面隐藏底部导航栏
- [ ] 返回社区页面恢复底部导航栏

### 工资钱包功能
- [ ] 工资钱包页面隐藏底部导航栏
- [ ] 返回上一页面恢复底部导航栏

### 整体体验
- [ ] 页面切换流畅
- [ ] 导航逻辑一致
- [ ] 无异常或错误

测试时间：____
测试设备：____
测试结果：✅ 通过 / ❌ 失败
备注：____
```

## 🚀 下一步

如果测试通过，说明底部导航栏隐藏问题已经成功修复。如果仍有问题，请：

1. 检查控制台是否有错误信息
2. 确认应用使用的是最新代码
3. 尝试完全重新构建应用
4. 查看详细的修复文档：`BOTTOM_NAVIGATION_BAR_FIX.md`

修复成功后，用户将享受到一致、专业的全屏页面体验！
