# 用户指南

本目录包含面向开发者和用户的操作指南、使用手册和故障排除文档。

## 📋 文档分类

### 👨‍💻 开发者指南
- [开发者入口指南](DEVELOPER_ENTRANCE_GUIDE.md) - 开发者模式的访问和使用指南

### 🛠️ 设置和配置指南
- [图标设置指南](ICON_SETUP_GUIDE.md) - 应用图标的设置和配置方法

### ⏰ 计时器功能指南
- [浮动计时器调试指南](FLOATING_TIMER_DEBUG_GUIDE.md) - 浮动计时器功能的调试方法
- [系统浮动计时器用户指南](SYSTEM_FLOATING_TIMER_USER_GUIDE.md) - 系统级浮动计时器的使用说明
- [计时器浮动窗口指南](TIMER_FLOATING_WINDOW_GUIDE.md) - 计时器浮动窗口的操作指南

## 🎯 指南类型

### 📖 用户操作指南
面向最终用户的功能使用说明
- 基础功能操作
- 高级功能使用
- 常见操作技巧
- 个性化设置

### 🔧 开发者指南
面向开发者的技术指南
- 开发环境搭建
- 调试工具使用
- 开发者模式功能
- 技术实现细节

### ⚙️ 配置指南
系统配置和设置相关
- 应用配置选项
- 系统集成设置
- 权限配置说明
- 性能优化设置

### 🆘 故障排除指南
常见问题的解决方法
- 安装和启动问题
- 功能异常处理
- 性能问题诊断
- 兼容性问题解决

## 📱 功能使用指南

### 时间盒子系统
- **创建时间盒**: 如何创建和配置学习任务
- **计时功能**: 专注计时和休息提醒的使用
- **进度追踪**: 查看学习进度和统计数据
- **工资计算**: 虚拟工资的计算和查看

### 记忆宫殿功能
- **图片上传**: 如何上传和管理地点图片
- **锚点标注**: 在图片上添加和编辑记忆锚点
- **场景切换**: 在不同记忆场景间切换
- **数据同步**: 记忆宫殿数据的备份和同步

### PAO 动作库
- **动作搜索**: 如何搜索和筛选动作
- **分类浏览**: 按类别浏览动作内容
- **个人收藏**: 收藏常用动作
- **训练模式**: 使用动作进行记忆训练

### 社区功能
- **文章浏览**: 浏览社区学习文章
- **内容分享**: 分享学习心得和经验
- **互动交流**: 与其他用户交流学习
- **资源下载**: 下载学习资源和材料

## 🔧 技术支持

### 常见问题解答 (FAQ)
- 应用无法启动怎么办？
- 数据丢失如何恢复？
- 功能异常如何处理？
- 性能问题如何优化？

### 系统要求
- **iOS**: iOS 12.0 或更高版本
- **Android**: Android API 21 (Android 5.0) 或更高版本
- **Web**: 现代浏览器支持 (Chrome, Safari, Firefox, Edge)
- **Desktop**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)

### 权限说明
- **存储权限**: 用于保存学习数据和图片
- **网络权限**: 用于数据同步和社区功能
- **通知权限**: 用于学习提醒和计时通知
- **相机权限**: 用于拍摄记忆宫殿图片

## 📞 获取帮助

### 技术支持渠道
- **文档查阅**: 优先查阅相关文档和指南
- **问题反馈**: 通过应用内反馈功能报告问题
- **社区求助**: 在用户社区寻求帮助
- **开发者联系**: 联系开发团队获取技术支持

### 反馈和建议
- 功能改进建议
- 用户体验反馈
- Bug 报告和问题反馈
- 新功能需求提交

## 🔗 相关链接

- [核心文档](../core/) - 了解产品功能和设计理念
- [开发文档](../development/) - 查看技术实现细节
- [问题解决](../troubleshooting/) - 查找问题解决方案
- [测试文档](../testing/) - 了解功能测试和验证

---

**维护说明**: 用户指南应该保持简洁明了，定期根据用户反馈和功能更新进行维护和改进。
