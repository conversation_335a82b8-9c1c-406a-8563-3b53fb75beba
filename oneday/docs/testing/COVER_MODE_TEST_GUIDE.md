# Cover模式沉浸式体验测试指南

## 🎯 功能概述

在Cover模式下，照片查看器会自动隐藏顶部工具栏和底部预览栏，提供沉浸式的全屏体验，便于用户专注于图片内容和添加知识点标记。

## 🧪 测试步骤

### 1. 进入照片查看器
1. 启动OneDay应用
2. 导航到"记忆宫殿"页面
3. 选择任意一个宫殿进入详情页面
4. 确保进入了照片查看器界面（黑色背景，全屏图片显示）

### 2. 验证Contain模式（默认状态）
**当前状态**：图片处于Contain模式（完整显示，可能有黑边）

**预期UI状态**：
- ✅ 顶部工具栏可见（返回按钮、标题、分享按钮等）
- ✅ 底部预览栏可见（可展开/收起）
- ✅ 上滑触发分享功能
- ✅ 下滑展开/收起底部预览栏

### 3. 切换到Cover模式
**操作**：在当前图片上**双击**

**预期变化**：
- 🎭 图片切换到Cover模式（填满屏幕，可能被裁剪）
- 🎭 顶部工具栏自动隐藏（向上滑出动画）
- 🎭 底部预览栏自动隐藏（向下滑出动画）
- 🎭 控制台显示：`🎭 模式切换: 进入Cover模式 - 隐藏UI`

### 4. 验证Cover模式沉浸式体验
**当前状态**：图片处于Cover模式（全屏显示）

**预期UI状态**：
- ❌ 顶部工具栏隐藏（不可见）
- ❌ 底部预览栏隐藏（不可见）
- ✅ 上滑触发图片平移（不是分享功能）
- ❌ 下滑预览功能被禁用
- ✅ 知识点标记功能正常（点击添加气泡）
- ✅ 双击可切换回Contain模式

### 5. 测试Cover模式下的交互功能

#### 5.1 图片平移功能
- **上滑**：图片向上平移
- **控制台日志**：`🖱️ Cover模式下的上滑手势 - 用于图片平移`

#### 5.2 下滑预览禁用
- **下滑**：不触发预览栏展开
- **控制台日志**：`📖 Cover模式下禁用下滑预览功能`

#### 5.3 知识点标记
- **点击图片**：正常添加知识点标记
- **拖拽气泡**：正常调整位置
- **编辑内容**：正常编辑知识点

### 6. 切换回Contain模式
**操作**：在Cover模式下**双击图片**

**预期变化**：
- 🎭 图片切换回Contain模式（完整显示）
- 🎭 顶部工具栏自动显示（向下滑入动画）
- 🎭 底部预览栏自动显示（向上滑入动画）
- 🎭 控制台显示：`🎭 模式切换: 退出Cover模式 - 显示UI`

## 🔍 调试日志说明

### 模式切换日志
```
🎭 模式切换: 进入Cover模式 - 隐藏UI
🎭 模式切换: 退出Cover模式 - 显示UI
📱 Cover模式下自动收起底部预览栏
```

### 手势功能日志
```
# Cover模式下
🖱️ Cover模式下的上滑手势 - 用于图片平移
🖱️ ✅ Cover模式图片平移: deltaY=-XXX, panAmount=XXX
📖 Cover模式下禁用下滑预览功能

# Contain模式下
📤 Contain模式下触发上滑分享手势
📖 触发下滑预览手势
```

## ⚡ 动画效果验证

### 顶部工具栏动画
- **隐藏**：向上滑出，透明度从1.0到0.0，300ms动画
- **显示**：向下滑入，透明度从0.0到1.0，300ms动画

### 底部预览栏动画
- **隐藏**：向下滑出，透明度从1.0到0.0，300ms动画
- **显示**：向上滑入，透明度从0.0到1.0，300ms动画

## 🎯 验证清单

### Cover模式进入
- [ ] 双击图片成功切换到Cover模式
- [ ] 顶部工具栏平滑隐藏
- [ ] 底部预览栏平滑隐藏
- [ ] 图片填满屏幕显示
- [ ] 控制台显示正确的模式切换日志

### Cover模式交互
- [ ] 上滑触发图片平移（不是分享）
- [ ] 下滑预览功能被禁用
- [ ] 点击添加知识点功能正常
- [ ] 拖拽调整知识点位置正常
- [ ] 编辑知识点内容功能正常

### Cover模式退出
- [ ] 双击图片成功切换回Contain模式
- [ ] 顶部工具栏平滑显示
- [ ] 底部预览栏平滑显示
- [ ] 上滑分享功能恢复
- [ ] 下滑预览功能恢复

### 页面切换兼容性
- [ ] 左右滑动切换图片功能正常
- [ ] 切换页面后模式状态正确更新
- [ ] 新页面默认为Contain模式

## 🚨 故障排除

### 如果UI没有隐藏
1. 检查控制台是否有模式切换日志
2. 确认双击是否成功触发模式切换
3. 检查`_shouldHideUI`状态是否正确更新

### 如果动画不流畅
1. 确认AnimatedPositioned和AnimatedOpacity正常工作
2. 检查动画时长和曲线设置
3. 验证UI层级结构是否正确

### 如果手势功能异常
1. 检查Cover模式下的手势日志
2. 确认上滑是否触发图片平移而不是分享
3. 验证下滑预览功能是否被正确禁用

## 💡 使用建议

1. **专注编辑**：在Cover模式下专注于图片内容和知识点标记
2. **快速切换**：使用双击快速在两种模式间切换
3. **沉浸体验**：Cover模式提供无干扰的全屏查看体验
4. **功能访问**：需要使用工具栏功能时切换回Contain模式
