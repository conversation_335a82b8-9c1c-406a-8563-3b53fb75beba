# TimeBox计时器小窗口功能测试清单

## 基础功能测试

### 1. 小窗口按钮显示
- [ ] 启动计时器后，内联计时器中显示小窗口按钮
- [ ] 按钮图标为 `picture_in_picture_alt`
- [ ] 按钮颜色为灰色 (#9B9A97)
- [ ] 按钮支持Tooltip显示"小窗口显示"
- [ ] 按钮位置在暂停/停止按钮右侧

### 2. 小窗口显示/隐藏
- [ ] 点击小窗口按钮后，小窗口正确显示
- [ ] 小窗口默认位置为 (20, 100)
- [ ] 小窗口尺寸为 280x120 像素
- [ ] 小窗口具有95%透明度效果
- [ ] 小窗口具有圆角和边框样式

### 3. 小窗口内容显示
- [ ] 顶部进度条正确显示任务进度
- [ ] 任务标题正确显示（支持文本截断）
- [ ] 关闭按钮显示在右上角
- [ ] 状态指示器正确显示（绿色=运行，灰色=暂停）
- [ ] 剩余时间以MM:SS格式正确显示
- [ ] 状态文字正确显示（"进行中"/"已暂停"）
- [ ] 当前收入正确计算和显示
- [ ] 控制按钮（暂停/恢复、停止）正确显示

## 交互功能测试

### 4. 拖拽移动
- [ ] 可以通过拖拽移动小窗口位置
- [ ] 拖拽过程中窗口跟随手指移动
- [ ] 松开手指后窗口固定在新位置
- [ ] 窗口不会移出屏幕左边界
- [ ] 窗口不会移出屏幕右边界
- [ ] 窗口不会移出屏幕上边界
- [ ] 窗口不会移出屏幕下边界（考虑底部安全区域）

### 5. 计时器控制
- [ ] 点击暂停按钮可以暂停计时器
- [ ] 点击恢复按钮可以恢复计时器
- [ ] 点击停止按钮可以停止计时器
- [ ] 控制操作与主计时器保持同步
- [ ] 按钮状态正确更新（暂停/播放图标切换）

### 6. 窗口关闭
- [ ] 点击关闭按钮可以关闭小窗口
- [ ] 停止计时器时小窗口自动关闭
- [ ] 关闭后小窗口完全消失
- [ ] 关闭后可以重新打开小窗口

## 状态同步测试

### 7. 实时更新
- [ ] 剩余时间每秒更新
- [ ] 进度条随时间推移更新
- [ ] 收入计算实时更新
- [ ] 状态指示器实时反映计时器状态
- [ ] 状态文字实时更新

### 8. 主界面同步
- [ ] 小窗口操作与内联计时器同步
- [ ] 在主界面暂停，小窗口状态同步更新
- [ ] 在主界面停止，小窗口自动关闭
- [ ] 任务完成时小窗口正确处理

## 边界情况测试

### 9. 异常情况处理
- [ ] 计时器未启动时不显示小窗口按钮
- [ ] 任务为null时小窗口不显示
- [ ] 快速点击按钮不会产生多个小窗口
- [ ] 屏幕旋转时小窗口位置合理调整
- [ ] 应用切换到后台再回来时状态正确

### 10. 性能测试
- [ ] 小窗口显示不影响应用性能
- [ ] 拖拽操作流畅无卡顿
- [ ] 长时间显示不会导致内存泄漏
- [ ] 频繁开关小窗口不会影响性能

## 用户体验测试

### 11. 视觉效果
- [ ] 小窗口不会过度遮挡主要内容
- [ ] 透明度设置合理，既不影响视野又保持可读性
- [ ] 颜色搭配符合OneDay应用主题
- [ ] 字体大小和间距合理
- [ ] 按钮大小适合触摸操作

### 12. 操作便利性
- [ ] 拖拽手势响应灵敏
- [ ] 按钮点击区域合理
- [ ] 关闭按钮容易点击
- [ ] 不会误触其他功能
- [ ] 操作反馈及时

## 兼容性测试

### 13. 设备兼容性
- [ ] iPhone不同尺寸屏幕正常显示
- [ ] iPad设备正常显示
- [ ] Android设备正常显示
- [ ] 不同分辨率下布局正确

### 14. 系统兼容性
- [ ] iOS不同版本正常运行
- [ ] Android不同版本正常运行
- [ ] 深色模式下显示正常
- [ ] 浅色模式下显示正常

## 测试结果记录

### 通过的测试项
- [ ] 记录通过的功能点
- [ ] 记录测试时间和设备信息

### 发现的问题
- [ ] 记录发现的bug和问题
- [ ] 记录问题的重现步骤
- [ ] 记录问题的严重程度

### 改进建议
- [ ] 记录用户体验改进建议
- [ ] 记录功能增强建议
- [ ] 记录性能优化建议

## 测试环境信息
- 测试日期：
- 测试设备：
- 系统版本：
- 应用版本：
- 测试人员：

## 测试结论
- [ ] 功能基本可用
- [ ] 需要修复的问题：
- [ ] 建议的改进：
- [ ] 是否可以发布：
