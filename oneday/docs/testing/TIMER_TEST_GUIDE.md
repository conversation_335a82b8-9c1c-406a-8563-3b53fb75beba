# TimeBox计时器测试指南

## 测试目标
验证TimeBox功能中计时器精度问题和休息界面闪烁问题的修复效果。

## 测试环境
- 设备：iPhone 16 Pro (模拟器)
- Flutter版本：最新稳定版
- 测试时间：建议至少10分钟完整测试

## 测试步骤

### 1. 计时器精度测试

#### 测试1.1：基本计时精度
1. 打开OneDay应用
2. 导航到TimeBox页面
3. 选择一个任务，点击"开始学习"启动计时器
4. 同时启动手机秒表或其他外部计时器
5. 观察5分钟，记录时间差异
6. **预期结果**：TimeBox计时器与外部计时器误差应在±1秒内

#### 测试1.2：应用后台前台切换
1. 启动TimeBox计时器
2. 将应用切换到后台（按Home键）
3. 等待30秒
4. 重新打开应用
5. 检查计时器是否继续正常计时
6. **预期结果**：计时器应该准确反映实际经过的时间

#### 测试1.3：长时间计时精度
1. 启动一个较长时间的任务（建议10分钟以上）
2. 每隔2分钟记录一次时间差异
3. 观察是否存在累积误差
4. **预期结果**：整个计时过程中误差应保持在±1秒内，不应累积

### 2. 休息界面稳定性测试

#### 测试2.1：PAO学习界面计时器
1. 启动一个短时间任务（1-2分钟）
2. 等待任务完成，自动进入PAO学习界面
3. 观察AppBar右上角的休息计时器显示
4. 注意观察是否有闪烁现象
5. **预期结果**：计时器显示应该稳定，无闪烁

#### 测试2.2：休息计时器精度
1. 在PAO学习界面中
2. 使用外部计时器对比休息计时器
3. 观察3-5分钟
4. **预期结果**：休息计时器应该准确倒计时

#### 测试2.3：开发者测试模式
1. 在TimeBox页面找到"【开发者测试】"按钮
2. 点击"进入"直接进入PAO学习界面
3. 观察休息计时器是否正常工作
4. **预期结果**：计时器正常显示和倒计时

### 3. 用户体验测试

#### 测试3.1：计时器控制功能
1. 启动计时器后，测试暂停功能
2. 测试恢复功能
3. 测试停止功能
4. **预期结果**：所有控制功能正常工作

#### 测试3.2：界面响应性
1. 在计时器运行时，测试界面滑动和交互
2. 观察是否有卡顿或延迟
3. **预期结果**：界面应该流畅响应

#### 测试3.3：多任务切换
1. 启动一个计时器
2. 切换到其他应用
3. 接听电话或使用其他功能
4. 返回OneDay应用
5. **预期结果**：计时器状态保持正确

## 问题记录模板

### 计时器精度问题
- **测试时间**：
- **测试时长**：
- **外部计时器时间**：
- **TimeBox计时器时间**：
- **误差**：
- **问题描述**：

### 界面闪烁问题
- **测试时间**：
- **闪烁频率**：
- **闪烁持续时间**：
- **问题描述**：

### 其他问题
- **问题类型**：
- **复现步骤**：
- **预期行为**：
- **实际行为**：

## 性能监控

### 使用Flutter DevTools
1. 在终端中点击DevTools链接
2. 打开Performance标签页
3. 在测试过程中监控：
   - CPU使用率
   - 内存使用情况
   - UI重建频率
   - 帧率稳定性

### 关键指标
- **帧率**：应保持在60fps
- **内存使用**：应该稳定，无明显泄漏
- **CPU使用**：计时器运行时不应过高
- **UI重建**：应该最小化不必要的重建

## 测试通过标准

### 计时器精度
- ✅ 基本计时误差 ≤ ±1秒
- ✅ 后台前台切换计时连续
- ✅ 长时间计时无累积误差
- ✅ 暂停恢复功能正常

### 界面稳定性
- ✅ 休息界面无闪烁
- ✅ 计时器显示稳定
- ✅ UI响应流畅
- ✅ 无明显性能问题

### 用户体验
- ✅ 所有控制功能正常
- ✅ 多任务切换无问题
- ✅ 界面交互流畅
- ✅ 无崩溃或异常

## 回归测试

在修复完成后，建议进行以下回归测试：

1. **基础功能测试**：确保原有功能未受影响
2. **性能基准测试**：对比修复前后的性能数据
3. **兼容性测试**：在不同设备和系统版本上测试
4. **压力测试**：长时间运行和极端使用场景测试

## 测试报告

测试完成后，请记录：
- 测试执行时间
- 发现的问题数量和严重程度
- 修复效果评估
- 建议的后续改进方向
