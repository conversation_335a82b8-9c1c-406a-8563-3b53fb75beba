# 番茄钟功能测试指南

## 测试目标
验证TimeBox番茄钟工作法的完整功能，包括：
- 工作时间和休息时间的自动切换
- 浮动窗口在休息时间的正确显示
- 状态指示器和文字的准确更新
- 无setState错误和内存泄漏

## 快速测试步骤

### 1. 创建测试任务
1. 打开OneDay应用
2. 进入"时间盒子"功能
3. 点击"+"按钮创建新任务
4. 设置任务信息：
   - 标题：`番茄钟测试`
   - 预计时长：`1分钟`（用于快速测试）
   - 分类：任意
   - 优先级：任意

### 2. 启动工作时间
1. 点击任务的"开始"按钮
2. **验证工作状态**：
   - ✅ 状态指示器显示绿色圆点
   - ✅ 状态文字显示"工作中"
   - ✅ 时间倒计时正常运行

### 3. 开启浮动窗口
1. 点击"小窗口显示"按钮（picture_in_picture_alt图标）
2. **验证浮动窗口**：
   - ✅ 窗口显示任务标题"番茄钟测试"
   - ✅ 窗口显示剩余时间
   - ✅ 窗口可以拖拽移动
   - ✅ 窗口保持100x40px尺寸和60-70%透明度

### 4. 等待工作时间结束
1. 等待1分钟工作时间结束
2. **验证自动切换到休息时间**：
   - ✅ 控制台输出："✅ 工作时间完成，自动进入休息时间"
   - ✅ 状态指示器变为蓝色圆点
   - ✅ 状态文字变为"休息中"
   - ✅ 浮动窗口标题立即变为"休息中"
   - ✅ 时间重置为5:00并开始倒计时
   - ✅ 无setState错误出现

### 5. 验证休息时间
1. 观察休息时间倒计时
2. **验证休息状态**：
   - ✅ 浮动窗口显示"休息中 04:xx"格式
   - ✅ 主界面状态保持"休息中"
   - ✅ 蓝色状态指示器持续显示
   - ✅ 时间同步准确

### 6. 测试手动停止（可选）
1. 在休息时间期间点击"停止"按钮
2. **验证停止功能**：
   - ✅ 计时器立即停止
   - ✅ 浮动窗口自动关闭
   - ✅ 状态重置为初始状态

### 7. 完整周期测试（可选）
1. 重新创建5分钟工作任务
2. 让完整的工作→休息周期自然完成
3. **验证完整周期**：
   - ✅ 工作5分钟后自动进入休息
   - ✅ 休息5分钟后自动停止
   - ✅ 浮动窗口自动关闭
   - ✅ 所有状态正确重置

## 常见问题排查

### 问题1：setState错误
**症状**：控制台出现"setState() called after dispose()"错误
**检查**：
- 确保所有setState调用都有mounted检查
- 验证组件dispose时正确移除监听器

### 问题2：浮动窗口不更新
**症状**：进入休息时间后浮动窗口仍显示任务标题
**检查**：
- 验证`_startRestTimerInternal`方法中的立即同步逻辑
- 确认浮动窗口服务的syncWithMasterTimer调用

### 问题3：状态指示器颜色错误
**症状**：状态指示器颜色与当前状态不匹配
**检查**：
- 验证PomodoroTimerState枚举的颜色定义
- 确认UI组件正确使用pomodoroState.color

### 问题4：时间不同步
**症状**：浮动窗口与主界面时间显示不一致
**检查**：
- 验证主计时器的时间源统一性
- 确认浮动窗口的syncWithMasterTimer调用频率

## 性能验证

### 内存泄漏检查
1. 多次进入/退出TimeBox页面
2. 启动/停止多个计时器周期
3. 观察内存使用情况是否稳定

### 计时精度检查
1. 使用秒表对比应用计时
2. 验证工作时间和休息时间的准确性
3. 确认浮动窗口时间与主界面一致

## 测试通过标准
- ✅ 无运行时错误（特别是setState错误）
- ✅ 工作→休息自动切换正常
- ✅ 浮动窗口实时更新标题
- ✅ 状态指示器颜色正确
- ✅ 时间显示同步准确
- ✅ 手动停止功能正常
- ✅ 内存使用稳定
- ✅ 用户体验流畅

## 报告问题
如果发现任何问题，请记录：
1. 具体的操作步骤
2. 预期行为 vs 实际行为
3. 控制台错误信息
4. 设备和系统版本信息
