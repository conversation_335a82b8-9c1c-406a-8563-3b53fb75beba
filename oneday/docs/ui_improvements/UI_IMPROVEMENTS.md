# OneDay 时间盒子功能 UI 优化

## 概述

本次优化重点改进了OneDay应用时间盒子功能中新建任务弹窗的UI设计，使其完全符合Notion风格设计规范。

## 主要改进

### 1. 弹窗容器样式优化

- **背景色**: 纯白色 `#FFFFFF`
- **边框圆角**: `BorderRadius.circular(12)` 
- **阴影**: 微妙阴影效果，模糊半径4px，偏移量(0,2)，透明度0.1
- **标题样式**: 
  - 颜色: `#37352F` (Notion深灰色)
  - 字重: `FontWeight.w600`
  - 字号: 18px
- **内容区域**: `EdgeInsets.symmetric(horizontal: 24, vertical: 20)`

### 2. 优先级选择器优化

**保持下拉选择器格式，增强视觉效果**:

- **高优先级**:
  - 颜色: `#E03E3E` (红色)
  - 图标: `Icons.priority_high`
  - 文字: "高优先级"

- **中优先级**:
  - 颜色: `#FFD700` (黄色)
  - 图标: `Icons.remove`
  - 文字: "中优先级"

- **低优先级**:
  - 颜色: `#0F7B6C` (绿色)
  - 图标: `Icons.low_priority`
  - 文字: "低优先级"

**下拉菜单项设计**:
- 每个选项包含图标 + 文字
- 图标使用对应的优先级颜色
- 保持原有的点击展开交互方式

### 3. 学科分类选择器优化

**保持下拉选择器格式，支持用户自定义分类**:

- **计算机科学**: `#E03E3E` + `Icons.computer`
- **数学**: `#FFD700` + `Icons.calculate`
- **英语**: `#0F7B6C` + `Icons.language`
- **政治**: `#2E7EED` + `Icons.account_balance`
- **休息/具身记忆**: `#0F7B6C` + `Icons.fitness_center`
- **其他**: `#9B9A97` + `Icons.category`

**视觉元素**:
- 每个选项前显示颜色圆点: `CircleAvatar(radius: 6)`
- 图标 + 文字的组合布局
- 支持更多用户自定义分类名称
- 点击展开可显示完整分类列表

### 4. 布局结构优化

**新的布局层次**:
1. 标题区域
2. 任务名称输入框
3. 描述输入框
4. 预计时长输入框
5. 优先级下拉选择器
6. 学科分类下拉选择器
7. 按钮区域

**间距设计**:
- 各区域间距: `SizedBox(height: 16)`
- 统一的表单字段样式
- 左对齐的布局结构: `crossAxisAlignment: CrossAxisAlignment.start`

### 5. 按钮区域重新设计

**布局改进**:
- 使用 `Row` + `MainAxisAlignment.end`
- 按钮间距: `SizedBox(width: 12)`

**按钮样式**:
- **取消按钮**: `TextButton`，灰色文字 `#9B9A97`
- **确定按钮**: `ElevatedButton`
  - 背景色: `#2E7EED` (Notion蓝色)
  - 文字: 白色
  - 最小尺寸: `Size(80, 48)`
  - 圆角: `BorderRadius.circular(8)`

### 6. 交互动画

- 下拉选择器使用Material Design默认展开动画
- 弹窗显示使用`showDialog`默认淡入动画
- 所有交互都有适当的视觉反馈
- 保持原有的点击展开交互模式

## 技术实现

### 核心组件

1. **TaskCreateDialog**: 新建任务弹窗
2. **TaskEditDialog**: 编辑任务弹窗 (同样优化)

### 辅助方法

```dart
// 获取优先级颜色
Color _getPriorityColor(TaskPriority priority)

// 获取优先级图标  
IconData _getPriorityIcon(TaskPriority priority)

// 获取分类图标
IconData _getCategoryIcon(String category)
```

### 颜色系统

遵循Mondrian艺术风格的OneDay配色方案:
- 红色: `#E03E3E` (计算机科学/高优先级)
- 黄色: `#FFD700` (数学/中优先级) 
- 绿色: `#0F7B6C` (英语/休息/低优先级)
- 蓝色: `#2E7EED` (政治/主要按钮)
- 深灰: `#37352F` (文字)
- 浅灰: `#9B9A97` (次要文字/其他分类)

## 测试覆盖

创建了完整的UI测试套件 (`timebox_ui_test.dart`):

- ✅ 下拉选择器组件存在性测试
- ✅ 表单字段和标签验证
- ✅ Notion风格样式验证
- ✅ 弹窗布局和按钮测试

## 兼容性

- 保持与现有数据模型完全兼容
- 保留所有原有功能逻辑
- 仅优化UI呈现层，不影响业务逻辑

## 用户体验改进

1. **保持熟悉的交互**: 保留下拉选择器的点击展开模式
2. **支持自定义分类**: 适应用户自定义的分类名称
3. **增强的视觉效果**: 下拉选项包含图标和颜色标识
4. **一致的设计语言**: 符合Notion设计规范
5. **更好的空间利用**: 下拉选择器节省界面空间
6. **可扩展性**: 支持更多分类选项而不影响界面布局

## 下一步计划

- [ ] 在其他弹窗中应用相同的设计模式
- [ ] 添加更多动画效果
- [ ] 优化移动端适配
- [ ] 添加无障碍支持
