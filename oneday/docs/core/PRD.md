# OneDay 产品需求文档 (PRD)

## 📋 文档信息

| 项目名称 | OneDay - 游戏化学习应用 |
|---------|----------------------|
| 文档版本 | v1.0 |
| 创建日期 | 2025年6月 |
| 最后更新 | 2025年6月 |
| 产品经理 | - |
| 开发团队 | Flutter跨平台开发 |

---

## 🎯 产品概述

### 产品愿景
OneDay致力于打造一款融合游戏化激励、记忆科学与健康管理的跨平台学习应用，通过创新的AR实景记忆锚点技术和时间盒子管理法，帮助用户构建高效、有趣、可持续的学习体系。

### 核心理念
- **Seize the Day（活在当下）**: 专注当下每一个学习时刻
- **One Day I Will...（终有一天我会...）**: 激发长远学习目标
- **记忆宫殿 × AR技术**: 将抽象知识与真实场景完美结合

### 目标用户
- **主要用户**: 备考人群（考研、考公、四六级、雅思等）的大学生 (18-25岁)
- **次要用户**: 职场学习者、终身学习爱好者 (25-35岁)
- **潜在用户**: 所有使用智能设备进行学习的人群

---

## 🏗️ 产品架构

### 技术架构
- **开发框架**: Flutter 3.x & Dart 3.x
- **状态管理**: Riverpod 2.0 + StateNotifier
- **路由管理**: GoRouter
- **数据持久化**: Isar NoSQL本地数据库
- **图片处理**: image_picker + cached_network_image
- **AR技术**: camera + ARCore/ARKit集成
- **国际化**: Flutter Intl (中英文切换)

### 设计规范
- **设计语言**: 参考Notion极简风格，"Less is More"
- **主色调**: #2E7EED (柔和蓝)、#7C3AED (优雅紫)
- **交互原则**: 直观自然，减少认知负担

---

## 🚀 核心功能模块

### 1. 时间盒子系统 ⏰

#### 功能描述
基于马斯克时间盒子法的专注学习管理系统，利用deadline效应提升学习专注度。

#### 核心功能
- **时间盒子创建**: 设定学习任务、时长、优先级
- **专注计时器**: 番茄钟模式，防打扰功能
- **进度追踪**: 实时显示完成情况和剩余时间
- **数据统计**: 每日/周/月学习时长统计
- **奖励机制**: 完成时间盒子获得虚拟工资

#### 技术实现
- `Timer`类实现精确计时
- `CircularProgressIndicator`显示进度
- 本地通知提醒时间节点
- 防息屏机制保持计时连续性

#### 用户价值
- 提升学习专注度
- 培养时间管理能力
- 通过游戏化激励保持动力

---

### 2. AR实景记忆锚点系统 🏛️

#### 功能描述
创新的AR增强现实记忆系统，用户可在真实场景照片上添加学习内容锚点，将抽象知识与具体空间位置关联。

#### 核心功能

##### 2.1 记忆宫殿管理
- **场景创建**: 上传个人场景照片（卧室、图书馆、咖啡厅等）
- **场景分类**: 按标签分类管理（生活、学习、工作等）
- **场景搜索**: 支持名称、标签快速搜索
- **场景分享**: 将优质场景分享到社区

##### 2.2 AR实景锚点编辑
- **锚点添加**: 双击照片空白区域添加记忆锚点
- **锚点定位**: 精确的坐标定位系统（比例坐标0.0-1.0）
- **锚点编辑**: 支持拖拽调整位置、编辑内容
- **锚点类型**: 文字、图片、语音、视频多种类型
- **锚点样式**: 多种气泡主题，个性化定制

##### 2.3 互动式学习体验
- **沉浸式浏览**: `InteractiveViewer`支持图片缩放、拖拽
- **锚点交互**: 点击锚点查看详细内容，支持展开/收起
- **学习路径**: 按顺序浏览锚点，形成学习路径
- **复习模式**: 隐藏锚点内容，测试记忆效果

##### 2.4 社交化功能
- **点赞系统**: 为优质锚点内容点赞
- **评论互动**: 在锚点下方评论交流
- **学习伙伴**: 与好友共享记忆宫殿
- **排行榜**: 锚点创建数量、点赞数排行

#### 技术实现

##### 前端技术
```dart
// 核心组件架构
Widget build(BuildContext context) {
  return Stack(
    children: [
      // 背景图片层
      InteractiveViewer(
        child: Image.network(scene.imagePath),
      ),
      // 锚点覆盖层
      ...anchors.map((anchor) => Positioned(
        left: anchor.xRatio * screenWidth,
        top: anchor.yRatio * screenHeight,
        child: AnchorBubble(anchor: anchor),
      )),
      // 交互控制层
      GestureDetector(
        onDoubleTap: _addNewAnchor,
        onLongPress: _enterEditMode,
      ),
    ],
  );
}
```

##### 数据模型
```dart
@collection
class MemoryScene {
  Id id = Isar.autoIncrement;
  String title;
  String imagePath;
  List<String> tags;
  DateTime createdAt;
  int viewCount;
  int likeCount;
}

@collection
class MemoryAnchor {
  Id id = Isar.autoIncrement;
  int sceneId;
  double xRatio; // X坐标比例 (0.0-1.0)
  double yRatio; // Y坐标比例 (0.0-1.0)
  String content;
  AnchorType type;
  DateTime createdAt;
  int likes;
  String? authorName;
  String? authorAvatar;
}
```

##### 核心算法
- **坐标转换**: 屏幕坐标 ↔ 比例坐标转换
- **手势识别**: 区分图片缩放与锚点拖拽手势
- **性能优化**: 虚拟化渲染，大量锚点时的性能保障

#### 用户价值
- **记忆效果提升**: 空间与知识关联，提升记忆效率
- **学习趣味性**: 游戏化的交互体验，增加学习乐趣
- **个性化学习**: 自定义学习场景，适应不同学习习惯
- **社交学习**: 分享交流，形成学习社区

---

### 3. 虚拟工资系统 💰

#### 功能描述
游戏化激励系统，用户通过学习获得虚拟工资，用于购买道具、抽奖、解锁功能。

#### 核心功能
- **工资计算**: 基于时薪200元标准，学习时长 × 时薪 = 收入
- **钱包管理**: 余额显示、收支明细、交易记录
- **道具商城**: 学习工具、主题皮肤、特殊功能解锁
- **抽奖系统**: 每日签到、完成任务获得抽奖机会
- **等级系统**: 累计收入解锁不同等级和特权

#### 技术实现
- 本地数据库记录所有交易
- 防作弊机制确保工资合理性
- 动画效果增强获得感

---

### 4. 运动健康系统 🏃‍♂️

#### 功能描述
基于PAO记忆法的运动系统，将英语单词拆解为People-Action-Object动作组合，在学习间隙推荐相应运动。

#### 核心功能
- **PAO动作库**: 丰富的动作库，支持自定义添加
- **智能推荐**: 根据当前学习内容推荐对应动作
- **运动计时**: 动作演示、计时计数、完成反馈
- **健康统计**: 运动时长、消耗卡路里统计
- **动作分享**: 用户自创动作分享到社区

#### 技术实现
- 动作视频播放和步骤指导
- 传感器检测运动完成情况
- 与时间盒子系统联动

---

### 5. 优化日志系统 📝

#### 功能描述
英文自省日志系统，帮助用户进行每日学习反思，优质内容可发布到社区。

#### 核心功能
- **Markdown编辑**: 支持富文本格式的日志编写
- **模板系统**: 提供多种反思模板（今日收获、改进计划等）
- **历史回顾**: 日历视图查看历史日志
- **社区发布**: 优质日志分享获得点赞和评论
- **AI分析**: 情感分析、学习趋势分析

#### 技术实现
- Markdown编辑器集成
- 本地存储和云端同步
- 文本情感分析API

---

### 6. 学习社区系统 👥

#### 功能描述
学习内容分享和交流平台，包含学习文章、动作库、优化日志等多种内容类型。

#### 核心功能
- **内容发布**: 支持图文、视频、音频多种格式
- **互动功能**: 点赞、评论、收藏、转发
- **话题标签**: 按学科、难度、类型分类
- **推荐算法**: 基于用户兴趣的智能推荐
- **私信功能**: 用户间私信交流

---

## 📊 功能优先级与开发规划

### 第一阶段：MVP核心功能 (4-6周)
**目标**: 构建可用的基础学习系统

#### 1.1 基础架构 ✅
- [x] 应用框架搭建（Flutter + Riverpod + GoRouter）
- [x] 主题系统和设计规范
- [x] 基础路由和状态管理

#### 1.2 用户系统 ✅
- [x] 启动页和引导页
- [x] 登录注册系统（含测试模式）
- [x] 用户信息管理

#### 1.3 时间盒子系统 🔄
- [ ] 时间盒子创建和管理
- [ ] 专注计时器功能
- [ ] 基础数据统计

#### 1.4 记忆宫殿基础 ✅
- [x] 记忆宫殿管理页
- [ ] 基础场景查看功能
- [ ] 简单锚点添加

#### 1.5 虚拟工资基础
- [ ] 基础工资计算和显示
- [ ] 简单的钱包界面

### 第二阶段：特色功能 (6-8周)
**目标**: 实现差异化的核心特色功能

#### 2.1 AR实景记忆锚点系统 🎯
- [ ] **场景详情页开发** (优先级: 🔥🔥🔥)
  - InteractiveViewer图片查看器
  - 锚点精确定位系统
  - 锚点增删改功能
  - 手势交互优化
- [ ] **社交化功能**
  - 点赞评论系统
  - 锚点分享功能
  - 用户互动界面
- [ ] **高级编辑功能**
  - 多种锚点类型支持
  - 批量锚点管理
  - 锚点搜索和筛选

#### 2.2 运动健康系统
- [ ] PAO动作库构建
- [ ] 动作推荐算法
- [ ] 运动计时和统计

#### 2.3 游戏化增强
- [ ] 道具商城系统
- [ ] 抽奖和奖励机制
- [ ] 成就系统

### 第三阶段：社交与优化 (4-6周)
**目标**: 完善用户体验和社区功能

#### 3.1 学习社区
- [ ] 内容发布和浏览
- [ ] 推荐算法优化
- [ ] 社区管理功能

#### 3.2 优化日志
- [ ] Markdown编辑器
- [ ] 日志模板系统
- [ ] AI情感分析

#### 3.3 系统优化
- [ ] 性能优化
- [ ] 国际化支持
- [ ] 数据同步和备份

---

## 🎨 用户体验设计

### 设计原则
1. **简洁至上**: 遵循Notion的极简设计理念
2. **功能可发现**: 重要功能一目了然，高频操作便捷
3. **视觉层次**: 清晰的信息架构和视觉层次
4. **交互反馈**: 及时的操作反馈和状态提示

### 关键用户流程

#### AR记忆锚点使用流程
1. **进入场景**: 首页 → 记忆宫殿 → 选择场景
2. **查看锚点**: 浏览已有锚点内容，缩放查看细节
3. **添加锚点**: 双击空白区域 → 输入内容 → 选择类型 → 保存
4. **编辑锚点**: 长按锚点 → 进入编辑模式 → 拖拽调整 → 修改内容
5. **分享互动**: 点赞优质锚点 → 添加评论 → 分享到社区

#### 学习会话流程
1. **创建时间盒子**: 设定任务和时长
2. **开始专注学习**: 启动计时器，进入专注模式
3. **休息运动**: 系统推荐PAO动作，劳逸结合
4. **完成奖励**: 获得虚拟工资，解锁成就
5. **记录反思**: 撰写学习日志，总结收获

---

## 📈 数据指标

### 核心指标
- **用户留存率**: 次日留存 > 70%，7日留存 > 40%
- **学习时长**: 日均学习时长 > 2小时
- **功能使用率**: AR锚点功能使用率 > 60%
- **社区活跃度**: 日活跃用户 > 1000人

### 业务指标
- **记忆宫殿数量**: 平均每用户创建 > 3个场景
- **锚点创建量**: 平均每场景 > 10个锚点
- **社区内容**: 日均UGC发布 > 100条
- **用户评价**: 应用商店评分 > 4.5分

---

## 🔒 技术方案

### 数据安全
- 用户数据本地加密存储
- 图片文件安全管理
- 社区内容审核机制

### 性能优化
- 图片懒加载和缓存策略
- 锚点虚拟化渲染
- 数据库查询优化

### 跨平台适配
- iOS和Android原生功能集成
- 响应式布局适配不同屏幕
- 平台特定UI优化

---

## 🚧 风险评估

### 技术风险
- **AR功能复杂度**: 锚点定位精度和手势冲突问题
- **性能挑战**: 大量锚点渲染的性能优化
- **兼容性问题**: 不同设备的适配挑战

### 产品风险
- **用户接受度**: AR记忆锚点概念的用户教育成本
- **内容质量**: 社区UGC内容质量控制
- **竞品压力**: 现有学习应用的竞争

### 解决方案
- 分阶段发布，逐步验证核心功能
- 建立用户反馈机制，快速迭代优化
- 重点投入AR功能的技术攻关

---

## 📅 里程碑计划

| 里程碑 | 时间节点 | 主要交付物 |
|--------|----------|------------|
| MVP版本 | Week 6 | 基础学习功能可用 |
| AR功能上线 | Week 10 | 实景记忆锚点系统 |
| 社区功能 | Week 14 | 用户分享和互动 |
| 正式发布 | Week 18 | 完整产品体验 |

---

## 🎯 成功标准

### 短期目标 (3个月)
- 完成MVP开发，核心功能可用
- 获得100+种子用户，收集反馈
- AR记忆锚点功能验证可行性

### 中期目标 (6个月)
- 用户数突破1000人
- 日活跃用户 > 300人
- 社区内容日均发布 > 50条

### 长期目标 (1年)
- 成为垂直领域知名学习应用
- 用户数突破10000人
- 形成良性的学习社区生态

---

*本PRD将根据开发进展和用户反馈持续更新迭代* 