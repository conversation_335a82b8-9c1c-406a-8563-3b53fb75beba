# 自定义动作库编辑页面功能改进

## 新增功能

### 1. 保存和草稿功能

#### 保存按钮
- **保存草稿按钮**: 当有未保存更改时显示，允许用户手动保存当前进度
- **完成按钮**: 最终保存并退出编辑页面的主要按钮
- **加载状态**: 保存过程中显示加载指示器，防止重复操作

#### 自动保存草稿
- **编辑时自动保存**: 用户编辑单个字母动作时自动保存到本地存储
- **后台保存**: 不影响用户界面，静默保存草稿状态
- **错误处理**: 自动保存失败时在控制台记录错误，不干扰用户操作

#### 草稿状态指示
- **未保存更改标识**: 底部操作栏显示"有未保存更改"标签
- **视觉反馈**: 使用黄色边框和背景色突出显示未保存状态
- **实时更新**: 编辑动作后立即更新状态指示

### 2. 返回确认功能

#### 智能返回处理
- **系统返回按钮**: 使用PopScope拦截系统返回手势
- **AppBar返回按钮**: 自定义返回按钮处理逻辑
- **未保存更改提醒**: 有未保存更改时弹出确认对话框

#### 确认对话框选项
- **取消**: 继续编辑，不离开页面
- **保存并离开**: 保存当前更改后退出
- **直接离开**: 放弃未保存更改直接退出

### 3. 用户体验改进

#### 视觉设计
- **Notion风格**: 统一的白色背景和12px圆角设计
- **状态指示**: 清晰的进度显示和状态反馈
- **响应式布局**: 适配不同屏幕尺寸

#### 交互优化
- **防重复操作**: 保存过程中禁用按钮，防止重复提交
- **即时反馈**: 操作完成后显示SnackBar提示
- **流畅动画**: 按钮状态切换和加载指示器动画

## 技术实现

### 状态管理
```dart
bool _hasUnsavedChanges = false;  // 跟踪未保存更改
bool _isSaving = false;           // 跟踪保存状态
```

### 自动保存机制
```dart
Future<void> _autoSaveDraft() async {
  try {
    await widget.customLibraryService.updateLibrary(_library);
    debugPrint('🔄 自动保存草稿成功');
  } catch (e) {
    debugPrint('❌ 自动保存草稿失败: $e');
  }
}
```

### 返回拦截
```dart
PopScope(
  canPop: !_hasUnsavedChanges,
  onPopInvokedWithResult: (didPop, result) async {
    if (!didPop && _hasUnsavedChanges) {
      await _onBackPressed();
    }
  },
  child: Scaffold(...),
)
```

## 使用流程

### 编辑动作
1. 点击字母卡片进入编辑对话框
2. 编辑动作信息（名称、描述、分类等）
3. 保存后自动更新本地状态并标记为有未保存更改
4. 自动保存草稿到本地存储

### 保存草稿
1. 编辑过程中点击"保存草稿"按钮
2. 手动保存当前所有更改
3. 清除未保存更改标识
4. 显示保存成功提示

### 完成编辑
1. 点击"完成"按钮
2. 保存所有更改到服务
3. 显示成功提示
4. 自动返回上级页面

### 安全退出
1. 有未保存更改时尝试返回
2. 弹出确认对话框
3. 选择保存并离开或直接离开
4. 根据选择执行相应操作

## 错误处理

### 保存失败
- 显示错误提示SnackBar
- 保持编辑状态，允许重试
- 记录错误日志便于调试

### 网络异常
- 自动保存失败时静默处理
- 手动保存失败时显示用户友好的错误信息
- 保持本地状态一致性

## 兼容性

### 现有功能
- 完全兼容现有的动作编辑功能
- 保持原有的批量操作功能
- 维护现有的数据结构和API

### 向后兼容
- 不影响现有的动作库数据
- 保持与其他页面的集成
- 维护现有的导航流程
