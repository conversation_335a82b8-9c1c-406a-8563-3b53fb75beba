# TimeBox计时器系统级浮动窗口实现

## 功能概述
为OneDay应用的TimeBox计时器实现了两项重要改进：
1. **标题居中显示优化**：提升小窗口的视觉效果
2. **系统级浮动窗口**：实现真正的跨页面、跨应用浮动显示

## 1. 标题居中显示优化

### 实现细节
- **布局改进**：使用Stack布局替代Row布局
- **标题居中**：使用Center组件确保标题在可用空间内居中
- **关闭按钮**：使用Positioned组件固定在右上角
- **文本截断**：保持ellipsis截断功能，支持长标题显示

### 技术实现
```dart
Stack(
  children: [
    // 居中的标题
    Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Text(
          taskTitle,
          textAlign: TextAlign.center,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ),
    // 右上角关闭按钮
    Positioned(
      right: 0,
      top: 0,
      child: CloseButton(...),
    ),
  ],
)
```

## 2. 系统级浮动窗口实现

### 核心架构

#### FloatingTimerService 服务类
- **单例模式**：确保全局唯一的浮动窗口实例
- **状态管理**：管理窗口显示状态、位置、计时器数据
- **生命周期**：处理窗口的显示、隐藏、更新操作

#### 主要功能
1. **全局Overlay**：使用`Overlay.of(context, rootOverlay: true)`实现系统级显示
2. **状态同步**：实时同步计时器状态到浮动窗口
3. **位置记忆**：保存和恢复窗口位置
4. **跨页面显示**：在用户切换页面时保持显示

### 技术实现

#### 服务类结构
```dart
class FloatingTimerService {
  static final FloatingTimerService _instance = FloatingTimerService._internal();
  
  // 全局状态
  OverlayEntry? _overlayEntry;
  bool _isVisible = false;
  Offset _position = const Offset(20, 100);
  
  // 计时器状态
  String? _taskTitle;
  int _remainingSeconds = 0;
  bool _isTimerRunning = false;
  
  // 核心方法
  void showFloatingTimer({...});
  void updateTimer({...});
  void hideFloatingTimer();
}
```

#### 窗口组件
```dart
class _FloatingTimerWidget extends StatefulWidget {
  // 实现拖拽、显示、交互功能
  // 保持与原有设计风格一致
}
```

### 集成方式

#### 页面级集成
- **导入服务**：`import '../../services/floating_timer_service.dart'`
- **实例化**：`final FloatingTimerService _floatingTimerService = FloatingTimerService()`
- **状态同步**：在计时器更新时同步到浮动服务

#### 交互逻辑
1. **切换模式**：点击小窗口按钮在页面内窗口和系统级窗口间切换
2. **状态同步**：暂停、恢复、停止操作同步到浮动窗口
3. **生命周期**：页面销毁时保持浮动窗口显示

## 3. 权限配置

### Android权限
```xml
<!-- 系统级悬浮窗权限 -->
<uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
<uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
<uses-permission android:name="android.permission.WAKE_LOCK" />
```

### iOS权限
```xml
<!-- 后台模式支持 -->
<key>UIBackgroundModes</key>
<array>
    <string>background-processing</string>
    <string>background-fetch</string>
</array>
```

## 4. 功能特性

### 系统级显示能力
- **跨页面显示**：在OneDay应用内切换页面时保持显示
- **应用级持久**：在应用切换到后台时继续显示
- **全局覆盖**：使用rootOverlay确保在所有内容之上显示
- **状态同步**：实时反映计时器状态变化

### 用户体验优化
- **无缝切换**：页面内窗口与系统级窗口间平滑切换
- **状态保持**：窗口位置、计时器状态完整保持
- **视觉一致**：保持原有的设计风格和交互方式
- **性能优化**：高效的状态管理和渲染机制

### 交互设计
- **智能切换**：点击小窗口按钮智能切换显示模式
- **拖拽支持**：系统级窗口支持拖拽移动
- **边界检测**：确保窗口不会移出屏幕边界
- **关闭逻辑**：支持手动关闭和自动关闭

## 5. 使用流程

### 基本使用
1. **启动计时器**：在TimeBox页面启动任务计时器
2. **显示页面内窗口**：计时器自动显示页面内小窗口
3. **切换系统级窗口**：点击小窗口按钮切换到系统级显示
4. **跨页面使用**：切换到其他页面时窗口保持显示
5. **状态同步**：所有计时器操作实时同步到浮动窗口

### 高级功能
- **位置调整**：拖拽移动窗口到合适位置
- **模式切换**：在页面内和系统级显示间自由切换
- **状态监控**：实时查看计时器状态和剩余时间
- **快速关闭**：点击关闭按钮或停止计时器

## 6. 技术优势

### 架构优势
- **服务分离**：浮动窗口逻辑独立于页面组件
- **状态管理**：统一的状态管理和同步机制
- **扩展性**：易于扩展和维护的架构设计

### 性能优势
- **高效渲染**：只在状态变化时重建窗口
- **内存优化**：合理的资源管理和清理机制
- **响应迅速**：流畅的交互和状态更新

### 兼容性
- **平台支持**：iOS和Android双平台支持
- **权限处理**：合理的权限申请和处理机制
- **版本兼容**：兼容不同版本的Flutter和系统

## 7. 注意事项

### 权限要求
- **Android**：需要用户手动授予悬浮窗权限
- **iOS**：需要后台模式权限（在应用内有效）
- **用户体验**：需要引导用户理解和授权

### 限制说明
- **iOS限制**：iOS系统限制，无法实现真正的系统级悬浮（仅应用内有效）
- **Android限制**：需要用户手动在设置中授予悬浮窗权限
- **性能考虑**：长时间显示可能影响设备性能

### 最佳实践
- **权限引导**：提供清晰的权限申请说明
- **用户控制**：允许用户随时关闭浮动窗口
- **状态管理**：确保状态同步的准确性和及时性

## 8. 未来扩展

### 功能扩展
- **多任务支持**：支持多个计时器同时显示
- **自定义样式**：允许用户自定义窗口样式
- **快捷操作**：添加更多快捷操作按钮

### 技术优化
- **权限自动化**：自动检测和申请必要权限
- **性能优化**：进一步优化渲染和内存使用
- **平台特性**：利用平台特有功能增强体验

这个实现为OneDay应用提供了真正实用的系统级浮动计时器功能，大大提升了用户的时间管理体验。
