# TimeBox番茄钟工作法实现

## 功能概述
为OneDay应用的TimeBox计时功能添加了完整的番茄钟工作法支持，包括工作时间结束后自动进入5分钟休息时间，以及休息时间的浮动窗口显示。

## 实现的功能

### 1. 番茄钟状态管理
- **PomodoroTimerState枚举**：定义了工作中、休息中、已停止三种状态
- **状态颜色**：每种状态都有对应的颜色标识
  - 工作中：绿色 (#0F7B6C)
  - 休息中：蓝色 (#2E7EED)
  - 已停止：灰色 (#9B9A97)

### 2. 全局计时器服务扩展
- **番茄钟状态属性**：添加了`_pomodoroState`状态管理
- **休息时间常量**：设置为5分钟（300秒）
- **状态getter方法**：
  - `pomodoroState`：获取当前番茄钟状态
  - `isWorkTime`：判断是否为工作时间
  - `isRestTime`：判断是否为休息时间

### 3. 自动状态切换
- **工作时间完成**：自动进入5分钟休息时间
- **休息时间完成**：自动停止计时器并清理状态
- **状态保持**：暂停/恢复操作保持当前番茄钟状态

### 4. 浮动窗口支持
- **标题区分**：
  - 工作时间：显示任务标题
  - 休息时间：显示"休息中"
- **时间同步**：浮动窗口与主界面保持时间一致
- **状态一致性**：浮动窗口正确显示工作/休息状态

### 5. UI界面更新
- **状态指示器**：根据番茄钟状态显示不同颜色
- **状态文字**：显示"工作中"、"休息中"或"已暂停"
- **视觉一致性**：所有UI元素都反映当前番茄钟状态

## 技术实现细节

### 1. 状态枚举定义
```dart
enum PomodoroTimerState {
  work,    // 工作中
  rest,    // 休息中
  stopped  // 已停止
}
```

### 2. 核心方法

#### 启动工作计时器
```dart
Future<void> startTimer(TimeBoxTask task) async {
  // 设置工作状态
  _pomodoroState = PomodoroTimerState.work;
  // 启动计时器逻辑...
}
```

#### 启动休息计时器
```dart
Future<void> startRestTimer() async {
  // 设置休息状态
  _pomodoroState = PomodoroTimerState.rest;
  _totalDurationSeconds = _restDurationSeconds; // 5分钟
  // 启动计时器逻辑...
}
```

#### 自动状态切换
```dart
void _completeTimer() {
  if (_pomodoroState == PomodoroTimerState.work) {
    // 工作时间结束，自动进入休息时间
    startRestTimer();
  } else if (_pomodoroState == PomodoroTimerState.rest) {
    // 休息时间结束，停止计时器
    _pomodoroState = PomodoroTimerState.stopped;
    // 清理状态...
  }
}
```

### 3. 浮动窗口标题逻辑
```dart
String displayTitle;
if (_pomodoroState == PomodoroTimerState.work && _currentTask != null) {
  displayTitle = _currentTask!.title;
} else if (_pomodoroState == PomodoroTimerState.rest) {
  displayTitle = '休息中';
} else {
  displayTitle = '计时器';
}
```

## 用户体验流程

### 完整番茄钟周期
1. **启动工作时间**：用户点击开始按钮，进入工作状态
2. **工作时间显示**：
   - 状态指示器显示绿色
   - 状态文字显示"工作中"
   - 浮动窗口显示任务标题
3. **工作时间结束**：自动进入休息时间
4. **休息时间显示**：
   - 状态指示器显示蓝色
   - 状态文字显示"休息中"
   - 浮动窗口显示"休息中"
5. **休息时间结束**：自动停止计时器

### 浮动窗口体验
- **工作时间**：显示任务名称和剩余时间
- **休息时间**：显示"休息中"和剩余休息时间
- **状态同步**：与主界面保持完全一致
- **系统级浮动**：支持跨应用显示（Android）

## 测试建议

### 手动测试步骤
1. **创建短时间任务**：设置1-2分钟的测试任务
2. **启动计时器**：验证工作状态显示
3. **开启浮动窗口**：验证浮动窗口显示任务标题
4. **等待工作时间结束**：验证自动进入休息时间
5. **验证休息状态**：检查状态指示器和文字变化
6. **验证浮动窗口**：确认显示"休息中"
7. **等待休息时间结束**：验证自动停止

### 验证要点
- ✅ 工作时间和休息时间的状态区分
- ✅ 浮动窗口标题的正确显示
- ✅ 状态指示器颜色的变化
- ✅ 自动状态切换的时机
- ✅ 时间同步的准确性
- ✅ 暂停/恢复功能的状态保持

## 修复的问题

### 1. setState错误修复
**问题**：在组件dispose后仍然调用setState，导致运行时错误
**解决方案**：
- 在所有setState调用前添加`mounted`检查
- 确保组件销毁后不会触发UI更新

```dart
void _updateTaskInList(TimeBoxTask updatedTask) {
  if (mounted) {  // 添加mounted检查
    setState(() {
      // 更新逻辑...
    });
  }
}
```

### 2. 浮动窗口休息时间同步修复
**问题**：工作时间结束进入休息时间后，浮动窗口没有立即更新显示"休息中"
**解决方案**：
- 在`_startRestTimerInternal`方法中立即同步浮动窗口
- 确保状态切换时浮动窗口实时更新

```dart
// 如果浮动窗口正在显示，立即更新它
if (_floatingTimerService.isVisible) {
  _floatingTimerService.syncWithMasterTimer(
    taskTitle: '休息中',
    remainingSeconds: _remainingSeconds,
    totalDurationSeconds: _totalDurationSeconds,
    isTimerRunning: _isTimerRunning,
    timerStartTime: _timerStartTime!,
  );
}
```

## 注意事项
- 休息时间固定为5分钟，符合番茄钟工作法标准
- 浮动窗口在休息时间期间保持系统级浮动能力
- 所有状态变化都有相应的视觉反馈
- 支持在任何时候手动停止计时器
- 所有UI更新都有mounted检查，避免内存泄漏
- 浮动窗口在状态切换时立即同步更新
