# 脏话屏蔽系统调试指南

## 问题描述

用户在社区发帖功能中输入违禁词（如"傻逼"、"操"等）后点击发布按钮，系统仍然显示"发布成功"，没有触发违禁词检测和警告对话框。

## 调试步骤

### 1. 检查资源文件配置

**问题**: `profanity_words.json` 文件可能没有正确配置在 `pubspec.yaml` 中。

**解决方案**: 已在 `pubspec.yaml` 中添加：
```yaml
assets:
  - assets/data/profanity_words.json
```

**验证**: 重新运行 `flutter clean && flutter pub get` 确保资源文件正确加载。

### 2. 使用调试测试页面

**创建测试页面**: `lib/debug/profanity_filter_test_page.dart`

**测试步骤**:
1. 在应用中导航到测试页面
2. 输入违禁词（如"傻逼"、"操"、"去死"）
3. 点击"测试过滤"按钮
4. 查看控制台输出和测试结果

**预期结果**:
- 控制台显示详细的调试信息
- 测试结果显示检测到违禁词
- 过滤后文本显示为替换文本（如"***"）

### 3. 检查控制台调试信息

**发布流程调试信息**:
```
🔍 开始检查违禁词...
📝 标题内容: "测试标题傻逼"
📝 正文内容: "这是一个测试内容操"
📄 ArticleImportService.processArticleContent() 开始处理: "测试标题傻逼"
📄 高亮设置: enabled=true, expertOnly=false, filterProfanity=true
📄 开始脏话过滤...
🛡️ ProfanityFilterService.filterText() 开始检测文本: "测试标题傻逼"
🛡️ 初始化完成，设置状态: enabled=true, filterLevel=ProfanityLevel.moderate
🛡️ 系统词库数量: 30
🛡️ 自定义词库数量: 0
🛡️ 总词库数量: 30
🛡️ 符合过滤级别的词汇数量: 25
🛡️ 检查的词汇: 傻逼, 操, 妈的, 去死, 白痴...
🛡️ 找到匹配词汇: 傻逼 -> 1 个匹配
🛡️ 总共检测到 1 个违禁词
📄 脏话过滤结果: 违禁=true, 过滤数=1
🔍 标题检测结果: 违禁=true, 过滤数=1
⚠️ 检测到 1 个违禁词，显示警告对话框
```

### 4. 常见问题排查

#### 问题1: 资源文件未加载
**症状**: 控制台显示"从资源文件加载系统脏话词库失败"
**解决**: 
1. 确认 `assets/data/profanity_words.json` 文件存在
2. 确认 `pubspec.yaml` 中已添加资源文件配置
3. 运行 `flutter clean && flutter pub get`

#### 问题2: 过滤功能未启用
**症状**: 控制台显示"过滤功能未启用或文本为空，跳过检测"
**解决**:
1. 检查 `ProfanityFilterSettings` 的默认值
2. 确认 `HighlightSettings.filterProfanity` 默认为 `true`
3. 清除应用数据重新测试

#### 问题3: 词库为空
**症状**: 控制台显示"系统词库数量: 0"
**解决**:
1. 检查 `profanity_words.json` 文件格式是否正确
2. 确认 JSON 解析没有错误
3. 检查文件编码是否为 UTF-8

#### 问题4: 过滤级别不匹配
**症状**: 控制台显示"符合过滤级别的词汇数量: 0"
**解决**:
1. 检查 `ProfanityFilterSettings.filterLevel` 设置
2. 确认词库中的词汇级别设置正确
3. 调整过滤级别为 `mild` 进行测试

### 5. 手动测试流程

#### 测试1: 基础功能测试
1. 打开社区发帖页面
2. 在标题输入框输入: "测试傻逼"
3. 在内容输入框输入: "这是测试内容操"
4. 点击发布按钮
5. 观察是否弹出警告对话框

#### 测试2: 不同级别词汇测试
1. 测试 mild 级别: "滚"
2. 测试 moderate 级别: "傻逼"、"操"
3. 测试 severe 级别: "去死"
4. 测试 extreme 级别: "杀"

#### 测试3: 英文词汇测试
1. 测试: "fuck"、"shit"、"damn"
2. 测试变体: "f*ck"、"sh*t"

### 6. 修复验证

**成功标志**:
1. 控制台显示完整的调试信息链
2. 检测到违禁词时弹出警告对话框
3. 用户选择继续发布后，内容被正确过滤
4. 发布成功提示包含过滤信息

**失败处理**:
1. 如果仍然无法检测，检查 `_findMatches` 方法的实现
2. 确认正则表达式匹配逻辑正确
3. 验证字符串比较是否区分大小写

### 7. 性能监控

**监控指标**:
- 词库加载时间
- 文本检测耗时
- 内存使用情况
- 用户体验流畅度

**优化建议**:
- 如果检测耗时过长，考虑异步处理
- 如果内存占用过高，考虑词库分页加载
- 如果用户体验不佳，考虑添加加载动画

## 总结

通过以上调试步骤，应该能够定位并解决脏话屏蔽系统的问题。关键是要确保：

1. 资源文件正确配置和加载
2. 过滤功能默认启用
3. 词库数据格式正确
4. 检测算法正常工作
5. UI交互逻辑正确

如果问题仍然存在，建议使用调试测试页面进行详细的功能验证，并根据控制台输出进行针对性修复。
