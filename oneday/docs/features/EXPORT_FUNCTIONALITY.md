# OneDay应用导出功能实现文档

## 功能概述

OneDay应用的导出功能允许用户将自定义动作库、记忆词库和系统默认动作库导出为JSON文件，方便备份、分享和迁移数据。

## 实现的功能

### ✅ 1. 智能导出逻辑
- **当前选中动作库导出**: 如果用户已选择自定义动作库，直接导出该动作库
- **导出选项对话框**: 如果没有选中动作库，显示多种导出选项

### ✅ 2. 导出选项
1. **导出所有自定义动作库** - 批量导出用户创建的所有动作库
2. **导出记忆词库** - 导出用户的自定义词汇
3. **导出系统默认动作库** - 导出系统动作库作为创建模板

### ✅ 3. 用户体验功能
- **进度指示器** - 显示导出进度
- **成功反馈** - 显示文件保存位置和导出结果
- **分享功能** - 导出后可直接分享文件
- **错误处理** - 完善的错误提示和异常处理

## 技术实现

### 文件格式
所有导出文件均采用JSON格式，包含以下标准结构：

```json
{
  "version": "1.0",
  "exportedAt": "2024-01-01T12:00:00Z",
  "appVersion": "1.0.0",
  "type": "export_type",
  "data": {
    // 具体数据内容
  }
}
```

### 导出类型

#### 1. 单个自定义动作库 (`custom_library`)
```json
{
  "type": "custom_library",
  "data": {
    "library": {
      "id": "library_id",
      "name": "动作库名称",
      "description": "描述",
      "category": "分类",
      "createdAt": "2024-01-01T12:00:00Z",
      "lastModified": "2024-01-01T12:00:00Z",
      "actions": {
        "A": {
          "letter": "A",
          "nameEn": "Action",
          "nameCn": "动作",
          "description": "动作描述",
          "category": "分类",
          "scene": "场景",
          "keywords": ["关键词"]
        }
      }
    }
  }
}
```

#### 2. 所有自定义动作库 (`custom_libraries`)
```json
{
  "type": "custom_libraries",
  "data": {
    "customLibraries": [
      // 动作库数组
    ]
  }
}
```

#### 3. 记忆词库 (`memory_vocabulary`)
```json
{
  "type": "memory_vocabulary",
  "data": {
    "memoryVocabulary": {
      "word1": {
        "word": "Word1",
        "definition": "定义",
        "phonetic": "音标",
        "addedAt": "2024-01-01T12:00:00Z"
      }
    },
    "wordCount": 100
  }
}
```

#### 4. 系统默认动作库模板 (`default_library_template`)
```json
{
  "type": "default_library_template",
  "data": {
    "library": {
      "id": "default_template",
      "name": "系统默认动作库模板",
      "description": "基于OneDay系统默认动作库创建的模板",
      "actions": {
        // A-Z字母动作映射
      }
    }
  }
}
```

### 文件命名规则
- **单个动作库**: `{动作库名称}_{时间戳}.json`
- **所有自定义动作库**: `OneDay_CustomLibraries_{时间戳}.json`
- **记忆词库**: `OneDay_MemoryVocabulary_{时间戳}.json`
- **默认动作库模板**: `OneDay_DefaultLibraryTemplate_{时间戳}.json`

### 存储位置
- **Android**: 应用Documents目录 (`/data/data/com.example.oneday/app_flutter/documents/`)
- **iOS**: 应用Documents目录，支持文件分享

## 使用方法

### 1. 访问导出功能
在动作库主页面，点击右上角菜单 → 选择"导出动作库"

### 2. 导出流程
1. **有选中动作库**: 直接导出当前选中的自定义动作库
2. **无选中动作库**: 显示导出选项对话框，选择要导出的内容类型

### 3. 导出后操作
- 查看导出成功对话框，显示文件保存路径
- 点击"分享"按钮可直接分享导出的文件
- 点击"完成"关闭对话框

## 错误处理

### 常见错误情况
1. **存储空间不足** - 显示相应错误提示
2. **权限不足** - 提示用户检查应用权限
3. **数据为空** - 提示用户没有可导出的内容
4. **网络或系统错误** - 显示具体错误信息

### 错误恢复
- 所有错误都会自动关闭进度指示器
- 显示用户友好的错误消息
- 不会影响应用的正常使用

## 扩展性

### 未来可扩展功能
1. **云端备份** - 支持导出到云存储服务
2. **批量导入** - 支持批量导入多个文件
3. **数据压缩** - 支持压缩导出文件
4. **加密导出** - 支持加密敏感数据
5. **定时备份** - 支持自动定时导出

### 兼容性
- 导出格式向后兼容
- 支持版本升级时的数据迁移
- 标准JSON格式便于第三方工具处理

## 开发者注意事项

### 依赖包
- `path_provider`: 获取文件系统路径
- `share_plus`: 文件分享功能
- `dart:convert`: JSON编码解码
- `dart:io`: 文件操作

### 关键方法
- `_exportLibrary()`: 主导出入口
- `_exportSingleLibrary()`: 单个动作库导出
- `_exportAllCustomLibraries()`: 批量动作库导出
- `_exportMemoryVocabulary()`: 记忆词库导出
- `_exportDefaultLibrary()`: 默认动作库导出
- `_shareFile()`: 文件分享

### 测试建议
1. 测试各种导出场景
2. 验证文件格式正确性
3. 测试错误处理逻辑
4. 验证分享功能
5. 测试大数据量导出性能
