# 空调伴侣 (Accompanion)

## 项目概述
空调伴侣是一款智能移动APP，专为优化家用空调使用而设计。通过简洁的交互界面，将复杂的数学物理计算（如热力学模型和能耗估算）转化为用户友好的体验，帮助用户根据个人需求设置最佳的空调温度、定时方案和运行模式。

应用解决了用户在日常空调使用中的两大痛点：
- **复杂设置难题**：许多用户不知道如何根据天气、房间环境和个人习惯精确调整空调，导致要么浪费电费，要么舒适度不足
- **信息不对称**：空调能耗计算涉及专业知识，普通用户难以手动计算，应用通过后台算法自动化处理这些问题

## 目标用户
- **主要群体**：注重生活品质和节能的都市家庭用户（25-45岁）
- **特点**：时间紧迫、健康意识高、节能导向、技术友好
- **需求**：简单易用、准确可靠、实用价值

## 技术选型
- 开发框架: Flutter & Dart
- 状态管理: Riverpod
- 数据持久化: Hive
- UI风格: 生成跨平台、自适应、高性能、视觉统一的 Flutter UI风格，自动调用各平台最优组件，采用Notion现代简约高级感设计
- 图表组件: fl_chart
- 网络请求: dio
- 天气API: 集成实时气象数据

## 应用结构
应用采用模块化设计，核心流程为：用户输入→智能计算→结果展示→优化建议，确保从复杂计算到简单输出的无缝转换。

## 页面结构

| 页面/Widget名称 | 用途 | 核心功能 | 技术实现建议 | 导航/用户流程 | 建议文件路径 |
|:---:|:---:|:---:|:---:|:---:|:---:|
| `启动页面` | 应用启动和初始化 | 品牌展示、数据预加载、权限检查 | `SplashScreen` + `FutureBuilder` + 动画效果 | 应用启动→自动跳转到主页或引导页 | `lib/screens/splash_screen.dart` |
| `用户引导页` | 首次使用引导 | 功能介绍、使用说明、权限申请 | `PageView` + `Indicator` + 滑动动画 | 首次启动→滑动浏览→进入主页 | `lib/screens/onboarding_screen.dart` |
| `主页面` | 核心功能入口 | 模式选择、快速设置、当前状态展示 | `Scaffold` + `Card` + 状态管理(Riverpod) | 应用主入口，导航到各功能模块 | `lib/screens/home_screen.dart` |
| `房间设置页` | 个性化参数配置 | 房间信息、空调型号、环境参数输入 | `Form` + `TextFormField` + 数据验证 | 主页→设置→保存返回主页 | `lib/screens/room_setup_screen.dart` |
| `模式选择页` | 运行模式配置 | 省电模式/舒适模式切换、参数调整 | `ToggleButtons` + `Slider` + 实时预览 | 主页→模式选择→确认应用 | `lib/screens/mode_selection_screen.dart` |
| `智能计算页` | 算法计算展示 | 温度计算、定时方案、实时天气集成 | `FutureBuilder` + API调用 + 加载动画 | 模式选择→计算→查看结果 | `lib/screens/calculation_screen.dart` |
| `电费分析页` | 能耗和费用展示 | 电费预估、能耗曲线、分时段分析 | `Charts` + `ListView` + 数据可视化 | 计算结果→详细分析→优化建议 | `lib/screens/cost_analysis_screen.dart` |
| `优化建议页` | 个性化建议展示 | 节能提示、健康建议、维护提醒 | `Card` + `ExpansionTile` + 图标展示 | 分析页→建议列表→详情查看 | `lib/screens/optimization_tips_screen.dart` |
| `设置页面` | 应用配置管理 | 用户偏好、通知设置、数据管理 | `ListTile` + `Switch` + 数据持久化 | 主页→设置→各项配置 | `lib/screens/settings_screen.dart` |
| `历史记录页` | 使用历史查看 | 历史方案、电费趋势、使用统计 | `ListView` + `DatePicker` + 本地存储 | 主页→历史→筛选查看 | `lib/screens/history_screen.dart` |

## 数据模型
- **房间信息模型**：面积、朝向、楼层、窗户类型
- **空调设备模型**：品牌、型号、能效比、制冷量
- **环境数据模型**：温度、湿度、天气状况
- **计算结果模型**：推荐温度、定时方案、预估电费
- **历史记录模型**：使用时间、设置参数、实际能耗

## 技术实现细节

### 启动页面

#### UI设计方案
使用Scaffold作为页面基础结构，配合Container和Column实现垂直居中布局。品牌Logo采用Image.asset展示，配合Hero动画实现平滑过渡。应用名称使用Text组件，采用Notion风格的简洁字体和#37352F主文字色。加载指示器使用CircularProgressIndicator，配合#2F76DA强调蓝色。背景采用渐变色LinearGradient，营造现代感和深度。

#### 数据管理方案
使用FutureBuilder管理异步初始化流程，通过Riverpod的FutureProvider管理应用初始化状态。本地存储检查使用Hive进行首次启动判断，权限检查集成到初始化流程中。

#### 交互实现
3秒自动跳转机制，无需用户手动操作。Logo缩放动画使用AnimationController和ScaleTransition，淡入淡出效果使用FadeTransition，加载进度使用TweenAnimationBuilder实现平滑动画。

#### 跨平台能力利用
使用Platform.isIOS和Platform.isAndroid进行平台特定适配，iOS采用Cupertino风格的加载指示器，Android采用Material Design，状态栏样式通过SystemChrome进行平台优化。

#### 可访问性考虑
为Logo添加Semantics标签，支持屏幕阅读器。加载状态提供语音反馈，支持动态字体大小调整。

#### 组件复用
创建可复用的AppLogo组件，抽取LoadingIndicator作为通用加载组件，定义统一的主题色彩和字体样式。

#### 功能完整性检查表
- [x] 品牌Logo展示和Hero动画
- [x] 应用名称和副标题显示
- [x] 背景渐变和视觉效果
- [x] 加载指示器和动画
- [x] 3秒自动跳转机制
- [x] 跨平台适配（iOS/Android）
- [x] 可访问性支持（Semantics）
- [x] 状态栏样式优化
- [x] 内存管理和资源释放

### 用户引导页

#### UI设计方案
使用PageView实现水平滑动的引导页面，配合PageController管理页面状态。每个引导页使用Column布局，包含插图、标题、描述文字和操作按钮。底部使用PageIndicator显示当前页面进度，采用#2F76DA强调色。插图区域使用Container和Icon组合，配合渐变背景营造层次感。

#### 数据管理方案
使用PageController管理页面切换状态，通过Riverpod的StateProvider管理当前页面索引。使用Hive存储用户是否已完成引导的标记，引导内容通过数据模型进行结构化管理。

#### 交互实现
支持手势滑动和按钮点击两种导航方式，最后一页显示"开始使用"按钮，其他页面显示"下一步"和"跳过"。页面切换使用平滑的过渡动画，支持返回上一页功能。

#### 跨平台能力利用
iOS和Android使用统一的滑动体验，按钮样式根据平台自动适配，支持不同屏幕尺寸的响应式布局。

#### 可访问性考虑
为每个引导页添加完整的Semantics描述，支持屏幕阅读器导航，按钮提供清晰的语音反馈。

#### 组件复用
创建OnboardingPage组件用于单个引导页，抽取PageIndicator作为通用页面指示器，定义统一的引导页数据模型。

#### 功能完整性检查表
- [x] 多页面滑动引导流程（4个引导页）
- [x] 功能介绍和使用说明展示
- [x] 页面指示器和进度显示
- [x] 跳过和下一步操作
- [x] 最后一页"开始使用"按钮
- [x] 手势滑动和按钮点击导航
- [x] 响应式布局适配
- [x] 可访问性支持（Semantics）
- [x] 状态管理和页面控制

### 主页面

#### UI设计方案
使用Scaffold配合AppBar和BottomNavigationBar构建主框架。主体内容使用SingleChildScrollView和Column实现垂直滚动布局。顶部状态卡片使用Card组件展示当前空调状态和环境信息。功能模块使用GridView布局，每个功能用Container和InkWell实现。快速操作区域使用Row布局，包含省电模式和舒适模式切换。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理主页状态，通过AsyncValue处理异步数据加载（天气、房间状态等）。使用Hive进行用户偏好和历史数据的本地存储，集成天气API获取实时环境数据。

#### 交互实现
模式切换使用ToggleButtons实现即时反馈，功能卡片支持点击导航到对应页面。下拉刷新使用RefreshIndicator更新数据，浮动操作按钮提供快速计算入口。

#### 跨平台能力利用
AppBar样式根据平台自动适配Material/Cupertino风格，使用MediaQuery进行响应式布局适配，支持不同屏幕密度的图标和文字缩放。

#### 可访问性考虑
为所有交互元素添加Semantics标签，支持键盘导航和屏幕阅读器，提供高对比度模式支持。

#### 组件复用
创建StatusCard组件展示状态信息，抽取FunctionCard作为功能模块通用组件，定义ModeToggle组件用于模式切换。

#### 功能完整性检查表
- [x] 当前状态展示（温度、湿度、天气）
- [x] 模式选择（省电/舒适模式切换）
- [x] 功能模块网格导航（6个功能入口）
- [x] 快速操作按钮（开关空调、智能优化）
- [x] 下拉刷新数据更新
- [x] 浮动操作按钮（快速计算）
- [x] 响应式布局和状态管理
- [x] 可访问性支持和交互反馈
- [x] 欢迎信息和时间问候

### 房间设置页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用Form和SingleChildScrollView实现表单滚动布局。输入字段使用TextFormField配合自定义装饰，遵循Material Design规范。选择器使用DropdownButtonFormField和ListTile实现多选项配置。数值输入使用Slider配合TextFormField提供直观的调节体验。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理房间设置状态，通过FormKey进行表单验证和数据收集。使用Hive进行房间信息的本地持久化存储，定义RoomInfo数据模型包含所有房间参数。

#### 交互实现
表单验证提供实时反馈和错误提示，保存按钮支持数据验证和成功提示。重置功能允许恢复默认设置，输入过程中提供预览和建议。

#### 跨平台能力利用
输入法适配支持数字键盘和文本输入，使用MediaQuery适配不同屏幕尺寸，滚动行为在iOS和Android上保持一致。

#### 可访问性考虑
为所有输入字段添加Semantics标签和提示，支持键盘导航和Tab键切换，提供清晰的错误信息和操作指导。

#### 组件复用
创建FormSection组件用于表单分组，抽取NumberInputField作为数值输入通用组件，定义DropdownField组件用于选择器。

#### 功能完整性检查表
- [x] 房间基本信息输入（面积、朝向、楼层、窗户类型）
- [x] 空调设备信息配置（品牌、型号、功率）
- [x] 电费和居住信息设置（电费单价、居住人数）
- [x] 表单验证和错误提示
- [x] 数据保存和成功反馈
- [x] 重置功能和确认对话框
- [x] 响应式布局和分组展示
- [x] 输入法适配和数值限制
- [x] 可访问性支持和导航

### 模式选择页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用SingleChildScrollView和Column实现垂直滚动布局。模式选择使用大型Card组件，配合RadioListTile实现单选功能。参数调整使用Slider配合数值显示，提供直观的调节体验。预览区域使用Container展示实时计算结果和预估效果。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理模式选择状态，定义ModeConfig数据模型包含模式参数和设置。通过AsyncValue处理实时计算和预览更新，使用Hive进行模式偏好的本地存储。

#### 交互实现
模式切换提供即时视觉反馈和参数更新，滑块调节支持实时预览和数值显示。应用按钮触发计算并导航到结果页面，重置功能恢复默认模式设置。

#### 跨平台能力利用
滑块组件在iOS和Android上保持一致的交互体验，使用MediaQuery适配不同屏幕尺寸和方向，触觉反馈在支持的平台上提供操作确认。

#### 可访问性考虑
为模式选择添加清晰的Semantics描述，滑块支持键盘导航和精确调节，提供语音反馈和操作指导。

#### 组件复用
创建ModeCard组件用于模式展示，抽取ParameterSlider作为参数调节通用组件，定义PreviewPanel组件用于结果预览。

#### 功能完整性检查表
- [x] 三种运行模式选择（省电/舒适/自定义）
- [x] 参数调整滑块（温度、容差、定时、优先级）
- [x] 智能自动调节开关
- [x] 实时预览和效果展示
- [x] 预估电费、舒适度、节能率计算
- [x] 应用设置并导航到计算页面
- [x] 重置功能和触觉反馈
- [x] 响应式布局和视觉反馈
- [x] 可访问性支持和交互优化

### 智能计算页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用SingleChildScrollView和Column实现垂直滚动布局。计算过程使用Stepper组件展示步骤进度和状态。结果展示使用Card和Container配合渐变背景突出重要信息。加载状态使用CircularProgressIndicator配合动画效果。

#### 数据管理方案
使用Riverpod的AsyncNotifierProvider管理异步计算状态，通过FutureBuilder处理计算过程的异步操作。集成天气API获取实时环境数据，使用复杂的热力学算法进行温度和能耗计算。计算结果通过CalculationResult数据模型进行结构化存储。

#### 交互实现
计算按钮触发异步计算流程并显示进度，支持计算过程中的取消操作。结果页面支持分享和保存功能，重新计算功能允许调整参数后再次计算。

#### 跨平台能力利用
使用Platform.isIOS和Platform.isAndroid适配不同平台的加载样式，通过MediaQuery适配不同屏幕尺寸的图表显示，支持横屏模式下的图表优化展示。

#### 可访问性考虑
为计算步骤添加详细的Semantics描述，支持屏幕阅读器播报计算进度和结果，提供语音反馈和操作指导。

#### 组件复用
创建CalculationStep组件用于步骤展示，抽取ResultCard作为结果展示通用组件，定义TemperatureChart组件用于温度曲线图。

#### 功能完整性检查表
- [x] 多步骤计算流程展示（获取天气→分析房间→计算→优化）
- [x] 实时进度和状态更新
- [x] 计算结果详细展示（温度、电费、节能率、舒适度）
- [x] 温度曲线和功耗数据生成
- [x] 优化建议和个性化推荐
- [x] 计算过程取消和重试功能
- [x] 错误处理和用户反馈
- [x] 导航到详细分析页面
- [x] 可访问性支持和进度播报

### 电费分析页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用SingleChildScrollView和Column实现垂直滚动布局。图表展示使用fl_chart库实现电费趋势图和能耗分析图。数据卡片使用Card组件展示关键指标和统计信息。时间选择使用TabBar和TabBarView实现日/周/月视图切换。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理电费分析状态，通过AsyncValue处理图表数据的异步加载。定义CostAnalysisData数据模型包含电费、能耗、时间等信息，使用Hive进行历史电费数据的本地存储和查询。

#### 交互实现
图表支持缩放、平移和数据点点击查看详情，时间范围选择提供快速切换和自定义范围。数据导出功能支持分享和保存报告，下拉刷新更新最新的电费数据。

#### 跨平台能力利用
图表渲染在不同平台上保持一致的性能和外观，使用MediaQuery适配不同屏幕尺寸的图表显示，支持横屏模式下的图表优化展示。

#### 可访问性考虑
为图表添加Semantics描述和数据摘要，支持屏幕阅读器播报关键数据和趋势，提供高对比度模式下的图表优化。

#### 组件复用
创建CostChart组件用于电费图表展示，抽取DataCard作为数据展示通用组件，定义TimeRangeSelector组件用于时间范围选择。

#### 功能完整性检查表
- [x] 电费趋势图表展示（日/周/月视图）
- [x] 分时段电费分析（峰时/谷时）
- [x] 能耗统计和关键指标展示
- [x] 节能效果评估和节能率计算
- [x] 详细统计数据和数据点分析
- [x] 节能建议和优化提示
- [x] 时间范围切换和数据刷新
- [x] 分享功能和数据导出
- [x] 错误处理和加载状态

### 优化建议页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用SingleChildScrollView和Column实现垂直滚动布局。建议卡片使用Card和ExpansionTile实现可展开的详细信息。分类标签使用Chip组件进行建议类型分组。优先级指示使用颜色编码和图标展示重要程度。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理建议列表状态，通过AsyncValue处理建议数据的异步加载和更新。定义OptimizationTip数据模型包含建议内容、类型、优先级等，使用智能算法根据用户数据生成个性化建议。

#### 交互实现
建议卡片支持展开/收起查看详细信息，标记已读功能帮助用户管理建议状态。分享功能支持将建议发送给他人，筛选功能按类型和优先级过滤建议。

#### 跨平台能力利用
展开动画在不同平台上保持流畅的用户体验，使用MediaQuery适配不同屏幕尺寸的卡片布局，支持深色模式下的颜色适配。

#### 可访问性考虑
为建议卡片添加完整的Semantics描述，支持屏幕阅读器播报建议内容和优先级，提供键盘导航和焦点管理。

#### 组件复用
创建TipCard组件用于建议展示，抽取PriorityIndicator作为优先级显示通用组件，定义CategoryChip组件用于分类标签。

#### 功能完整性检查表
- [x] 分类展示各种优化建议（节能、舒适、维护、健康、费用）
- [x] 优先级标识和颜色编码（紧急、高、中、低）
- [x] 详细信息展开查看和ExpansionTile交互
- [x] 已读状态管理和标记功能
- [x] 分类筛选和未读筛选功能
- [x] 统计信息展示（未读数量、潜在节省、紧急建议）
- [x] 分享功能和操作按钮
- [x] 时间显示和相对时间格式化
- [x] 错误处理和加载状态

### 设置页面

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用ListView实现分组设置项的垂直滚动布局。设置项使用ListTile、SwitchListTile、SliderListTile等组件。分组使用Card和分隔线实现清晰的视觉层级。用户信息区域使用UserAccountsDrawerHeader风格的头部展示。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理设置状态，通过Hive进行设置数据的本地持久化存储。定义AppSettings数据模型包含所有配置选项，支持设置的导入导出和备份恢复功能。

#### 交互实现
开关设置提供即时反馈和状态更新，数值设置使用滑块和输入框组合。重置功能提供确认对话框防止误操作，关于页面展示应用信息和版本详情。

#### 跨平台能力利用
设置项样式根据平台自动适配Material/Cupertino风格，使用MediaQuery适配不同屏幕尺寸的布局，支持系统主题和深色模式切换。

#### 可访问性考虑
为所有设置项添加清晰的Semantics描述，支持键盘导航和屏幕阅读器，提供高对比度模式和字体大小调节。

#### 组件复用
创建SettingsSection组件用于设置分组，抽取SettingsTile作为设置项通用组件，定义ConfirmDialog组件用于确认操作。

#### 功能完整性检查表
- [x] 用户信息展示和编辑入口
- [x] 通知设置（启用通知、节能提示）
- [x] 计算设置（自动计算、默认温度、容差、单位）
- [x] 界面设置（深色模式、语言、字体、触觉反馈、声音）
- [x] 数据和隐私设置（数据同步、分析、缓存清理、隐私政策）
- [x] 关于应用（版本信息、更新检查、用户反馈、开源许可）
- [x] 设置导入导出和重置功能
- [x] 确认对话框和用户反馈
- [x] 分组布局和视觉层级

### 历史记录页

#### UI设计方案
使用Scaffold配合AppBar构建页面框架，支持返回导航。主体内容使用ListView和Card实现历史记录的垂直滚动展示。时间筛选使用DateRangePicker和TabBar实现日期范围选择。记录卡片使用ExpansionTile支持展开查看详细信息。统计图表使用简化的图表组件展示趋势数据。

#### 数据管理方案
使用Riverpod的StateNotifierProvider管理历史记录状态，通过AsyncValue处理历史数据的异步加载和分页。定义HistoryRecord数据模型包含使用时间、设置参数、实际能耗等，使用Hive进行历史数据的本地存储和高效查询。

#### 交互实现
支持按日期范围筛选历史记录，记录卡片支持展开查看详细参数和分析。下拉刷新和上拉加载更多记录，支持删除单条记录或批量清理历史。

#### 跨平台能力利用
日期选择器根据平台自动适配Material/Cupertino风格，使用MediaQuery适配不同屏幕尺寸的卡片布局，支持横屏模式下的优化展示。

#### 可访问性考虑
为历史记录添加完整的Semantics描述，支持屏幕阅读器播报记录内容和时间，提供键盘导航和焦点管理。

#### 组件复用
创建HistoryCard组件用于记录展示，抽取DateFilter作为日期筛选通用组件，定义TrendChart组件用于趋势图表。

#### 功能完整性检查表
- [x] 历史使用记录展示和时间线布局
- [x] 按日期范围和模式筛选记录
- [x] 详细参数和统计信息展示
- [x] 统计概览（总电费、总用电、平均舒适度）
- [x] 记录卡片展开查看详细信息
- [x] 删除单条记录和批量清理功能
- [x] 数据导出和分享功能
- [x] 下拉刷新和错误处理
- [x] 空状态展示和用户引导

## 开发状态跟踪
| 页面/组件名称 | 开发状态 | 文件路径 |
|:---:|:---:|:---:|
| 启动页面 | 已完成 | `lib/screens/splash_screen.dart` |
| 用户引导页 | 已完成 | `lib/screens/onboarding_screen.dart` |
| 主页面 | 已完成 | `lib/screens/home_screen.dart` |
| 房间设置页 | 已完成 | `lib/screens/room_setup_screen.dart` |
| 模式选择页 | 已完成 | `lib/screens/mode_selection_screen.dart` |
| 智能计算页 | 已完成 | `lib/screens/calculation_screen.dart` |
| 电费分析页 | 已完成 | `lib/screens/cost_analysis_screen.dart` |
| 优化建议页 | 已完成 | `lib/screens/optimization_tips_screen.dart` |
| 设置页面 | 已完成 | `lib/screens/settings_screen.dart` |
| 历史记录页 | 已完成 | `lib/screens/history_screen.dart` |


