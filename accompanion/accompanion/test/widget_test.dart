// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'package:accompanion/main.dart';

void main() {
  testWidgets('App launches successfully', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ProviderScope(child: AccompanionApp()));

    // Verify that the splash screen is displayed
    expect(find.text('空调管家'), findsOneWidget);

    // Wait for splash screen animation
    await tester.pump(const Duration(seconds: 3));

    // The app should navigate to onboarding or home screen
    // This is a basic test to ensure the app starts without crashing
  });
}
