import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 时间范围枚举
enum TimeRange { daily, weekly, monthly }

/// 电费分析数据点
class CostDataPoint {
  final DateTime time;
  final double cost;
  final double consumption;
  final double temperature;

  const CostDataPoint({
    required this.time,
    required this.cost,
    required this.consumption,
    required this.temperature,
  });
}

/// 电费分析数据模型
class CostAnalysisData {
  final TimeRange timeRange;
  final List<CostDataPoint> dataPoints;
  final double totalCost;
  final double averageCost;
  final double totalConsumption;
  final double averageConsumption;
  final double savingsRate;
  final double peakCost;
  final double offPeakCost;

  const CostAnalysisData({
    required this.timeRange,
    required this.dataPoints,
    required this.totalCost,
    required this.averageCost,
    required this.totalConsumption,
    required this.averageConsumption,
    required this.savingsRate,
    required this.peakCost,
    required this.offPeakCost,
  });
}

/// 电费分析状态管理
class CostAnalysisNotifier extends StateNotifier<AsyncValue<CostAnalysisData>> {
  CostAnalysisNotifier() : super(const AsyncValue.loading()) {
    loadData(TimeRange.daily);
  }

  /// 加载数据
  Future<void> loadData(TimeRange timeRange) async {
    state = const AsyncValue.loading();
    
    try {
      await Future.delayed(const Duration(milliseconds: 800)); // 模拟网络请求
      final data = _generateMockData(timeRange);
      state = AsyncValue.data(data);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// 生成模拟数据
  CostAnalysisData _generateMockData(TimeRange timeRange) {
    final random = Random();
    final now = DateTime.now();
    final dataPoints = <CostDataPoint>[];
    
    int pointCount;
    DateTime startTime;
    
    switch (timeRange) {
      case TimeRange.daily:
        pointCount = 24;
        startTime = DateTime(now.year, now.month, now.day);
        break;
      case TimeRange.weekly:
        pointCount = 7;
        startTime = now.subtract(const Duration(days: 6));
        break;
      case TimeRange.monthly:
        pointCount = 30;
        startTime = now.subtract(const Duration(days: 29));
        break;
    }

    for (int i = 0; i < pointCount; i++) {
      DateTime time;
      switch (timeRange) {
        case TimeRange.daily:
          time = startTime.add(Duration(hours: i));
          break;
        case TimeRange.weekly:
          time = startTime.add(Duration(days: i));
          break;
        case TimeRange.monthly:
          time = startTime.add(Duration(days: i));
          break;
      }

      // 生成模拟数据
      final baseCost = 0.6; // 基础电费
      final timeVariation = sin(i * 2 * pi / pointCount) * 0.2; // 时间变化
      final randomVariation = (random.nextDouble() - 0.5) * 0.1; // 随机变化
      final cost = baseCost + timeVariation + randomVariation;
      
      final consumption = cost / 0.6; // 根据电费计算用电量
      final temperature = 24 + sin(i * 2 * pi / pointCount) * 3 + (random.nextDouble() - 0.5);

      dataPoints.add(CostDataPoint(
        time: time,
        cost: cost.clamp(0.3, 1.2),
        consumption: consumption.clamp(0.5, 2.0),
        temperature: temperature.clamp(20, 30),
      ));
    }

    final totalCost = dataPoints.fold(0.0, (sum, point) => sum + point.cost);
    final totalConsumption = dataPoints.fold(0.0, (sum, point) => sum + point.consumption);
    final averageCost = totalCost / dataPoints.length;
    final averageConsumption = totalConsumption / dataPoints.length;
    
    // 计算峰谷电费
    final peakHours = dataPoints.where((p) => p.time.hour >= 8 && p.time.hour <= 22);
    final offPeakHours = dataPoints.where((p) => p.time.hour < 8 || p.time.hour > 22);
    
    final peakCost = peakHours.isNotEmpty 
        ? peakHours.fold(0.0, (sum, point) => sum + point.cost) 
        : 0.0;
    final offPeakCost = offPeakHours.isNotEmpty 
        ? offPeakHours.fold(0.0, (sum, point) => sum + point.cost) 
        : 0.0;

    return CostAnalysisData(
      timeRange: timeRange,
      dataPoints: dataPoints,
      totalCost: totalCost,
      averageCost: averageCost,
      totalConsumption: totalConsumption,
      averageConsumption: averageConsumption,
      savingsRate: 0.15 + random.nextDouble() * 0.2, // 15-35%的节能率
      peakCost: peakCost,
      offPeakCost: offPeakCost,
    );
  }
}

/// 电费分析状态提供者
final costAnalysisProvider = StateNotifierProvider<CostAnalysisNotifier, AsyncValue<CostAnalysisData>>((ref) {
  return CostAnalysisNotifier();
});

/// 电费分析页面 - 能耗和费用展示
/// 
/// 功能特点：
/// - 电费趋势图表展示
/// - 分时段电费分析
/// - 能耗统计和对比
/// - 节能效果评估
class CostAnalysisScreen extends ConsumerStatefulWidget {
  const CostAnalysisScreen({super.key});

  @override
  ConsumerState<CostAnalysisScreen> createState() => _CostAnalysisScreenState();
}

class _CostAnalysisScreenState extends ConsumerState<CostAnalysisScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  TimeRange _selectedTimeRange = TimeRange.daily;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    if (_tabController.indexIsChanging) return;
    
    final newTimeRange = TimeRange.values[_tabController.index];
    if (newTimeRange != _selectedTimeRange) {
      setState(() {
        _selectedTimeRange = newTimeRange;
      });
      ref.read(costAnalysisProvider.notifier).loadData(newTimeRange);
    }
  }

  @override
  Widget build(BuildContext context) {
    final analysisState = ref.watch(costAnalysisProvider);
    
    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '电费分析',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          IconButton(
            icon: const Icon(Icons.share),
            onPressed: () {
              // TODO: 实现分享功能
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('分享功能开发中')),
              );
            },
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF2F76DA),
          unselectedLabelColor: const Color(0xFF6E6E6E),
          indicatorColor: const Color(0xFF2F76DA),
          tabs: const [
            Tab(text: '今日'),
            Tab(text: '本周'),
            Tab(text: '本月'),
          ],
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(costAnalysisProvider.notifier).loadData(_selectedTimeRange),
        child: analysisState.when(
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(error),
          data: (data) => _buildDataState(data),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2F76DA)),
          ),
          SizedBox(height: 16),
          Text(
            '正在加载电费数据...',
            style: TextStyle(
              fontSize: 16,
              color: Color(0xFF6E6E6E),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Icons.error_outline,
            size: 64,
            color: Color(0xFFEB5757),
          ),
          const SizedBox(height: 16),
          const Text(
            '加载失败',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: const TextStyle(
              fontSize: 14,
              color: Color(0xFF6E6E6E),
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.read(costAnalysisProvider.notifier).loadData(_selectedTimeRange),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建数据状态
  Widget _buildDataState(CostAnalysisData data) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 概览卡片
          _buildOverviewCards(data),
          const SizedBox(height: 24),
          // 电费趋势图
          _buildCostChart(data),
          const SizedBox(height: 24),
          // 详细统计
          _buildDetailedStats(data),
          const SizedBox(height: 24),
          // 节能建议
          _buildSavingsTips(data),
        ],
      ),
    );
  }

  /// 构建概览卡片
  Widget _buildOverviewCards(CostAnalysisData data) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: '总电费',
            value: '${data.totalCost.toStringAsFixed(2)}元',
            icon: Icons.attach_money,
            color: const Color(0xFF2F76DA),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: '总用电量',
            value: '${data.totalConsumption.toStringAsFixed(1)}度',
            icon: Icons.flash_on,
            color: const Color(0xFF4CAF50),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: '节能率',
            value: '${(data.savingsRate * 100).toStringAsFixed(0)}%',
            icon: Icons.eco,
            color: const Color(0xFF9061F9),
          ),
        ),
      ],
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                color: Color(0xFF6E6E6E),
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建电费趋势图
  Widget _buildCostChart(CostAnalysisData data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '电费趋势',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              height: 200,
              decoration: BoxDecoration(
                color: const Color(0xFFF8F9FA),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.show_chart,
                      size: 48,
                      color: Color(0xFF2F76DA),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '电费趋势图',
                      style: TextStyle(
                        fontSize: 16,
                        color: Color(0xFF6E6E6E),
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '需要集成 fl_chart 库',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF9B9A97),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建详细统计
  Widget _buildDetailedStats(CostAnalysisData data) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '详细统计',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            _buildStatRow('平均电费', '${data.averageCost.toStringAsFixed(2)}元'),
            const SizedBox(height: 12),
            _buildStatRow('平均用电量', '${data.averageConsumption.toStringAsFixed(1)}度'),
            const SizedBox(height: 12),
            _buildStatRow('峰时电费', '${data.peakCost.toStringAsFixed(2)}元'),
            const SizedBox(height: 12),
            _buildStatRow('谷时电费', '${data.offPeakCost.toStringAsFixed(2)}元'),
            const SizedBox(height: 12),
            _buildStatRow('数据点数', '${data.dataPoints.length}个'),
          ],
        ),
      ),
    );
  }

  /// 构建统计行
  Widget _buildStatRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 16,
            color: Color(0xFF6E6E6E),
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  /// 构建节能建议
  Widget _buildSavingsTips(CostAnalysisData data) {
    final tips = [
      '在用电高峰期（8:00-22:00）适当调高空调温度可节省电费',
      '夜间使用谷时电价，建议在23:00后开启空调',
      '当前节能率为${(data.savingsRate * 100).toStringAsFixed(0)}%，表现良好',
      '建议定期清洁空调滤网，可提高能效比约10%',
    ];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(
                  Icons.lightbulb,
                  color: Color(0xFFFF9800),
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '节能建议',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...tips.asMap().entries.map((entry) {
              final index = entry.key;
              final tip = entry.value;
              return Padding(
                padding: EdgeInsets.only(bottom: index < tips.length - 1 ? 12 : 0),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 6,
                      height: 6,
                      margin: const EdgeInsets.only(top: 8, right: 12),
                      decoration: const BoxDecoration(
                        color: Color(0xFF2F76DA),
                        shape: BoxShape.circle,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        tip,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Color(0xFF6E6E6E),
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
