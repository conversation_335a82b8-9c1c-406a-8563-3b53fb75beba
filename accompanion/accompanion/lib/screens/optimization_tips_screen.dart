import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 建议优先级枚举
enum TipPriority { low, medium, high, urgent }

/// 建议类型枚举
enum TipCategory { energy, comfort, maintenance, health, cost }

/// 优化建议数据模型
class OptimizationTip {
  final String id;
  final String title;
  final String summary;
  final String details;
  final TipCategory category;
  final TipPriority priority;
  final IconData icon;
  final double potentialSavings;
  final bool isRead;
  final DateTime createdAt;

  const OptimizationTip({
    required this.id,
    required this.title,
    required this.summary,
    required this.details,
    required this.category,
    required this.priority,
    required this.icon,
    required this.potentialSavings,
    this.isRead = false,
    required this.createdAt,
  });

  OptimizationTip copyWith({
    String? id,
    String? title,
    String? summary,
    String? details,
    TipCategory? category,
    TipPriority? priority,
    IconData? icon,
    double? potentialSavings,
    bool? isRead,
    DateTime? createdAt,
  }) {
    return OptimizationTip(
      id: id ?? this.id,
      title: title ?? this.title,
      summary: summary ?? this.summary,
      details: details ?? this.details,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      icon: icon ?? this.icon,
      potentialSavings: potentialSavings ?? this.potentialSavings,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// 建议筛选状态
class TipsFilterState {
  final TipCategory? selectedCategory;
  final TipPriority? selectedPriority;
  final bool showOnlyUnread;

  const TipsFilterState({
    this.selectedCategory,
    this.selectedPriority,
    this.showOnlyUnread = false,
  });

  TipsFilterState copyWith({
    TipCategory? selectedCategory,
    TipPriority? selectedPriority,
    bool? showOnlyUnread,
  }) {
    return TipsFilterState(
      selectedCategory: selectedCategory,
      selectedPriority: selectedPriority,
      showOnlyUnread: showOnlyUnread ?? this.showOnlyUnread,
    );
  }
}

/// 优化建议状态管理
class OptimizationTipsNotifier
    extends StateNotifier<AsyncValue<List<OptimizationTip>>> {
  OptimizationTipsNotifier() : super(const AsyncValue.loading()) {
    loadTips();
  }

  /// 加载建议
  Future<void> loadTips() async {
    state = const AsyncValue.loading();

    try {
      await Future.delayed(const Duration(milliseconds: 600)); // 模拟网络请求
      final tips = _generateMockTips();
      state = AsyncValue.data(tips);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// 标记建议为已读
  void markAsRead(String tipId) {
    state.whenData((tips) {
      // 检查是否需要更新
      final tipIndex = tips.indexWhere((tip) => tip.id == tipId);
      if (tipIndex == -1 || tips[tipIndex].isRead) {
        return; // 建议不存在或已经是已读状态，无需更新
      }

      final updatedTips = List<OptimizationTip>.from(tips);
      updatedTips[tipIndex] = tips[tipIndex].copyWith(isRead: true);
      state = AsyncValue.data(updatedTips);
    });
  }

  /// 生成模拟建议数据
  List<OptimizationTip> _generateMockTips() {
    final random = Random();
    final now = DateTime.now();

    final mockTips = [
      OptimizationTip(
        id: '1',
        title: '夜间温度调节',
        summary: '晚上22:00后将温度调高1°C，可节省约15%的电费',
        details:
            '根据您的使用数据分析，夜间人体对温度的敏感度降低，适当调高温度不会影响睡眠质量，但可以显著降低空调能耗。建议将夜间温度设置为26-27°C。',
        category: TipCategory.energy,
        priority: TipPriority.high,
        icon: Icons.nightlight_round,
        potentialSavings: 0.15,
        createdAt: now.subtract(const Duration(hours: 2)),
      ),
      OptimizationTip(
        id: '2',
        title: '定期清洁滤网',
        summary: '空调滤网需要清洁，可提高制冷效率约10%',
        details:
            '检测到您的空调已运行较长时间，滤网可能积累了灰尘。脏污的滤网会增加空调负荷，降低制冷效率。建议每月清洁一次滤网，或根据使用频率调整清洁周期。',
        category: TipCategory.maintenance,
        priority: TipPriority.medium,
        icon: Icons.cleaning_services,
        potentialSavings: 0.10,
        createdAt: now.subtract(const Duration(days: 1)),
      ),
      OptimizationTip(
        id: '3',
        title: '风扇辅助降温',
        summary: '同时开启风扇和空调，可将空调温度上调3度，既保持舒适度又能显著节省电费',
        details:
            '风扇可以增强空气流动，提高人体散热效率，让您在较高温度下也能感受到凉爽。建议将空调温度设置为26-27°C，同时开启风扇，这样既能保持舒适度，又能节省约25%的电费。',
        category: TipCategory.energy,
        priority: TipPriority.high,
        icon: Icons.air,
        potentialSavings: 0.25,
        createdAt: now.subtract(const Duration(hours: 3)),
      ),
      OptimizationTip(
        id: '4',
        title: '使用定时功能',
        summary: '开启定时关机功能，避免整夜运行浪费电力',
        details:
            '分析显示您经常忘记关闭空调，导致不必要的电力消耗。建议使用定时关机功能，设置在入睡后2-3小时自动关闭，或使用睡眠模式逐步调高温度。',
        category: TipCategory.cost,
        priority: TipPriority.medium,
        icon: Icons.timer,
        potentialSavings: 0.20,
        createdAt: now.subtract(const Duration(hours: 6)),
      ),
      OptimizationTip(
        id: '5',
        title: '房间密封性检查',
        summary: '检查门窗密封性，减少冷气流失',
        details:
            '良好的房间密封性可以显著提高空调效率。检查门窗是否关闭严密，窗帘是否能有效阻挡阳光直射。如有必要，可以使用密封条改善门窗密封性。',
        category: TipCategory.energy,
        priority: TipPriority.low,
        icon: Icons.door_front_door,
        potentialSavings: 0.08,
        createdAt: now.subtract(const Duration(days: 2)),
      ),
      OptimizationTip(
        id: '6',
        title: '湿度控制建议',
        summary: '当前湿度偏高，建议开启除湿功能',
        details:
            '检测到室内湿度较高，这会影响体感温度和舒适度。高湿度环境下，即使温度不高也会感觉闷热。建议开启空调的除湿功能，或适当降低温度设置。',
        category: TipCategory.comfort,
        priority: TipPriority.medium,
        icon: Icons.water_drop,
        potentialSavings: 0.05,
        createdAt: now.subtract(const Duration(hours: 4)),
      ),
      OptimizationTip(
        id: '7',
        title: '健康使用提醒',
        summary: '避免温差过大，预防空调病',
        details:
            '室内外温差不宜超过7°C，过大的温差容易导致感冒等健康问题。建议根据室外温度合理设置空调温度，并定期开窗通风，保持空气流通。',
        category: TipCategory.health,
        priority: TipPriority.urgent,
        icon: Icons.health_and_safety,
        potentialSavings: 0.0,
        createdAt: now.subtract(const Duration(minutes: 30)),
      ),
    ];

    // 随机设置一些为已读状态
    return mockTips.map((tip) {
      return random.nextBool() ? tip.copyWith(isRead: true) : tip;
    }).toList();
  }
}

/// 筛选状态管理
final tipsFilterProvider = StateProvider<TipsFilterState>((ref) {
  return const TipsFilterState();
});

/// 优化建议状态提供者
final optimizationTipsProvider =
    StateNotifierProvider<
      OptimizationTipsNotifier,
      AsyncValue<List<OptimizationTip>>
    >((ref) {
      return OptimizationTipsNotifier();
    });

/// 筛选后的建议列表
final filteredTipsProvider = Provider<AsyncValue<List<OptimizationTip>>>((ref) {
  final tipsAsync = ref.watch(optimizationTipsProvider);
  final filter = ref.watch(tipsFilterProvider);

  return tipsAsync.when(
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
    data: (tips) {
      var filteredTips = tips;

      // 按类别筛选
      if (filter.selectedCategory != null) {
        filteredTips = filteredTips
            .where((tip) => tip.category == filter.selectedCategory)
            .toList();
      }

      // 按优先级筛选
      if (filter.selectedPriority != null) {
        filteredTips = filteredTips
            .where((tip) => tip.priority == filter.selectedPriority)
            .toList();
      }

      // 只显示未读
      if (filter.showOnlyUnread) {
        filteredTips = filteredTips.where((tip) => !tip.isRead).toList();
      }

      // 按优先级和创建时间排序
      filteredTips.sort((a, b) {
        final priorityOrder = {
          TipPriority.urgent: 0,
          TipPriority.high: 1,
          TipPriority.medium: 2,
          TipPriority.low: 3,
        };

        final priorityComparison = priorityOrder[a.priority]!.compareTo(
          priorityOrder[b.priority]!,
        );
        if (priorityComparison != 0) return priorityComparison;

        return b.createdAt.compareTo(a.createdAt); // 新的在前
      });

      return AsyncValue.data(filteredTips);
    },
  );
});

/// 优化建议页面 - 个性化建议展示
///
/// 功能特点：
/// - 分类展示各种优化建议
/// - 优先级标识和筛选功能
/// - 详细信息展开查看
/// - 已读状态管理
class OptimizationTipsScreen extends ConsumerWidget {
  const OptimizationTipsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tipsAsync = ref.watch(filteredTipsProvider);
    final filter = ref.watch(tipsFilterProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '优化建议',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          Semantics(
            label: filter.showOnlyUnread ? '显示所有建议' : '只显示未读建议',
            button: true,
            child: IconButton(
              icon: Icon(
                filter.showOnlyUnread
                    ? Icons.mark_email_read
                    : Icons.mark_email_unread,
                color: const Color(0xFF6E6E6E),
              ),
              onPressed: () {
                ref.read(tipsFilterProvider.notifier).state = filter.copyWith(
                  showOnlyUnread: !filter.showOnlyUnread,
                );
              },
            ),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.filter_list, color: Color(0xFF6E6E6E)),
            onSelected: (value) {
              switch (value) {
                case 'clear':
                  ref.read(tipsFilterProvider.notifier).state =
                      const TipsFilterState();
                  break;
                case 'category':
                  _showCategoryFilterDialog(context, ref);
                  break;
                case 'priority':
                  _showPriorityFilterDialog(context, ref);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'category', child: Text('按类别筛选')),
              const PopupMenuItem(value: 'priority', child: Text('按优先级筛选')),
              const PopupMenuItem(value: 'clear', child: Text('清除筛选')),
            ],
          ),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(optimizationTipsProvider.notifier).loadTips(),
        child: tipsAsync.when(
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(ref, error),
          data: (tips) => _buildTipsList(context, ref, tips),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2F76DA)),
          ),
          SizedBox(height: 16),
          Text(
            '正在加载建议...',
            style: TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(WidgetRef ref, Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Color(0xFFEB5757)),
          const SizedBox(height: 16),
          const Text(
            '加载失败',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: const TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () =>
                ref.read(optimizationTipsProvider.notifier).loadTips(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建建议列表
  Widget _buildTipsList(
    BuildContext context,
    WidgetRef ref,
    List<OptimizationTip> tips,
  ) {
    if (tips.isEmpty) {
      return _buildEmptyState();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 统计信息
          _buildStatsCard(tips),
          const SizedBox(height: 16),
          // 分类筛选
          _buildCategoryFilter(ref),
          const SizedBox(height: 16),
          // 建议列表
          ...tips.map(
            (tip) => Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: _buildTipCard(context, ref, tip),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.lightbulb_outline, size: 64, color: Color(0xFF9B9A97)),
          SizedBox(height: 16),
          Text(
            '暂无建议',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '当前没有符合筛选条件的建议',
            style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatsCard(List<OptimizationTip> tips) {
    final unreadCount = tips.where((tip) => !tip.isRead).length;
    final totalSavings = tips.fold(
      0.0,
      (sum, tip) => sum + tip.potentialSavings,
    );
    final urgentCount = tips
        .where((tip) => tip.priority == TipPriority.urgent)
        .length;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem(
              '未读建议',
              '$unreadCount条',
              Icons.mark_email_unread,
              const Color(0xFF2F76DA),
            ),
            _buildStatItem(
              '潜在节省',
              '${(totalSavings * 100).toStringAsFixed(0)}%',
              Icons.savings,
              const Color(0xFF4CAF50),
            ),
            _buildStatItem(
              '紧急建议',
              '$urgentCount条',
              Icons.priority_high,
              const Color(0xFFEB5757),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF6E6E6E)),
        ),
      ],
    );
  }

  /// 构建分类筛选
  Widget _buildCategoryFilter(WidgetRef ref) {
    final categories = [
      (TipCategory.energy, '节能', Icons.eco, const Color(0xFF4CAF50)),
      (TipCategory.comfort, '舒适', Icons.favorite, const Color(0xFF2F76DA)),
      (TipCategory.maintenance, '维护', Icons.build, const Color(0xFF9061F9)),
      (
        TipCategory.health,
        '健康',
        Icons.health_and_safety,
        const Color(0xFFEB5757),
      ),
      (TipCategory.cost, '费用', Icons.attach_money, const Color(0xFFFF9800)),
    ];

    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final (category, label, icon, color) = categories[index];
          final filter = ref.watch(tipsFilterProvider);
          final isSelected = filter.selectedCategory == category;

          return Padding(
            padding: EdgeInsets.only(
              right: index < categories.length - 1 ? 8 : 0,
            ),
            child: Semantics(
              label: '筛选类别：$label',
              button: true,
              selected: isSelected,
              child: FilterChip(
                selected: isSelected,
                label: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      icon,
                      size: 16,
                      color: isSelected ? Colors.white : color,
                    ),
                    const SizedBox(width: 4),
                    Flexible(
                      child: Text(label, overflow: TextOverflow.ellipsis),
                    ),
                  ],
                ),
                selectedColor: color,
                checkmarkColor: Colors.white,
                onSelected: (selected) {
                  ref.read(tipsFilterProvider.notifier).state = filter.copyWith(
                    selectedCategory: selected ? category : null,
                  );
                },
              ),
            ),
          );
        },
      ),
    );
  }

  /// 构建建议卡片
  Widget _buildTipCard(
    BuildContext context,
    WidgetRef ref,
    OptimizationTip tip,
  ) {
    return Semantics(
      label: '优化建议：${tip.title}，${tip.isRead ? '已读' : '未读'}',
      hint: '点击展开查看详细信息',
      child: Card(
        elevation: tip.isRead ? 1 : 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ExpansionTile(
          leading: _buildPriorityIndicator(tip.priority),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  tip.title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: tip.isRead
                        ? const Color(0xFF9B9A97)
                        : const Color(0xFF37352F),
                  ),
                ),
              ),
              if (!tip.isRead)
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Color(0xFF2F76DA),
                    shape: BoxShape.circle,
                  ),
                ),
            ],
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                tip.summary,
                style: const TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  _buildCategoryChip(tip.category),
                  const SizedBox(width: 8),
                  if (tip.potentialSavings > 0)
                    Chip(
                      label: Text(
                        '节省${(tip.potentialSavings * 100).toStringAsFixed(0)}%',
                        style: const TextStyle(fontSize: 12),
                      ),
                      backgroundColor: const Color(
                        0xFF4CAF50,
                      ).withValues(alpha: 0.1),
                      labelStyle: const TextStyle(color: Color(0xFF4CAF50)),
                      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                    ),
                ],
              ),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    tip.details,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6E6E6E),
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        _formatDateTime(tip.createdAt),
                        style: const TextStyle(
                          fontSize: 12,
                          color: Color(0xFF9B9A97),
                        ),
                      ),
                      Row(
                        children: [
                          if (!tip.isRead)
                            TextButton(
                              onPressed: () {
                                ref
                                    .read(optimizationTipsProvider.notifier)
                                    .markAsRead(tip.id);
                              },
                              child: const Text('标记已读'),
                            ),
                          TextButton(
                            onPressed: () => _shareTip(context, tip),
                            child: const Text('分享'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
          onExpansionChanged: (expanded) {
            if (expanded && !tip.isRead) {
              ref.read(optimizationTipsProvider.notifier).markAsRead(tip.id);
            }
          },
        ),
      ),
    );
  }

  /// 构建优先级指示器
  Widget _buildPriorityIndicator(TipPriority priority) {
    final config = {
      TipPriority.urgent: (Icons.priority_high, Color(0xFFEB5757)),
      TipPriority.high: (Icons.keyboard_arrow_up, Color(0xFFFF9800)),
      TipPriority.medium: (Icons.remove, Color(0xFF2F76DA)),
      TipPriority.low: (Icons.keyboard_arrow_down, Color(0xFF4CAF50)),
    };

    final (icon, color) = config[priority]!;

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(icon, color: color, size: 20),
    );
  }

  /// 构建分类标签
  Widget _buildCategoryChip(TipCategory category) {
    final config = {
      TipCategory.energy: ('节能', Color(0xFF4CAF50)),
      TipCategory.comfort: ('舒适', Color(0xFF2F76DA)),
      TipCategory.maintenance: ('维护', Color(0xFF9061F9)),
      TipCategory.health: ('健康', Color(0xFFEB5757)),
      TipCategory.cost: ('费用', Color(0xFFFF9800)),
    };

    final (label, color) = config[category]!;

    return Chip(
      label: Text(label, style: const TextStyle(fontSize: 12)),
      backgroundColor: color.withValues(alpha: 0.1),
      labelStyle: TextStyle(color: color),
      materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}分钟前';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}小时前';
    } else {
      return '${difference.inDays}天前';
    }
  }

  /// 显示类别筛选对话框
  void _showCategoryFilterDialog(BuildContext context, WidgetRef ref) {
    final categories = [
      (null, '全部类别'),
      (TipCategory.energy, '节能'),
      (TipCategory.comfort, '舒适'),
      (TipCategory.maintenance, '维护'),
      (TipCategory.health, '健康'),
      (TipCategory.cost, '费用'),
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择类别'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: categories.map((item) {
            final (category, label) = item;
            return ListTile(
              title: Text(label),
              onTap: () {
                final filter = ref.read(tipsFilterProvider);
                ref.read(tipsFilterProvider.notifier).state = filter.copyWith(
                  selectedCategory: category,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 显示优先级筛选对话框
  void _showPriorityFilterDialog(BuildContext context, WidgetRef ref) {
    final priorities = [
      (null, '全部优先级'),
      (TipPriority.urgent, '紧急'),
      (TipPriority.high, '高'),
      (TipPriority.medium, '中'),
      (TipPriority.low, '低'),
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('选择优先级'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: priorities.map((item) {
            final (priority, label) = item;
            return ListTile(
              title: Text(label),
              onTap: () {
                final filter = ref.read(tipsFilterProvider);
                ref.read(tipsFilterProvider.notifier).state = filter.copyWith(
                  selectedPriority: priority,
                );
                Navigator.of(context).pop();
              },
            );
          }).toList(),
        ),
      ),
    );
  }

  /// 分享建议
  void _shareTip(BuildContext context, OptimizationTip tip) {
    final shareText =
        '''
空调管家 - 优化建议

${tip.title}

${tip.summary}

详细说明：
${tip.details}

${tip.potentialSavings > 0 ? '预计节省：${(tip.potentialSavings * 100).toStringAsFixed(0)}%' : ''}

来自空调管家APP
''';

    // 这里可以集成share_plus包来实现真正的分享功能
    // 目前先显示分享内容
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('分享内容'),
        content: SingleChildScrollView(child: Text(shareText)),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('关闭'),
          ),
          TextButton(
            onPressed: () {
              // TODO: 集成share_plus包实现真正的分享
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('分享功能需要集成share_plus包')),
              );
            },
            child: const Text('分享'),
          ),
        ],
      ),
    );
  }
}
