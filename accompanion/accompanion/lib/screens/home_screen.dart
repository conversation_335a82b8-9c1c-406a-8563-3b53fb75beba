import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:math';

/// 运行模式枚举
enum ACMode {
  eco, // 省电模式
  comfort, // 舒适模式
}

/// 主页状态数据模型
class HomeState {
  final ACMode currentMode;
  final double currentTemp;
  final double targetTemp;
  final double humidity;
  final String weatherCondition;
  final bool isACRunning;
  final double estimatedCost;

  const HomeState({
    this.currentMode = ACMode.comfort,
    this.currentTemp = 26.5,
    this.targetTemp = 24.0,
    this.humidity = 65.0,
    this.weatherCondition = '晴天',
    this.isACRunning = false,
    this.estimatedCost = 0.0,
  });

  HomeState copyWith({
    ACMode? currentMode,
    double? currentTemp,
    double? targetTemp,
    double? humidity,
    String? weatherCondition,
    bool? isACRunning,
    double? estimatedCost,
  }) {
    return HomeState(
      currentMode: currentMode ?? this.currentMode,
      currentTemp: currentTemp ?? this.currentTemp,
      targetTemp: targetTemp ?? this.targetTemp,
      humidity: humidity ?? this.humidity,
      weatherCondition: weatherCondition ?? this.weatherCondition,
      isACRunning: isACRunning ?? this.isACRunning,
      estimatedCost: estimatedCost ?? this.estimatedCost,
    );
  }
}

/// 主页状态管理
class HomeNotifier extends StateNotifier<HomeState> {
  HomeNotifier() : super(const HomeState());

  /// 切换运行模式
  void toggleMode(ACMode mode) {
    state = state.copyWith(currentMode: mode);
  }

  /// 更新目标温度
  void updateTargetTemp(double temp) {
    state = state.copyWith(targetTemp: temp);
  }

  /// 切换空调运行状态
  void toggleACRunning() {
    state = state.copyWith(isACRunning: !state.isACRunning);
  }

  /// 刷新数据（模拟获取最新的天气和环境数据）
  Future<void> refreshData() async {
    // 模拟网络请求延迟
    await Future.delayed(const Duration(milliseconds: 800));

    final random = Random();

    // 模拟天气条件更新
    final weatherConditions = ['晴天', '多云', '阴天', '小雨', '雷阵雨'];
    final newWeather =
        weatherConditions[random.nextInt(weatherConditions.length)];

    // 模拟温度和湿度数据更新（基于当前值进行小幅波动）
    final tempVariation = (random.nextDouble() - 0.5) * 2; // ±1度变化
    final humidityVariation = (random.nextDouble() - 0.5) * 10; // ±5%变化

    final newCurrentTemp = (state.currentTemp + tempVariation).clamp(
      20.0,
      35.0,
    );
    final newHumidity = (state.humidity + humidityVariation).clamp(40.0, 80.0);

    // 根据天气条件调整预估电费
    double costMultiplier = 1.0;
    switch (newWeather) {
      case '晴天':
        costMultiplier = 1.2; // 晴天制冷需求高
        break;
      case '多云':
        costMultiplier = 1.0;
        break;
      case '阴天':
        costMultiplier = 0.9;
        break;
      case '小雨':
      case '雷阵雨':
        costMultiplier = 0.8; // 雨天制冷需求低
        break;
    }

    final newEstimatedCost = state.isACRunning
        ? (2.5 + random.nextDouble() * 1.5) * costMultiplier
        : 0.0;

    // 更新状态
    state = state.copyWith(
      currentTemp: newCurrentTemp,
      humidity: newHumidity,
      weatherCondition: newWeather,
      estimatedCost: newEstimatedCost,
    );

    debugPrint(
      '🔄 数据已刷新: 温度=${newCurrentTemp.toStringAsFixed(1)}°C, '
      '湿度=${newHumidity.toStringAsFixed(0)}%, 天气=$newWeather',
    );
  }
}

/// 主页状态提供者
final homeProvider = StateNotifierProvider<HomeNotifier, HomeState>((ref) {
  return HomeNotifier();
});

/// 主页面 - 应用的核心功能入口
///
/// 功能特点：
/// - 当前状态展示（温度、湿度、天气）
/// - 模式选择（省电/舒适模式）
/// - 功能模块导航
/// - 快速操作和设置
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final homeState = ref.watch(homeProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: _buildAppBar(context),
      body: RefreshIndicator(
        onRefresh: () async {
          // 刷新数据（天气、状态等）
          await ref.read(homeProvider.notifier).refreshData();
        },
        child: SingleChildScrollView(
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 欢迎信息
              _buildWelcomeSection(),
              const SizedBox(height: 24),
              // 当前状态卡片
              _buildStatusCard(homeState),
              const SizedBox(height: 24),
              // 模式切换
              _buildModeToggle(context, ref, homeState),
              const SizedBox(height: 32),
              // 功能模块
              _buildFunctionGrid(context),
              const SizedBox(height: 24),
              // 快速操作
              _buildQuickActions(context, ref),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(context),
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: const Text(
        '空调管家',
        style: TextStyle(
          color: Color(0xFF37352F),
          fontSize: 24,
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.settings, color: Color(0xFF6E6E6E)),
          onPressed: () {
            Navigator.pushNamed(context, '/settings');
          },
        ),
      ],
    );
  }

  /// 构建欢迎区域
  Widget _buildWelcomeSection() {
    final hour = DateTime.now().hour;
    String greeting;
    if (hour < 12) {
      greeting = '早上好';
    } else if (hour < 18) {
      greeting = '下午好';
    } else {
      greeting = '晚上好';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          greeting,
          style: const TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
        ),
        const SizedBox(height: 4),
        const Text(
          '让我们为您优化空调使用',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard(HomeState state) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF2F76DA), Color(0xFF1E5BB8)],
          ),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前温度',
                      style: TextStyle(color: Colors.white70, fontSize: 14),
                    ),
                    Text(
                      '${state.currentTemp.toStringAsFixed(1)}°C',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Icon(
                      state.isACRunning ? Icons.ac_unit : Icons.power_off,
                      color: Colors.white,
                      size: 32,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      state.isACRunning ? '运行中' : '已关闭',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatusItem(
                  '目标温度',
                  '${state.targetTemp.toStringAsFixed(1)}°C',
                ),
                _buildStatusItem('湿度', '${state.humidity.toStringAsFixed(0)}%'),
                _buildStatusItem('天气', state.weatherCondition),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建状态项
  Widget _buildStatusItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 12),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  /// 构建模式切换
  Widget _buildModeToggle(
    BuildContext context,
    WidgetRef ref,
    HomeState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '运行模式',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildModeCard(
                context: context,
                title: '省电模式',
                description: '优先节能',
                icon: Icons.eco,
                isSelected: state.currentMode == ACMode.eco,
                onTap: () =>
                    ref.read(homeProvider.notifier).toggleMode(ACMode.eco),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildModeCard(
                context: context,
                title: '舒适模式',
                description: '优先舒适',
                icon: Icons.favorite,
                isSelected: state.currentMode == ACMode.comfort,
                onTap: () =>
                    ref.read(homeProvider.notifier).toggleMode(ACMode.comfort),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建模式卡片
  Widget _buildModeCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? const Color(0xFF2F76DA) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? const Color(0xFF2F76DA)
                : const Color(0xFFE3E2E0),
            width: 2,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? Colors.white : const Color(0xFF6E6E6E),
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                color: isSelected ? Colors.white : const Color(0xFF37352F),
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                color: isSelected ? Colors.white70 : const Color(0xFF6E6E6E),
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建功能网格
  Widget _buildFunctionGrid(BuildContext context) {
    final functions = [
      {'title': '房间设置', 'icon': Icons.home, 'route': '/room-setup'},
      {'title': '智能计算', 'icon': Icons.calculate, 'route': '/calculation'},
      {'title': '电费分析', 'icon': Icons.analytics, 'route': '/cost-analysis'},
      {'title': '优化建议', 'icon': Icons.lightbulb, 'route': '/suggestions'},
      {'title': '历史记录', 'icon': Icons.history, 'route': '/history'},
      {'title': '更多设置', 'icon': Icons.more_horiz, 'route': '/settings'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '功能模块',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1.0,
          ),
          itemCount: functions.length,
          itemBuilder: (context, index) {
            final function = functions[index];
            return _buildFunctionCard(
              context: context,
              title: function['title'] as String,
              icon: function['icon'] as IconData,
              route: function['route'] as String,
            );
          },
        ),
      ],
    );
  }

  /// 构建功能卡片
  Widget _buildFunctionCard({
    required BuildContext context,
    required String title,
    required IconData icon,
    required String route,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(context, route);
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, size: 32, color: const Color(0xFF2F76DA)),
              const SizedBox(height: 8),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建快速操作
  Widget _buildQuickActions(BuildContext context, WidgetRef ref) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          '快速操作',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  ref.read(homeProvider.notifier).toggleACRunning();
                },
                icon: const Icon(Icons.power_settings_new),
                label: const Text('开关空调'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF4CAF50),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: () {
                  Navigator.pushNamed(context, '/calculation');
                },
                icon: const Icon(Icons.auto_awesome),
                label: const Text('智能优化'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: const Color(0xFF2F76DA),
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// 构建浮动操作按钮
  Widget _buildFloatingActionButton(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        Navigator.pushNamed(context, '/calculation');
      },
      backgroundColor: const Color(0xFF2F76DA),
      child: const Icon(Icons.calculate, color: Colors.white),
    );
  }
}
