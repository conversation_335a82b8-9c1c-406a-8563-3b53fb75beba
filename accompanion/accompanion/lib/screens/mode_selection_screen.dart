import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';

/// 运行模式枚举
enum ACOperationMode {
  eco, // 省电模式
  comfort, // 舒适模式
  custom, // 自定义模式
}

/// 模式配置数据模型
class ModeConfig {
  final ACOperationMode mode;
  final double targetTemp; // 目标温度
  final double tempTolerance; // 温度容差
  final int timerHours; // 定时小时数
  final bool autoAdjust; // 自动调节
  final double energyPriority; // 节能优先级 (0-1)
  final double comfortPriority; // 舒适优先级 (0-1)

  const ModeConfig({
    this.mode = ACOperationMode.comfort,
    this.targetTemp = 24.0,
    this.tempTolerance = 1.0,
    this.timerHours = 8,
    this.autoAdjust = true,
    this.energyPriority = 0.5,
    this.comfortPriority = 0.5,
  });

  ModeConfig copyWith({
    ACOperationMode? mode,
    double? targetTemp,
    double? tempTolerance,
    int? timerHours,
    bool? autoAdjust,
    double? energyPriority,
    double? comfortPriority,
  }) {
    return ModeConfig(
      mode: mode ?? this.mode,
      targetTemp: targetTemp ?? this.targetTemp,
      tempTolerance: tempTolerance ?? this.tempTolerance,
      timerHours: timerHours ?? this.timerHours,
      autoAdjust: autoAdjust ?? this.autoAdjust,
      energyPriority: energyPriority ?? this.energyPriority,
      comfortPriority: comfortPriority ?? this.comfortPriority,
    );
  }

  /// 获取预设模式配置
  static ModeConfig getPresetConfig(ACOperationMode mode) {
    switch (mode) {
      case ACOperationMode.eco:
        return const ModeConfig(
          mode: ACOperationMode.eco,
          targetTemp: 26.0,
          tempTolerance: 2.0,
          timerHours: 6,
          autoAdjust: true,
          energyPriority: 0.8,
          comfortPriority: 0.2,
        );
      case ACOperationMode.comfort:
        return const ModeConfig(
          mode: ACOperationMode.comfort,
          targetTemp: 24.0,
          tempTolerance: 0.5,
          timerHours: 8,
          autoAdjust: true,
          energyPriority: 0.2,
          comfortPriority: 0.8,
        );
      case ACOperationMode.custom:
        return const ModeConfig(
          mode: ACOperationMode.custom,
          targetTemp: 25.0,
          tempTolerance: 1.0,
          timerHours: 8,
          autoAdjust: false,
          energyPriority: 0.5,
          comfortPriority: 0.5,
        );
    }
  }
}

/// 模式选择状态管理
class ModeSelectionNotifier extends StateNotifier<ModeConfig> {
  ModeSelectionNotifier() : super(const ModeConfig());

  /// 切换运行模式
  void selectMode(ACOperationMode mode) {
    state = ModeConfig.getPresetConfig(mode);
  }

  /// 更新目标温度
  void updateTargetTemp(double temp) {
    state = state.copyWith(targetTemp: temp);
  }

  /// 更新温度容差
  void updateTempTolerance(double tolerance) {
    state = state.copyWith(tempTolerance: tolerance);
  }

  /// 更新定时小时数
  void updateTimerHours(int hours) {
    state = state.copyWith(timerHours: hours);
  }

  /// 切换自动调节
  void toggleAutoAdjust() {
    state = state.copyWith(autoAdjust: !state.autoAdjust);
  }

  /// 更新节能优先级
  void updateEnergyPriority(double priority) {
    state = state.copyWith(
      energyPriority: priority,
      comfortPriority: 1.0 - priority,
    );
  }

  /// 重置为默认配置
  void reset() {
    state = const ModeConfig();
  }

  /// 保存当前配置到本地存储
  Future<void> saveConfiguration() async {
    try {
      final box = await Hive.openBox('mode_settings');

      // 保存模式配置
      await box.put('selected_mode', state.mode.name);
      await box.put('target_temp', state.targetTemp);
      await box.put('temp_tolerance', state.tempTolerance);
      await box.put('timer_hours', state.timerHours);
      await box.put('auto_adjust', state.autoAdjust);
      await box.put('energy_priority', state.energyPriority);
      await box.put('comfort_priority', state.comfortPriority);
      await box.put('last_saved', DateTime.now().toIso8601String());

      debugPrint('✅ 模式配置已保存: ${state.mode.name}, 温度=${state.targetTemp}°C');
    } catch (e) {
      debugPrint('❌ 保存模式配置失败: $e');
      rethrow;
    }
  }
}

/// 模式选择状态提供者
final modeSelectionProvider =
    StateNotifierProvider<ModeSelectionNotifier, ModeConfig>((ref) {
      return ModeSelectionNotifier();
    });

/// 模式选择页面 - 运行模式配置
///
/// 功能特点：
/// - 省电模式/舒适模式/自定义模式选择
/// - 温度、定时等参数调整
/// - 实时预览和效果展示
/// - 应用设置并进入计算流程
class ModeSelectionScreen extends ConsumerWidget {
  const ModeSelectionScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final modeConfig = ref.watch(modeSelectionProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '模式选择',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          TextButton(
            onPressed: () => ref.read(modeSelectionProvider.notifier).reset(),
            child: const Text('重置', style: TextStyle(color: Color(0xFF6E6E6E))),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 模式选择区域
            _buildModeSelection(context, ref, modeConfig),
            const SizedBox(height: 24),
            // 参数调整区域
            _buildParameterAdjustment(context, ref, modeConfig),
            const SizedBox(height: 24),
            // 预览区域
            _buildPreviewPanel(modeConfig),
            const SizedBox(height: 32),
            // 应用按钮
            _buildApplyButton(context, ref, modeConfig),
          ],
        ),
      ),
    );
  }

  /// 构建模式选择区域
  Widget _buildModeSelection(
    BuildContext context,
    WidgetRef ref,
    ModeConfig config,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '选择运行模式',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            _buildModeCard(
              context: context,
              ref: ref,
              mode: ACOperationMode.eco,
              title: '省电模式',
              description: '优先节能，适度降低舒适度以节省电费',
              icon: Icons.eco,
              color: const Color(0xFF4CAF50),
              isSelected: config.mode == ACOperationMode.eco,
            ),
            const SizedBox(height: 12),
            _buildModeCard(
              context: context,
              ref: ref,
              mode: ACOperationMode.comfort,
              title: '舒适模式',
              description: '优先舒适，保持最佳体感温度和湿度',
              icon: Icons.favorite,
              color: const Color(0xFF2F76DA),
              isSelected: config.mode == ACOperationMode.comfort,
            ),
            const SizedBox(height: 12),
            _buildModeCard(
              context: context,
              ref: ref,
              mode: ACOperationMode.custom,
              title: '自定义模式',
              description: '手动调节各项参数，完全个性化设置',
              icon: Icons.tune,
              color: const Color(0xFF9061F9),
              isSelected: config.mode == ACOperationMode.custom,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建模式卡片
  Widget _buildModeCard({
    required BuildContext context,
    required WidgetRef ref,
    required ACOperationMode mode,
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required bool isSelected,
  }) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        ref.read(modeSelectionProvider.notifier).selectMode(mode);
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? color.withValues(alpha: 0.1) : Colors.white,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? color : const Color(0xFFE3E2E0),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: isSelected ? color : const Color(0xFFF5F5F5),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                icon,
                color: isSelected ? Colors.white : const Color(0xFF6E6E6E),
                size: 24,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: isSelected ? color : const Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Color(0xFF6E6E6E),
                    ),
                  ),
                ],
              ),
            ),
            if (isSelected) Icon(Icons.check_circle, color: color, size: 24),
          ],
        ),
      ),
    );
  }

  /// 构建参数调整区域
  Widget _buildParameterAdjustment(
    BuildContext context,
    WidgetRef ref,
    ModeConfig config,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '参数调整',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 20),
            _buildSliderParameter(
              context: context,
              label: '目标温度',
              value: config.targetTemp,
              min: 16.0,
              max: 30.0,
              divisions: 28,
              unit: '°C',
              onChanged: (value) {
                ref
                    .read(modeSelectionProvider.notifier)
                    .updateTargetTemp(value);
              },
            ),
            const SizedBox(height: 20),
            _buildSliderParameter(
              context: context,
              label: '温度容差',
              value: config.tempTolerance,
              min: 0.5,
              max: 3.0,
              divisions: 5,
              unit: '°C',
              onChanged: (value) {
                ref
                    .read(modeSelectionProvider.notifier)
                    .updateTempTolerance(value);
              },
            ),
            const SizedBox(height: 20),
            _buildSliderParameter(
              context: context,
              label: '定时时长',
              value: config.timerHours.toDouble(),
              min: 1.0,
              max: 12.0,
              divisions: 11,
              unit: '小时',
              onChanged: (value) {
                ref
                    .read(modeSelectionProvider.notifier)
                    .updateTimerHours(value.round());
              },
            ),
            if (config.mode == ACOperationMode.custom) ...[
              const SizedBox(height: 20),
              _buildSliderParameter(
                context: context,
                label: '节能优先级',
                value: config.energyPriority,
                min: 0.0,
                max: 1.0,
                divisions: 10,
                unit: '',
                onChanged: (value) {
                  ref
                      .read(modeSelectionProvider.notifier)
                      .updateEnergyPriority(value);
                },
              ),
            ],
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text(
                '智能自动调节',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF37352F),
                ),
              ),
              subtitle: const Text(
                '根据环境变化自动优化参数',
                style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
              ),
              value: config.autoAdjust,
              activeColor: const Color(0xFF2F76DA),
              onChanged: (value) {
                ref.read(modeSelectionProvider.notifier).toggleAutoAdjust();
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建滑块参数
  Widget _buildSliderParameter({
    required BuildContext context,
    required String label,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required String unit,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF37352F),
              ),
            ),
            Text(
              '${value.toStringAsFixed(value % 1 == 0 ? 0 : 1)}$unit',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2F76DA),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: const Color(0xFF2F76DA),
            inactiveTrackColor: const Color(0xFFE3E2E0),
            thumbColor: const Color(0xFF2F76DA),
            overlayColor: const Color(0xFF2F76DA).withValues(alpha: 0.2),
            valueIndicatorColor: const Color(0xFF2F76DA),
          ),
          child: Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  /// 构建预览面板
  Widget _buildPreviewPanel(ModeConfig config) {
    // 简单的预估计算
    final estimatedCost = _calculateEstimatedCost(config);
    final comfortLevel = _calculateComfortLevel(config);
    final energySaving = _calculateEnergySaving(config);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF2F76DA), Color(0xFF1E5BB8)],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '预览效果',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildPreviewItem(
                  '预估电费',
                  '${estimatedCost.toStringAsFixed(2)}元',
                ),
                _buildPreviewItem(
                  '舒适度',
                  '${(comfortLevel * 100).toStringAsFixed(0)}%',
                ),
                _buildPreviewItem(
                  '节能率',
                  '${(energySaving * 100).toStringAsFixed(0)}%',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预览项
  Widget _buildPreviewItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建应用按钮
  Widget _buildApplyButton(
    BuildContext context,
    WidgetRef ref,
    ModeConfig config,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () async {
          // 应用设置并导航到计算页面
          try {
            // 保存当前配置
            await ref.read(modeSelectionProvider.notifier).saveConfiguration();

            if (context.mounted) {
              // 显示保存成功提示
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('${config.mode.name}模式配置已保存'),
                  backgroundColor: const Color(0xFF4CAF50),
                  duration: const Duration(seconds: 1),
                ),
              );

              // 短暂延迟后导航到计算页面
              await Future.delayed(const Duration(milliseconds: 500));

              if (context.mounted) {
                Navigator.pushNamed(context, '/calculation');
              }
            }
          } catch (e) {
            if (context.mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('保存配置失败，请重试'),
                  backgroundColor: Colors.red,
                ),
              );
            }
          }
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2F76DA),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: const Text(
          '应用设置并开始计算',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  /// 计算预估电费
  double _calculateEstimatedCost(ModeConfig config) {
    // 简化的电费计算逻辑
    final baseCost = 3.5; // 基础电费
    final tempFactor = (26 - config.targetTemp) * 0.2; // 温度因子
    final timeFactor = config.timerHours * 0.1; // 时间因子
    final modeFactor = config.mode == ACOperationMode.eco
        ? -0.5
        : config.mode == ACOperationMode.comfort
        ? 0.3
        : 0.0;

    return (baseCost + tempFactor + timeFactor + modeFactor).clamp(1.0, 8.0);
  }

  /// 计算舒适度
  double _calculateComfortLevel(ModeConfig config) {
    // 简化的舒适度计算逻辑
    final tempComfort = (26 - (config.targetTemp - 24).abs()) / 26;
    final toleranceComfort = (3 - config.tempTolerance) / 3;
    final modeComfort = config.mode == ACOperationMode.comfort
        ? 1.0
        : config.mode == ACOperationMode.eco
        ? 0.7
        : 0.85;

    return ((tempComfort + toleranceComfort + modeComfort) / 3).clamp(0.0, 1.0);
  }

  /// 计算节能率
  double _calculateEnergySaving(ModeConfig config) {
    // 简化的节能率计算逻辑
    final tempSaving = (config.targetTemp - 22) / 8;
    final toleranceSaving = config.tempTolerance / 3;
    final modeSaving = config.mode == ACOperationMode.eco
        ? 1.0
        : config.mode == ACOperationMode.comfort
        ? 0.3
        : 0.6;

    return ((tempSaving + toleranceSaving + modeSaving) / 3).clamp(0.0, 1.0);
  }
}
