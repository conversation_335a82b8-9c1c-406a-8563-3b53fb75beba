import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 计算步骤枚举
enum CalculationStep {
  fetchingWeather, // 获取天气数据
  analyzingRoom, // 分析房间环境
  calculating, // 进行热力学计算
  optimizing, // 优化参数
  completed, // 计算完成
}

/// 计算结果数据模型
class CalculationResult {
  final double optimalTemp; // 最佳温度
  final double estimatedCost; // 预估电费
  final double energySavingRate; // 节能率
  final double comfortIndex; // 舒适指数
  final List<double> temperatureCurve; // 温度曲线
  final List<double> powerConsumption; // 功耗曲线
  final String recommendation; // 优化建议
  final DateTime calculatedAt; // 计算时间

  const CalculationResult({
    required this.optimalTemp,
    required this.estimatedCost,
    required this.energySavingRate,
    required this.comfortIndex,
    required this.temperatureCurve,
    required this.powerConsumption,
    required this.recommendation,
    required this.calculatedAt,
  });
}

/// 计算状态数据模型
class CalculationState {
  final CalculationStep currentStep;
  final double progress;
  final CalculationResult? result;
  final String? error;
  final bool isCalculating;

  const CalculationState({
    this.currentStep = CalculationStep.fetchingWeather,
    this.progress = 0.0,
    this.result,
    this.error,
    this.isCalculating = false,
  });

  CalculationState copyWith({
    CalculationStep? currentStep,
    double? progress,
    CalculationResult? result,
    String? error,
    bool? isCalculating,
  }) {
    return CalculationState(
      currentStep: currentStep ?? this.currentStep,
      progress: progress ?? this.progress,
      result: result ?? this.result,
      error: error ?? this.error,
      isCalculating: isCalculating ?? this.isCalculating,
    );
  }
}

/// 计算状态管理
class CalculationNotifier extends StateNotifier<CalculationState> {
  CalculationNotifier() : super(const CalculationState());

  Timer? _calculationTimer;

  /// 开始计算
  Future<void> startCalculation() async {
    if (state.isCalculating) return;

    state = state.copyWith(
      isCalculating: true,
      currentStep: CalculationStep.fetchingWeather,
      progress: 0.0,
      error: null,
      result: null,
    );

    try {
      // 模拟计算过程
      await _simulateCalculationSteps();
    } catch (e) {
      state = state.copyWith(error: e.toString(), isCalculating: false);
    }
  }

  /// 取消计算
  void cancelCalculation() {
    _calculationTimer?.cancel();
    state = state.copyWith(
      isCalculating: false,
      currentStep: CalculationStep.fetchingWeather,
      progress: 0.0,
    );
  }

  /// 重置状态
  void reset() {
    _calculationTimer?.cancel();
    state = const CalculationState();
  }

  /// 模拟计算步骤
  Future<void> _simulateCalculationSteps() async {
    final steps = [
      (CalculationStep.fetchingWeather, '获取天气数据...', 0.2),
      (CalculationStep.analyzingRoom, '分析房间环境...', 0.4),
      (CalculationStep.calculating, '进行热力学计算...', 0.7),
      (CalculationStep.optimizing, '优化参数设置...', 0.9),
      (CalculationStep.completed, '计算完成', 1.0),
    ];

    for (final (step, _, progress) in steps) {
      if (!state.isCalculating) return; // 检查是否被取消

      state = state.copyWith(currentStep: step, progress: progress);

      // 模拟计算时间
      await Future.delayed(
        Duration(milliseconds: 800 + Random().nextInt(1200)),
      );
    }

    // 生成计算结果
    final result = _generateCalculationResult();
    state = state.copyWith(result: result, isCalculating: false);
  }

  /// 生成计算结果
  CalculationResult _generateCalculationResult() {
    final random = Random();

    // 生成温度曲线（24小时）
    final temperatureCurve = List.generate(24, (index) {
      final baseTemp = 24.0;
      final variation = sin(index * pi / 12) * 2; // 正弦波变化
      return baseTemp + variation + (random.nextDouble() - 0.5);
    });

    // 生成功耗曲线
    final powerConsumption = List.generate(24, (index) {
      final basePower = 1.2;
      final variation = cos(index * pi / 12) * 0.5;
      return basePower + variation + (random.nextDouble() - 0.5) * 0.2;
    });

    return CalculationResult(
      optimalTemp: 24.5 + (random.nextDouble() - 0.5) * 2,
      estimatedCost: 3.2 + random.nextDouble() * 2,
      energySavingRate: 0.15 + random.nextDouble() * 0.3,
      comfortIndex: 0.8 + random.nextDouble() * 0.2,
      temperatureCurve: temperatureCurve,
      powerConsumption: powerConsumption,
      recommendation: _generateRecommendation(),
      calculatedAt: DateTime.now(),
    );
  }

  /// 生成优化建议
  String _generateRecommendation() {
    final recommendations = [
      '建议在晚上22:00后将温度调高1°C，可节省约15%的电费',
      '当前设置已经很优化，建议保持现有参数',
      '建议开启自动调节功能，系统将根据环境变化智能优化',
      '检测到房间密封性较好，可适当降低风速以节能',
      '建议定期清洁空调滤网，可提高制冷效率约10%',
    ];
    return recommendations[Random().nextInt(recommendations.length)];
  }

  @override
  void dispose() {
    _calculationTimer?.cancel();
    super.dispose();
  }
}

/// 计算状态提供者
final calculationProvider =
    StateNotifierProvider<CalculationNotifier, CalculationState>((ref) {
      return CalculationNotifier();
    });

/// 智能计算页面 - 核心算法展示和计算流程
///
/// 功能特点：
/// - 多步骤计算流程展示
/// - 实时进度和状态更新
/// - 计算结果详细展示
/// - 温度曲线和功耗分析
class CalculationScreen extends ConsumerWidget {
  const CalculationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final calculationState = ref.watch(calculationProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '智能计算',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          if (calculationState.isCalculating)
            TextButton(
              onPressed: () =>
                  ref.read(calculationProvider.notifier).cancelCalculation(),
              child: const Text(
                '取消',
                style: TextStyle(color: Color(0xFFEB5757)),
              ),
            ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (!calculationState.isCalculating &&
                calculationState.result == null)
              _buildStartSection(context, ref),
            if (calculationState.isCalculating)
              _buildCalculationProgress(calculationState),
            if (calculationState.result != null)
              _buildResultSection(context, ref, calculationState.result!),
            if (calculationState.error != null)
              _buildErrorSection(context, ref, calculationState.error!),
          ],
        ),
      ),
    );
  }

  /// 构建开始计算区域
  Widget _buildStartSection(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(32),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [Color(0xFF2F76DA), Color(0xFF1E5BB8)],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Column(
            children: [
              const Icon(Icons.calculate, size: 64, color: Colors.white),
              const SizedBox(height: 16),
              const Text(
                '智能计算系统',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '基于热力学模型和实时数据\n为您计算最优空调设置',
                style: TextStyle(fontSize: 16, color: Colors.white70),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () =>
                    ref.read(calculationProvider.notifier).startCalculation(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: const Color(0xFF2F76DA),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24),
                  ),
                ),
                child: const Text(
                  '开始计算',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 24),
        _buildCalculationSteps(),
      ],
    );
  }

  /// 构建计算步骤说明
  Widget _buildCalculationSteps() {
    final steps = [
      ('获取天气数据', '实时获取温度、湿度等环境信息', Icons.cloud),
      ('分析房间环境', '根据房间参数计算热负荷', Icons.home),
      ('热力学计算', '应用专业算法优化温度设置', Icons.functions),
      ('参数优化', '平衡舒适度与节能效果', Icons.tune),
    ];

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '计算流程',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            ...steps.asMap().entries.map((entry) {
              final index = entry.key;
              final (title, description, icon) = entry.value;
              return Padding(
                padding: EdgeInsets.only(
                  bottom: index < steps.length - 1 ? 16 : 0,
                ),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Icon(
                        icon,
                        color: const Color(0xFF2F76DA),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF37352F),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            description,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF6E6E6E),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// 构建计算进度
  Widget _buildCalculationProgress(CalculationState state) {
    final stepNames = {
      CalculationStep.fetchingWeather: '获取天气数据',
      CalculationStep.analyzingRoom: '分析房间环境',
      CalculationStep.calculating: '进行热力学计算',
      CalculationStep.optimizing: '优化参数设置',
      CalculationStep.completed: '计算完成',
    };

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const Text(
              '正在计算中...',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: 80,
              height: 80,
              child: CircularProgressIndicator(
                value: state.progress,
                strokeWidth: 6,
                backgroundColor: const Color(0xFFE3E2E0),
                valueColor: const AlwaysStoppedAnimation<Color>(
                  Color(0xFF2F76DA),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Text(
              '${(state.progress * 100).toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Color(0xFF2F76DA),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              stepNames[state.currentStep] ?? '',
              style: const TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建结果区域
  Widget _buildResultSection(
    BuildContext context,
    WidgetRef ref,
    CalculationResult result,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 主要结果卡片
        _buildMainResultCard(result),
        const SizedBox(height: 16),
        // 详细数据
        _buildDetailedResults(result),
        const SizedBox(height: 16),
        // 优化建议
        _buildRecommendationCard(result),
        const SizedBox(height: 24),
        // 操作按钮
        _buildActionButtons(context, ref),
      ],
    );
  }

  /// 构建主要结果卡片
  Widget _buildMainResultCard(CalculationResult result) {
    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          gradient: const LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [Color(0xFF4CAF50), Color(0xFF2E7D32)],
          ),
        ),
        child: Column(
          children: [
            const Icon(Icons.check_circle, size: 48, color: Colors.white),
            const SizedBox(height: 16),
            const Text(
              '计算完成！',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '为您找到了最优的空调设置方案',
              style: TextStyle(fontSize: 16, color: Colors.white70),
            ),
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildResultItem(
                  '最佳温度',
                  '${result.optimalTemp.toStringAsFixed(1)}°C',
                ),
                _buildResultItem(
                  '预估电费',
                  '${result.estimatedCost.toStringAsFixed(2)}元',
                ),
                _buildResultItem(
                  '节能率',
                  '${(result.energySavingRate * 100).toStringAsFixed(0)}%',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建结果项
  Widget _buildResultItem(String label, String value) {
    return Column(
      children: [
        Text(
          label,
          style: const TextStyle(color: Colors.white70, fontSize: 14),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建详细结果
  Widget _buildDetailedResults(CalculationResult result) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '详细分析',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailItem(
              '舒适指数',
              '${(result.comfortIndex * 100).toStringAsFixed(0)}%',
              Icons.favorite,
            ),
            const SizedBox(height: 12),
            _buildDetailItem(
              '计算时间',
              _formatDateTime(result.calculatedAt),
              Icons.access_time,
            ),
            const SizedBox(height: 12),
            _buildDetailItem(
              '数据点数',
              '${result.temperatureCurve.length}个',
              Icons.analytics,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建详细项
  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, color: const Color(0xFF2F76DA), size: 20),
        const SizedBox(width: 12),
        Text(
          label,
          style: const TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
        ),
        const Spacer(),
        Text(
          value,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: Color(0xFF37352F),
          ),
        ),
      ],
    );
  }

  /// 构建建议卡片
  Widget _buildRecommendationCard(CalculationResult result) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.lightbulb, color: Color(0xFFFF9800), size: 24),
                const SizedBox(width: 8),
                const Text(
                  '优化建议',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              result.recommendation,
              style: const TextStyle(
                fontSize: 16,
                color: Color(0xFF6E6E6E),
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/cost-analysis');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              '查看详细分析',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton(
            onPressed: () {
              ref.read(calculationProvider.notifier).reset();
            },
            style: OutlinedButton.styleFrom(
              foregroundColor: const Color(0xFF2F76DA),
              side: const BorderSide(color: Color(0xFF2F76DA)),
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: const Text(
              '重新计算',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建错误区域
  Widget _buildErrorSection(BuildContext context, WidgetRef ref, String error) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            const Icon(Icons.error, size: 48, color: Color(0xFFEB5757)),
            const SizedBox(height: 16),
            const Text(
              '计算出错',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: const TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton(
              onPressed: () => ref.read(calculationProvider.notifier).reset(),
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF2F76DA),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('重试'),
            ),
          ],
        ),
      ),
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
