import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 引导页数据模型
class OnboardingPageData {
  final String title;
  final String description;
  final IconData icon;
  final Color backgroundColor;
  final Color iconColor;

  const OnboardingPageData({
    required this.title,
    required this.description,
    required this.icon,
    required this.backgroundColor,
    required this.iconColor,
  });
}

/// 当前页面索引状态管理
final currentPageProvider = StateProvider<int>((ref) => 0);

/// 用户引导页面 - 首次使用时的功能介绍和使用说明
///
/// 功能特点：
/// - 多页面滑动引导流程
/// - 功能介绍和使用说明
/// - 跳过和下一步操作
/// - 完成引导后进入主页
class OnboardingScreen extends ConsumerStatefulWidget {
  const OnboardingScreen({super.key});

  @override
  ConsumerState<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends ConsumerState<OnboardingScreen> {
  late PageController _pageController;

  // 引导页面数据
  static const List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: '智能温度计算',
      description: '基于房间环境和天气数据，智能计算最适合的空调温度设置，让您既舒适又省电。',
      icon: Icons.thermostat,
      backgroundColor: Color(0xFF2F76DA),
      iconColor: Colors.white,
    ),
    OnboardingPageData(
      title: '省电与舒适模式',
      description: '一键切换省电模式和舒适模式，根据您的需求自动优化空调运行参数。',
      icon: Icons.eco,
      backgroundColor: Color(0xFF4CAF50),
      iconColor: Colors.white,
    ),
    OnboardingPageData(
      title: '电费预估分析',
      description: '实时计算电费消耗，提供详细的能耗分析和节能建议，帮您控制用电成本。',
      icon: Icons.analytics,
      backgroundColor: Color(0xFF9061F9),
      iconColor: Colors.white,
    ),
    OnboardingPageData(
      title: '个性化建议',
      description: '根据您的使用习惯和环境变化，提供专业的空调使用建议和维护提醒。',
      icon: Icons.lightbulb,
      backgroundColor: Color(0xFFFF9800),
      iconColor: Colors.white,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  /// 跳转到下一页
  void _nextPage() {
    final currentPage = ref.read(currentPageProvider);
    if (currentPage < _pages.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  /// 跳过引导
  void _skipOnboarding() {
    _completeOnboarding();
  }

  /// 完成引导流程
  void _completeOnboarding() async {
    // 保存引导完成状态到本地存储
    try {
      final box = await Hive.openBox('app_settings');
      await box.put('completed_onboarding', true);

      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    } catch (e) {
      debugPrint('保存引导完成状态失败: $e');
      // 即使保存失败也继续导航
      if (mounted) {
        Navigator.of(context).pushReplacementNamed('/home');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final currentPage = ref.watch(currentPageProvider);

    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // 顶部跳过按钮
            _buildTopBar(),
            // 主要内容区域
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                onPageChanged: (index) {
                  ref.read(currentPageProvider.notifier).state = index;
                },
                itemCount: _pages.length,
                itemBuilder: (context, index) {
                  return _buildOnboardingPage(_pages[index]);
                },
              ),
            ),
            // 底部导航区域
            _buildBottomNavigation(currentPage),
          ],
        ),
      ),
    );
  }

  /// 构建顶部栏
  Widget _buildTopBar() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          TextButton(
            onPressed: _skipOnboarding,
            child: const Text(
              '跳过',
              style: TextStyle(color: Color(0xFF6E6E6E), fontSize: 16),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建单个引导页
  Widget _buildOnboardingPage(OnboardingPageData pageData) {
    return Semantics(
      label: '引导页面：${pageData.title}',
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // 图标区域
            _buildIconSection(pageData),
            const SizedBox(height: 48),
            // 标题
            _buildTitle(pageData.title),
            const SizedBox(height: 24),
            // 描述
            _buildDescription(pageData.description),
          ],
        ),
      ),
    );
  }

  /// 构建图标区域
  Widget _buildIconSection(OnboardingPageData pageData) {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            pageData.backgroundColor,
            pageData.backgroundColor.withValues(alpha: 0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(80),
        boxShadow: [
          BoxShadow(
            color: pageData.backgroundColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Icon(pageData.icon, size: 80, color: pageData.iconColor),
    );
  }

  /// 构建标题
  Widget _buildTitle(String title) {
    return Text(
      title,
      style: const TextStyle(
        fontSize: 28,
        fontWeight: FontWeight.bold,
        color: Color(0xFF37352F),
      ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建描述
  Widget _buildDescription(String description) {
    return Text(
      description,
      style: const TextStyle(
        fontSize: 16,
        color: Color(0xFF6E6E6E),
        height: 1.5,
      ),
      textAlign: TextAlign.center,
    );
  }

  /// 构建底部导航
  Widget _buildBottomNavigation(int currentPage) {
    return Padding(
      padding: const EdgeInsets.all(32.0),
      child: Column(
        children: [
          // 页面指示器
          _buildPageIndicator(currentPage),
          const SizedBox(height: 32),
          // 导航按钮
          _buildNavigationButtons(currentPage),
        ],
      ),
    );
  }

  /// 构建页面指示器
  Widget _buildPageIndicator(int currentPage) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(
        _pages.length,
        (index) => Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: currentPage == index ? 24 : 8,
          height: 8,
          decoration: BoxDecoration(
            color: currentPage == index
                ? const Color(0xFF2F76DA)
                : const Color(0xFFE3E2E0),
            borderRadius: BorderRadius.circular(4),
          ),
        ),
      ),
    );
  }

  /// 构建导航按钮
  Widget _buildNavigationButtons(int currentPage) {
    final isLastPage = currentPage == _pages.length - 1;

    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: _nextPage,
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF2F76DA),
          foregroundColor: Colors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          elevation: 2,
        ),
        child: Text(
          isLastPage ? '开始使用' : '下一步',
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}
