import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 房间信息数据模型
class RoomInfo {
  final double area; // 房间面积（平方米）
  final String orientation; // 朝向
  final int floor; // 楼层
  final String windowType; // 窗户类型
  final String acBrand; // 空调品牌
  final String acModel; // 空调型号
  final double acPower; // 空调功率（匹）
  final double electricityRate; // 电费单价（元/度）
  final int occupants; // 居住人数

  const RoomInfo({
    this.area = 20.0,
    this.orientation = '南向',
    this.floor = 5,
    this.windowType = '普通玻璃',
    this.acBrand = '格力',
    this.acModel = '1.5匹变频',
    this.acPower = 1.5,
    this.electricityRate = 0.6,
    this.occupants = 2,
  });

  RoomInfo copyWith({
    double? area,
    String? orientation,
    int? floor,
    String? windowType,
    String? acBrand,
    String? acModel,
    double? acPower,
    double? electricityRate,
    int? occupants,
  }) {
    return RoomInfo(
      area: area ?? this.area,
      orientation: orientation ?? this.orientation,
      floor: floor ?? this.floor,
      windowType: windowType ?? this.windowType,
      acBrand: acBrand ?? this.acBrand,
      acModel: acModel ?? this.acModel,
      acPower: acPower ?? this.acPower,
      electricityRate: electricityRate ?? this.electricityRate,
      occupants: occupants ?? this.occupants,
    );
  }
}

/// 房间设置状态管理
class RoomSetupNotifier extends StateNotifier<RoomInfo> {
  RoomSetupNotifier() : super(const RoomInfo());

  /// 更新房间面积
  void updateArea(double area) {
    state = state.copyWith(area: area);
  }

  /// 更新朝向
  void updateOrientation(String orientation) {
    state = state.copyWith(orientation: orientation);
  }

  /// 更新楼层
  void updateFloor(int floor) {
    state = state.copyWith(floor: floor);
  }

  /// 更新窗户类型
  void updateWindowType(String windowType) {
    state = state.copyWith(windowType: windowType);
  }

  /// 更新空调品牌
  void updateACBrand(String brand) {
    state = state.copyWith(acBrand: brand);
  }

  /// 更新空调型号
  void updateACModel(String model) {
    state = state.copyWith(acModel: model);
  }

  /// 更新空调功率
  void updateACPower(double power) {
    state = state.copyWith(acPower: power);
  }

  /// 更新电费单价
  void updateElectricityRate(double rate) {
    state = state.copyWith(electricityRate: rate);
  }

  /// 更新居住人数
  void updateOccupants(int occupants) {
    state = state.copyWith(occupants: occupants);
  }

  /// 重置为默认值
  void reset() {
    state = const RoomInfo();
  }
}

/// 房间设置状态提供者
final roomSetupProvider = StateNotifierProvider<RoomSetupNotifier, RoomInfo>((
  ref,
) {
  return RoomSetupNotifier();
});

/// 房间信息设置页面 - 个性化参数配置
///
/// 功能特点：
/// - 房间基本信息输入（面积、朝向、楼层）
/// - 空调设备信息配置
/// - 电费和居住信息设置
/// - 数据验证和保存功能
class RoomSetupScreen extends ConsumerStatefulWidget {
  const RoomSetupScreen({super.key});

  @override
  ConsumerState<RoomSetupScreen> createState() => _RoomSetupScreenState();
}

class _RoomSetupScreenState extends ConsumerState<RoomSetupScreen> {
  final _formKey = GlobalKey<FormState>();
  final _areaController = TextEditingController();
  final _floorController = TextEditingController();
  final _powerController = TextEditingController();
  final _rateController = TextEditingController();
  final _occupantsController = TextEditingController();

  // 选项数据
  static const List<String> _orientations = [
    '南向',
    '北向',
    '东向',
    '西向',
    '东南',
    '西南',
    '东北',
    '西北',
  ];
  static const List<String> _windowTypes = ['普通玻璃', '双层玻璃', '中空玻璃', '低辐射玻璃'];
  static const List<String> _acBrands = [
    '格力',
    '美的',
    '海尔',
    '奥克斯',
    '志高',
    '海信',
    '其他',
  ];
  static const List<String> _acModels = [
    '1匹定频',
    '1匹变频',
    '1.5匹定频',
    '1.5匹变频',
    '2匹定频',
    '2匹变频',
    '3匹变频',
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
  }

  /// 初始化控制器
  void _initializeControllers() {
    final roomInfo = ref.read(roomSetupProvider);
    _areaController.text = roomInfo.area.toString();
    _floorController.text = roomInfo.floor.toString();
    _powerController.text = roomInfo.acPower.toString();
    _rateController.text = roomInfo.electricityRate.toString();
    _occupantsController.text = roomInfo.occupants.toString();
  }

  @override
  void dispose() {
    _areaController.dispose();
    _floorController.dispose();
    _powerController.dispose();
    _rateController.dispose();
    _occupantsController.dispose();
    super.dispose();
  }

  /// 保存设置
  void _saveSettings() async {
    if (_formKey.currentState!.validate()) {
      try {
        final box = await Hive.openBox('room_settings');
        final roomInfo = ref.read(roomSetupProvider);

        // 保存房间信息
        await box.put(
          'room_area',
          double.tryParse(_areaController.text) ?? 0.0,
        );
        await box.put('room_floor', int.tryParse(_floorController.text) ?? 1);
        await box.put('room_orientation', roomInfo.orientation);
        await box.put('window_type', roomInfo.windowType);
        await box.put('ac_brand', roomInfo.acBrand);
        await box.put('ac_model', roomInfo.acModel);
        await box.put(
          'ac_power',
          double.tryParse(_powerController.text) ?? 0.0,
        );
        await box.put(
          'electricity_rate',
          double.tryParse(_rateController.text) ?? 0.0,
        );
        await box.put(
          'occupants_count',
          int.tryParse(_occupantsController.text) ?? 1,
        );

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('房间信息已保存'),
              backgroundColor: Color(0xFF4CAF50),
            ),
          );
          Navigator.of(context).pop();
        }
      } catch (e) {
        debugPrint('❌ 保存房间设置失败: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('保存失败，请重试'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  /// 重置设置
  void _resetSettings() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('重置确认'),
        content: const Text('确定要重置所有设置为默认值吗？'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              ref.read(roomSetupProvider.notifier).reset();
              _initializeControllers();
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final roomInfo = ref.watch(roomSetupProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '房间设置',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          TextButton(
            onPressed: _resetSettings,
            child: const Text('重置', style: TextStyle(color: Color(0xFF6E6E6E))),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // 房间基本信息
              _buildSection(
                title: '房间基本信息',
                children: [
                  _buildNumberField(
                    context: context,
                    controller: _areaController,
                    label: '房间面积',
                    suffix: '平方米',
                    onChanged: (value) {
                      final area = double.tryParse(value) ?? 0;
                      ref.read(roomSetupProvider.notifier).updateArea(area);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入房间面积';
                      final area = double.tryParse(value);
                      if (area == null || area <= 0) return '请输入有效的面积';
                      if (area > 200) return '面积不能超过200平方米';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildDropdownField(
                    label: '房间朝向',
                    value: roomInfo.orientation,
                    items: _orientations,
                    onChanged: (value) {
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateOrientation(value!);
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildNumberField(
                    context: context,
                    controller: _floorController,
                    label: '楼层',
                    suffix: '层',
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final floor = int.tryParse(value) ?? 1;
                      ref.read(roomSetupProvider.notifier).updateFloor(floor);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入楼层';
                      final floor = int.tryParse(value);
                      if (floor == null || floor <= 0) return '请输入有效的楼层';
                      if (floor > 100) return '楼层不能超过100层';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildDropdownField(
                    label: '窗户类型',
                    value: roomInfo.windowType,
                    items: _windowTypes,
                    onChanged: (value) {
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateWindowType(value!);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // 空调设备信息
              _buildSection(
                title: '空调设备信息',
                children: [
                  _buildDropdownField(
                    label: '空调品牌',
                    value: roomInfo.acBrand,
                    items: _acBrands,
                    onChanged: (value) {
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateACBrand(value!);
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildDropdownField(
                    label: '空调型号',
                    value: roomInfo.acModel,
                    items: _acModels,
                    onChanged: (value) {
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateACModel(value!);
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildNumberField(
                    context: context,
                    controller: _powerController,
                    label: '空调功率',
                    suffix: '匹',
                    onChanged: (value) {
                      final power = double.tryParse(value) ?? 1.0;
                      ref.read(roomSetupProvider.notifier).updateACPower(power);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入空调功率';
                      final power = double.tryParse(value);
                      if (power == null || power <= 0) return '请输入有效的功率';
                      if (power > 10) return '功率不能超过10匹';
                      return null;
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),
              // 其他信息
              _buildSection(
                title: '其他信息',
                children: [
                  _buildNumberField(
                    context: context,
                    controller: _rateController,
                    label: '电费单价',
                    suffix: '元/度',
                    onChanged: (value) {
                      final rate = double.tryParse(value) ?? 0.6;
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateElectricityRate(rate);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入电费单价';
                      final rate = double.tryParse(value);
                      if (rate == null || rate <= 0) return '请输入有效的电费单价';
                      if (rate > 5) return '电费单价不能超过5元/度';
                      return null;
                    },
                  ),
                  const SizedBox(height: 16),
                  _buildNumberField(
                    context: context,
                    controller: _occupantsController,
                    label: '居住人数',
                    suffix: '人',
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final occupants = int.tryParse(value) ?? 1;
                      ref
                          .read(roomSetupProvider.notifier)
                          .updateOccupants(occupants);
                    },
                    validator: (value) {
                      if (value == null || value.isEmpty) return '请输入居住人数';
                      final occupants = int.tryParse(value);
                      if (occupants == null || occupants <= 0) {
                        return '请输入有效的人数';
                      }
                      if (occupants > 20) return '人数不能超过20人';
                      return null;
                    },
                  ),
                ],
              ),
              const SizedBox(height: 32),
              // 保存按钮
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _saveSettings,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF2F76DA),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    '保存设置',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建表单分组
  Widget _buildSection({
    required String title,
    required List<Widget> children,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Color(0xFF37352F),
              ),
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// 构建数字输入字段
  Widget _buildNumberField({
    required BuildContext context,
    required TextEditingController controller,
    required String label,
    required String suffix,
    TextInputType keyboardType = const TextInputType.numberWithOptions(
      decimal: true,
    ),
    required Function(String) onChanged,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      inputFormatters: keyboardType == TextInputType.number
          ? [FilteringTextInputFormatter.digitsOnly]
          : [FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*'))],
      decoration: InputDecoration(
        labelText: label,
        suffixText: suffix,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF2F76DA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      onChanged: onChanged,
      validator: validator,
    );
  }

  /// 构建下拉选择字段
  Widget _buildDropdownField({
    required String label,
    required String value,
    required List<String> items,
    required Function(String?) onChanged,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFFE3E2E0)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: Color(0xFF2F76DA), width: 2),
        ),
        filled: true,
        fillColor: Colors.white,
      ),
      items: items.map((String item) {
        return DropdownMenuItem<String>(value: item, child: Text(item));
      }).toList(),
      onChanged: onChanged,
    );
  }
}
