import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 历史记录数据模型
class HistoryRecord {
  final String id;
  final DateTime timestamp;
  final double targetTemperature;
  final double actualTemperature;
  final String mode;
  final double duration; // 运行时长（小时）
  final double energyConsumption; // 能耗（度）
  final double cost; // 电费（元）
  final double comfortScore; // 舒适度评分
  final Map<String, dynamic> settings; // 详细设置参数

  const HistoryRecord({
    required this.id,
    required this.timestamp,
    required this.targetTemperature,
    required this.actualTemperature,
    required this.mode,
    required this.duration,
    required this.energyConsumption,
    required this.cost,
    required this.comfortScore,
    required this.settings,
  });
}

/// 历史记录筛选条件
class HistoryFilter {
  final DateTime? startDate;
  final DateTime? endDate;
  final String? mode;
  final bool sortByNewest;

  const HistoryFilter({
    this.startDate,
    this.endDate,
    this.mode,
    this.sortByNewest = true,
  });

  HistoryFilter copyWith({
    DateTime? startDate,
    DateTime? endDate,
    String? mode,
    bool? sortByNewest,
  }) {
    return HistoryFilter(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      mode: mode ?? this.mode,
      sortByNewest: sortByNewest ?? this.sortByNewest,
    );
  }
}

/// 历史记录状态管理
class HistoryNotifier extends StateNotifier<AsyncValue<List<HistoryRecord>>> {
  HistoryNotifier() : super(const AsyncValue.loading()) {
    loadHistory();
  }

  /// 加载历史记录
  Future<void> loadHistory([HistoryFilter? filter]) async {
    state = const AsyncValue.loading();

    try {
      await Future.delayed(const Duration(milliseconds: 600)); // 模拟数据加载
      final records = _generateMockHistory();

      // 应用筛选条件
      var filteredRecords = records;
      if (filter != null) {
        filteredRecords = _applyFilter(records, filter);
      }

      state = AsyncValue.data(filteredRecords);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }

  /// 删除历史记录
  Future<void> deleteRecord(String recordId) async {
    state.whenData((records) {
      final updatedRecords = records
          .where((record) => record.id != recordId)
          .toList();
      state = AsyncValue.data(updatedRecords);
    });
  }

  /// 清空所有历史记录
  Future<void> clearAllHistory() async {
    state = const AsyncValue.data([]);
  }

  /// 应用筛选条件
  List<HistoryRecord> _applyFilter(
    List<HistoryRecord> records,
    HistoryFilter filter,
  ) {
    var filtered = records;

    // 按日期筛选
    if (filter.startDate != null) {
      filtered = filtered
          .where(
            (record) =>
                record.timestamp.isAfter(filter.startDate!) ||
                record.timestamp.isAtSameMomentAs(filter.startDate!),
          )
          .toList();
    }
    if (filter.endDate != null) {
      filtered = filtered
          .where(
            (record) => record.timestamp.isBefore(
              filter.endDate!.add(const Duration(days: 1)),
            ),
          )
          .toList();
    }

    // 按模式筛选
    if (filter.mode != null && filter.mode!.isNotEmpty) {
      filtered = filtered
          .where((record) => record.mode == filter.mode)
          .toList();
    }

    // 排序
    filtered.sort(
      (a, b) => filter.sortByNewest
          ? b.timestamp.compareTo(a.timestamp)
          : a.timestamp.compareTo(b.timestamp),
    );

    return filtered;
  }

  /// 生成模拟历史数据
  List<HistoryRecord> _generateMockHistory() {
    final random = Random();
    final now = DateTime.now();
    final records = <HistoryRecord>[];

    // 生成过去30天的记录
    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      final recordCount = random.nextInt(3) + 1; // 每天1-3条记录

      for (int j = 0; j < recordCount; j++) {
        final timestamp = date.subtract(Duration(hours: random.nextInt(24)));
        final mode = ['省电模式', '舒适模式', '自定义模式'][random.nextInt(3)];
        final targetTemp = 22.0 + random.nextDouble() * 6; // 22-28度
        final actualTemp =
            targetTemp + (random.nextDouble() - 0.5) * 2; // ±1度误差
        final duration = 2.0 + random.nextDouble() * 6; // 2-8小时
        final energyConsumption =
            duration * (0.8 + random.nextDouble() * 0.4); // 0.8-1.2度/小时
        final cost =
            energyConsumption * (0.5 + random.nextDouble() * 0.3); // 0.5-0.8元/度
        final comfortScore = 0.7 + random.nextDouble() * 0.3; // 70-100分

        records.add(
          HistoryRecord(
            id: 'record_${i}_$j',
            timestamp: timestamp,
            targetTemperature: targetTemp,
            actualTemperature: actualTemp,
            mode: mode,
            duration: duration,
            energyConsumption: energyConsumption,
            cost: cost,
            comfortScore: comfortScore,
            settings: {
              'humidity': 50 + random.nextInt(20),
              'windSpeed': random.nextInt(3) + 1,
              'timer': random.nextBool(),
              'autoMode': random.nextBool(),
            },
          ),
        );
      }
    }

    return records;
  }
}

/// 历史记录筛选状态
final historyFilterProvider = StateProvider<HistoryFilter>((ref) {
  return const HistoryFilter();
});

/// 历史记录状态提供者
final historyProvider =
    StateNotifierProvider<HistoryNotifier, AsyncValue<List<HistoryRecord>>>((
      ref,
    ) {
      return HistoryNotifier();
    });

/// 筛选后的历史记录
final filteredHistoryProvider = Provider<AsyncValue<List<HistoryRecord>>>((
  ref,
) {
  final historyAsync = ref.watch(historyProvider);
  final filter = ref.watch(historyFilterProvider);

  return historyAsync.when(
    loading: () => const AsyncValue.loading(),
    error: (error, stack) => AsyncValue.error(error, stack),
    data: (records) {
      final filteredRecords = ref
          .read(historyProvider.notifier)
          ._applyFilter(records, filter);
      return AsyncValue.data(filteredRecords);
    },
  );
});

/// 历史记录页面 - 使用历史查看
///
/// 功能特点：
/// - 历史使用记录展示
/// - 按日期和模式筛选
/// - 详细参数和统计信息
/// - 趋势分析和对比
class HistoryScreen extends ConsumerStatefulWidget {
  const HistoryScreen({super.key});

  @override
  ConsumerState<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends ConsumerState<HistoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final historyAsync = ref.watch(filteredHistoryProvider);
    final filter = ref.watch(historyFilterProvider);

    return Scaffold(
      backgroundColor: const Color(0xFFFAFAFA),
      appBar: AppBar(
        title: const Text(
          '历史记录',
          style: TextStyle(
            color: Color(0xFF37352F),
            fontSize: 20,
            fontWeight: FontWeight.w600,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        iconTheme: const IconThemeData(color: Color(0xFF37352F)),
        actions: [
          IconButton(
            icon: const Icon(Icons.date_range, color: Color(0xFF6E6E6E)),
            onPressed: () => _showDatePicker(context),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert, color: Color(0xFF6E6E6E)),
            onSelected: (value) {
              switch (value) {
                case 'clear_all':
                  _showClearAllDialog(context);
                  break;
                case 'export':
                  _exportHistory(context);
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(value: 'clear_all', child: Text('清空历史')),
              const PopupMenuItem(value: 'export', child: Text('导出数据')),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: const Color(0xFF2F76DA),
          unselectedLabelColor: const Color(0xFF6E6E6E),
          indicatorColor: const Color(0xFF2F76DA),
          isScrollable: true,
          tabs: const [
            Tab(text: '全部'),
            Tab(text: '省电模式'),
            Tab(text: '舒适模式'),
            Tab(text: '自定义模式'),
          ],
          onTap: (index) {
            final modes = [null, '省电模式', '舒适模式', '自定义模式'];
            ref.read(historyFilterProvider.notifier).state = filter.copyWith(
              mode: modes[index],
            );
          },
        ),
      ),
      body: RefreshIndicator(
        onRefresh: () => ref.read(historyProvider.notifier).loadHistory(filter),
        child: historyAsync.when(
          loading: () => _buildLoadingState(),
          error: (error, stack) => _buildErrorState(error),
          data: (records) => _buildHistoryList(context, records),
        ),
      ),
    );
  }

  /// 构建加载状态
  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF2F76DA)),
          ),
          SizedBox(height: 16),
          Text(
            '正在加载历史记录...',
            style: TextStyle(fontSize: 16, color: Color(0xFF6E6E6E)),
          ),
        ],
      ),
    );
  }

  /// 构建错误状态
  Widget _buildErrorState(Object error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Color(0xFFEB5757)),
          const SizedBox(height: 16),
          const Text(
            '加载失败',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error.toString(),
            style: const TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: () => ref.read(historyProvider.notifier).loadHistory(),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF2F76DA),
              foregroundColor: Colors.white,
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  /// 构建历史记录列表
  Widget _buildHistoryList(BuildContext context, List<HistoryRecord> records) {
    if (records.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        // 统计概览
        _buildStatsOverview(records),
        // 记录列表
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: records.length,
            itemBuilder: (context, index) {
              final record = records[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: _buildHistoryCard(context, record),
              );
            },
          ),
        ),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Color(0xFF9B9A97)),
          SizedBox(height: 16),
          Text(
            '暂无历史记录',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Color(0xFF37352F),
            ),
          ),
          SizedBox(height: 8),
          Text(
            '开始使用空调伴侣后，这里会显示您的使用历史',
            style: TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建统计概览
  Widget _buildStatsOverview(List<HistoryRecord> records) {
    final totalCost = records.fold(0.0, (sum, record) => sum + record.cost);
    final totalEnergy = records.fold(
      0.0,
      (sum, record) => sum + record.energyConsumption,
    );
    final avgComfort =
        records.fold(0.0, (sum, record) => sum + record.comfortScore) /
        records.length;

    return Container(
      margin: const EdgeInsets.all(16),
      child: Card(
        elevation: 2,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem(
                '总电费',
                '${totalCost.toStringAsFixed(2)}元',
                Icons.attach_money,
                const Color(0xFF2F76DA),
              ),
              _buildStatItem(
                '总用电',
                '${totalEnergy.toStringAsFixed(1)}度',
                Icons.flash_on,
                const Color(0xFF4CAF50),
              ),
              _buildStatItem(
                '平均舒适度',
                '${(avgComfort * 100).toStringAsFixed(0)}%',
                Icons.favorite,
                const Color(0xFFEB5757),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建统计项
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Color(0xFF6E6E6E)),
        ),
      ],
    );
  }

  /// 构建历史记录卡片
  Widget _buildHistoryCard(BuildContext context, HistoryRecord record) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: _buildModeIcon(record.mode),
        title: Row(
          children: [
            Expanded(
              child: Text(
                _formatDateTime(record.timestamp),
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF37352F),
                ),
              ),
            ),
            Text(
              '${record.cost.toStringAsFixed(2)}元',
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: Color(0xFF2F76DA),
              ),
            ),
          ],
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${record.mode} • ${record.targetTemperature.toStringAsFixed(1)}°C • ${record.duration.toStringAsFixed(1)}小时',
              style: const TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                _buildInfoChip(
                  '${record.energyConsumption.toStringAsFixed(1)}度',
                  Icons.flash_on,
                ),
                const SizedBox(width: 8),
                _buildInfoChip(
                  '${(record.comfortScore * 100).toStringAsFixed(0)}%',
                  Icons.favorite,
                ),
              ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  '详细信息',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF37352F),
                  ),
                ),
                const SizedBox(height: 12),
                _buildDetailRow(
                  '目标温度',
                  '${record.targetTemperature.toStringAsFixed(1)}°C',
                ),
                _buildDetailRow(
                  '实际温度',
                  '${record.actualTemperature.toStringAsFixed(1)}°C',
                ),
                _buildDetailRow(
                  '运行时长',
                  '${record.duration.toStringAsFixed(1)}小时',
                ),
                _buildDetailRow(
                  '能耗',
                  '${record.energyConsumption.toStringAsFixed(2)}度',
                ),
                _buildDetailRow(
                  '舒适度',
                  '${(record.comfortScore * 100).toStringAsFixed(0)}%',
                ),
                if (record.settings.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  const Text(
                    '设置参数',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFF37352F),
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...record.settings.entries.map((entry) {
                    return _buildDetailRow(
                      _formatSettingKey(entry.key),
                      entry.value.toString(),
                    );
                  }),
                ],
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      onPressed: () {
                        ref
                            .read(historyProvider.notifier)
                            .deleteRecord(record.id);
                        ScaffoldMessenger.of(
                          context,
                        ).showSnackBar(const SnackBar(content: Text('记录已删除')));
                      },
                      child: const Text(
                        '删除',
                        style: TextStyle(color: Color(0xFFEB5757)),
                      ),
                    ),
                    const SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        // TODO: 实现分享功能
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('分享功能开发中')),
                        );
                      },
                      child: const Text('分享'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模式图标
  Widget _buildModeIcon(String mode) {
    final config = {
      '省电模式': (Icons.eco, Color(0xFF4CAF50)),
      '舒适模式': (Icons.favorite, Color(0xFF2F76DA)),
      '自定义模式': (Icons.tune, Color(0xFF9061F9)),
    };

    final (icon, color) =
        config[mode] ?? (Icons.ac_unit, const Color(0xFF6E6E6E));

    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Icon(icon, color: color, size: 20),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: const Color(0xFF2F76DA).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: const Color(0xFF2F76DA)),
          const SizedBox(width: 4),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Color(0xFF2F76DA)),
          ),
        ],
      ),
    );
  }

  /// 构建详细信息行
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: const TextStyle(fontSize: 14, color: Color(0xFF6E6E6E)),
          ),
          Text(
            value,
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Color(0xFF37352F),
            ),
          ),
        ],
      ),
    );
  }

  /// 显示日期选择器
  void _showDatePicker(BuildContext context) async {
    final filter = ref.read(historyFilterProvider);
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: filter.startDate != null && filter.endDate != null
          ? DateTimeRange(start: filter.startDate!, end: filter.endDate!)
          : null,
    );

    if (dateRange != null) {
      ref.read(historyFilterProvider.notifier).state = filter.copyWith(
        startDate: dateRange.start,
        endDate: dateRange.end,
      );
    }
  }

  /// 显示清空所有记录对话框
  void _showClearAllDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('清空历史记录'),
        content: const Text('确定要清空所有历史记录吗？此操作无法撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () {
              ref.read(historyProvider.notifier).clearAllHistory();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(
                context,
              ).showSnackBar(const SnackBar(content: Text('历史记录已清空')));
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  /// 导出历史记录
  void _exportHistory(BuildContext context) {
    // TODO: 实现导出功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('导出功能开发中')));
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.month}月${dateTime.day}日 ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  /// 格式化设置键名
  String _formatSettingKey(String key) {
    final keyMap = {
      'humidity': '湿度',
      'windSpeed': '风速',
      'timer': '定时',
      'autoMode': '自动模式',
    };
    return keyMap[key] ?? key;
  }
}
