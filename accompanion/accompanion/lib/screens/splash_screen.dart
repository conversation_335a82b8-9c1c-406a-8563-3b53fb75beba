import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

/// 启动页面 - 应用启动时的品牌展示和初始化页面
///
/// 功能特点：
/// - 品牌Logo展示和动画效果
/// - 应用初始化和权限检查
/// - 自动跳转到主页或引导页
/// - 跨平台适配和可访问性支持
class SplashScreen extends ConsumerStatefulWidget {
  const SplashScreen({super.key});

  @override
  ConsumerState<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends ConsumerState<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _fadeController;
  late Animation<double> _logoScale;
  late Animation<double> _fadeAnimation;

  Timer? _navigationTimer;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startInitialization();
  }

  /// 初始化动画控制器
  void _initializeAnimations() {
    // Logo缩放动画控制器
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // 淡入淡出动画控制器
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Logo缩放动画
    _logoScale = Tween<double>(begin: 0.5, end: 1.0).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.elasticOut),
    );

    // 淡入动画
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    // 启动动画
    _fadeController.forward();
    _logoController.forward();
  }

  /// 开始应用初始化流程
  void _startInitialization() {
    // 设置状态栏样式
    _setSystemUIOverlay();

    // 3秒后自动跳转
    _navigationTimer = Timer(const Duration(seconds: 3), () {
      _navigateToNextScreen();
    });
  }

  /// 设置系统UI样式
  void _setSystemUIOverlay() {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.transparent,
      ),
    );
  }

  /// 导航到下一个页面
  void _navigateToNextScreen() async {
    if (!mounted) return;

    // 检查是否首次启动
    final isFirstTime = await _checkFirstTime();

    if (!mounted) return;

    if (isFirstTime) {
      // 首次启动，跳转到引导页
      Navigator.of(context).pushReplacementNamed('/onboarding');
    } else {
      // 非首次启动，跳转到主页
      Navigator.of(context).pushReplacementNamed('/home');
    }
  }

  /// 检查是否首次启动
  Future<bool> _checkFirstTime() async {
    try {
      final box = await Hive.openBox('app_settings');
      final hasCompletedOnboarding = box.get(
        'completed_onboarding',
        defaultValue: false,
      );
      return !hasCompletedOnboarding;
    } catch (e) {
      // 如果出错，默认为首次启动
      debugPrint('检查首次启动状态失败: $e');
      return true;
    }
  }

  @override
  void dispose() {
    _logoController.dispose();
    _fadeController.dispose();
    _navigationTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: _buildBackgroundGradient(),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                // 主要内容区域
                Expanded(
                  flex: 3,
                  child: Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo动画
                        _buildAnimatedLogo(),
                        const SizedBox(height: 32),
                        // 应用名称
                        _buildAppTitle(),
                        const SizedBox(height: 16),
                        // 副标题
                        _buildSubtitle(),
                        const SizedBox(height: 48),
                        // 加载指示器
                        _buildLoadingIndicator(),
                      ],
                    ),
                  ),
                ),
                // 底部信息
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      _buildVersionInfo(),
                      const SizedBox(height: 16),
                      _buildCopyright(),
                      const SizedBox(height: 32),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建背景渐变
  BoxDecoration _buildBackgroundGradient() {
    return const BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFF2F76DA), // 主强调蓝色
          Color(0xFF1E5BB8), // 深蓝色
          Color(0xFF0B57D0), // 更深的蓝色
        ],
        stops: [0.0, 0.5, 1.0],
      ),
    );
  }

  /// 构建动画Logo
  Widget _buildAnimatedLogo() {
    return Semantics(
      label: '空调管家应用Logo',
      child: ScaleTransition(
        scale: _logoScale,
        child: Hero(
          tag: 'app_logo',
          child: Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: const Icon(
              Icons.ac_unit,
              size: 64,
              color: Color(0xFF2F76DA),
            ),
          ),
        ),
      ),
    );
  }

  /// 构建应用标题
  Widget _buildAppTitle() {
    return const Text(
      '空调管家',
      style: TextStyle(
        fontSize: 32,
        fontWeight: FontWeight.bold,
        color: Colors.white,
        letterSpacing: 2.0,
      ),
    );
  }

  /// 构建副标题
  Widget _buildSubtitle() {
    return const Text(
      '智能优化 · 节能舒适',
      style: TextStyle(fontSize: 16, color: Colors.white70, letterSpacing: 1.0),
    );
  }

  /// 构建加载指示器
  Widget _buildLoadingIndicator() {
    return Semantics(
      label: '应用正在加载中',
      child: SizedBox(
        width: 32,
        height: 32,
        child: Platform.isIOS
            ? const CircularProgressIndicator.adaptive(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              )
            : const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                strokeWidth: 3.0,
              ),
      ),
    );
  }

  /// 构建版本信息
  Widget _buildVersionInfo() {
    return const Text(
      'Version 1.0.0',
      style: TextStyle(fontSize: 12, color: Colors.white60),
    );
  }

  /// 构建版权信息
  Widget _buildCopyright() {
    return const Text(
      '© 2024 空调管家团队',
      style: TextStyle(fontSize: 12, color: Colors.white60),
    );
  }
}
