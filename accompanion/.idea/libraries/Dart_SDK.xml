<component name="libraryTable">
  <library name="Dart SDK">
    <CLASSES>
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/_internal" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/async" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/cli" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/collection" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/concurrent" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/convert" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/core" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/developer" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/ffi" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/html" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/indexed_db" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/io" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/isolate" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/js" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/js_interop" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/js_interop_unsafe" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/js_util" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/math" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/mirrors" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/svg" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/typed_data" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/web_audio" />
      <root url="file://$PROJECT_DIR$/../../flutter/bin/cache/dart-sdk/lib/web_gl" />
    </CLASSES>
    <JAVADOC />
    <SOURCES />
  </library>
</component>