# Details

Date : 2025-07-16 03:26:19

Directory /Users/<USER>/development/flutter_apps

Total : 406 files,  178276 codes, 6809 comments, 14410 blanks, all 199495 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/Augrules.backup.md](/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [.augment/rules/Augrules.md](/.augment/rules/Augrules.md) | Markdown | 428 | 0 | 125 | 553 |
| [NOTION\_PROJECT\_TEMPLATE.md](/NOTION_PROJECT_TEMPLATE.md) | Markdown | 137 | 0 | 55 | 192 |
| [ONEDAY\_PROJECT\_ANALYSIS.md](/ONEDAY_PROJECT_ANALYSIS.md) | Markdown | 105 | 0 | 47 | 152 |
| [USER\_COMMUNITY\_TEMPLATE.md](/USER_COMMUNITY_TEMPLATE.md) | Markdown | 250 | 0 | 119 | 369 |
| [accompanion/accompanion/.augment/rules/Augrules.backup.md](/accompanion/accompanion/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [accompanion/accompanion/.augment/rules/Augrules.md](/accompanion/accompanion/.augment/rules/Augrules.md) | Markdown | 428 | 0 | 125 | 553 |
| [accompanion/accompanion/README.md](/accompanion/accompanion/README.md) | Markdown | 283 | 0 | 93 | 376 |
| [accompanion/accompanion/analysis\_options.yaml](/accompanion/accompanion/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [accompanion/accompanion/android/app/src/debug/AndroidManifest.xml](/accompanion/accompanion/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [accompanion/accompanion/android/app/src/main/AndroidManifest.xml](/accompanion/accompanion/android/app/src/main/AndroidManifest.xml) | XML | 34 | 11 | 1 | 46 |
| [accompanion/accompanion/android/app/src/main/res/drawable-v21/launch\_background.xml](/accompanion/accompanion/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [accompanion/accompanion/android/app/src/main/res/drawable/launch\_background.xml](/accompanion/accompanion/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [accompanion/accompanion/android/app/src/main/res/values-night/styles.xml](/accompanion/accompanion/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [accompanion/accompanion/android/app/src/main/res/values/styles.xml](/accompanion/accompanion/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [accompanion/accompanion/android/app/src/profile/AndroidManifest.xml](/accompanion/accompanion/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [accompanion/accompanion/android/gradle.properties](/accompanion/accompanion/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties](/accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [accompanion/accompanion/ios/Podfile](/accompanion/accompanion/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [accompanion/accompanion/ios/Runner/AppDelegate.swift](/accompanion/accompanion/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 122 | 0 | 1 | 123 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard](/accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard](/accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h](/accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [accompanion/accompanion/ios/RunnerTests/RunnerTests.swift](/accompanion/accompanion/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [accompanion/accompanion/lib/main.dart](/accompanion/accompanion/lib/main.dart) | Dart | 79 | 42 | 16 | 137 |
| [accompanion/accompanion/lib/screens/calculation\_screen.dart](/accompanion/accompanion/lib/screens/calculation_screen.dart) | Dart | 713 | 39 | 43 | 795 |
| [accompanion/accompanion/lib/screens/cost\_analysis\_screen.dart](/accompanion/accompanion/lib/screens/cost_analysis_screen.dart) | Dart | 546 | 30 | 40 | 616 |
| [accompanion/accompanion/lib/screens/history\_screen.dart](/accompanion/accompanion/lib/screens/history_screen.dart) | Dart | 652 | 42 | 54 | 748 |
| [accompanion/accompanion/lib/screens/home\_screen.dart](/accompanion/accompanion/lib/screens/home_screen.dart) | Dart | 493 | 30 | 25 | 548 |
| [accompanion/accompanion/lib/screens/mode\_selection\_screen.dart](/accompanion/accompanion/lib/screens/mode_selection_screen.dart) | Dart | 569 | 38 | 32 | 639 |
| [accompanion/accompanion/lib/screens/onboarding\_screen.dart](/accompanion/accompanion/lib/screens/onboarding_screen.dart) | Dart | 253 | 30 | 24 | 307 |
| [accompanion/accompanion/lib/screens/optimization\_tips\_screen.dart](/accompanion/accompanion/lib/screens/optimization_tips_screen.dart) | Dart | 799 | 44 | 62 | 905 |
| [accompanion/accompanion/lib/screens/room\_setup\_screen.dart](/accompanion/accompanion/lib/screens/room_setup_screen.dart) | Dart | 494 | 32 | 30 | 556 |
| [accompanion/accompanion/lib/screens/settings\_screen.dart](/accompanion/accompanion/lib/screens/settings_screen.dart) | Dart | 586 | 64 | 45 | 695 |
| [accompanion/accompanion/lib/screens/splash\_screen.dart](/accompanion/accompanion/lib/screens/splash_screen.dart) | Dart | 224 | 33 | 24 | 281 |
| [accompanion/accompanion/linux/CMakeLists.txt](/accompanion/accompanion/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [accompanion/accompanion/linux/flutter/CMakeLists.txt](/accompanion/accompanion/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [accompanion/accompanion/linux/flutter/generated\_plugin\_registrant.cc](/accompanion/accompanion/linux/flutter/generated_plugin_registrant.cc) | C++ | 3 | 4 | 5 | 12 |
| [accompanion/accompanion/linux/flutter/generated\_plugin\_registrant.h](/accompanion/accompanion/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [accompanion/accompanion/linux/flutter/generated\_plugins.cmake](/accompanion/accompanion/linux/flutter/generated_plugins.cmake) | CMake | 18 | 0 | 6 | 24 |
| [accompanion/accompanion/linux/runner/CMakeLists.txt](/accompanion/accompanion/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [accompanion/accompanion/linux/runner/main.cc](/accompanion/accompanion/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [accompanion/accompanion/linux/runner/my\_application.cc](/accompanion/accompanion/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [accompanion/accompanion/linux/runner/my\_application.h](/accompanion/accompanion/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift](/accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 6 | 3 | 4 | 13 |
| [accompanion/accompanion/macos/Podfile](/accompanion/accompanion/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [accompanion/accompanion/macos/Runner/AppDelegate.swift](/accompanion/accompanion/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 1 | 69 |
| [accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib](/accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [accompanion/accompanion/macos/Runner/MainFlutterWindow.swift](/accompanion/accompanion/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [accompanion/accompanion/macos/RunnerTests/RunnerTests.swift](/accompanion/accompanion/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [accompanion/accompanion/pubspec.yaml](/accompanion/accompanion/pubspec.yaml) | YAML | 21 | 63 | 17 | 101 |
| [accompanion/accompanion/test/widget\_test.dart](/accompanion/accompanion/test/widget_test.dart) | Dart | 14 | 10 | 7 | 31 |
| [accompanion/accompanion/web/index.html](/accompanion/accompanion/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [accompanion/accompanion/web/manifest.json](/accompanion/accompanion/web/manifest.json) | JSON | 35 | 0 | 1 | 36 |
| [accompanion/accompanion/windows/CMakeLists.txt](/accompanion/accompanion/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [accompanion/accompanion/windows/flutter/CMakeLists.txt](/accompanion/accompanion/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [accompanion/accompanion/windows/flutter/generated\_plugin\_registrant.cc](/accompanion/accompanion/windows/flutter/generated_plugin_registrant.cc) | C++ | 3 | 4 | 5 | 12 |
| [accompanion/accompanion/windows/flutter/generated\_plugin\_registrant.h](/accompanion/accompanion/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [accompanion/accompanion/windows/flutter/generated\_plugins.cmake](/accompanion/accompanion/windows/flutter/generated_plugins.cmake) | CMake | 18 | 0 | 6 | 24 |
| [accompanion/accompanion/windows/runner/CMakeLists.txt](/accompanion/accompanion/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [accompanion/accompanion/windows/runner/flutter\_window.cpp](/accompanion/accompanion/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [accompanion/accompanion/windows/runner/flutter\_window.h](/accompanion/accompanion/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [accompanion/accompanion/windows/runner/main.cpp](/accompanion/accompanion/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [accompanion/accompanion/windows/runner/resource.h](/accompanion/accompanion/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [accompanion/accompanion/windows/runner/utils.cpp](/accompanion/accompanion/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [accompanion/accompanion/windows/runner/utils.h](/accompanion/accompanion/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [accompanion/accompanion/windows/runner/win32\_window.cpp](/accompanion/accompanion/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [accompanion/accompanion/windows/runner/win32\_window.h](/accompanion/accompanion/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 384 | 0 | 112 | 496 |
| [oneday/ABILITY\_RADAR\_FEATURE.md](/oneday/ABILITY_RADAR_FEATURE.md) | Markdown | 137 | 0 | 47 | 184 |
| [oneday/ACHIEVEMENT\_SYSTEM\_SUMMARY.md](/oneday/ACHIEVEMENT_SYSTEM_SUMMARY.md) | Markdown | 166 | 0 | 54 | 220 |
| [oneday/ACHIEVEMENT\_SYSTEM\_TEST\_GUIDE.md](/oneday/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md) | Markdown | 87 | 0 | 28 | 115 |
| [oneday/COMPREHENSIVE\_NAVIGATION\_FIX\_SUMMARY.md](/oneday/COMPREHENSIVE_NAVIGATION_FIX_SUMMARY.md) | Markdown | 176 | 0 | 46 | 222 |
| [oneday/FLOATING\_TIMER\_LIFECYCLE\_FIX.md](/oneday/FLOATING_TIMER_LIFECYCLE_FIX.md) | Markdown | 144 | 0 | 44 | 188 |
| [oneday/FLOATING\_TIMER\_REST\_FINAL\_REPORT.md](/oneday/FLOATING_TIMER_REST_FINAL_REPORT.md) | Markdown | 121 | 0 | 34 | 155 |
| [oneday/FLOATING\_TIMER\_REST\_SYNC\_FIX.md](/oneday/FLOATING_TIMER_REST_SYNC_FIX.md) | Markdown | 202 | 0 | 55 | 257 |
| [oneday/FLOATING\_TIMER\_UI\_IMPROVEMENTS.md](/oneday/FLOATING_TIMER_UI_IMPROVEMENTS.md) | Markdown | 141 | 0 | 35 | 176 |
| [oneday/HOME\_PAGE\_CLEANUP\_SUMMARY.md](/oneday/HOME_PAGE_CLEANUP_SUMMARY.md) | Markdown | 71 | 0 | 26 | 97 |
| [oneday/NAVIGATION\_FIX\_COMPLETE.md](/oneday/NAVIGATION_FIX_COMPLETE.md) | Markdown | 145 | 0 | 32 | 177 |
| [oneday/README.md](/oneday/README.md) | Markdown | 1,167 | 0 | 263 | 1,430 |
| [oneday/STUDY\_SESSION\_COMPLETION\_IMPLEMENTATION.md](/oneday/STUDY_SESSION_COMPLETION_IMPLEMENTATION.md) | Markdown | 166 | 0 | 45 | 211 |
| [oneday/STUDY\_SESSION\_COMPLETION\_VERIFICATION.md](/oneday/STUDY_SESSION_COMPLETION_VERIFICATION.md) | Markdown | 153 | 0 | 38 | 191 |
| [oneday/TIMEBOX\_FLOATING\_TIMER\_FIXES.md](/oneday/TIMEBOX_FLOATING_TIMER_FIXES.md) | Markdown | 182 | 0 | 43 | 225 |
| [oneday/TIMEBOX\_REST\_SYNC\_FIX.md](/oneday/TIMEBOX_REST_SYNC_FIX.md) | Markdown | 147 | 0 | 36 | 183 |
| [oneday/TIMEBOX\_SEARCH\_REMOVAL.md](/oneday/TIMEBOX_SEARCH_REMOVAL.md) | Markdown | 151 | 0 | 40 | 191 |
| [oneday/analysis\_options.yaml](/oneday/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [oneday/android/app/src/debug/AndroidManifest.xml](/oneday/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/app/src/main/AndroidManifest.xml](/oneday/android/app/src/main/AndroidManifest.xml) | XML | 41 | 13 | 3 | 57 |
| [oneday/android/app/src/main/res/drawable-v21/launch\_background.xml](/oneday/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/drawable/launch\_background.xml](/oneday/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/values-night/styles.xml](/oneday/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/main/res/values/styles.xml](/oneday/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/profile/AndroidManifest.xml](/oneday/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/gradle.properties](/oneday/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [oneday/android/gradle/wrapper/gradle-wrapper.properties](/oneday/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [oneday/assets/data/Action.html](/oneday/assets/data/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [oneday/assets/data/prefixes.json](/oneday/assets/data/prefixes.json) | JSON | 88 | 0 | 1 | 89 |
| [oneday/assets/data/suffixes.json](/oneday/assets/data/suffixes.json) | JSON | 99 | 0 | 1 | 100 |
| [oneday/assets/data/vocabulary.json](/oneday/assets/data/vocabulary.json) | JSON | 84,516 | 0 | 0 | 84,516 |
| [oneday/assets/data/word\_roots.json](/oneday/assets/data/word_roots.json) | JSON | 158 | 0 | 1 | 159 |
| [oneday/assets/data/动作.html](/oneday/assets/data/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [oneday/assets/icons/README.md](/oneday/assets/icons/README.md) | Markdown | 33 | 0 | 13 | 46 |
| [oneday/build.yaml](/oneday/build.yaml) | YAML | 11 | 6 | 1 | 18 |
| [oneday/docs/DEVELOPER\_TOOLS\_UPDATE.md](/oneday/docs/DEVELOPER_TOOLS_UPDATE.md) | Markdown | 149 | 0 | 48 | 197 |
| [oneday/docs/NEW\_STORE\_ITEMS.md](/oneday/docs/NEW_STORE_ITEMS.md) | Markdown | 125 | 0 | 40 | 165 |
| [oneday/docs/PROFILE\_AVATAR\_CLICK\_UPDATE.md](/oneday/docs/PROFILE_AVATAR_CLICK_UPDATE.md) | Markdown | 108 | 0 | 37 | 145 |
| [oneday/docs/README.md](/oneday/docs/README.md) | Markdown | 71 | 0 | 20 | 91 |
| [oneday/docs/UI\_DEMO\_GUIDE.md](/oneday/docs/UI_DEMO_GUIDE.md) | Markdown | 91 | 0 | 22 | 113 |
| [oneday/docs/UI\_IMPROVEMENTS.md](/oneday/docs/UI_IMPROVEMENTS.md) | Markdown | 116 | 0 | 42 | 158 |
| [oneday/docs/core/ARCHITECTURE.md](/oneday/docs/core/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [oneday/docs/core/FEATURES.md](/oneday/docs/core/FEATURES.md) | Markdown | 601 | 0 | 120 | 721 |
| [oneday/docs/core/PRD.md](/oneday/docs/core/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [oneday/docs/core/README.md](/oneday/docs/core/README.md) | Markdown | 32 | 0 | 10 | 42 |
| [oneday/docs/development/COMMUNITY\_ARTICLE\_IMPORT\_FEATURE.md](/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md) | Markdown | 118 | 0 | 37 | 155 |
| [oneday/docs/development/CUSTOM\_EXERCISE\_CATEGORY\_FEATURE.md](/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md) | Markdown | 193 | 0 | 43 | 236 |
| [oneday/docs/development/EDIT\_ALBUM\_ENHANCEMENT.md](/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | 167 | 0 | 43 | 210 |
| [oneday/docs/development/FLOATING\_TIMER\_FEATURE.md](/oneday/docs/development/FLOATING_TIMER_FEATURE.md) | Markdown | 74 | 0 | 20 | 94 |
| [oneday/docs/development/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | 160 | 2 | 40 | 202 |
| [oneday/docs/development/NOTION\_STYLE\_UPDATES.md](/oneday/docs/development/NOTION_STYLE_UPDATES.md) | Markdown | 125 | 0 | 44 | 169 |
| [oneday/docs/development/POMODORO\_TIMER\_IMPLEMENTATION.md](/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md) | Markdown | 155 | 0 | 27 | 182 |
| [oneday/docs/development/README.md](/oneday/docs/development/README.md) | Markdown | 41 | 0 | 18 | 59 |
| [oneday/docs/development/REMOVE\_CAMERA\_FEATURE.md](/oneday/docs/development/REMOVE_CAMERA_FEATURE.md) | Markdown | 105 | 0 | 35 | 140 |
| [oneday/docs/development/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/docs/development/SHARE\_UI\_RESTORE\_FEATURE.md](/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md) | Markdown | 120 | 0 | 35 | 155 |
| [oneday/docs/development/SWIPE\_GESTURE\_DEMO.md](/oneday/docs/development/SWIPE_GESTURE_DEMO.md) | Markdown | 216 | 0 | 48 | 264 |
| [oneday/docs/development/VOCABULARY\_SCROLLBAR\_ENHANCEMENT.md](/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md) | Markdown | 126 | 0 | 38 | 164 |
| [oneday/docs/development/WORD\_DISPLAY\_LOWERCASE\_UPDATE.md](/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md) | Markdown | 117 | 0 | 30 | 147 |
| [oneday/docs/development/blueprint.md](/oneday/docs/development/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [oneday/docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_GUIDE.md](/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md) | Markdown | 142 | 0 | 37 | 179 |
| [oneday/docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_IMPLEMENTATION\_SUMMARY.md](/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md) | Markdown | 204 | 0 | 45 | 249 |
| [oneday/docs/features/CUSTOM\_ACTION\_LIBRARY\_IMPLEMENTATION.md](/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md) | Markdown | 183 | 0 | 42 | 225 |
| [oneday/docs/features/CUSTOM\_ACTION\_LIBRARY\_USER\_GUIDE.md](/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md) | Markdown | 127 | 0 | 50 | 177 |
| [oneday/docs/features/DIALOG\_BACKGROUND\_BEFORE\_AFTER\_COMPARISON.md](/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md) | Markdown | 215 | 0 | 59 | 274 |
| [oneday/docs/features/DIALOG\_BACKGROUND\_UNIFICATION.md](/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md) | Markdown | 213 | 0 | 63 | 276 |
| [oneday/docs/features/FINAL\_DIALOG\_BACKGROUND\_REPORT.md](/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md) | Markdown | 145 | 0 | 40 | 185 |
| [oneday/docs/features/GRADUATE\_VOCABULARY\_MANAGER.md](/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md) | Markdown | 137 | 0 | 42 | 179 |
| [oneday/docs/features/VOCABULARY\_MANAGER\_IMPLEMENTATION\_SUMMARY.md](/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md) | Markdown | 156 | 0 | 42 | 198 |
| [oneday/docs/fixes/EXERCISE\_LIBRARY\_PAGE\_IMPORT\_FIX.md](/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md) | Markdown | 91 | 0 | 43 | 134 |
| [oneday/docs/guides/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [oneday/docs/guides/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | 199 | 0 | 60 | 259 |
| [oneday/docs/guides/ICON\_SETUP\_GUIDE.md](/oneday/docs/guides/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [oneday/docs/guides/README.md](/oneday/docs/guides/README.md) | Markdown | 91 | 0 | 27 | 118 |
| [oneday/docs/guides/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | 117 | 0 | 41 | 158 |
| [oneday/docs/guides/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | 69 | 0 | 27 | 96 |
| [oneday/docs/releases/README.md](/oneday/docs/releases/README.md) | Markdown | 107 | 0 | 30 | 137 |
| [oneday/docs/testing/COMPREHENSIVE\_TEST\_GUIDE.md](/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | 276 | 0 | 96 | 372 |
| [oneday/docs/testing/COVER\_MODE\_TEST\_GUIDE.md](/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [oneday/docs/testing/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | 114 | 0 | 27 | 141 |
| [oneday/docs/testing/POMODORO\_TEST\_GUIDE.md](/oneday/docs/testing/POMODORO_TEST_GUIDE.md) | Markdown | 105 | 0 | 20 | 125 |
| [oneday/docs/testing/QUICK\_TEST\_STEPS.md](/oneday/docs/testing/QUICK_TEST_STEPS.md) | Markdown | 85 | 0 | 26 | 111 |
| [oneday/docs/testing/README.md](/oneday/docs/testing/README.md) | Markdown | 92 | 0 | 30 | 122 |
| [oneday/docs/testing/TESTING\_GUIDE.md](/oneday/docs/testing/TESTING_GUIDE.md) | Markdown | 103 | 0 | 34 | 137 |
| [oneday/docs/testing/TIMER\_TEST\_GUIDE.md](/oneday/docs/testing/TIMER_TEST_GUIDE.md) | Markdown | 121 | 0 | 32 | 153 |
| [oneday/docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | 154 | 0 | 43 | 197 |
| [oneday/docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | 144 | 0 | 43 | 187 |
| [oneday/docs/troubleshooting/BOTTOM\_NAVIGATION\_BAR\_FIX.md](/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md) | Markdown | 244 | 0 | 50 | 294 |
| [oneday/docs/troubleshooting/CALENDAR\_TASK\_DISPLAY\_FIX.md](/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md) | Markdown | 219 | 0 | 39 | 258 |
| [oneday/docs/troubleshooting/COLOR\_SYSTEM\_RESTORATION.md](/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md) | Markdown | 134 | 0 | 34 | 168 |
| [oneday/docs/troubleshooting/COMPREHENSIVE\_NAVIGATION\_AUDIT.md](/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md) | Markdown | 192 | 0 | 48 | 240 |
| [oneday/docs/troubleshooting/DIALOG\_LAYOUT\_BUGFIX.md](/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md) | Markdown | 206 | 0 | 42 | 248 |
| [oneday/docs/troubleshooting/DIALOG\_LAYOUT\_OPTIMIZATION.md](/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md) | Markdown | 192 | 0 | 46 | 238 |
| [oneday/docs/troubleshooting/EXERCISE\_LIBRARY\_UI\_OPTIMIZATION.md](/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md) | Markdown | 111 | 0 | 36 | 147 |
| [oneday/docs/troubleshooting/FINAL\_NAVIGATION\_TEST\_REPORT.md](/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md) | Markdown | 118 | 0 | 38 | 156 |
| [oneday/docs/troubleshooting/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | 190 | 0 | 50 | 240 |
| [oneday/docs/troubleshooting/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | 142 | 0 | 35 | 177 |
| [oneday/docs/troubleshooting/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | 129 | 0 | 35 | 164 |
| [oneday/docs/troubleshooting/HOW\_TO\_TEST\_NAVIGATION\_FIX.md](/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md) | Markdown | 147 | 0 | 51 | 198 |
| [oneday/docs/troubleshooting/IPAD\_STAGE\_MANAGER\_LAYOUT\_FIX.md](/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md) | Markdown | 200 | 0 | 31 | 231 |
| [oneday/docs/troubleshooting/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | 191 | 0 | 42 | 233 |
| [oneday/docs/troubleshooting/NAVIGATION\_FIX\_PROGRESS\_REPORT.md](/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md) | Markdown | 121 | 0 | 38 | 159 |
| [oneday/docs/troubleshooting/NAVIGATION\_FIX\_SUMMARY.md](/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md) | Markdown | 133 | 0 | 39 | 172 |
| [oneday/docs/troubleshooting/PROBLEM\_SOLUTIONS.md](/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md) | Markdown | 288 | 0 | 106 | 394 |
| [oneday/docs/troubleshooting/README.md](/oneday/docs/troubleshooting/README.md) | Markdown | 54 | 0 | 22 | 76 |
| [oneday/docs/troubleshooting/SIDEBAR\_BUTTON\_OPTIMIZATION.md](/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md) | Markdown | 147 | 0 | 37 | 184 |
| [oneday/docs/troubleshooting/TIMEBOX\_REST\_SKIP\_NAVIGATION\_FIX.md](/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md) | Markdown | 157 | 0 | 52 | 209 |
| [oneday/docs/troubleshooting/TIMEBOX\_TIMER\_FIXES.md](/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md) | Markdown | 115 | 0 | 27 | 142 |
| [oneday/docs/troubleshooting/TIMER\_FIXES\_PROGRESS.md](/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md) | Markdown | 131 | 0 | 35 | 166 |
| [oneday/docs/troubleshooting/UI\_OVERFLOW\_FIXES\_SUMMARY.md](/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md) | Markdown | 98 | 0 | 29 | 127 |
| [oneday/docs/troubleshooting/VOCABULARY\_SERVICE\_FIX.md](/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md) | Markdown | 219 | 0 | 52 | 271 |
| [oneday/docs/troubleshooting/WORD\_TRAINING\_OPTIMIZATION.md](/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md) | Markdown | 108 | 0 | 39 | 147 |
| [oneday/docs/ui\_improvements/CATEGORY\_SELECTOR\_PURE\_TEXT\_FIX.md](/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md) | Markdown | 118 | 0 | 28 | 146 |
| [oneday/docs/ui\_improvements/IPAD\_CALENDAR\_OVERFLOW\_FIX\_COMPLETE.md](/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md) | Markdown | 129 | 0 | 33 | 162 |
| [oneday/docs/ui\_improvements/IPAD\_CALENDAR\_RESPONSIVE\_FIX.md](/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md) | Markdown | 178 | 0 | 38 | 216 |
| [oneday/docs/ui\_improvements/REMOVE\_DUPLICATE\_MENU\_ITEMS.md](/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md) | Markdown | 111 | 0 | 36 | 147 |
| [oneday/example/calendar\_responsive\_demo.dart](/oneday/example/calendar_responsive_demo.dart) | Dart | 342 | 16 | 24 | 382 |
| [oneday/example/calendar\_task\_display\_demo.dart](/oneday/example/calendar_task_display_demo.dart) | Dart | 373 | 19 | 25 | 417 |
| [oneday/example/color\_scheme\_comparison\_demo.dart](/oneday/example/color_scheme_comparison_demo.dart) | Dart | 412 | 16 | 31 | 459 |
| [oneday/example/navigation\_bottom\_bar\_demo.dart](/oneday/example/navigation_bottom_bar_demo.dart) | Dart | 366 | 9 | 15 | 390 |
| [oneday/example/responsive\_layout\_demo.dart](/oneday/example/responsive_layout_demo.dart) | Dart | 560 | 13 | 26 | 599 |
| [oneday/example/study\_session\_completion\_demo.dart](/oneday/example/study_session_completion_demo.dart) | Dart | 427 | 7 | 24 | 458 |
| [oneday/example/timebox\_rest\_skip\_demo.dart](/oneday/example/timebox_rest_skip_demo.dart) | Dart | 449 | 24 | 40 | 513 |
| [oneday/ios/Podfile](/oneday/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [oneday/ios/Runner/AppDelegate.swift](/oneday/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 116 | 0 | 1 | 117 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard](/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [oneday/ios/Runner/Base.lproj/Main.storyboard](/oneday/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [oneday/ios/Runner/Runner-Bridging-Header.h](/oneday/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [oneday/ios/RunnerTests/RunnerTests.swift](/oneday/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/lib/core/constants/app\_icons.dart](/oneday/lib/core/constants/app_icons.dart) | Dart | 35 | 5 | 3 | 43 |
| [oneday/lib/core/data/pao\_exercises\_data.dart](/oneday/lib/core/data/pao_exercises_data.dart) | Dart | 317 | 28 | 25 | 370 |
| [oneday/lib/debug/route\_debug\_page.dart](/oneday/lib/debug/route_debug_page.dart) | Dart | 268 | 10 | 15 | 293 |
| [oneday/lib/features/ability\_radar/models/ability\_radar\_models.dart](/oneday/lib/features/ability_radar/models/ability_radar_models.dart) | Dart | 169 | 39 | 56 | 264 |
| [oneday/lib/features/ability\_radar/models/ability\_radar\_models.g.dart](/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart) | Dart | 199 | 32 | 18 | 249 |
| [oneday/lib/features/ability\_radar/pages/ability\_radar\_page.dart](/oneday/lib/features/ability_radar/pages/ability_radar_page.dart) | Dart | 448 | 41 | 41 | 530 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_mock\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart) | Dart | 173 | 5 | 6 | 184 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart) | Dart | 282 | 21 | 57 | 360 |
| [oneday/lib/features/ability\_radar/services/ability\_radar\_service.dart](/oneday/lib/features/ability_radar/services/ability_radar_service.dart) | Dart | 467 | 45 | 106 | 618 |
| [oneday/lib/features/ability\_radar/widgets/ability\_radar\_chart.dart](/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart) | Dart | 284 | 18 | 23 | 325 |
| [oneday/lib/features/achievement/README.md](/oneday/lib/features/achievement/README.md) | Markdown | 132 | 0 | 41 | 173 |
| [oneday/lib/features/achievement/data/achievements\_data.dart](/oneday/lib/features/achievement/data/achievements_data.dart) | Dart | 444 | 18 | 12 | 474 |
| [oneday/lib/features/achievement/models/achievement.dart](/oneday/lib/features/achievement/models/achievement.dart) | Dart | 169 | 29 | 39 | 237 |
| [oneday/lib/features/achievement/models/achievement.g.dart](/oneday/lib/features/achievement/models/achievement.g.dart) | Dart | 125 | 24 | 11 | 160 |
| [oneday/lib/features/achievement/models/badge.dart](/oneday/lib/features/achievement/models/badge.dart) | Dart | 230 | 50 | 69 | 349 |
| [oneday/lib/features/achievement/models/badge.g.dart](/oneday/lib/features/achievement/models/badge.g.dart) | Dart | 184 | 40 | 17 | 241 |
| [oneday/lib/features/achievement/models/user\_level.dart](/oneday/lib/features/achievement/models/user_level.dart) | Dart | 264 | 41 | 48 | 353 |
| [oneday/lib/features/achievement/models/user\_level.g.dart](/oneday/lib/features/achievement/models/user_level.g.dart) | Dart | 91 | 21 | 11 | 123 |
| [oneday/lib/features/achievement/pages/achievement\_page.dart](/oneday/lib/features/achievement/pages/achievement_page.dart) | Dart | 485 | 21 | 39 | 545 |
| [oneday/lib/features/achievement/pages/leaderboard\_page.dart](/oneday/lib/features/achievement/pages/leaderboard_page.dart) | Dart | 336 | 13 | 20 | 369 |
| [oneday/lib/features/achievement/providers/achievement\_provider.dart](/oneday/lib/features/achievement/providers/achievement_provider.dart) | Dart | 224 | 38 | 44 | 306 |
| [oneday/lib/features/achievement/services/achievement\_service.dart](/oneday/lib/features/achievement/services/achievement_service.dart) | Dart | 322 | 38 | 64 | 424 |
| [oneday/lib/features/achievement/services/achievement\_trigger\_service.dart](/oneday/lib/features/achievement/services/achievement_trigger_service.dart) | Dart | 113 | 61 | 63 | 237 |
| [oneday/lib/features/achievement/widgets/achievement\_grid.dart](/oneday/lib/features/achievement/widgets/achievement_grid.dart) | Dart | 380 | 19 | 32 | 431 |
| [oneday/lib/features/achievement/widgets/achievement\_unlock\_notification.dart](/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart) | Dart | 300 | 20 | 45 | 365 |
| [oneday/lib/features/achievement/widgets/skill\_levels\_card.dart](/oneday/lib/features/achievement/widgets/skill_levels_card.dart) | Dart | 323 | 15 | 42 | 380 |
| [oneday/lib/features/achievement/widgets/user\_level\_card.dart](/oneday/lib/features/achievement/widgets/user_level_card.dart) | Dart | 326 | 7 | 29 | 362 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | 666 | 38 | 41 | 745 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 1,164 | 92 | 80 | 1,336 |
| [oneday/lib/features/community/article\_import\_service.dart](/oneday/lib/features/community/article_import_service.dart) | Dart | 191 | 26 | 40 | 257 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 781 | 24 | 58 | 863 |
| [oneday/lib/features/community/community\_post\_editor\_page.dart](/oneday/lib/features/community/community_post_editor_page.dart) | Dart | 679 | 38 | 59 | 776 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.dart](/oneday/lib/features/daily_plan/models/daily_plan.dart) | Dart | 151 | 29 | 34 | 214 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.g.dart](/oneday/lib/features/daily_plan/models/daily_plan.g.dart) | Dart | 49 | 13 | 7 | 69 |
| [oneday/lib/features/daily\_plan/notifiers/daily\_plan\_notifier.dart](/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart) | Dart | 204 | 31 | 44 | 279 |
| [oneday/lib/features/daily\_plan/services/daily\_plan\_storage\_service.dart](/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart) | Dart | 216 | 33 | 48 | 297 |
| [oneday/lib/features/exercise/create\_custom\_library\_dialog.dart](/oneday/lib/features/exercise/create_custom_library_dialog.dart) | Dart | 276 | 7 | 17 | 300 |
| [oneday/lib/features/exercise/custom\_action\_editor\_dialog.dart](/oneday/lib/features/exercise/custom_action_editor_dialog.dart) | Dart | 500 | 17 | 37 | 554 |
| [oneday/lib/features/exercise/custom\_action\_library.dart](/oneday/lib/features/exercise/custom_action_library.dart) | Dart | 203 | 25 | 29 | 257 |
| [oneday/lib/features/exercise/custom\_action\_library\_service.dart](/oneday/lib/features/exercise/custom_action_library_service.dart) | Dart | 227 | 29 | 46 | 302 |
| [oneday/lib/features/exercise/custom\_exercise\_category.dart](/oneday/lib/features/exercise/custom_exercise_category.dart) | Dart | 164 | 15 | 23 | 202 |
| [oneday/lib/features/exercise/custom\_library\_editor\_page.dart](/oneday/lib/features/exercise/custom_library_editor_page.dart) | Dart | 432 | 18 | 30 | 480 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 2,401 | 133 | 156 | 2,690 |
| [oneday/lib/features/exercise/exercise\_session\_page.dart](/oneday/lib/features/exercise/exercise_session_page.dart) | Dart | 985 | 72 | 87 | 1,144 |
| [oneday/lib/features/exercise/focused\_training\_page.dart](/oneday/lib/features/exercise/focused_training_page.dart) | Dart | 435 | 18 | 41 | 494 |
| [oneday/lib/features/exercise/manage\_custom\_libraries\_dialog.dart](/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart) | Dart | 641 | 15 | 28 | 684 |
| [oneday/lib/features/exercise/pao\_integration\_service.dart](/oneday/lib/features/exercise/pao_integration_service.dart) | Dart | 188 | 32 | 46 | 266 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/learning\_report/learning\_report\_page.dart](/oneday/lib/features/learning_report/learning_report_page.dart) | Dart | 757 | 42 | 54 | 853 |
| [oneday/lib/features/learning\_report/models/learning\_report\_models.dart](/oneday/lib/features/learning_report/models/learning_report_models.dart) | Dart | 203 | 61 | 77 | 341 |
| [oneday/lib/features/learning\_report/models/learning\_report\_models.g.dart](/oneday/lib/features/learning_report/models/learning_report_models.g.dart) | Dart | 301 | 51 | 25 | 377 |
| [oneday/lib/features/learning\_report/services/learning\_report\_service.dart](/oneday/lib/features/learning_report/services/learning_report_service.dart) | Dart | 474 | 61 | 100 | 635 |
| [oneday/lib/features/learning\_report/widgets/chart\_widgets.dart](/oneday/lib/features/learning_report/widgets/chart_widgets.dart) | Dart | 764 | 14 | 30 | 808 |
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | 104 | 4 | 6 | 114 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 5,124 | 387 | 475 | 5,986 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 3,204 | 493 | 575 | 4,272 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | 1,672 | 186 | 224 | 2,082 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 241 | 18 | 29 | 288 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 785 | 59 | 75 | 919 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 808 | 49 | 81 | 938 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 701 | 30 | 58 | 789 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 1,324 | 66 | 135 | 1,525 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 772 | 49 | 62 | 883 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | 186 | 12 | 22 | 220 |
| [oneday/lib/features/study\_time/models/study\_time\_models.dart](/oneday/lib/features/study_time/models/study_time_models.dart) | Dart | 264 | 58 | 68 | 390 |
| [oneday/lib/features/study\_time/models/study\_time\_models.g.dart](/oneday/lib/features/study_time/models/study_time_models.g.dart) | Dart | 175 | 34 | 12 | 221 |
| [oneday/lib/features/study\_time/providers/study\_time\_providers.dart](/oneday/lib/features/study_time/providers/study_time_providers.dart) | Dart | 241 | 26 | 46 | 313 |
| [oneday/lib/features/study\_time/services/study\_time\_statistics\_service.dart](/oneday/lib/features/study_time/services/study_time_statistics_service.dart) | Dart | 319 | 60 | 79 | 458 |
| [oneday/lib/features/study\_time/test\_data\_sync\_page.dart](/oneday/lib/features/study_time/test_data_sync_page.dart) | Dart | 251 | 11 | 17 | 279 |
| [oneday/lib/features/time\_box/models/timebox\_models.dart](/oneday/lib/features/time_box/models/timebox_models.dart) | Dart | 273 | 43 | 56 | 372 |
| [oneday/lib/features/time\_box/models/timebox\_models.g.dart](/oneday/lib/features/time_box/models/timebox_models.g.dart) | Dart | 71 | 15 | 8 | 94 |
| [oneday/lib/features/time\_box/providers/timebox\_provider.dart](/oneday/lib/features/time_box/providers/timebox_provider.dart) | Dart | 217 | 19 | 25 | 261 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 3,104 | 192 | 232 | 3,528 |
| [oneday/lib/features/time\_box/widgets/study\_session\_completion\_dialog.dart](/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart) | Dart | 405 | 6 | 21 | 432 |
| [oneday/lib/features/vocabulary/create\_vocabulary\_page.dart](/oneday/lib/features/vocabulary/create_vocabulary_page.dart) | Dart | 452 | 11 | 39 | 502 |
| [oneday/lib/features/vocabulary/fsrs\_algorithm.dart](/oneday/lib/features/vocabulary/fsrs_algorithm.dart) | Dart | 221 | 41 | 40 | 302 |
| [oneday/lib/features/vocabulary/fsrs\_service.dart](/oneday/lib/features/vocabulary/fsrs_service.dart) | Dart | 359 | 61 | 78 | 498 |
| [oneday/lib/features/vocabulary/graduate\_vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart) | Dart | 597 | 30 | 50 | 677 |
| [oneday/lib/features/vocabulary/vocabulary\_cache\_manager.dart](/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart) | Dart | 233 | 33 | 64 | 330 |
| [oneday/lib/features/vocabulary/vocabulary\_category\_page.dart](/oneday/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 940 | 53 | 77 | 1,070 |
| [oneday/lib/features/vocabulary/vocabulary\_learning\_service.dart](/oneday/lib/features/vocabulary/vocabulary_learning_service.dart) | Dart | 232 | 28 | 47 | 307 |
| [oneday/lib/features/vocabulary/vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | 1,004 | 42 | 75 | 1,121 |
| [oneday/lib/features/vocabulary/vocabulary\_page.dart](/oneday/lib/features/vocabulary/vocabulary_page.dart) | Dart | 162 | 2 | 12 | 176 |
| [oneday/lib/features/vocabulary/vocabulary\_service.dart](/oneday/lib/features/vocabulary/vocabulary_service.dart) | Dart | 521 | 71 | 119 | 711 |
| [oneday/lib/features/vocabulary/vocabulary\_statistics\_page.dart](/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart) | Dart | 845 | 29 | 83 | 957 |
| [oneday/lib/features/vocabulary/word\_meaning\_service.dart](/oneday/lib/features/vocabulary/word_meaning_service.dart) | Dart | 162 | 17 | 32 | 211 |
| [oneday/lib/features/vocabulary/word\_model.dart](/oneday/lib/features/vocabulary/word_model.dart) | Dart | 590 | 21 | 43 | 654 |
| [oneday/lib/features/vocabulary/word\_root\_service.dart](/oneday/lib/features/vocabulary/word_root_service.dart) | Dart | 189 | 28 | 37 | 254 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 1,632 | 74 | 97 | 1,803 |
| [oneday/lib/features/wage\_system/wage\_wallet\_page.dart](/oneday/lib/features/wage_system/wage_wallet_page.dart) | Dart | 826 | 36 | 57 | 919 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 252 | 23 | 11 | 286 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 333 | 32 | 40 | 405 |
| [oneday/lib/services/app\_initialization\_service.dart](/oneday/lib/services/app_initialization_service.dart) | Dart | 60 | 16 | 19 | 95 |
| [oneday/lib/services/first\_time\_service.dart](/oneday/lib/services/first_time_service.dart) | Dart | 44 | 16 | 13 | 73 |
| [oneday/lib/services/floating\_timer\_service.dart](/oneday/lib/services/floating_timer_service.dart) | Dart | 472 | 77 | 79 | 628 |
| [oneday/lib/services/global\_timer\_service.dart](/oneday/lib/services/global_timer_service.dart) | Dart | 324 | 85 | 96 | 505 |
| [oneday/lib/services/providers/study\_session\_completion\_provider.dart](/oneday/lib/services/providers/study_session_completion_provider.dart) | Dart | 19 | 2 | 4 | 25 |
| [oneday/lib/services/study\_session\_completion\_service.dart](/oneday/lib/services/study_session_completion_service.dart) | Dart | 185 | 37 | 45 | 267 |
| [oneday/lib/services/system\_overlay\_permission.dart](/oneday/lib/services/system_overlay_permission.dart) | Dart | 248 | 26 | 24 | 298 |
| [oneday/lib/shared/config/image\_compression\_config.dart](/oneday/lib/shared/config/image_compression_config.dart) | Dart | 69 | 20 | 19 | 108 |
| [oneday/lib/shared/services/enhanced\_share\_service.dart](/oneday/lib/shared/services/enhanced_share_service.dart) | Dart | 160 | 27 | 37 | 224 |
| [oneday/lib/shared/utils/image\_compression\_utils.dart](/oneday/lib/shared/utils/image_compression_utils.dart) | Dart | 187 | 35 | 44 | 266 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 246 | 62 | 62 | 370 |
| [oneday/lib/shared/utils/simple\_image\_test.dart](/oneday/lib/shared/utils/simple_image_test.dart) | Dart | 83 | 14 | 26 | 123 |
| [oneday/lib/shared/utils/ui\_utils.dart](/oneday/lib/shared/utils/ui_utils.dart) | Dart | 94 | 19 | 9 | 122 |
| [oneday/lib/shared/widgets/icons/wechat\_icon.dart](/oneday/lib/shared/widgets/icons/wechat_icon.dart) | Dart | 62 | 8 | 14 | 84 |
| [oneday/lib/shared/widgets/oneday\_logo.dart](/oneday/lib/shared/widgets/oneday_logo.dart) | Dart | 204 | 17 | 43 | 264 |
| [oneday/linux/CMakeLists.txt](/oneday/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [oneday/linux/flutter/CMakeLists.txt](/oneday/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [oneday/linux/flutter/generated\_plugin\_registrant.cc](/oneday/linux/flutter/generated_plugin_registrant.cc) | C++ | 11 | 4 | 5 | 20 |
| [oneday/linux/flutter/generated\_plugin\_registrant.h](/oneday/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/linux/flutter/generated\_plugins.cmake](/oneday/linux/flutter/generated_plugins.cmake) | CMake | 20 | 0 | 6 | 26 |
| [oneday/linux/runner/CMakeLists.txt](/oneday/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [oneday/linux/runner/main.cc](/oneday/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [oneday/linux/runner/my\_application.cc](/oneday/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [oneday/linux/runner/my\_application.h](/oneday/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [oneday/macos/Flutter/GeneratedPluginRegistrant.swift](/oneday/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 14 | 3 | 4 | 21 |
| [oneday/macos/Podfile](/oneday/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [oneday/macos/Runner/AppDelegate.swift](/oneday/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [oneday/macos/Runner/Base.lproj/MainMenu.xib](/oneday/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [oneday/macos/Runner/MainFlutterWindow.swift](/oneday/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [oneday/macos/RunnerTests/RunnerTests.swift](/oneday/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 55 | 72 | 29 | 156 |
| [oneday/scripts/transform\_vocabulary.py](/oneday/scripts/transform_vocabulary.py) | Python | 152 | 26 | 32 | 210 |
| [oneday/scripts/verify\_navigation\_fix.dart](/oneday/scripts/verify_navigation_fix.dart) | Dart | 175 | 20 | 45 | 240 |
| [oneday/test/add\_child\_category\_test.dart](/oneday/test/add_child_category_test.dart) | Dart | 146 | 30 | 48 | 224 |
| [oneday/test/calendar\_task\_display\_test.dart](/oneday/test/calendar_task_display_test.dart) | Dart | 251 | 13 | 36 | 300 |
| [oneday/test/category\_edit\_fix\_test.dart](/oneday/test/category_edit_fix_test.dart) | Dart | 101 | 15 | 21 | 137 |
| [oneday/test/category\_edit\_state\_test.dart](/oneday/test/category_edit_state_test.dart) | Dart | 147 | 40 | 55 | 242 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 152 | 38 | 52 | 242 |
| [oneday/test/category\_sidebar\_edit\_test.dart](/oneday/test/category_sidebar_edit_test.dart) | Dart | 171 | 45 | 61 | 277 |
| [oneday/test/context\_aware\_album\_creation\_test.dart](/oneday/test/context_aware_album_creation_test.dart) | Dart | 98 | 6 | 20 | 124 |
| [oneday/test/create\_album\_success\_message\_test.dart](/oneday/test/create_album_success_message_test.dart) | Dart | 172 | 20 | 27 | 219 |
| [oneday/test/date\_format\_test.dart](/oneday/test/date_format_test.dart) | Dart | 44 | 5 | 13 | 62 |
| [oneday/test/developer\_tools\_test.dart](/oneday/test/developer_tools_test.dart) | Dart | 132 | 12 | 29 | 173 |
| [oneday/test/exercise\_library\_ui\_test.dart](/oneday/test/exercise_library_ui_test.dart) | Dart | 156 | 31 | 55 | 242 |
| [oneday/test/features/exercise/custom\_action\_library\_ui\_test.dart](/oneday/test/features/exercise/custom_action_library_ui_test.dart) | Dart | 184 | 27 | 39 | 250 |
| [oneday/test/features/exercise/exercise\_library\_page\_import\_test.dart](/oneday/test/features/exercise/exercise_library_page_import_test.dart) | Dart | 21 | 5 | 6 | 32 |
| [oneday/test/features/study\_time/study\_time\_statistics\_test.dart](/oneday/test/features/study_time/study_time_statistics_test.dart) | Dart | 264 | 13 | 38 | 315 |
| [oneday/test/features/time\_box/timebox\_ui\_test.dart](/oneday/test/features/time_box/timebox_ui_test.dart) | Dart | 125 | 11 | 18 | 154 |
| [oneday/test/features/ui/color\_consistency\_test.dart](/oneday/test/features/ui/color_consistency_test.dart) | Dart | 208 | 19 | 30 | 257 |
| [oneday/test/features/ui/dialog\_background\_simple\_test.dart](/oneday/test/features/ui/dialog_background_simple_test.dart) | Dart | 198 | 17 | 23 | 238 |
| [oneday/test/features/ui/dialog\_background\_test.dart](/oneday/test/features/ui/dialog_background_test.dart) | Dart | 228 | 23 | 32 | 283 |
| [oneday/test/features/ui/dropdown\_background\_test.dart](/oneday/test/features/ui/dropdown_background_test.dart) | Dart | 352 | 25 | 25 | 402 |
| [oneday/test/features/ui/dropdown\_pure\_white\_test.dart](/oneday/test/features/ui/dropdown_pure_white_test.dart) | Dart | 262 | 14 | 22 | 298 |
| [oneday/test/features/ui/final\_verification\_test.dart](/oneday/test/features/ui/final_verification_test.dart) | Dart | 233 | 24 | 29 | 286 |
| [oneday/test/features/ui/material3\_surface\_tinting\_fix\_test.dart](/oneday/test/features/ui/material3_surface_tinting_fix_test.dart) | Dart | 252 | 16 | 23 | 291 |
| [oneday/test/graduate\_vocabulary\_manager\_test.dart](/oneday/test/graduate_vocabulary_manager_test.dart) | Dart | 59 | 6 | 20 | 85 |
| [oneday/test/integration/study\_session\_completion\_integration\_test.dart](/oneday/test/integration/study_session_completion_integration_test.dart) | Dart | 108 | 51 | 54 | 213 |
| [oneday/test/ipad\_calendar\_overflow\_test.dart](/oneday/test/ipad_calendar_overflow_test.dart) | Dart | 119 | 21 | 38 | 178 |
| [oneday/test/learning\_efficiency\_metrics\_test.dart](/oneday/test/learning_efficiency_metrics_test.dart) | Dart | 145 | 10 | 13 | 168 |
| [oneday/test/memory\_palace\_card\_test.dart](/oneday/test/memory_palace_card_test.dart) | Dart | 201 | 13 | 29 | 243 |
| [oneday/test/memory\_palace\_persistence\_test.dart](/oneday/test/memory_palace_persistence_test.dart) | Dart | 166 | 14 | 25 | 205 |
| [oneday/test/navigation\_bottom\_bar\_test.dart](/oneday/test/navigation_bottom_bar_test.dart) | Dart | 164 | 37 | 50 | 251 |
| [oneday/test/onboarding\_web\_navigation\_test.dart](/oneday/test/onboarding_web_navigation_test.dart) | Dart | 30 | 9 | 11 | 50 |
| [oneday/test/photo\_album\_creator\_test.dart](/oneday/test/photo_album_creator_test.dart) | Dart | 81 | 2 | 17 | 100 |
| [oneday/test/photo\_album\_dialog\_layout\_test.dart](/oneday/test/photo_album_dialog_layout_test.dart) | Dart | 101 | 25 | 32 | 158 |
| [oneday/test/photo\_album\_dialog\_test.dart](/oneday/test/photo_album_dialog_test.dart) | Dart | 96 | 21 | 28 | 145 |
| [oneday/test/profile\_avatar\_click\_test.dart](/oneday/test/profile_avatar_click_test.dart) | Dart | 137 | 16 | 33 | 186 |
| [oneday/test/reflection\_calendar\_responsive\_test.dart](/oneday/test/reflection_calendar_responsive_test.dart) | Dart | 140 | 30 | 49 | 219 |
| [oneday/test/reflection\_log\_integration\_test.dart](/oneday/test/reflection_log_integration_test.dart) | Dart | 61 | 11 | 19 | 91 |
| [oneday/test/responsive\_layout\_test.dart](/oneday/test/responsive_layout_test.dart) | Dart | 230 | 12 | 24 | 266 |
| [oneday/test/services/study\_session\_completion\_service\_test.dart](/oneday/test/services/study_session_completion_service_test.dart) | Dart | 223 | 32 | 39 | 294 |
| [oneday/test/store\_new\_items\_test.dart](/oneday/test/store_new_items_test.dart) | Dart | 95 | 13 | 27 | 135 |
| [oneday/test/test\_bubble\_alignment\_adjustment.dart](/oneday/test/test_bubble_alignment_adjustment.dart) | Dart | 103 | 9 | 23 | 135 |
| [oneday/test/test\_bubble\_alignment\_verification.dart](/oneday/test/test_bubble_alignment_verification.dart) | Dart | 103 | 10 | 26 | 139 |
| [oneday/test/test\_bubble\_center\_alignment.dart](/oneday/test/test_bubble_center_alignment.dart) | Dart | 92 | 8 | 20 | 120 |
| [oneday/test/test\_bubble\_positioning\_fix.dart](/oneday/test/test_bubble_positioning_fix.dart) | Dart | 109 | 6 | 27 | 142 |
| [oneday/test/test\_compilation\_fix.dart](/oneday/test/test_compilation_fix.dart) | Dart | 144 | 7 | 29 | 180 |
| [oneday/test/test\_complete\_image\_compression.dart](/oneday/test/test_complete_image_compression.dart) | Dart | 186 | 9 | 38 | 233 |
| [oneday/test/test\_coordinate\_analysis.dart](/oneday/test/test_coordinate_analysis.dart) | Dart | 104 | 9 | 27 | 140 |
| [oneday/test/test\_coordinate\_debug.dart](/oneday/test/test_coordinate_debug.dart) | Dart | 84 | 5 | 18 | 107 |
| [oneday/test/test\_coordinate\_transformation\_fix.dart](/oneday/test/test_coordinate_transformation_fix.dart) | Dart | 110 | 16 | 24 | 150 |
| [oneday/test/test\_image\_compression\_implementation.dart](/oneday/test/test_image_compression_implementation.dart) | Dart | 147 | 7 | 33 | 187 |
| [oneday/test/test\_matrix\_analysis.dart](/oneday/test/test_matrix_analysis.dart) | Dart | 130 | 8 | 37 | 175 |
| [oneday/test/test\_matrix\_fix\_verification.dart](/oneday/test/test_matrix_fix_verification.dart) | Dart | 105 | 14 | 31 | 150 |
| [oneday/test/test\_position\_fix\_verification.dart](/oneday/test/test_position_fix_verification.dart) | Dart | 38 | 4 | 11 | 53 |
| [oneday/test/three\_dot\_menu\_test.dart](/oneday/test/three_dot_menu_test.dart) | Dart | 108 | 24 | 32 | 164 |
| [oneday/test/timebox\_rest\_skip\_test.dart](/oneday/test/timebox_rest_skip_test.dart) | Dart | 201 | 20 | 39 | 260 |
| [oneday/test/ui\_overflow\_test.dart](/oneday/test/ui_overflow_test.dart) | Dart | 101 | 20 | 32 | 153 |
| [oneday/test/vocabulary\_scrollbar\_test.dart](/oneday/test/vocabulary_scrollbar_test.dart) | Dart | 98 | 8 | 22 | 128 |
| [oneday/test/vocabulary\_test.dart](/oneday/test/vocabulary_test.dart) | Dart | 216 | 9 | 29 | 254 |
| [oneday/test/widget\_test.dart](/oneday/test/widget_test.dart) | Dart | 11 | 9 | 6 | 26 |
| [oneday/test/word\_meaning\_service\_test.dart](/oneday/test/word_meaning_service_test.dart) | Dart | 62 | 2 | 14 | 78 |
| [oneday/web/index.html](/oneday/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [oneday/web/manifest.json](/oneday/web/manifest.json) | JSON | 35 | 0 | 0 | 35 |
| [oneday/windows/CMakeLists.txt](/oneday/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [oneday/windows/flutter/CMakeLists.txt](/oneday/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [oneday/windows/flutter/generated\_plugin\_registrant.cc](/oneday/windows/flutter/generated_plugin_registrant.cc) | C++ | 15 | 4 | 5 | 24 |
| [oneday/windows/flutter/generated\_plugin\_registrant.h](/oneday/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/windows/flutter/generated\_plugins.cmake](/oneday/windows/flutter/generated_plugins.cmake) | CMake | 22 | 0 | 6 | 28 |
| [oneday/windows/runner/CMakeLists.txt](/oneday/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [oneday/windows/runner/flutter\_window.cpp](/oneday/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [oneday/windows/runner/flutter\_window.h](/oneday/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [oneday/windows/runner/main.cpp](/oneday/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [oneday/windows/runner/resource.h](/oneday/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [oneday/windows/runner/utils.cpp](/oneday/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [oneday/windows/runner/utils.h](/oneday/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [oneday/windows/runner/win32\_window.cpp](/oneday/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [oneday/windows/runner/win32\_window.h](/oneday/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 0 | 0 | 1 | 1 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)