{"file:///Users/<USER>/development/flutter_apps/oneday/assets/data/Action.html": {"language": "HTML", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json": {"language": "JSON", "code": 99, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/%E5%8A%A8%E4%BD%9C.html": {"language": "HTML", "code": 693, "comment": 23, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md": {"language": "<PERSON><PERSON>", "code": 33, "comment": 0, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json": {"language": "JSON", "code": 88, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake": {"language": "CMake", "code": 22, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json": {"language": "JSON", "code": 158, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 87, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 15, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/STUDY_SESSION_COMPLETION_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 153, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_REST_SYNC_FIX.md": {"language": "<PERSON><PERSON>", "code": 202, "comment": 0, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/build.yaml": {"language": "YAML", "code": 11, "comment": 6, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt": {"language": "CMake", "code": 98, "comment": 0, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart": {"language": "Dart", "code": 333, "comment": 32, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt": {"language": "CMake", "code": 89, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml": {"language": "YAML", "code": 3, "comment": 22, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart": {"language": "Dart", "code": 324, "comment": 85, "blank": 96}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/main.dart": {"language": "Dart", "code": 252, "comment": 23, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart": {"language": "Dart", "code": 472, "comment": 77, "blank": 79}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/app_initialization_service.dart": {"language": "Dart", "code": 60, "comment": 16, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/debug/route_debug_page.dart": {"language": "Dart", "code": 268, "comment": 10, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/study_session_completion_service.dart": {"language": "Dart", "code": 185, "comment": 37, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart": {"language": "Dart", "code": 248, "comment": 26, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart": {"language": "Dart", "code": 44, "comment": 16, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_LIFECYCLE_FIX.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/providers/study_session_completion_provider.dart": {"language": "Dart", "code": 19, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart": {"language": "Dart", "code": 94, "comment": 19, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart": {"language": "Dart", "code": 69, "comment": 20, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart": {"language": "Dart", "code": 246, "comment": 62, "blank": 62}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart": {"language": "Dart", "code": 187, "comment": 35, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>": {"language": "Dart", "code": 1093, "comment": 74, "blank": 65}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart": {"language": "Dart", "code": 83, "comment": 14, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart": {"language": "Dart", "code": 204, "comment": 17, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart": {"language": "Dart", "code": 160, "comment": 27, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart": {"language": "Dart", "code": 62, "comment": 8, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart": {"language": "Dart", "code": 666, "comment": 38, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart": {"language": "Dart", "code": 435, "comment": 18, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library.dart": {"language": "Dart", "code": 203, "comment": 25, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/pao_integration_service.dart": {"language": "Dart", "code": 188, "comment": 32, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart": {"language": "Dart", "code": 641, "comment": 15, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_editor_dialog.dart": {"language": "Dart", "code": 500, "comment": 17, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_exercise_category.dart": {"language": "Dart", "code": 164, "comment": 15, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart": {"language": "Dart", "code": 241, "comment": 18, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_custom_library_dialog.dart": {"language": "Dart", "code": 276, "comment": 7, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart": {"language": "Dart", "code": 1324, "comment": 66, "blank": 135}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library_service.dart": {"language": "Dart", "code": 227, "comment": 29, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart": {"language": "Dart", "code": 2401, "comment": 133, "blank": 156}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart": {"language": "Dart", "code": 985, "comment": 72, "blank": 87}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart": {"language": "Dart", "code": 781, "comment": 24, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/article_import_service.dart": {"language": "Dart", "code": 191, "comment": 26, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_post_editor_page.dart": {"language": "Dart", "code": 679, "comment": 38, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart": {"language": "Dart", "code": 785, "comment": 59, "blank": 75}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_library_editor_page.dart": {"language": "Dart", "code": 432, "comment": 18, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart": {"language": "Dart", "code": 233, "comment": 33, "blank": 64}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart": {"language": "Dart", "code": 597, "comment": 30, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart": {"language": "Dart", "code": 232, "comment": 28, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_service.dart": {"language": "Dart", "code": 474, "comment": 61, "blank": 100}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/widgets/chart_widgets.dart": {"language": "Dart", "code": 764, "comment": 14, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_service.dart": {"language": "Dart", "code": 359, "comment": 61, "blank": 78}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart": {"language": "Dart", "code": 189, "comment": 28, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart": {"language": "Dart", "code": 1004, "comment": 42, "blank": 75}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_algorithm.dart": {"language": "Dart", "code": 221, "comment": 41, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart": {"language": "Dart", "code": 521, "comment": 71, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart": {"language": "Dart", "code": 940, "comment": 53, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/learning_report_page.dart": {"language": "Dart", "code": 757, "comment": 42, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart": {"language": "Dart", "code": 452, "comment": 11, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart": {"language": "Dart", "code": 162, "comment": 17, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart": {"language": "Dart", "code": 590, "comment": 21, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart": {"language": "Dart", "code": 5124, "comment": 387, "blank": 475}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart": {"language": "Dart", "code": 162, "comment": 2, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart": {"language": "Dart", "code": 3204, "comment": 493, "blank": 575}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart": {"language": "Dart", "code": 845, "comment": 29, "blank": 83}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.dart": {"language": "Dart", "code": 203, "comment": 61, "blank": 77}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.g.dart": {"language": "Dart", "code": 301, "comment": 51, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart": {"language": "Dart", "code": 1672, "comment": 186, "blank": 224}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart": {"language": "Dart", "code": 284, "comment": 18, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/pages/ability_radar_page.dart": {"language": "Dart", "code": 448, "comment": 41, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart": {"language": "Dart", "code": 104, "comment": 4, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart": {"language": "Dart", "code": 701, "comment": 30, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart": {"language": "Dart", "code": 199, "comment": 32, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.dart": {"language": "Dart", "code": 169, "comment": 39, "blank": 56}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/ability_radar_service.dart": {"language": "Dart", "code": 467, "comment": 45, "blank": 106}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart": {"language": "Dart", "code": 808, "comment": 49, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart": {"language": "Dart", "code": 282, "comment": 21, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart": {"language": "Dart", "code": 151, "comment": 29, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/README.md": {"language": "<PERSON><PERSON>", "code": 132, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart": {"language": "Dart", "code": 1164, "comment": 92, "blank": 80}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart": {"language": "Dart", "code": 173, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart": {"language": "Dart", "code": 49, "comment": 13, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/achievement_page.dart": {"language": "Dart", "code": 485, "comment": 21, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart": {"language": "Dart", "code": 204, "comment": 31, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/data/achievements_data.dart": {"language": "Dart", "code": 444, "comment": 18, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.dart": {"language": "Dart", "code": 169, "comment": 29, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/leaderboard_page.dart": {"language": "Dart", "code": 336, "comment": 13, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.dart": {"language": "Dart", "code": 264, "comment": 58, "blank": 68}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.g.dart": {"language": "Dart", "code": 125, "comment": 24, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.g.dart": {"language": "Dart", "code": 91, "comment": 21, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/services/study_time_statistics_service.dart": {"language": "Dart", "code": 319, "comment": 60, "blank": 79}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.g.dart": {"language": "Dart", "code": 184, "comment": 40, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.g.dart": {"language": "Dart", "code": 175, "comment": 34, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.dart": {"language": "Dart", "code": 230, "comment": 50, "blank": 69}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/user_level_card.dart": {"language": "Dart", "code": 326, "comment": 7, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart": {"language": "Dart", "code": 300, "comment": 20, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.dart": {"language": "Dart", "code": 264, "comment": 41, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart": {"language": "Dart", "code": 1632, "comment": 74, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/test_data_sync_page.dart": {"language": "Dart", "code": 251, "comment": 11, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_grid.dart": {"language": "Dart", "code": 380, "comment": 19, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart": {"language": "Dart", "code": 826, "comment": 36, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart": {"language": "Dart", "code": 186, "comment": 12, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/skill_levels_card.dart": {"language": "Dart", "code": 323, "comment": 15, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/providers/achievement_provider.dart": {"language": "Dart", "code": 224, "comment": 38, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_service.dart": {"language": "Dart", "code": 322, "comment": 38, "blank": 64}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_trigger_service.dart": {"language": "Dart", "code": 113, "comment": 61, "blank": 63}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/providers/study_time_providers.dart": {"language": "Dart", "code": 241, "comment": 26, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart": {"language": "Dart", "code": 3104, "comment": 192, "blank": 232}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart": {"language": "Dart", "code": 772, "comment": 49, "blank": 62}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart": {"language": "Dart", "code": 317, "comment": 28, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart": {"language": "Dart", "code": 35, "comment": 5, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart": {"language": "Dart", "code": 216, "comment": 33, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/ACHIEVEMENT_SYSTEM_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 166, "comment": 0, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt": {"language": "CMake", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/HOME_PAGE_CLEANUP_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/pubspec.yaml": {"language": "YAML", "code": 55, "comment": 72, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/ABILITY_RADAR_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart": {"language": "Dart", "code": 405, "comment": 6, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart": {"language": "Dart", "code": 273, "comment": 43, "blank": 56}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart": {"language": "Dart", "code": 71, "comment": 15, "blank": 8}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMEBOX_FLOATING_TIMER_FIXES.md": {"language": "<PERSON><PERSON>", "code": 182, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/verify_navigation_fix.dart": {"language": "Dart", "code": 175, "comment": 20, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart": {"language": "Dart", "code": 217, "comment": 19, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py": {"language": "Python", "code": 152, "comment": 26, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/README.md": {"language": "<PERSON><PERSON>", "code": 1167, "comment": 0, "blank": 263}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake": {"language": "CMake", "code": 20, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Podfile": {"language": "<PERSON>", "code": 31, "comment": 3, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/example/navigation_bottom_bar_demo.dart": {"language": "Dart", "code": 366, "comment": 9, "blank": 15}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 11, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt": {"language": "CMake", "code": 79, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/example/timebox_rest_skip_demo.dart": {"language": "Dart", "code": 449, "comment": 24, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/example/study_session_completion_demo.dart": {"language": "Dart", "code": 427, "comment": 7, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/example/calendar_responsive_demo.dart": {"language": "Dart", "code": 342, "comment": 16, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/example/responsive_layout_demo.dart": {"language": "Dart", "code": 560, "comment": 13, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMEBOX_REST_SYNC_FIX.md": {"language": "<PERSON><PERSON>", "code": 147, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle.properties": {"language": "Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/DEVELOPER_TOOLS_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 149, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/example/calendar_task_display_demo.dart": {"language": "Dart", "code": 373, "comment": 19, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/README.md": {"language": "<PERSON><PERSON>", "code": 71, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/example/color_scheme_comparison_demo.dart": {"language": "Dart", "code": 412, "comment": 16, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/releases/README.md": {"language": "<PERSON><PERSON>", "code": 107, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md": {"language": "<PERSON><PERSON>", "code": 145, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/COMPREHENSIVE_NAVIGATION_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 176, "comment": 0, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 156, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 204, "comment": 0, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 127, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md": {"language": "<PERSON><PERSON>", "code": 215, "comment": 0, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md": {"language": "<PERSON><PERSON>", "code": 213, "comment": 0, "blank": 63}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/ICON_SETUP_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 231, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/UI_IMPROVEMENTS.md": {"language": "<PERSON><PERSON>", "code": 116, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md": {"language": "<PERSON><PERSON>", "code": 178, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 199, "comment": 0, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/PROFILE_AVATAR_CLICK_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 183, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/NEW_STORE_ITEMS.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/README.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/UI_DEMO_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 133, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md": {"language": "<PERSON><PERSON>", "code": 200, "comment": 0, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md": {"language": "<PERSON><PERSON>", "code": 219, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md": {"language": "<PERSON><PERSON>", "code": 134, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 192, "comment": 0, "blank": 46}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md": {"language": "<PERSON><PERSON>", "code": 91, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 190, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md": {"language": "<PERSON><PERSON>", "code": 244, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 98, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 147, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md": {"language": "<PERSON><PERSON>", "code": 192, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md": {"language": "<PERSON><PERSON>", "code": 288, "comment": 0, "blank": 106}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md": {"language": "<PERSON><PERSON>", "code": 147, "comment": 0, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md": {"language": "<PERSON><PERSON>", "code": 206, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md": {"language": "<PERSON><PERSON>", "code": 157, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 111, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md": {"language": "<PERSON><PERSON>", "code": 191, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 154, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md": {"language": "<PERSON><PERSON>", "code": 219, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md": {"language": "<PERSON><PERSON>", "code": 131, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md": {"language": "<PERSON><PERSON>", "code": 114, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/QUICK_TEST_STEPS.md": {"language": "<PERSON><PERSON>", "code": 85, "comment": 0, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/TESTING_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 103, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/README.md": {"language": "<PERSON><PERSON>", "code": 92, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/POMODORO_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/README.md": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 276, "comment": 0, "blank": 96}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/README.md": {"language": "<PERSON><PERSON>", "code": 32, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/testing/TIMER_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/PRD.md": {"language": "<PERSON><PERSON>", "code": 339, "comment": 0, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md": {"language": "<PERSON><PERSON>", "code": 167, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/FEATURES.md": {"language": "<PERSON><PERSON>", "code": 601, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/core/ARCHITECTURE.md": {"language": "<PERSON><PERSON>", "code": 380, "comment": 0, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/NOTION_STYLE_UPDATES.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md": {"language": "<PERSON><PERSON>", "code": 126, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 155, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/web/index.html": {"language": "HTML", "code": 19, "comment": 15, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 74, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 160, "comment": 2, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/README.md": {"language": "<PERSON><PERSON>", "code": 41, "comment": 0, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 193, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SWIPE_GESTURE_DEMO.md": {"language": "<PERSON><PERSON>", "code": 216, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 120, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/REMOVE_CAMERA_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/blueprint.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt": {"language": "CMake", "code": 104, "comment": 0, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc": {"language": "C++", "code": 83, "comment": 21, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 118, "comment": 0, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 41, "comment": 13, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt": {"language": "CMake", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/STUDY_SESSION_COMPLETION_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 166, "comment": 0, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/NAVIGATION_FIX_COMPLETE.md": {"language": "<PERSON><PERSON>", "code": 145, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_UI_IMPROVEMENTS.md": {"language": "<PERSON><PERSON>", "code": 141, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMEBOX_SEARCH_REMOVAL.md": {"language": "<PERSON><PERSON>", "code": 151, "comment": 0, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/tempPrompts.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules.md": {"language": "<PERSON><PERSON>", "code": 384, "comment": 0, "blank": 112}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 116, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart": {"language": "Dart", "code": 146, "comment": 30, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/test/responsive_layout_test.dart": {"language": "Dart", "code": 230, "comment": 12, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_REST_FINAL_REPORT.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 250, "comment": 0, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/test/reflection_calendar_responsive_test.dart": {"language": "Dart", "code": 140, "comment": 30, "blank": 49}, "file:///Users/<USER>/development/flutter_apps/oneday/test/timebox_rest_skip_test.dart": {"language": "Dart", "code": 201, "comment": 20, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart": {"language": "Dart", "code": 98, "comment": 6, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart": {"language": "Dart", "code": 11, "comment": 9, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_analysis.dart": {"language": "Dart", "code": 104, "comment": 9, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart": {"language": "Dart", "code": 108, "comment": 24, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart": {"language": "Dart", "code": 101, "comment": 25, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart": {"language": "Dart", "code": 152, "comment": 38, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_verification.dart": {"language": "Dart", "code": 103, "comment": 10, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/test/learning_efficiency_metrics_test.dart": {"language": "Dart", "code": 145, "comment": 10, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_matrix_fix_verification.dart": {"language": "Dart", "code": 105, "comment": 14, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart": {"language": "Dart", "code": 166, "comment": 14, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_matrix_analysis.dart": {"language": "Dart", "code": 130, "comment": 8, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/study_time/study_time_statistics_test.dart": {"language": "Dart", "code": 264, "comment": 13, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Podfile": {"language": "<PERSON>", "code": 32, "comment": 1, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_ui_test.dart": {"language": "Dart", "code": 184, "comment": 27, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/test/calendar_task_display_test.dart": {"language": "Dart", "code": 251, "comment": 13, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md": {"language": "<PERSON><PERSON>", "code": 428, "comment": 0, "blank": 125}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md": {"language": "<PERSON><PERSON>", "code": 388, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift": {"language": "Swift", "code": 14, "comment": 3, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.dart": {"language": "Dart", "code": 223, "comment": 32, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/test/integration/study_session_completion_integration_test.dart": {"language": "Dart", "code": 108, "comment": 51, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/oneday/test/vocabulary_scrollbar_test.dart": {"language": "Dart", "code": 98, "comment": 8, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/test/date_format_test.dart": {"language": "Dart", "code": 44, "comment": 5, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/test/store_new_items_test.dart": {"language": "Dart", "code": 95, "comment": 13, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart": {"language": "Dart", "code": 101, "comment": 15, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/test/ipad_calendar_overflow_test.dart": {"language": "Dart", "code": 119, "comment": 21, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart": {"language": "Dart", "code": 62, "comment": 2, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_adjustment.dart": {"language": "Dart", "code": 103, "comment": 9, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_compilation_fix.dart": {"language": "Dart", "code": 144, "comment": 7, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart": {"language": "Dart", "code": 172, "comment": 20, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/profile_avatar_click_test.dart": {"language": "Dart", "code": 137, "comment": 16, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/reflection_log_integration_test.dart": {"language": "Dart", "code": 61, "comment": 11, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart": {"language": "Dart", "code": 81, "comment": 2, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_debug.dart": {"language": "Dart", "code": 84, "comment": 5, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_page_import_test.dart": {"language": "Dart", "code": 21, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart": {"language": "Dart", "code": 216, "comment": 9, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/ui_overflow_test.dart": {"language": "Dart", "code": 101, "comment": 20, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart": {"language": "Dart", "code": 30, "comment": 9, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/test/navigation_bottom_bar_test.dart": {"language": "Dart", "code": 164, "comment": 37, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/test/developer_tools_test.dart": {"language": "Dart", "code": 132, "comment": 12, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_image_compression_implementation.dart": {"language": "Dart", "code": 147, "comment": 7, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart": {"language": "Dart", "code": 171, "comment": 45, "blank": 61}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart": {"language": "Dart", "code": 96, "comment": 21, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_transformation_fix.dart": {"language": "Dart", "code": 110, "comment": 16, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_center_alignment.dart": {"language": "Dart", "code": 92, "comment": 8, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/exercise_library_ui_test.dart": {"language": "Dart", "code": 156, "comment": 31, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart": {"language": "Dart", "code": 201, "comment": 13, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_bubble_positioning_fix.dart": {"language": "Dart", "code": 109, "comment": 6, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart": {"language": "Dart", "code": 147, "comment": 40, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/time_box/timebox_ui_test.dart": {"language": "Dart", "code": 125, "comment": 11, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_complete_image_compression.dart": {"language": "Dart", "code": 186, "comment": 9, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/test/test_position_fix_verification.dart": {"language": "Dart", "code": 38, "comment": 4, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/main.dart": {"language": "Dart", "code": 79, "comment": 42, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/splash_screen.dart": {"language": "Dart", "code": 224, "comment": 33, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/history_screen.dart": {"language": "Dart", "code": 652, "comment": 42, "blank": 54}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/mode_selection_screen.dart": {"language": "Dart", "code": 569, "comment": 38, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/calculation_screen.dart": {"language": "Dart", "code": 713, "comment": 39, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/onboarding_screen.dart": {"language": "Dart", "code": 253, "comment": 30, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/home_screen.dart": {"language": "Dart", "code": 493, "comment": 30, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/cost_analysis_screen.dart": {"language": "Dart", "code": 546, "comment": 30, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugins.cmake": {"language": "CMake", "code": 18, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/graduate_vocabulary_manager_test.dart": {"language": "Dart", "code": 59, "comment": 6, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/CMakeLists.txt": {"language": "CMake", "code": 98, "comment": 0, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/CMakeLists.txt": {"language": "CMake", "code": 89, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/optimization_tips_screen.dart": {"language": "Dart", "code": 799, "comment": 44, "blank": 62}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/room_setup_screen.dart": {"language": "Dart", "code": 494, "comment": 32, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/analysis_options.yaml": {"language": "YAML", "code": 3, "comment": 22, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 3, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/settings_screen.dart": {"language": "Dart", "code": 586, "comment": 64, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_simple_test.dart": {"language": "Dart", "code": 198, "comment": 17, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_background_test.dart": {"language": "Dart", "code": 352, "comment": 25, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/color_consistency_test.dart": {"language": "Dart", "code": 208, "comment": 19, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/material3_surface_tinting_fix_test.dart": {"language": "Dart", "code": 252, "comment": 16, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/pubspec.yaml": {"language": "YAML", "code": 21, "comment": 63, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_pure_white_test.dart": {"language": "Dart", "code": 262, "comment": 14, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/README.md": {"language": "<PERSON><PERSON>", "code": 283, "comment": 0, "blank": 93}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_test.dart": {"language": "Dart", "code": 228, "comment": 23, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test/features/ui/final_verification_test.dart": {"language": "Dart", "code": 233, "comment": 24, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/CMakeLists.txt": {"language": "CMake", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/CMakeLists.txt": {"language": "CMake", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/my_application.cc": {"language": "C++", "code": 83, "comment": 21, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/gradle.properties": {"language": "Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Podfile": {"language": "<PERSON>", "code": 31, "comment": 3, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 122, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugins.cmake": {"language": "CMake", "code": 18, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 3, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/CMakeLists.txt": {"language": "CMake", "code": 79, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/web/index.html": {"language": "HTML", "code": 19, "comment": 15, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/CMakeLists.txt": {"language": "CMake", "code": 104, "comment": 0, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/test/widget_test.dart": {"language": "Dart", "code": 14, "comment": 10, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/.augment/rules/Augrules.backup.md": {"language": "<PERSON><PERSON>", "code": 388, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/.augment/rules/Augrules.md": {"language": "<PERSON><PERSON>", "code": 428, "comment": 0, "blank": 125}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Podfile": {"language": "<PERSON>", "code": 32, "comment": 1, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 34, "comment": 11, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift": {"language": "Swift", "code": 6, "comment": 3, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/profile/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json": {"language": "JSON", "code": 84516, "comment": 0, "blank": 0}}