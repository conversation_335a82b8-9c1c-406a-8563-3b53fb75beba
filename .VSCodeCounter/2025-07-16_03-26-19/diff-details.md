# Diff Details

Date : 2025-07-16 03:26:19

Directory /Users/<USER>/development/flutter_apps

Total : 344 files,  2099 codes, 3012 comments, 6507 blanks, all 11618 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [accompanion/accompanion/.augment/rules/Augrules.backup.md](/accompanion/accompanion/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [accompanion/accompanion/.augment/rules/Augrules.md](/accompanion/accompanion/.augment/rules/Augrules.md) | Markdown | 428 | 0 | 125 | 553 |
| [accompanion/accompanion/README.md](/accompanion/accompanion/README.md) | Markdown | 283 | 0 | 93 | 376 |
| [accompanion/accompanion/analysis\_options.yaml](/accompanion/accompanion/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [accompanion/accompanion/android/app/src/debug/AndroidManifest.xml](/accompanion/accompanion/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [accompanion/accompanion/android/app/src/main/AndroidManifest.xml](/accompanion/accompanion/android/app/src/main/AndroidManifest.xml) | XML | 34 | 11 | 1 | 46 |
| [accompanion/accompanion/android/app/src/main/res/drawable-v21/launch\_background.xml](/accompanion/accompanion/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [accompanion/accompanion/android/app/src/main/res/drawable/launch\_background.xml](/accompanion/accompanion/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [accompanion/accompanion/android/app/src/main/res/values-night/styles.xml](/accompanion/accompanion/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [accompanion/accompanion/android/app/src/main/res/values/styles.xml](/accompanion/accompanion/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [accompanion/accompanion/android/app/src/profile/AndroidManifest.xml](/accompanion/accompanion/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [accompanion/accompanion/android/gradle.properties](/accompanion/accompanion/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties](/accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [accompanion/accompanion/ios/Podfile](/accompanion/accompanion/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [accompanion/accompanion/ios/Runner/AppDelegate.swift](/accompanion/accompanion/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 122 | 0 | 1 | 123 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard](/accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard](/accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h](/accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [accompanion/accompanion/ios/RunnerTests/RunnerTests.swift](/accompanion/accompanion/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [accompanion/accompanion/lib/main.dart](/accompanion/accompanion/lib/main.dart) | Dart | 79 | 42 | 16 | 137 |
| [accompanion/accompanion/lib/screens/calculation\_screen.dart](/accompanion/accompanion/lib/screens/calculation_screen.dart) | Dart | 713 | 39 | 43 | 795 |
| [accompanion/accompanion/lib/screens/cost\_analysis\_screen.dart](/accompanion/accompanion/lib/screens/cost_analysis_screen.dart) | Dart | 546 | 30 | 40 | 616 |
| [accompanion/accompanion/lib/screens/history\_screen.dart](/accompanion/accompanion/lib/screens/history_screen.dart) | Dart | 652 | 42 | 54 | 748 |
| [accompanion/accompanion/lib/screens/home\_screen.dart](/accompanion/accompanion/lib/screens/home_screen.dart) | Dart | 493 | 30 | 25 | 548 |
| [accompanion/accompanion/lib/screens/mode\_selection\_screen.dart](/accompanion/accompanion/lib/screens/mode_selection_screen.dart) | Dart | 569 | 38 | 32 | 639 |
| [accompanion/accompanion/lib/screens/onboarding\_screen.dart](/accompanion/accompanion/lib/screens/onboarding_screen.dart) | Dart | 253 | 30 | 24 | 307 |
| [accompanion/accompanion/lib/screens/optimization\_tips\_screen.dart](/accompanion/accompanion/lib/screens/optimization_tips_screen.dart) | Dart | 799 | 44 | 62 | 905 |
| [accompanion/accompanion/lib/screens/room\_setup\_screen.dart](/accompanion/accompanion/lib/screens/room_setup_screen.dart) | Dart | 494 | 32 | 30 | 556 |
| [accompanion/accompanion/lib/screens/settings\_screen.dart](/accompanion/accompanion/lib/screens/settings_screen.dart) | Dart | 586 | 64 | 45 | 695 |
| [accompanion/accompanion/lib/screens/splash\_screen.dart](/accompanion/accompanion/lib/screens/splash_screen.dart) | Dart | 224 | 33 | 24 | 281 |
| [accompanion/accompanion/linux/CMakeLists.txt](/accompanion/accompanion/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [accompanion/accompanion/linux/flutter/CMakeLists.txt](/accompanion/accompanion/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [accompanion/accompanion/linux/flutter/generated\_plugin\_registrant.cc](/accompanion/accompanion/linux/flutter/generated_plugin_registrant.cc) | C++ | 3 | 4 | 5 | 12 |
| [accompanion/accompanion/linux/flutter/generated\_plugin\_registrant.h](/accompanion/accompanion/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [accompanion/accompanion/linux/flutter/generated\_plugins.cmake](/accompanion/accompanion/linux/flutter/generated_plugins.cmake) | CMake | 18 | 0 | 6 | 24 |
| [accompanion/accompanion/linux/runner/CMakeLists.txt](/accompanion/accompanion/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [accompanion/accompanion/linux/runner/main.cc](/accompanion/accompanion/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [accompanion/accompanion/linux/runner/my\_application.cc](/accompanion/accompanion/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [accompanion/accompanion/linux/runner/my\_application.h](/accompanion/accompanion/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift](/accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 6 | 3 | 4 | 13 |
| [accompanion/accompanion/macos/Podfile](/accompanion/accompanion/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [accompanion/accompanion/macos/Runner/AppDelegate.swift](/accompanion/accompanion/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 1 | 69 |
| [accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib](/accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [accompanion/accompanion/macos/Runner/MainFlutterWindow.swift](/accompanion/accompanion/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [accompanion/accompanion/macos/RunnerTests/RunnerTests.swift](/accompanion/accompanion/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [accompanion/accompanion/pubspec.yaml](/accompanion/accompanion/pubspec.yaml) | YAML | 21 | 63 | 17 | 101 |
| [accompanion/accompanion/test/widget\_test.dart](/accompanion/accompanion/test/widget_test.dart) | Dart | 14 | 10 | 7 | 31 |
| [accompanion/accompanion/web/index.html](/accompanion/accompanion/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [accompanion/accompanion/web/manifest.json](/accompanion/accompanion/web/manifest.json) | JSON | 35 | 0 | 1 | 36 |
| [accompanion/accompanion/windows/CMakeLists.txt](/accompanion/accompanion/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [accompanion/accompanion/windows/flutter/CMakeLists.txt](/accompanion/accompanion/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [accompanion/accompanion/windows/flutter/generated\_plugin\_registrant.cc](/accompanion/accompanion/windows/flutter/generated_plugin_registrant.cc) | C++ | 3 | 4 | 5 | 12 |
| [accompanion/accompanion/windows/flutter/generated\_plugin\_registrant.h](/accompanion/accompanion/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [accompanion/accompanion/windows/flutter/generated\_plugins.cmake](/accompanion/accompanion/windows/flutter/generated_plugins.cmake) | CMake | 18 | 0 | 6 | 24 |
| [accompanion/accompanion/windows/runner/CMakeLists.txt](/accompanion/accompanion/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [accompanion/accompanion/windows/runner/flutter\_window.cpp](/accompanion/accompanion/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [accompanion/accompanion/windows/runner/flutter\_window.h](/accompanion/accompanion/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [accompanion/accompanion/windows/runner/main.cpp](/accompanion/accompanion/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [accompanion/accompanion/windows/runner/resource.h](/accompanion/accompanion/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [accompanion/accompanion/windows/runner/utils.cpp](/accompanion/accompanion/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [accompanion/accompanion/windows/runner/utils.h](/accompanion/accompanion/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [accompanion/accompanion/windows/runner/win32\_window.cpp](/accompanion/accompanion/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [accompanion/accompanion/windows/runner/win32\_window.h](/accompanion/accompanion/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |
| [oneday/.cursorrules.backup.md](/oneday/.cursorrules.backup.md) | Markdown | -384 | 0 | -119 | -503 |
| [oneday/.cursorrules\_副本.md](/oneday/.cursorrules_%E5%89%AF%E6%9C%AC.md) | Markdown | -384 | 0 | -119 | -503 |
| [oneday/ABILITY\_RADAR\_FEATURE.md](/oneday/ABILITY_RADAR_FEATURE.md) | Markdown | 137 | 0 | 47 | 184 |
| [oneday/ACHIEVEMENT\_SYSTEM\_SUMMARY.md](/oneday/ACHIEVEMENT_SYSTEM_SUMMARY.md) | Markdown | 166 | 0 | 54 | 220 |
| [oneday/ACHIEVEMENT\_SYSTEM\_TEST\_GUIDE.md](/oneday/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md) | Markdown | 87 | 0 | 28 | 115 |
| [oneday/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/oneday/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | -154 | 0 | -43 | -197 |
| [oneday/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/oneday/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | -144 | 0 | -43 | -187 |
| [oneday/ARCHITECTURE.md](/oneday/ARCHITECTURE.md) | Markdown | -380 | 0 | -81 | -461 |
| [oneday/Action.html](/oneday/Action.html) | HTML | 0 | 0 | -1 | -1 |
| [oneday/COMPREHENSIVE\_NAVIGATION\_FIX\_SUMMARY.md](/oneday/COMPREHENSIVE_NAVIGATION_FIX_SUMMARY.md) | Markdown | 176 | 0 | 46 | 222 |
| [oneday/COMPREHENSIVE\_TEST\_GUIDE.md](/oneday/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | -276 | 0 | -96 | -372 |
| [oneday/COVER\_MODE\_TEST\_GUIDE.md](/oneday/COVER_MODE_TEST_GUIDE.md) | Markdown | -115 | 0 | -35 | -150 |
| [oneday/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | -59 | 0 | -24 | -83 |
| [oneday/EDIT\_ALBUM\_ENHANCEMENT.md](/oneday/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | -167 | 0 | -43 | -210 |
| [oneday/FEATURES.md](/oneday/FEATURES.md) | Markdown | -601 | 0 | -120 | -721 |
| [oneday/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/oneday/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | -199 | 0 | -60 | -259 |
| [oneday/FLOATING\_TIMER\_FEATURE.md](/oneday/FLOATING_TIMER_FEATURE.md) | Markdown | -74 | 0 | -20 | -94 |
| [oneday/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/oneday/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | -190 | 0 | -50 | -240 |
| [oneday/FLOATING\_TIMER\_LIFECYCLE\_FIX.md](/oneday/FLOATING_TIMER_LIFECYCLE_FIX.md) | Markdown | 144 | 0 | 44 | 188 |
| [oneday/FLOATING\_TIMER\_REST\_FINAL\_REPORT.md](/oneday/FLOATING_TIMER_REST_FINAL_REPORT.md) | Markdown | 121 | 0 | 34 | 155 |
| [oneday/FLOATING\_TIMER\_REST\_SYNC\_FIX.md](/oneday/FLOATING_TIMER_REST_SYNC_FIX.md) | Markdown | 202 | 0 | 55 | 257 |
| [oneday/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/oneday/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | -160 | -2 | -40 | -202 |
| [oneday/FLOATING\_TIMER\_UI\_IMPROVEMENTS.md](/oneday/FLOATING_TIMER_UI_IMPROVEMENTS.md) | Markdown | 141 | 0 | 35 | 176 |
| [oneday/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/oneday/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | -142 | 0 | -35 | -177 |
| [oneday/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/oneday/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | -129 | 0 | -35 | -164 |
| [oneday/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/oneday/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | -114 | 0 | -27 | -141 |
| [oneday/HOME\_PAGE\_CLEANUP\_SUMMARY.md](/oneday/HOME_PAGE_CLEANUP_SUMMARY.md) | Markdown | 71 | 0 | 26 | 97 |
| [oneday/ICON\_SETUP\_GUIDE.md](/oneday/ICON_SETUP_GUIDE.md) | Markdown | -231 | 0 | -28 | -259 |
| [oneday/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/oneday/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | -191 | 0 | -42 | -233 |
| [oneday/NAVIGATION\_FIX\_COMPLETE.md](/oneday/NAVIGATION_FIX_COMPLETE.md) | Markdown | 145 | 0 | 32 | 177 |
| [oneday/NOTION\_STYLE\_UPDATES.md](/oneday/NOTION_STYLE_UPDATES.md) | Markdown | -125 | 0 | -44 | -169 |
| [oneday/PRD.md](/oneday/PRD.md) | Markdown | -339 | 0 | -97 | -436 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | -250 | 0 | -95 | -345 |
| [oneday/QUICK\_TEST\_STEPS.md](/oneday/QUICK_TEST_STEPS.md) | Markdown | -85 | 0 | -26 | -111 |
| [oneday/README.md](/oneday/README.md) | Markdown | 4 | 0 | 4 | 8 |
| [oneday/REMOVE\_CAMERA\_FEATURE.md](/oneday/REMOVE_CAMERA_FEATURE.md) | Markdown | -105 | 0 | -35 | -140 |
| [oneday/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | -99 | 0 | -29 | -128 |
| [oneday/SHARE\_UI\_RESTORE\_FEATURE.md](/oneday/SHARE_UI_RESTORE_FEATURE.md) | Markdown | -120 | 0 | -35 | -155 |
| [oneday/STUDY\_SESSION\_COMPLETION\_IMPLEMENTATION.md](/oneday/STUDY_SESSION_COMPLETION_IMPLEMENTATION.md) | Markdown | 166 | 0 | 45 | 211 |
| [oneday/STUDY\_SESSION\_COMPLETION\_VERIFICATION.md](/oneday/STUDY_SESSION_COMPLETION_VERIFICATION.md) | Markdown | 153 | 0 | 38 | 191 |
| [oneday/SWIPE\_GESTURE\_DEMO.md](/oneday/SWIPE_GESTURE_DEMO.md) | Markdown | -216 | 0 | -48 | -264 |
| [oneday/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/oneday/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | -117 | 0 | -41 | -158 |
| [oneday/TESTING\_GUIDE.md](/oneday/TESTING_GUIDE.md) | Markdown | -103 | 0 | -34 | -137 |
| [oneday/TIMEBOX\_FLOATING\_TIMER\_FIXES.md](/oneday/TIMEBOX_FLOATING_TIMER_FIXES.md) | Markdown | 182 | 0 | 43 | 225 |
| [oneday/TIMEBOX\_REST\_SYNC\_FIX.md](/oneday/TIMEBOX_REST_SYNC_FIX.md) | Markdown | 147 | 0 | 36 | 183 |
| [oneday/TIMEBOX\_SEARCH\_REMOVAL.md](/oneday/TIMEBOX_SEARCH_REMOVAL.md) | Markdown | 151 | 0 | 40 | 191 |
| [oneday/TIMEBOX\_TIMER\_FIXES.md](/oneday/TIMEBOX_TIMER_FIXES.md) | Markdown | -115 | 0 | -27 | -142 |
| [oneday/TIMER\_FIXES\_PROGRESS.md](/oneday/TIMER_FIXES_PROGRESS.md) | Markdown | -131 | 0 | -35 | -166 |
| [oneday/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/oneday/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | -69 | 0 | -27 | -96 |
| [oneday/TIMER\_TEST\_GUIDE.md](/oneday/TIMER_TEST_GUIDE.md) | Markdown | -121 | 0 | -32 | -153 |
| [oneday/WORD\_TRAINING\_OPTIMIZATION.md](/oneday/WORD_TRAINING_OPTIMIZATION.md) | Markdown | -108 | 0 | -39 | -147 |
| [oneday/assets/data/Action.html](/oneday/assets/data/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [oneday/assets/data/vocabulary\_original.json](/oneday/assets/data/vocabulary_original.json) | JSON | -38,714 | 0 | -1 | -38,715 |
| [oneday/assets/data/动作.html](/oneday/assets/data/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [oneday/blueprint.md](/oneday/blueprint.md) | Markdown | -69 | 0 | -19 | -88 |
| [oneday/docs/DEVELOPER\_TOOLS\_UPDATE.md](/oneday/docs/DEVELOPER_TOOLS_UPDATE.md) | Markdown | 149 | 0 | 48 | 197 |
| [oneday/docs/NEW\_STORE\_ITEMS.md](/oneday/docs/NEW_STORE_ITEMS.md) | Markdown | 125 | 0 | 40 | 165 |
| [oneday/docs/PROFILE\_AVATAR\_CLICK\_UPDATE.md](/oneday/docs/PROFILE_AVATAR_CLICK_UPDATE.md) | Markdown | 108 | 0 | 37 | 145 |
| [oneday/docs/README.md](/oneday/docs/README.md) | Markdown | 71 | 0 | 20 | 91 |
| [oneday/docs/UI\_DEMO\_GUIDE.md](/oneday/docs/UI_DEMO_GUIDE.md) | Markdown | 91 | 0 | 22 | 113 |
| [oneday/docs/UI\_IMPROVEMENTS.md](/oneday/docs/UI_IMPROVEMENTS.md) | Markdown | 116 | 0 | 42 | 158 |
| [oneday/docs/core/ARCHITECTURE.md](/oneday/docs/core/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [oneday/docs/core/FEATURES.md](/oneday/docs/core/FEATURES.md) | Markdown | 601 | 0 | 120 | 721 |
| [oneday/docs/core/PRD.md](/oneday/docs/core/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [oneday/docs/core/README.md](/oneday/docs/core/README.md) | Markdown | 32 | 0 | 10 | 42 |
| [oneday/docs/development/COMMUNITY\_ARTICLE\_IMPORT\_FEATURE.md](/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md) | Markdown | 118 | 0 | 37 | 155 |
| [oneday/docs/development/CUSTOM\_EXERCISE\_CATEGORY\_FEATURE.md](/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md) | Markdown | 193 | 0 | 43 | 236 |
| [oneday/docs/development/EDIT\_ALBUM\_ENHANCEMENT.md](/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | 167 | 0 | 43 | 210 |
| [oneday/docs/development/FLOATING\_TIMER\_FEATURE.md](/oneday/docs/development/FLOATING_TIMER_FEATURE.md) | Markdown | 74 | 0 | 20 | 94 |
| [oneday/docs/development/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | 160 | 2 | 40 | 202 |
| [oneday/docs/development/NOTION\_STYLE\_UPDATES.md](/oneday/docs/development/NOTION_STYLE_UPDATES.md) | Markdown | 125 | 0 | 44 | 169 |
| [oneday/docs/development/POMODORO\_TIMER\_IMPLEMENTATION.md](/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md) | Markdown | 155 | 0 | 27 | 182 |
| [oneday/docs/development/README.md](/oneday/docs/development/README.md) | Markdown | 41 | 0 | 18 | 59 |
| [oneday/docs/development/REMOVE\_CAMERA\_FEATURE.md](/oneday/docs/development/REMOVE_CAMERA_FEATURE.md) | Markdown | 105 | 0 | 35 | 140 |
| [oneday/docs/development/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/docs/development/SHARE\_UI\_RESTORE\_FEATURE.md](/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md) | Markdown | 120 | 0 | 35 | 155 |
| [oneday/docs/development/SWIPE\_GESTURE\_DEMO.md](/oneday/docs/development/SWIPE_GESTURE_DEMO.md) | Markdown | 216 | 0 | 48 | 264 |
| [oneday/docs/development/VOCABULARY\_SCROLLBAR\_ENHANCEMENT.md](/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md) | Markdown | 126 | 0 | 38 | 164 |
| [oneday/docs/development/WORD\_DISPLAY\_LOWERCASE\_UPDATE.md](/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md) | Markdown | 117 | 0 | 30 | 147 |
| [oneday/docs/development/blueprint.md](/oneday/docs/development/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [oneday/docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_GUIDE.md](/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md) | Markdown | 142 | 0 | 37 | 179 |
| [oneday/docs/features/CREATE\_ACTION\_LIBRARY\_BUTTON\_IMPLEMENTATION\_SUMMARY.md](/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md) | Markdown | 204 | 0 | 45 | 249 |
| [oneday/docs/features/CUSTOM\_ACTION\_LIBRARY\_IMPLEMENTATION.md](/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md) | Markdown | 183 | 0 | 42 | 225 |
| [oneday/docs/features/CUSTOM\_ACTION\_LIBRARY\_USER\_GUIDE.md](/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md) | Markdown | 127 | 0 | 50 | 177 |
| [oneday/docs/features/DIALOG\_BACKGROUND\_BEFORE\_AFTER\_COMPARISON.md](/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md) | Markdown | 215 | 0 | 59 | 274 |
| [oneday/docs/features/DIALOG\_BACKGROUND\_UNIFICATION.md](/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md) | Markdown | 213 | 0 | 63 | 276 |
| [oneday/docs/features/FINAL\_DIALOG\_BACKGROUND\_REPORT.md](/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md) | Markdown | 145 | 0 | 40 | 185 |
| [oneday/docs/features/GRADUATE\_VOCABULARY\_MANAGER.md](/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md) | Markdown | 137 | 0 | 42 | 179 |
| [oneday/docs/features/VOCABULARY\_MANAGER\_IMPLEMENTATION\_SUMMARY.md](/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md) | Markdown | 156 | 0 | 42 | 198 |
| [oneday/docs/fixes/EXERCISE\_LIBRARY\_PAGE\_IMPORT\_FIX.md](/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md) | Markdown | 91 | 0 | 43 | 134 |
| [oneday/docs/guides/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [oneday/docs/guides/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | 199 | 0 | 60 | 259 |
| [oneday/docs/guides/ICON\_SETUP\_GUIDE.md](/oneday/docs/guides/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [oneday/docs/guides/README.md](/oneday/docs/guides/README.md) | Markdown | 91 | 0 | 27 | 118 |
| [oneday/docs/guides/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | 117 | 0 | 41 | 158 |
| [oneday/docs/guides/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | 69 | 0 | 27 | 96 |
| [oneday/docs/releases/README.md](/oneday/docs/releases/README.md) | Markdown | 107 | 0 | 30 | 137 |
| [oneday/docs/testing/COMPREHENSIVE\_TEST\_GUIDE.md](/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | 276 | 0 | 96 | 372 |
| [oneday/docs/testing/COVER\_MODE\_TEST\_GUIDE.md](/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [oneday/docs/testing/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | 114 | 0 | 27 | 141 |
| [oneday/docs/testing/POMODORO\_TEST\_GUIDE.md](/oneday/docs/testing/POMODORO_TEST_GUIDE.md) | Markdown | 105 | 0 | 20 | 125 |
| [oneday/docs/testing/QUICK\_TEST\_STEPS.md](/oneday/docs/testing/QUICK_TEST_STEPS.md) | Markdown | 85 | 0 | 26 | 111 |
| [oneday/docs/testing/README.md](/oneday/docs/testing/README.md) | Markdown | 92 | 0 | 30 | 122 |
| [oneday/docs/testing/TESTING\_GUIDE.md](/oneday/docs/testing/TESTING_GUIDE.md) | Markdown | 103 | 0 | 34 | 137 |
| [oneday/docs/testing/TIMER\_TEST\_GUIDE.md](/oneday/docs/testing/TIMER_TEST_GUIDE.md) | Markdown | 121 | 0 | 32 | 153 |
| [oneday/docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | 154 | 0 | 43 | 197 |
| [oneday/docs/troubleshooting/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | 144 | 0 | 43 | 187 |
| [oneday/docs/troubleshooting/BOTTOM\_NAVIGATION\_BAR\_FIX.md](/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md) | Markdown | 244 | 0 | 50 | 294 |
| [oneday/docs/troubleshooting/CALENDAR\_TASK\_DISPLAY\_FIX.md](/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md) | Markdown | 219 | 0 | 39 | 258 |
| [oneday/docs/troubleshooting/COLOR\_SYSTEM\_RESTORATION.md](/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md) | Markdown | 134 | 0 | 34 | 168 |
| [oneday/docs/troubleshooting/COMPREHENSIVE\_NAVIGATION\_AUDIT.md](/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md) | Markdown | 192 | 0 | 48 | 240 |
| [oneday/docs/troubleshooting/DIALOG\_LAYOUT\_BUGFIX.md](/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md) | Markdown | 206 | 0 | 42 | 248 |
| [oneday/docs/troubleshooting/DIALOG\_LAYOUT\_OPTIMIZATION.md](/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md) | Markdown | 192 | 0 | 46 | 238 |
| [oneday/docs/troubleshooting/EXERCISE\_LIBRARY\_UI\_OPTIMIZATION.md](/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md) | Markdown | 111 | 0 | 36 | 147 |
| [oneday/docs/troubleshooting/FINAL\_NAVIGATION\_TEST\_REPORT.md](/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md) | Markdown | 118 | 0 | 38 | 156 |
| [oneday/docs/troubleshooting/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | 190 | 0 | 50 | 240 |
| [oneday/docs/troubleshooting/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | 142 | 0 | 35 | 177 |
| [oneday/docs/troubleshooting/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | 129 | 0 | 35 | 164 |
| [oneday/docs/troubleshooting/HOW\_TO\_TEST\_NAVIGATION\_FIX.md](/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md) | Markdown | 147 | 0 | 51 | 198 |
| [oneday/docs/troubleshooting/IPAD\_STAGE\_MANAGER\_LAYOUT\_FIX.md](/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md) | Markdown | 200 | 0 | 31 | 231 |
| [oneday/docs/troubleshooting/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | 191 | 0 | 42 | 233 |
| [oneday/docs/troubleshooting/NAVIGATION\_FIX\_PROGRESS\_REPORT.md](/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md) | Markdown | 121 | 0 | 38 | 159 |
| [oneday/docs/troubleshooting/NAVIGATION\_FIX\_SUMMARY.md](/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md) | Markdown | 133 | 0 | 39 | 172 |
| [oneday/docs/troubleshooting/PROBLEM\_SOLUTIONS.md](/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md) | Markdown | 288 | 0 | 106 | 394 |
| [oneday/docs/troubleshooting/README.md](/oneday/docs/troubleshooting/README.md) | Markdown | 54 | 0 | 22 | 76 |
| [oneday/docs/troubleshooting/SIDEBAR\_BUTTON\_OPTIMIZATION.md](/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md) | Markdown | 147 | 0 | 37 | 184 |
| [oneday/docs/troubleshooting/TIMEBOX\_REST\_SKIP\_NAVIGATION\_FIX.md](/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md) | Markdown | 157 | 0 | 52 | 209 |
| [oneday/docs/troubleshooting/TIMEBOX\_TIMER\_FIXES.md](/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md) | Markdown | 115 | 0 | 27 | 142 |
| [oneday/docs/troubleshooting/TIMER\_FIXES\_PROGRESS.md](/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md) | Markdown | 131 | 0 | 35 | 166 |
| [oneday/docs/troubleshooting/UI\_OVERFLOW\_FIXES\_SUMMARY.md](/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md) | Markdown | 98 | 0 | 29 | 127 |
| [oneday/docs/troubleshooting/VOCABULARY\_SERVICE\_FIX.md](/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md) | Markdown | 219 | 0 | 52 | 271 |
| [oneday/docs/troubleshooting/WORD\_TRAINING\_OPTIMIZATION.md](/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md) | Markdown | 108 | 0 | 39 | 147 |
| [oneday/docs/ui\_improvements/CATEGORY\_SELECTOR\_PURE\_TEXT\_FIX.md](/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md) | Markdown | 118 | 0 | 28 | 146 |
| [oneday/docs/ui\_improvements/IPAD\_CALENDAR\_OVERFLOW\_FIX\_COMPLETE.md](/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md) | Markdown | 129 | 0 | 33 | 162 |
| [oneday/docs/ui\_improvements/IPAD\_CALENDAR\_RESPONSIVE\_FIX.md](/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md) | Markdown | 178 | 0 | 38 | 216 |
| [oneday/docs/ui\_improvements/REMOVE\_DUPLICATE\_MENU\_ITEMS.md](/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md) | Markdown | 111 | 0 | 36 | 147 |
| [oneday/example/calendar\_responsive\_demo.dart](/oneday/example/calendar_responsive_demo.dart) | Dart | 342 | 16 | 24 | 382 |
| [oneday/example/calendar\_task\_display\_demo.dart](/oneday/example/calendar_task_display_demo.dart) | Dart | 373 | 19 | 25 | 417 |
| [oneday/example/color\_scheme\_comparison\_demo.dart](/oneday/example/color_scheme_comparison_demo.dart) | Dart | 412 | 16 | 31 | 459 |
| [oneday/example/navigation\_bottom\_bar\_demo.dart](/oneday/example/navigation_bottom_bar_demo.dart) | Dart | 366 | 9 | 15 | 390 |
| [oneday/example/responsive\_layout\_demo.dart](/oneday/example/responsive_layout_demo.dart) | Dart | 560 | 13 | 26 | 599 |
| [oneday/example/study\_session\_completion\_demo.dart](/oneday/example/study_session_completion_demo.dart) | Dart | 427 | 7 | 24 | 458 |
| [oneday/example/timebox\_rest\_skip\_demo.dart](/oneday/example/timebox_rest_skip_demo.dart) | Dart | 449 | 24 | 40 | 513 |
| [oneday/lib/core/data/pao\_exercises\_data.dart](/oneday/lib/core/data/pao_exercises_data.dart) | Dart | 45 | 10 | 8 | 63 |
| [oneday/lib/debug/route\_debug\_page.dart](/oneday/lib/debug/route_debug_page.dart) | Dart | 268 | 10 | 15 | 293 |
| [oneday/lib/features/ability\_radar/models/ability\_radar\_models.dart](/oneday/lib/features/ability_radar/models/ability_radar_models.dart) | Dart | 169 | 39 | 56 | 264 |
| [oneday/lib/features/ability\_radar/models/ability\_radar\_models.g.dart](/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart) | Dart | 199 | 32 | 18 | 249 |
| [oneday/lib/features/ability\_radar/pages/ability\_radar\_page.dart](/oneday/lib/features/ability_radar/pages/ability_radar_page.dart) | Dart | 448 | 41 | 41 | 530 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_mock\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart) | Dart | 173 | 5 | 6 | 184 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart) | Dart | 282 | 21 | 57 | 360 |
| [oneday/lib/features/ability\_radar/services/ability\_radar\_service.dart](/oneday/lib/features/ability_radar/services/ability_radar_service.dart) | Dart | 467 | 45 | 106 | 618 |
| [oneday/lib/features/ability\_radar/widgets/ability\_radar\_chart.dart](/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart) | Dart | 284 | 18 | 23 | 325 |
| [oneday/lib/features/achievement/README.md](/oneday/lib/features/achievement/README.md) | Markdown | 132 | 0 | 41 | 173 |
| [oneday/lib/features/achievement/data/achievements\_data.dart](/oneday/lib/features/achievement/data/achievements_data.dart) | Dart | 444 | 18 | 12 | 474 |
| [oneday/lib/features/achievement/models/achievement.dart](/oneday/lib/features/achievement/models/achievement.dart) | Dart | 169 | 29 | 39 | 237 |
| [oneday/lib/features/achievement/models/achievement.g.dart](/oneday/lib/features/achievement/models/achievement.g.dart) | Dart | 125 | 24 | 11 | 160 |
| [oneday/lib/features/achievement/models/badge.dart](/oneday/lib/features/achievement/models/badge.dart) | Dart | 230 | 50 | 69 | 349 |
| [oneday/lib/features/achievement/models/badge.g.dart](/oneday/lib/features/achievement/models/badge.g.dart) | Dart | 184 | 40 | 17 | 241 |
| [oneday/lib/features/achievement/models/user\_level.dart](/oneday/lib/features/achievement/models/user_level.dart) | Dart | 264 | 41 | 48 | 353 |
| [oneday/lib/features/achievement/models/user\_level.g.dart](/oneday/lib/features/achievement/models/user_level.g.dart) | Dart | 91 | 21 | 11 | 123 |
| [oneday/lib/features/achievement/pages/achievement\_page.dart](/oneday/lib/features/achievement/pages/achievement_page.dart) | Dart | 485 | 21 | 39 | 545 |
| [oneday/lib/features/achievement/pages/leaderboard\_page.dart](/oneday/lib/features/achievement/pages/leaderboard_page.dart) | Dart | 336 | 13 | 20 | 369 |
| [oneday/lib/features/achievement/providers/achievement\_provider.dart](/oneday/lib/features/achievement/providers/achievement_provider.dart) | Dart | 224 | 38 | 44 | 306 |
| [oneday/lib/features/achievement/services/achievement\_service.dart](/oneday/lib/features/achievement/services/achievement_service.dart) | Dart | 322 | 38 | 64 | 424 |
| [oneday/lib/features/achievement/services/achievement\_trigger\_service.dart](/oneday/lib/features/achievement/services/achievement_trigger_service.dart) | Dart | 113 | 61 | 63 | 237 |
| [oneday/lib/features/achievement/widgets/achievement\_grid.dart](/oneday/lib/features/achievement/widgets/achievement_grid.dart) | Dart | 380 | 19 | 32 | 431 |
| [oneday/lib/features/achievement/widgets/achievement\_unlock\_notification.dart](/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart) | Dart | 300 | 20 | 45 | 365 |
| [oneday/lib/features/achievement/widgets/skill\_levels\_card.dart](/oneday/lib/features/achievement/widgets/skill_levels_card.dart) | Dart | 323 | 15 | 42 | 380 |
| [oneday/lib/features/achievement/widgets/user\_level\_card.dart](/oneday/lib/features/achievement/widgets/user_level_card.dart) | Dart | 326 | 7 | 29 | 362 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 192 | 23 | 12 | 227 |
| [oneday/lib/features/community/article\_import\_service.dart](/oneday/lib/features/community/article_import_service.dart) | Dart | 191 | 26 | 40 | 257 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/lib/features/community/community\_post\_editor\_page.dart](/oneday/lib/features/community/community_post_editor_page.dart) | Dart | 679 | 38 | 59 | 776 |
| [oneday/lib/features/exercise/create\_custom\_library\_dialog.dart](/oneday/lib/features/exercise/create_custom_library_dialog.dart) | Dart | 276 | 7 | 17 | 300 |
| [oneday/lib/features/exercise/custom\_action\_editor\_dialog.dart](/oneday/lib/features/exercise/custom_action_editor_dialog.dart) | Dart | 500 | 17 | 37 | 554 |
| [oneday/lib/features/exercise/custom\_action\_library.dart](/oneday/lib/features/exercise/custom_action_library.dart) | Dart | 203 | 25 | 29 | 257 |
| [oneday/lib/features/exercise/custom\_action\_library\_service.dart](/oneday/lib/features/exercise/custom_action_library_service.dart) | Dart | 227 | 29 | 46 | 302 |
| [oneday/lib/features/exercise/custom\_exercise\_category.dart](/oneday/lib/features/exercise/custom_exercise_category.dart) | Dart | 164 | 15 | 23 | 202 |
| [oneday/lib/features/exercise/custom\_library\_editor\_page.dart](/oneday/lib/features/exercise/custom_library_editor_page.dart) | Dart | 432 | 18 | 30 | 480 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 564 | 35 | 44 | 643 |
| [oneday/lib/features/exercise/focused\_training\_page.dart](/oneday/lib/features/exercise/focused_training_page.dart) | Dart | 38 | 3 | 6 | 47 |
| [oneday/lib/features/exercise/manage\_custom\_libraries\_dialog.dart](/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart) | Dart | 641 | 15 | 28 | 684 |
| [oneday/lib/features/exercise/pao\_integration\_service.dart](/oneday/lib/features/exercise/pao_integration_service.dart) | Dart | 188 | 32 | 46 | 266 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/learning\_report/learning\_report\_page.dart](/oneday/lib/features/learning_report/learning_report_page.dart) | Dart | 757 | 42 | 54 | 853 |
| [oneday/lib/features/learning\_report/models/learning\_report\_models.dart](/oneday/lib/features/learning_report/models/learning_report_models.dart) | Dart | 203 | 61 | 77 | 341 |
| [oneday/lib/features/learning\_report/models/learning\_report\_models.g.dart](/oneday/lib/features/learning_report/models/learning_report_models.g.dart) | Dart | 301 | 51 | 25 | 377 |
| [oneday/lib/features/learning\_report/services/learning\_report\_service.dart](/oneday/lib/features/learning_report/services/learning_report_service.dart) | Dart | 474 | 61 | 100 | 635 |
| [oneday/lib/features/learning\_report/widgets/chart\_widgets.dart](/oneday/lib/features/learning_report/widgets/chart_widgets.dart) | Dart | 764 | 14 | 30 | 808 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 49 | 4 | 5 | 58 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 274 | 58 | 74 | 406 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_backup.dart](/oneday/lib/features/memory_palace/scene_detail_page_backup.dart) | Dart | -2,394 | -224 | -292 | -2,910 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 204 | 4 | -1 | 207 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 72 | 5 | 5 | 82 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 74 | 7 | 9 | 90 |
| [oneday/lib/features/study\_time/models/study\_time\_models.dart](/oneday/lib/features/study_time/models/study_time_models.dart) | Dart | 264 | 58 | 68 | 390 |
| [oneday/lib/features/study\_time/models/study\_time\_models.g.dart](/oneday/lib/features/study_time/models/study_time_models.g.dart) | Dart | 175 | 34 | 12 | 221 |
| [oneday/lib/features/study\_time/providers/study\_time\_providers.dart](/oneday/lib/features/study_time/providers/study_time_providers.dart) | Dart | 241 | 26 | 46 | 313 |
| [oneday/lib/features/study\_time/services/study\_time\_statistics\_service.dart](/oneday/lib/features/study_time/services/study_time_statistics_service.dart) | Dart | 319 | 60 | 79 | 458 |
| [oneday/lib/features/study\_time/test\_data\_sync\_page.dart](/oneday/lib/features/study_time/test_data_sync_page.dart) | Dart | 251 | 11 | 17 | 279 |
| [oneday/lib/features/time\_box/models/timebox\_models.dart](/oneday/lib/features/time_box/models/timebox_models.dart) | Dart | 29 | 1 | 5 | 35 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 441 | 61 | 50 | 552 |
| [oneday/lib/features/time\_box/widgets/study\_session\_completion\_dialog.dart](/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart) | Dart | 405 | 6 | 21 | 432 |
| [oneday/lib/features/vocabulary/fsrs\_algorithm.dart](/oneday/lib/features/vocabulary/fsrs_algorithm.dart) | Dart | 221 | 41 | 40 | 302 |
| [oneday/lib/features/vocabulary/fsrs\_service.dart](/oneday/lib/features/vocabulary/fsrs_service.dart) | Dart | 359 | 61 | 78 | 498 |
| [oneday/lib/features/vocabulary/graduate\_vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart) | Dart | 597 | 30 | 50 | 677 |
| [oneday/lib/features/vocabulary/vocabulary\_category\_page.dart](/oneday/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 46 | 5 | 6 | 57 |
| [oneday/lib/features/vocabulary/vocabulary\_data\_converter.dart](/oneday/lib/features/vocabulary/vocabulary_data_converter.dart) | Dart | -194 | -31 | -40 | -265 |
| [oneday/lib/features/vocabulary/vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | 13 | 2 | 3 | 18 |
| [oneday/lib/features/vocabulary/vocabulary\_service.dart](/oneday/lib/features/vocabulary/vocabulary_service.dart) | Dart | 114 | 11 | 31 | 156 |
| [oneday/lib/features/vocabulary/word\_model.dart](/oneday/lib/features/vocabulary/word_model.dart) | Dart | 137 | 14 | 9 | 160 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 220 | 20 | 19 | 259 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 10 | 0 | -1 | 9 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 95 | 12 | 13 | 120 |
| [oneday/lib/services/app\_initialization\_service.dart](/oneday/lib/services/app_initialization_service.dart) | Dart | 60 | 16 | 19 | 95 |
| [oneday/lib/services/floating\_timer\_service.dart](/oneday/lib/services/floating_timer_service.dart) | Dart | 38 | 12 | 10 | 60 |
| [oneday/lib/services/global\_timer\_service.dart](/oneday/lib/services/global_timer_service.dart) | Dart | 163 | 43 | 44 | 250 |
| [oneday/lib/services/providers/study\_session\_completion\_provider.dart](/oneday/lib/services/providers/study_session_completion_provider.dart) | Dart | 19 | 2 | 4 | 25 |
| [oneday/lib/services/study\_session\_completion\_service.dart](/oneday/lib/services/study_session_completion_service.dart) | Dart | 185 | 37 | 45 | 267 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 1 | 1 | 1 | 3 |
| [oneday/scripts/verify\_navigation\_fix.dart](/oneday/scripts/verify_navigation_fix.dart) | Dart | 175 | 20 | 45 | 240 |
| [oneday/test/calendar\_task\_display\_test.dart](/oneday/test/calendar_task_display_test.dart) | Dart | 251 | 13 | 36 | 300 |
| [oneday/test/date\_format\_test.dart](/oneday/test/date_format_test.dart) | Dart | 44 | 5 | 13 | 62 |
| [oneday/test/developer\_tools\_test.dart](/oneday/test/developer_tools_test.dart) | Dart | 132 | 12 | 29 | 173 |
| [oneday/test/exercise\_library\_ui\_test.dart](/oneday/test/exercise_library_ui_test.dart) | Dart | 156 | 31 | 55 | 242 |
| [oneday/test/features/exercise/custom\_action\_library\_ui\_test.dart](/oneday/test/features/exercise/custom_action_library_ui_test.dart) | Dart | 184 | 27 | 39 | 250 |
| [oneday/test/features/exercise/exercise\_library\_page\_import\_test.dart](/oneday/test/features/exercise/exercise_library_page_import_test.dart) | Dart | 21 | 5 | 6 | 32 |
| [oneday/test/features/study\_time/study\_time\_statistics\_test.dart](/oneday/test/features/study_time/study_time_statistics_test.dart) | Dart | 264 | 13 | 38 | 315 |
| [oneday/test/features/time\_box/timebox\_ui\_test.dart](/oneday/test/features/time_box/timebox_ui_test.dart) | Dart | 125 | 11 | 18 | 154 |
| [oneday/test/features/ui/color\_consistency\_test.dart](/oneday/test/features/ui/color_consistency_test.dart) | Dart | 208 | 19 | 30 | 257 |
| [oneday/test/features/ui/dialog\_background\_simple\_test.dart](/oneday/test/features/ui/dialog_background_simple_test.dart) | Dart | 198 | 17 | 23 | 238 |
| [oneday/test/features/ui/dialog\_background\_test.dart](/oneday/test/features/ui/dialog_background_test.dart) | Dart | 228 | 23 | 32 | 283 |
| [oneday/test/features/ui/dropdown\_background\_test.dart](/oneday/test/features/ui/dropdown_background_test.dart) | Dart | 352 | 25 | 25 | 402 |
| [oneday/test/features/ui/dropdown\_pure\_white\_test.dart](/oneday/test/features/ui/dropdown_pure_white_test.dart) | Dart | 262 | 14 | 22 | 298 |
| [oneday/test/features/ui/final\_verification\_test.dart](/oneday/test/features/ui/final_verification_test.dart) | Dart | 233 | 24 | 29 | 286 |
| [oneday/test/features/ui/material3\_surface\_tinting\_fix\_test.dart](/oneday/test/features/ui/material3_surface_tinting_fix_test.dart) | Dart | 252 | 16 | 23 | 291 |
| [oneday/test/graduate\_vocabulary\_manager\_test.dart](/oneday/test/graduate_vocabulary_manager_test.dart) | Dart | 59 | 6 | 20 | 85 |
| [oneday/test/integration/study\_session\_completion\_integration\_test.dart](/oneday/test/integration/study_session_completion_integration_test.dart) | Dart | 108 | 51 | 54 | 213 |
| [oneday/test/ipad\_calendar\_overflow\_test.dart](/oneday/test/ipad_calendar_overflow_test.dart) | Dart | 119 | 21 | 38 | 178 |
| [oneday/test/learning\_efficiency\_metrics\_test.dart](/oneday/test/learning_efficiency_metrics_test.dart) | Dart | 145 | 10 | 13 | 168 |
| [oneday/test/navigation\_bottom\_bar\_test.dart](/oneday/test/navigation_bottom_bar_test.dart) | Dart | 164 | 37 | 50 | 251 |
| [oneday/test/profile\_avatar\_click\_test.dart](/oneday/test/profile_avatar_click_test.dart) | Dart | 137 | 16 | 33 | 186 |
| [oneday/test/reflection\_calendar\_responsive\_test.dart](/oneday/test/reflection_calendar_responsive_test.dart) | Dart | 140 | 30 | 49 | 219 |
| [oneday/test/reflection\_log\_integration\_test.dart](/oneday/test/reflection_log_integration_test.dart) | Dart | 61 | 11 | 19 | 91 |
| [oneday/test/responsive\_layout\_test.dart](/oneday/test/responsive_layout_test.dart) | Dart | 230 | 12 | 24 | 266 |
| [oneday/test/services/study\_session\_completion\_service\_test.dart](/oneday/test/services/study_session_completion_service_test.dart) | Dart | 223 | 32 | 39 | 294 |
| [oneday/test/store\_new\_items\_test.dart](/oneday/test/store_new_items_test.dart) | Dart | 95 | 13 | 27 | 135 |
| [oneday/test/test\_bubble\_alignment\_adjustment.dart](/oneday/test/test_bubble_alignment_adjustment.dart) | Dart | 103 | 9 | 23 | 135 |
| [oneday/test/test\_bubble\_alignment\_verification.dart](/oneday/test/test_bubble_alignment_verification.dart) | Dart | 103 | 10 | 26 | 139 |
| [oneday/test/test\_bubble\_center\_alignment.dart](/oneday/test/test_bubble_center_alignment.dart) | Dart | 92 | 8 | 20 | 120 |
| [oneday/test/test\_bubble\_positioning\_fix.dart](/oneday/test/test_bubble_positioning_fix.dart) | Dart | 109 | 6 | 27 | 142 |
| [oneday/test/test\_compilation\_fix.dart](/oneday/test/test_compilation_fix.dart) | Dart | 144 | 7 | 29 | 180 |
| [oneday/test/test\_complete\_image\_compression.dart](/oneday/test/test_complete_image_compression.dart) | Dart | 186 | 9 | 38 | 233 |
| [oneday/test/test\_coordinate\_analysis.dart](/oneday/test/test_coordinate_analysis.dart) | Dart | 104 | 9 | 27 | 140 |
| [oneday/test/test\_coordinate\_debug.dart](/oneday/test/test_coordinate_debug.dart) | Dart | 84 | 5 | 18 | 107 |
| [oneday/test/test\_coordinate\_transformation\_fix.dart](/oneday/test/test_coordinate_transformation_fix.dart) | Dart | 110 | 16 | 24 | 150 |
| [oneday/test/test\_image\_compression\_implementation.dart](/oneday/test/test_image_compression_implementation.dart) | Dart | 147 | 7 | 33 | 187 |
| [oneday/test/test\_matrix\_analysis.dart](/oneday/test/test_matrix_analysis.dart) | Dart | 130 | 8 | 37 | 175 |
| [oneday/test/test\_matrix\_fix\_verification.dart](/oneday/test/test_matrix_fix_verification.dart) | Dart | 105 | 14 | 31 | 150 |
| [oneday/test/test\_position\_fix\_verification.dart](/oneday/test/test_position_fix_verification.dart) | Dart | 38 | 4 | 11 | 53 |
| [oneday/test/timebox\_rest\_skip\_test.dart](/oneday/test/timebox_rest_skip_test.dart) | Dart | 201 | 20 | 39 | 260 |
| [oneday/test/ui\_overflow\_test.dart](/oneday/test/ui_overflow_test.dart) | Dart | 101 | 20 | 32 | 153 |
| [oneday/test/vocabulary\_scrollbar\_test.dart](/oneday/test/vocabulary_scrollbar_test.dart) | Dart | 98 | 8 | 22 | 128 |
| [oneday/test\_bubble\_alignment\_adjustment.dart](/oneday/test_bubble_alignment_adjustment.dart) | Dart | -103 | -9 | -23 | -135 |
| [oneday/test\_bubble\_alignment\_verification.dart](/oneday/test_bubble_alignment_verification.dart) | Dart | -103 | -10 | -26 | -139 |
| [oneday/test\_bubble\_center\_alignment.dart](/oneday/test_bubble_center_alignment.dart) | Dart | -92 | -8 | -20 | -120 |
| [oneday/test\_bubble\_positioning\_fix.dart](/oneday/test_bubble_positioning_fix.dart) | Dart | -109 | -6 | -27 | -142 |
| [oneday/test\_compilation\_fix.dart](/oneday/test_compilation_fix.dart) | Dart | -144 | -7 | -29 | -180 |
| [oneday/test\_complete\_image\_compression.dart](/oneday/test_complete_image_compression.dart) | Dart | -186 | -9 | -38 | -233 |
| [oneday/test\_coordinate\_analysis.dart](/oneday/test_coordinate_analysis.dart) | Dart | -104 | -9 | -27 | -140 |
| [oneday/test\_coordinate\_debug.dart](/oneday/test_coordinate_debug.dart) | Dart | -84 | -5 | -18 | -107 |
| [oneday/test\_coordinate\_transformation\_fix.dart](/oneday/test_coordinate_transformation_fix.dart) | Dart | -110 | -16 | -24 | -150 |
| [oneday/test\_image\_compression\_implementation.dart](/oneday/test_image_compression_implementation.dart) | Dart | -147 | -7 | -33 | -187 |
| [oneday/test\_matrix\_analysis.dart](/oneday/test_matrix_analysis.dart) | Dart | -130 | -8 | -37 | -175 |
| [oneday/test\_matrix\_fix\_verification.dart](/oneday/test_matrix_fix_verification.dart) | Dart | -105 | -14 | -31 | -150 |
| [oneday/test\_position\_fix\_verification.dart](/oneday/test_position_fix_verification.dart) | Dart | -38 | -4 | -11 | -53 |
| [oneday/动作.html](/oneday/%E5%8A%A8%E4%BD%9C.html) | HTML | -693 | -23 | -53 | -769 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details