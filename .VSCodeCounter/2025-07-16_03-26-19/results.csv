"filename", "language", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Markdown", "C++", "C<PERSON>ake", "<PERSON><PERSON><PERSON>", "Dar<PERSON>", "Python", "Ruby", "Properties", "XML", "Swift", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md", "Markdown", 0, 0, 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 508
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md", "Markdown", 0, 0, 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 125, 553
"/Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md", "Markdown", 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 192
"/Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md", "Markdown", 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 152
"/Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md", "Markdown", 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 369
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/.augment/rules/Augrules.backup.md", "Markdown", 0, 0, 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 508
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/.augment/rules/Augrules.md", "Markdown", 0, 0, 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 125, 553
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/README.md", "Markdown", 0, 0, 283, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 93, 376
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/analysis_options.yaml", "YAML", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 22, 4, 29
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 0, 11, 1, 46
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/app/src/profile/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/gradle.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/android/gradle/wrapper/gradle-wrapper.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 1, 6
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 0, 3, 10, 44
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 2, 14
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 123
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 1, 1, 38
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 1, 1, 27
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/main.dart", "Dart", 0, 0, 0, 0, 0, 0, 79, 0, 0, 0, 0, 0, 42, 16, 137
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/calculation_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 713, 0, 0, 0, 0, 0, 39, 43, 795
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/cost_analysis_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 546, 0, 0, 0, 0, 0, 30, 40, 616
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/history_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 652, 0, 0, 0, 0, 0, 42, 54, 748
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/home_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 493, 0, 0, 0, 0, 0, 30, 25, 548
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/mode_selection_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 569, 0, 0, 0, 0, 0, 38, 32, 639
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/onboarding_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 253, 0, 0, 0, 0, 0, 30, 24, 307
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/optimization_tips_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 799, 0, 0, 0, 0, 0, 44, 62, 905
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/room_setup_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 494, 0, 0, 0, 0, 0, 32, 30, 556
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/settings_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 586, 0, 0, 0, 0, 0, 64, 45, 695
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/splash_screen.dart", "Dart", 0, 0, 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 33, 24, 281
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/CMakeLists.txt", "CMake", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 25, 129
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 10, 89
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 12
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 6, 24
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/main.cc", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/my_application.cc", "C++", 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 131
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/linux/runner/my_application.h", "C++", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 3, 4, 13
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 1, 10, 43
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 3, 14
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 69
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 1, 344
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 4, 16
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/pubspec.yaml", "YAML", 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 63, 17, 101
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/test/widget_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 14, 0, 0, 0, 0, 0, 10, 7, 31
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/web/index.html", "HTML", 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 5, 39
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/web/manifest.json", "JSON", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 36
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/CMakeLists.txt", "CMake", 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 12, 110
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 12
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 0, 6, 24
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 7, 41
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/flutter_window.cpp", "C++", 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/flutter_window.h", "C++", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/main.cpp", "C++", 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/resource.h", "C++", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/utils.cpp", "C++", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/utils.h", "C++", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/win32_window.cpp", "C++", 0, 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/windows/runner/win32_window.h", "C++", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 31, 24, 103
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.md", "Markdown", 0, 0, 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 496
"/Users/<USER>/development/flutter_apps/oneday/ABILITY_RADAR_FEATURE.md", "Markdown", 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 184
"/Users/<USER>/development/flutter_apps/oneday/ACHIEVEMENT_SYSTEM_SUMMARY.md", "Markdown", 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 220
"/Users/<USER>/development/flutter_apps/oneday/ACHIEVEMENT_SYSTEM_TEST_GUIDE.md", "Markdown", 0, 0, 87, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 115
"/Users/<USER>/development/flutter_apps/oneday/COMPREHENSIVE_NAVIGATION_FIX_SUMMARY.md", "Markdown", 0, 0, 176, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 222
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_LIFECYCLE_FIX.md", "Markdown", 0, 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 188
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_REST_FINAL_REPORT.md", "Markdown", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 155
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_REST_SYNC_FIX.md", "Markdown", 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 257
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_UI_IMPROVEMENTS.md", "Markdown", 0, 0, 141, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 176
"/Users/<USER>/development/flutter_apps/oneday/HOME_PAGE_CLEANUP_SUMMARY.md", "Markdown", 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 97
"/Users/<USER>/development/flutter_apps/oneday/NAVIGATION_FIX_COMPLETE.md", "Markdown", 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 177
"/Users/<USER>/development/flutter_apps/oneday/README.md", "Markdown", 0, 0, 1167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 263, 1430
"/Users/<USER>/development/flutter_apps/oneday/STUDY_SESSION_COMPLETION_IMPLEMENTATION.md", "Markdown", 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 211
"/Users/<USER>/development/flutter_apps/oneday/STUDY_SESSION_COMPLETION_VERIFICATION.md", "Markdown", 0, 0, 153, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 191
"/Users/<USER>/development/flutter_apps/oneday/TIMEBOX_FLOATING_TIMER_FIXES.md", "Markdown", 0, 0, 182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 225
"/Users/<USER>/development/flutter_apps/oneday/TIMEBOX_REST_SYNC_FIX.md", "Markdown", 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 183
"/Users/<USER>/development/flutter_apps/oneday/TIMEBOX_SEARCH_REMOVAL.md", "Markdown", 0, 0, 151, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 191
"/Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml", "YAML", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 22, 4, 29
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 13, 3, 57
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/gradle.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 1, 6
"/Users/<USER>/development/flutter_apps/oneday/assets/data/Action.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json", "JSON", 0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 89
"/Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json", "JSON", 0, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 100
"/Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json", "JSON", 0, 84516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84516
"/Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json", "JSON", 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 159
"/Users/<USER>/development/flutter_apps/oneday/assets/data/动作.html", "HTML", 693, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md", "Markdown", 0, 0, 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 46
"/Users/<USER>/development/flutter_apps/oneday/build.yaml", "YAML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 6, 1, 18
"/Users/<USER>/development/flutter_apps/oneday/docs/DEVELOPER_TOOLS_UPDATE.md", "Markdown", 0, 0, 149, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 197
"/Users/<USER>/development/flutter_apps/oneday/docs/NEW_STORE_ITEMS.md", "Markdown", 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 165
"/Users/<USER>/development/flutter_apps/oneday/docs/PROFILE_AVATAR_CLICK_UPDATE.md", "Markdown", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 145
"/Users/<USER>/development/flutter_apps/oneday/docs/README.md", "Markdown", 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 91
"/Users/<USER>/development/flutter_apps/oneday/docs/UI_DEMO_GUIDE.md", "Markdown", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 113
"/Users/<USER>/development/flutter_apps/oneday/docs/UI_IMPROVEMENTS.md", "Markdown", 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 158
"/Users/<USER>/development/flutter_apps/oneday/docs/core/ARCHITECTURE.md", "Markdown", 0, 0, 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 461
"/Users/<USER>/development/flutter_apps/oneday/docs/core/FEATURES.md", "Markdown", 0, 0, 601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 721
"/Users/<USER>/development/flutter_apps/oneday/docs/core/PRD.md", "Markdown", 0, 0, 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 436
"/Users/<USER>/development/flutter_apps/oneday/docs/core/README.md", "Markdown", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 42
"/Users/<USER>/development/flutter_apps/oneday/docs/development/COMMUNITY_ARTICLE_IMPORT_FEATURE.md", "Markdown", 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 155
"/Users/<USER>/development/flutter_apps/oneday/docs/development/CUSTOM_EXERCISE_CATEGORY_FEATURE.md", "Markdown", 0, 0, 193, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 236
"/Users/<USER>/development/flutter_apps/oneday/docs/development/EDIT_ALBUM_ENHANCEMENT.md", "Markdown", 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 210
"/Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_FEATURE.md", "Markdown", 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 94
"/Users/<USER>/development/flutter_apps/oneday/docs/development/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md", "Markdown", 0, 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 40, 202
"/Users/<USER>/development/flutter_apps/oneday/docs/development/NOTION_STYLE_UPDATES.md", "Markdown", 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 169
"/Users/<USER>/development/flutter_apps/oneday/docs/development/POMODORO_TIMER_IMPLEMENTATION.md", "Markdown", 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 182
"/Users/<USER>/development/flutter_apps/oneday/docs/development/README.md", "Markdown", 0, 0, 41, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 59
"/Users/<USER>/development/flutter_apps/oneday/docs/development/REMOVE_CAMERA_FEATURE.md", "Markdown", 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 140
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_FUNCTION_SIMPLIFICATION.md", "Markdown", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 128
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SHARE_UI_RESTORE_FEATURE.md", "Markdown", 0, 0, 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 155
"/Users/<USER>/development/flutter_apps/oneday/docs/development/SWIPE_GESTURE_DEMO.md", "Markdown", 0, 0, 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 264
"/Users/<USER>/development/flutter_apps/oneday/docs/development/VOCABULARY_SCROLLBAR_ENHANCEMENT.md", "Markdown", 0, 0, 126, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 164
"/Users/<USER>/development/flutter_apps/oneday/docs/development/WORD_DISPLAY_LOWERCASE_UPDATE.md", "Markdown", 0, 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/development/blueprint.md", "Markdown", 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 88
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_GUIDE.md", "Markdown", 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 179
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CREATE_ACTION_LIBRARY_BUTTON_IMPLEMENTATION_SUMMARY.md", "Markdown", 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 249
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_IMPLEMENTATION.md", "Markdown", 0, 0, 183, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 225
"/Users/<USER>/development/flutter_apps/oneday/docs/features/CUSTOM_ACTION_LIBRARY_USER_GUIDE.md", "Markdown", 0, 0, 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 177
"/Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_BEFORE_AFTER_COMPARISON.md", "Markdown", 0, 0, 215, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 274
"/Users/<USER>/development/flutter_apps/oneday/docs/features/DIALOG_BACKGROUND_UNIFICATION.md", "Markdown", 0, 0, 213, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 276
"/Users/<USER>/development/flutter_apps/oneday/docs/features/FINAL_DIALOG_BACKGROUND_REPORT.md", "Markdown", 0, 0, 145, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 185
"/Users/<USER>/development/flutter_apps/oneday/docs/features/GRADUATE_VOCABULARY_MANAGER.md", "Markdown", 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 179
"/Users/<USER>/development/flutter_apps/oneday/docs/features/VOCABULARY_MANAGER_IMPLEMENTATION_SUMMARY.md", "Markdown", 0, 0, 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 198
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/EXERCISE_LIBRARY_PAGE_IMPORT_FIX.md", "Markdown", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 134
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/DEVELOPER_ENTRANCE_GUIDE.md", "Markdown", 0, 0, 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 83
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/FLOATING_TIMER_DEBUG_GUIDE.md", "Markdown", 0, 0, 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/ICON_SETUP_GUIDE.md", "Markdown", 0, 0, 231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/README.md", "Markdown", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 118
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/SYSTEM_FLOATING_TIMER_USER_GUIDE.md", "Markdown", 0, 0, 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 158
"/Users/<USER>/development/flutter_apps/oneday/docs/guides/TIMER_FLOATING_WINDOW_GUIDE.md", "Markdown", 0, 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 96
"/Users/<USER>/development/flutter_apps/oneday/docs/releases/README.md", "Markdown", 0, 0, 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/COMPREHENSIVE_TEST_GUIDE.md", "Markdown", 0, 0, 276, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 372
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/COVER_MODE_TEST_GUIDE.md", "Markdown", 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 150
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/FLOATING_WINDOW_TEST_CHECKLIST.md", "Markdown", 0, 0, 114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 141
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/POMODORO_TEST_GUIDE.md", "Markdown", 0, 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 125
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/QUICK_TEST_STEPS.md", "Markdown", 0, 0, 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 111
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/README.md", "Markdown", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 122
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/TESTING_GUIDE.md", "Markdown", 0, 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/testing/TIMER_TEST_GUIDE.md", "Markdown", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 153
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_SUMMARY.md", "Markdown", 0, 0, 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 197
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/ANNOTATION_SCALE_FIX_TEST_GUIDE.md", "Markdown", 0, 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 187
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/BOTTOM_NAVIGATION_BAR_FIX.md", "Markdown", 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 294
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/CALENDAR_TASK_DISPLAY_FIX.md", "Markdown", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 258
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COLOR_SYSTEM_RESTORATION.md", "Markdown", 0, 0, 134, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 168
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/COMPREHENSIVE_NAVIGATION_AUDIT.md", "Markdown", 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 240
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_BUGFIX.md", "Markdown", 0, 0, 206, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 248
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/DIALOG_LAYOUT_OPTIMIZATION.md", "Markdown", 0, 0, 192, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 46, 238
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/EXERCISE_LIBRARY_UI_OPTIMIZATION.md", "Markdown", 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FINAL_NAVIGATION_TEST_REPORT.md", "Markdown", 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 156
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_TIMER_FIXES_VERIFICATION.md", "Markdown", 0, 0, 190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 240
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_FINAL_OPTIMIZATION.md", "Markdown", 0, 0, 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 177
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md", "Markdown", 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 164
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/HOW_TO_TEST_NAVIGATION_FIX.md", "Markdown", 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 51, 198
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/IPAD_STAGE_MANAGER_LAYOUT_FIX.md", "Markdown", 0, 0, 200, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 231
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/KNOWLEDGE_POINT_SYNC_FIX.md", "Markdown", 0, 0, 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 233
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_PROGRESS_REPORT.md", "Markdown", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 159
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/NAVIGATION_FIX_SUMMARY.md", "Markdown", 0, 0, 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 172
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md", "Markdown", 0, 0, 288, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 106, 394
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/README.md", "Markdown", 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 76
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/SIDEBAR_BUTTON_OPTIMIZATION.md", "Markdown", 0, 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37, 184
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_REST_SKIP_NAVIGATION_FIX.md", "Markdown", 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 209
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMEBOX_TIMER_FIXES.md", "Markdown", 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/TIMER_FIXES_PROGRESS.md", "Markdown", 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/UI_OVERFLOW_FIXES_SUMMARY.md", "Markdown", 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 127
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/VOCABULARY_SERVICE_FIX.md", "Markdown", 0, 0, 219, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 271
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/WORD_TRAINING_OPTIMIZATION.md", "Markdown", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 147
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/CATEGORY_SELECTOR_PURE_TEXT_FIX.md", "Markdown", 0, 0, 118, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 146
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_OVERFLOW_FIX_COMPLETE.md", "Markdown", 0, 0, 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 162
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/IPAD_CALENDAR_RESPONSIVE_FIX.md", "Markdown", 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 216
"/Users/<USER>/development/flutter_apps/oneday/docs/ui_improvements/REMOVE_DUPLICATE_MENU_ITEMS.md", "Markdown", 0, 0, 111, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 147
"/Users/<USER>/development/flutter_apps/oneday/example/calendar_responsive_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 342, 0, 0, 0, 0, 0, 16, 24, 382
"/Users/<USER>/development/flutter_apps/oneday/example/calendar_task_display_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 373, 0, 0, 0, 0, 0, 19, 25, 417
"/Users/<USER>/development/flutter_apps/oneday/example/color_scheme_comparison_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 412, 0, 0, 0, 0, 0, 16, 31, 459
"/Users/<USER>/development/flutter_apps/oneday/example/navigation_bottom_bar_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 366, 0, 0, 0, 0, 0, 9, 15, 390
"/Users/<USER>/development/flutter_apps/oneday/example/responsive_layout_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 560, 0, 0, 0, 0, 0, 13, 26, 599
"/Users/<USER>/development/flutter_apps/oneday/example/study_session_completion_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 427, 0, 0, 0, 0, 0, 7, 24, 458
"/Users/<USER>/development/flutter_apps/oneday/example/timebox_rest_skip_demo.dart", "Dart", 0, 0, 0, 0, 0, 0, 449, 0, 0, 0, 0, 0, 24, 40, 513
"/Users/<USER>/development/flutter_apps/oneday/ios/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 0, 3, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 2, 14
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 117
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 1, 1, 38
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 1, 1, 27
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart", "Dart", 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 5, 3, 43
"/Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart", "Dart", 0, 0, 0, 0, 0, 0, 317, 0, 0, 0, 0, 0, 28, 25, 370
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/route_debug_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 268, 0, 0, 0, 0, 0, 10, 15, 293
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.dart", "Dart", 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 39, 56, 264
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 199, 0, 0, 0, 0, 0, 32, 18, 249
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/pages/ability_radar_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 448, 0, 0, 0, 0, 0, 41, 41, 530
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart", "Dart", 0, 0, 0, 0, 0, 0, 173, 0, 0, 0, 0, 0, 5, 6, 184
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart", "Dart", 0, 0, 0, 0, 0, 0, 282, 0, 0, 0, 0, 0, 21, 57, 360
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/ability_radar_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 467, 0, 0, 0, 0, 0, 45, 106, 618
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart", "Dart", 0, 0, 0, 0, 0, 0, 284, 0, 0, 0, 0, 0, 18, 23, 325
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/README.md", "Markdown", 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 173
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/data/achievements_data.dart", "Dart", 0, 0, 0, 0, 0, 0, 444, 0, 0, 0, 0, 0, 18, 12, 474
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.dart", "Dart", 0, 0, 0, 0, 0, 0, 169, 0, 0, 0, 0, 0, 29, 39, 237
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/achievement.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 24, 11, 160
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.dart", "Dart", 0, 0, 0, 0, 0, 0, 230, 0, 0, 0, 0, 0, 50, 69, 349
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/badge.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 40, 17, 241
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.dart", "Dart", 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 41, 48, 353
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 91, 0, 0, 0, 0, 0, 21, 11, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/achievement_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 485, 0, 0, 0, 0, 0, 21, 39, 545
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/pages/leaderboard_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 336, 0, 0, 0, 0, 0, 13, 20, 369
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/providers/achievement_provider.dart", "Dart", 0, 0, 0, 0, 0, 0, 224, 0, 0, 0, 0, 0, 38, 44, 306
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 322, 0, 0, 0, 0, 0, 38, 64, 424
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/services/achievement_trigger_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 113, 0, 0, 0, 0, 0, 61, 63, 237
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_grid.dart", "Dart", 0, 0, 0, 0, 0, 0, 380, 0, 0, 0, 0, 0, 19, 32, 431
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart", "Dart", 0, 0, 0, 0, 0, 0, 300, 0, 0, 0, 0, 0, 20, 45, 365
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/skill_levels_card.dart", "Dart", 0, 0, 0, 0, 0, 0, 323, 0, 0, 0, 0, 0, 15, 42, 380
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/user_level_card.dart", "Dart", 0, 0, 0, 0, 0, 0, 326, 0, 0, 0, 0, 0, 7, 29, 362
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 666, 0, 0, 0, 0, 0, 38, 41, 745
"/Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 1164, 0, 0, 0, 0, 0, 92, 80, 1336
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/article_import_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 191, 0, 0, 0, 0, 0, 26, 40, 257
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 781, 0, 0, 0, 0, 0, 24, 58, 863
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_post_editor_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 679, 0, 0, 0, 0, 0, 38, 59, 776
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart", "Dart", 0, 0, 0, 0, 0, 0, 151, 0, 0, 0, 0, 0, 29, 34, 214
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 49, 0, 0, 0, 0, 0, 13, 7, 69
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart", "Dart", 0, 0, 0, 0, 0, 0, 204, 0, 0, 0, 0, 0, 31, 44, 279
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 216, 0, 0, 0, 0, 0, 33, 48, 297
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_custom_library_dialog.dart", "Dart", 0, 0, 0, 0, 0, 0, 276, 0, 0, 0, 0, 0, 7, 17, 300
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_editor_dialog.dart", "Dart", 0, 0, 0, 0, 0, 0, 500, 0, 0, 0, 0, 0, 17, 37, 554
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library.dart", "Dart", 0, 0, 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 25, 29, 257
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 227, 0, 0, 0, 0, 0, 29, 46, 302
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_exercise_category.dart", "Dart", 0, 0, 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 15, 23, 202
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_library_editor_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 432, 0, 0, 0, 0, 0, 18, 30, 480
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 2401, 0, 0, 0, 0, 0, 133, 156, 2690
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 985, 0, 0, 0, 0, 0, 72, 87, 1144
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 435, 0, 0, 0, 0, 0, 18, 41, 494
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart", "Dart", 0, 0, 0, 0, 0, 0, 641, 0, 0, 0, 0, 0, 15, 28, 684
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/pao_integration_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 188, 0, 0, 0, 0, 0, 32, 46, 266
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 0, 0, 0, 0, 0, 1093, 0, 0, 0, 0, 0, 74, 65, 1232
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/learning_report_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 757, 0, 0, 0, 0, 0, 42, 54, 853
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.dart", "Dart", 0, 0, 0, 0, 0, 0, 203, 0, 0, 0, 0, 0, 61, 77, 341
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/models/learning_report_models.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 0, 51, 25, 377
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/services/learning_report_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 474, 0, 0, 0, 0, 0, 61, 100, 635
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/widgets/chart_widgets.dart", "Dart", 0, 0, 0, 0, 0, 0, 764, 0, 0, 0, 0, 0, 14, 30, 808
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 4, 6, 114
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 5124, 0, 0, 0, 0, 0, 387, 475, 5986
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 3204, 0, 0, 0, 0, 0, 493, 575, 4272
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_副本.dart", "Dart", 0, 0, 0, 0, 0, 0, 1672, 0, 0, 0, 0, 0, 186, 224, 2082
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 241, 0, 0, 0, 0, 0, 18, 29, 288
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 785, 0, 0, 0, 0, 0, 59, 75, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 808, 0, 0, 0, 0, 0, 49, 81, 938
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 701, 0, 0, 0, 0, 0, 30, 58, 789
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 1324, 0, 0, 0, 0, 0, 66, 135, 1525
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 772, 0, 0, 0, 0, 0, 49, 62, 883
"/Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 186, 0, 0, 0, 0, 0, 12, 22, 220
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.dart", "Dart", 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 58, 68, 390
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/models/study_time_models.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 34, 12, 221
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/providers/study_time_providers.dart", "Dart", 0, 0, 0, 0, 0, 0, 241, 0, 0, 0, 0, 0, 26, 46, 313
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/services/study_time_statistics_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 319, 0, 0, 0, 0, 0, 60, 79, 458
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/test_data_sync_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 251, 0, 0, 0, 0, 0, 11, 17, 279
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart", "Dart", 0, 0, 0, 0, 0, 0, 273, 0, 0, 0, 0, 0, 43, 56, 372
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart", "Dart", 0, 0, 0, 0, 0, 0, 71, 0, 0, 0, 0, 0, 15, 8, 94
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart", "Dart", 0, 0, 0, 0, 0, 0, 217, 0, 0, 0, 0, 0, 19, 25, 261
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 3104, 0, 0, 0, 0, 0, 192, 232, 3528
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/widgets/study_session_completion_dialog.dart", "Dart", 0, 0, 0, 0, 0, 0, 405, 0, 0, 0, 0, 0, 6, 21, 432
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 452, 0, 0, 0, 0, 0, 11, 39, 502
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_algorithm.dart", "Dart", 0, 0, 0, 0, 0, 0, 221, 0, 0, 0, 0, 0, 41, 40, 302
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 359, 0, 0, 0, 0, 0, 61, 78, 498
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 597, 0, 0, 0, 0, 0, 30, 50, 677
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart", "Dart", 0, 0, 0, 0, 0, 0, 233, 0, 0, 0, 0, 0, 33, 64, 330
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 940, 0, 0, 0, 0, 0, 53, 77, 1070
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 232, 0, 0, 0, 0, 0, 28, 47, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 1004, 0, 0, 0, 0, 0, 42, 75, 1121
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 162, 0, 0, 0, 0, 0, 2, 12, 176
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 521, 0, 0, 0, 0, 0, 71, 119, 711
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 845, 0, 0, 0, 0, 0, 29, 83, 957
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 162, 0, 0, 0, 0, 0, 17, 32, 211
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart", "Dart", 0, 0, 0, 0, 0, 0, 590, 0, 0, 0, 0, 0, 21, 43, 654
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 189, 0, 0, 0, 0, 0, 28, 37, 254
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 1632, 0, 0, 0, 0, 0, 74, 97, 1803
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 826, 0, 0, 0, 0, 0, 36, 57, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 0, 0, 0, 0, 0, 252, 0, 0, 0, 0, 0, 23, 11, 286
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 0, 0, 0, 0, 0, 333, 0, 0, 0, 0, 0, 32, 40, 405
"/Users/<USER>/development/flutter_apps/oneday/lib/services/app_initialization_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 60, 0, 0, 0, 0, 0, 16, 19, 95
"/Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 16, 13, 73
"/Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 472, 0, 0, 0, 0, 0, 77, 79, 628
"/Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 324, 0, 0, 0, 0, 0, 85, 96, 505
"/Users/<USER>/development/flutter_apps/oneday/lib/services/providers/study_session_completion_provider.dart", "Dart", 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 2, 4, 25
"/Users/<USER>/development/flutter_apps/oneday/lib/services/study_session_completion_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 185, 0, 0, 0, 0, 0, 37, 45, 267
"/Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart", "Dart", 0, 0, 0, 0, 0, 0, 248, 0, 0, 0, 0, 0, 26, 24, 298
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart", "Dart", 0, 0, 0, 0, 0, 0, 69, 0, 0, 0, 0, 0, 20, 19, 108
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 160, 0, 0, 0, 0, 0, 27, 37, 224
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart", "Dart", 0, 0, 0, 0, 0, 0, 187, 0, 0, 0, 0, 0, 35, 44, 266
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart", "Dart", 0, 0, 0, 0, 0, 0, 246, 0, 0, 0, 0, 0, 62, 62, 370
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 83, 0, 0, 0, 0, 0, 14, 26, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart", "Dart", 0, 0, 0, 0, 0, 0, 94, 0, 0, 0, 0, 0, 19, 9, 122
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart", "Dart", 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 8, 14, 84
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart", "Dart", 0, 0, 0, 0, 0, 0, 204, 0, 0, 0, 0, 0, 17, 43, 264
"/Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt", "CMake", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 25, 129
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 10, 89
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 20
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc", "C++", 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 131
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h", "C++", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 3, 4, 21
"/Users/<USER>/development/flutter_apps/oneday/macos/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 0, 1, 10, 43
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 3, 14
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 1, 344
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 4, 16
"/Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 72, 29, 156
"/Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py", "Python", 0, 0, 0, 0, 0, 0, 0, 152, 0, 0, 0, 0, 26, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/scripts/verify_navigation_fix.dart", "Dart", 0, 0, 0, 0, 0, 0, 175, 0, 0, 0, 0, 0, 20, 45, 240
"/Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 146, 0, 0, 0, 0, 0, 30, 48, 224
"/Users/<USER>/development/flutter_apps/oneday/test/calendar_task_display_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 251, 0, 0, 0, 0, 0, 13, 36, 300
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 15, 21, 137
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 40, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 152, 0, 0, 0, 0, 0, 38, 52, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 171, 0, 0, 0, 0, 0, 45, 61, 277
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 6, 20, 124
"/Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 172, 0, 0, 0, 0, 0, 20, 27, 219
"/Users/<USER>/development/flutter_apps/oneday/test/date_format_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 44, 0, 0, 0, 0, 0, 5, 13, 62
"/Users/<USER>/development/flutter_apps/oneday/test/developer_tools_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 132, 0, 0, 0, 0, 0, 12, 29, 173
"/Users/<USER>/development/flutter_apps/oneday/test/exercise_library_ui_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 156, 0, 0, 0, 0, 0, 31, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_ui_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 184, 0, 0, 0, 0, 0, 27, 39, 250
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/exercise_library_page_import_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 5, 6, 32
"/Users/<USER>/development/flutter_apps/oneday/test/features/study_time/study_time_statistics_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 264, 0, 0, 0, 0, 0, 13, 38, 315
"/Users/<USER>/development/flutter_apps/oneday/test/features/time_box/timebox_ui_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 125, 0, 0, 0, 0, 0, 11, 18, 154
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/color_consistency_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 208, 0, 0, 0, 0, 0, 19, 30, 257
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_simple_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 198, 0, 0, 0, 0, 0, 17, 23, 238
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dialog_background_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 228, 0, 0, 0, 0, 0, 23, 32, 283
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_background_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 352, 0, 0, 0, 0, 0, 25, 25, 402
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_pure_white_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 262, 0, 0, 0, 0, 0, 14, 22, 298
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/final_verification_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 233, 0, 0, 0, 0, 0, 24, 29, 286
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/material3_surface_tinting_fix_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 252, 0, 0, 0, 0, 0, 16, 23, 291
"/Users/<USER>/development/flutter_apps/oneday/test/graduate_vocabulary_manager_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 6, 20, 85
"/Users/<USER>/development/flutter_apps/oneday/test/integration/study_session_completion_integration_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 51, 54, 213
"/Users/<USER>/development/flutter_apps/oneday/test/ipad_calendar_overflow_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 119, 0, 0, 0, 0, 0, 21, 38, 178
"/Users/<USER>/development/flutter_apps/oneday/test/learning_efficiency_metrics_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 145, 0, 0, 0, 0, 0, 10, 13, 168
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 201, 0, 0, 0, 0, 0, 13, 29, 243
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 166, 0, 0, 0, 0, 0, 14, 25, 205
"/Users/<USER>/development/flutter_apps/oneday/test/navigation_bottom_bar_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 164, 0, 0, 0, 0, 0, 37, 50, 251
"/Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 0, 0, 9, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 81, 0, 0, 0, 0, 0, 2, 17, 100
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 25, 32, 158
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 96, 0, 0, 0, 0, 0, 21, 28, 145
"/Users/<USER>/development/flutter_apps/oneday/test/profile_avatar_click_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 137, 0, 0, 0, 0, 0, 16, 33, 186
"/Users/<USER>/development/flutter_apps/oneday/test/reflection_calendar_responsive_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 140, 0, 0, 0, 0, 0, 30, 49, 219
"/Users/<USER>/development/flutter_apps/oneday/test/reflection_log_integration_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 61, 0, 0, 0, 0, 0, 11, 19, 91
"/Users/<USER>/development/flutter_apps/oneday/test/responsive_layout_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 230, 0, 0, 0, 0, 0, 12, 24, 266
"/Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 223, 0, 0, 0, 0, 0, 32, 39, 294
"/Users/<USER>/development/flutter_apps/oneday/test/store_new_items_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 95, 0, 0, 0, 0, 0, 13, 27, 135
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_adjustment.dart", "Dart", 0, 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 0, 9, 23, 135
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_verification.dart", "Dart", 0, 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 0, 10, 26, 139
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_center_alignment.dart", "Dart", 0, 0, 0, 0, 0, 0, 92, 0, 0, 0, 0, 0, 8, 20, 120
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_positioning_fix.dart", "Dart", 0, 0, 0, 0, 0, 0, 109, 0, 0, 0, 0, 0, 6, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/test/test_compilation_fix.dart", "Dart", 0, 0, 0, 0, 0, 0, 144, 0, 0, 0, 0, 0, 7, 29, 180
"/Users/<USER>/development/flutter_apps/oneday/test/test_complete_image_compression.dart", "Dart", 0, 0, 0, 0, 0, 0, 186, 0, 0, 0, 0, 0, 9, 38, 233
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_analysis.dart", "Dart", 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 9, 27, 140
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_debug.dart", "Dart", 0, 0, 0, 0, 0, 0, 84, 0, 0, 0, 0, 0, 5, 18, 107
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_transformation_fix.dart", "Dart", 0, 0, 0, 0, 0, 0, 110, 0, 0, 0, 0, 0, 16, 24, 150
"/Users/<USER>/development/flutter_apps/oneday/test/test_image_compression_implementation.dart", "Dart", 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 0, 0, 7, 33, 187
"/Users/<USER>/development/flutter_apps/oneday/test/test_matrix_analysis.dart", "Dart", 0, 0, 0, 0, 0, 0, 130, 0, 0, 0, 0, 0, 8, 37, 175
"/Users/<USER>/development/flutter_apps/oneday/test/test_matrix_fix_verification.dart", "Dart", 0, 0, 0, 0, 0, 0, 105, 0, 0, 0, 0, 0, 14, 31, 150
"/Users/<USER>/development/flutter_apps/oneday/test/test_position_fix_verification.dart", "Dart", 0, 0, 0, 0, 0, 0, 38, 0, 0, 0, 0, 0, 4, 11, 53
"/Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 108, 0, 0, 0, 0, 0, 24, 32, 164
"/Users/<USER>/development/flutter_apps/oneday/test/timebox_rest_skip_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 201, 0, 0, 0, 0, 0, 20, 39, 260
"/Users/<USER>/development/flutter_apps/oneday/test/ui_overflow_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 101, 0, 0, 0, 0, 0, 20, 32, 153
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_scrollbar_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 8, 22, 128
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 216, 0, 0, 0, 0, 0, 9, 29, 254
"/Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 9, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 0, 0, 2, 14, 78
"/Users/<USER>/development/flutter_apps/oneday/web/index.html", "HTML", 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 5, 39
"/Users/<USER>/development/flutter_apps/oneday/web/manifest.json", "JSON", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35
"/Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt", "CMake", 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 12, 110
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 24
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 7, 41
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp", "C++", 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h", "C++", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp", "C++", 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h", "C++", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp", "C++", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h", "C++", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp", "C++", 0, 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h", "C++", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 31, 24, 103
"/Users/<USER>/development/flutter_apps/tempPrompts.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"Total", "-", 731, 85351, 18532, 1100, 928, 93, 70182, 152, 126, 16, 947, 118, 6809, 14410, 199495