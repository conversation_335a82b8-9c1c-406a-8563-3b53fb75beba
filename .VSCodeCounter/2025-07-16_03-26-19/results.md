# Summary

Date : 2025-07-16 03:26:19

Directory /Users/<USER>/development/flutter_apps

Total : 406 files,  178276 codes, 6809 comments, 14410 blanks, all 199495 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| JSON | 12 | 85,351 | 0 | 9 | 85,360 |
| Dart | 185 | 70,182 | 6,151 | 8,438 | 84,771 |
| Markdown | 111 | 18,532 | 2 | 5,138 | 23,672 |
| C++ | 32 | 1,100 | 262 | 378 | 1,740 |
| XML | 20 | 947 | 108 | 26 | 1,081 |
| CMake | 16 | 928 | 0 | 184 | 1,112 |
| HTML | 4 | 731 | 53 | 64 | 848 |
| Python | 1 | 152 | 26 | 32 | 210 |
| Ruby | 4 | 126 | 8 | 40 | 174 |
| Swift | 12 | 118 | 14 | 42 | 174 |
| YAML | 5 | 93 | 185 | 55 | 333 |
| Properties | 4 | 16 | 0 | 4 | 20 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 406 | 178,276 | 6,809 | 14,410 | 199,495 |
| . (Files) | 4 | 492 | 0 | 222 | 714 |
| .augment | 2 | 816 | 0 | 245 | 1,061 |
| .augment/rules | 2 | 816 | 0 | 245 | 1,061 |
| accompanion | 67 | 8,412 | 729 | 1,108 | 10,249 |
| accompanion/accompanion | 67 | 8,412 | 729 | 1,108 | 10,249 |
| accompanion/accompanion (Files) | 3 | 307 | 85 | 114 | 506 |
| accompanion/accompanion/.augment | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/.augment/rules | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/android | 9 | 74 | 51 | 11 | 136 |
| accompanion/accompanion/android (Files) | 1 | 3 | 0 | 1 | 4 |
| accompanion/accompanion/android/app | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/app/src/main | 5 | 60 | 43 | 7 | 110 |
| accompanion/accompanion/android/app/src/main (Files) | 1 | 34 | 11 | 1 | 46 |
| accompanion/accompanion/android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| accompanion/accompanion/android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/gradle | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/ios | 9 | 260 | 7 | 23 | 290 |
| accompanion/accompanion/ios (Files) | 1 | 31 | 3 | 10 | 44 |
| accompanion/accompanion/ios/Runner | 7 | 222 | 2 | 9 | 233 |
| accompanion/accompanion/ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| accompanion/accompanion/ios/Runner/Assets.xcassets | 3 | 148 | 0 | 4 | 152 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| accompanion/accompanion/ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| accompanion/accompanion/ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/lib | 11 | 5,408 | 424 | 395 | 6,227 |
| accompanion/accompanion/lib (Files) | 1 | 79 | 42 | 16 | 137 |
| accompanion/accompanion/lib/screens | 10 | 5,329 | 382 | 379 | 6,090 |
| accompanion/accompanion/linux | 9 | 325 | 37 | 92 | 454 |
| accompanion/accompanion/linux (Files) | 1 | 104 | 0 | 25 | 129 |
| accompanion/accompanion/linux/flutter | 4 | 105 | 9 | 27 | 141 |
| accompanion/accompanion/linux/runner | 4 | 116 | 28 | 40 | 184 |
| accompanion/accompanion/macos | 7 | 479 | 6 | 27 | 512 |
| accompanion/accompanion/macos (Files) | 1 | 32 | 1 | 10 | 43 |
| accompanion/accompanion/macos/Flutter | 1 | 6 | 3 | 4 | 13 |
| accompanion/accompanion/macos/Runner | 4 | 434 | 0 | 9 | 443 |
| accompanion/accompanion/macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| accompanion/accompanion/macos/Runner/Assets.xcassets | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| accompanion/accompanion/macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/test | 1 | 14 | 10 | 7 | 31 |
| accompanion/accompanion/web | 2 | 54 | 15 | 6 | 75 |
| accompanion/accompanion/windows | 14 | 675 | 94 | 188 | 957 |
| accompanion/accompanion/windows (Files) | 1 | 89 | 0 | 20 | 109 |
| accompanion/accompanion/windows/flutter | 4 | 124 | 9 | 29 | 162 |
| accompanion/accompanion/windows/runner | 9 | 462 | 85 | 139 | 686 |
| oneday | 333 | 168,556 | 6,080 | 12,835 | 187,471 |
| oneday (Files) | 20 | 3,809 | 100 | 1,012 | 4,921 |
| oneday/android | 9 | 81 | 53 | 13 | 147 |
| oneday/android (Files) | 1 | 3 | 0 | 1 | 4 |
| oneday/android/app | 7 | 73 | 53 | 11 | 137 |
| oneday/android/app/src | 7 | 73 | 53 | 11 | 137 |
| oneday/android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| oneday/android/app/src/main | 5 | 67 | 45 | 9 | 121 |
| oneday/android/app/src/main (Files) | 1 | 41 | 13 | 3 | 57 |
| oneday/android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| oneday/android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| oneday/android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| oneday/android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| oneday/android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| oneday/android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| oneday/android/gradle | 1 | 5 | 0 | 1 | 6 |
| oneday/android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| oneday/assets | 7 | 85,587 | 23 | 70 | 85,680 |
| oneday/assets/data | 6 | 85,554 | 23 | 57 | 85,634 |
| oneday/assets/icons | 1 | 33 | 0 | 13 | 46 |
| oneday/docs | 81 | 12,214 | 2 | 3,297 | 15,513 |
| oneday/docs (Files) | 6 | 660 | 0 | 209 | 869 |
| oneday/docs/core | 4 | 1,352 | 0 | 308 | 1,660 |
| oneday/docs/development | 15 | 1,885 | 2 | 506 | 2,393 |
| oneday/docs/features | 9 | 1,522 | 0 | 420 | 1,942 |
| oneday/docs/fixes | 1 | 91 | 0 | 43 | 134 |
| oneday/docs/guides | 6 | 766 | 0 | 207 | 973 |
| oneday/docs/releases | 1 | 107 | 0 | 30 | 137 |
| oneday/docs/testing | 8 | 1,011 | 0 | 300 | 1,311 |
| oneday/docs/troubleshooting | 27 | 4,284 | 0 | 1,139 | 5,423 |
| oneday/docs/ui_improvements | 4 | 536 | 0 | 135 | 671 |
| oneday/example | 7 | 2,929 | 104 | 185 | 3,218 |
| oneday/ios | 9 | 254 | 7 | 23 | 284 |
| oneday/ios (Files) | 1 | 31 | 3 | 10 | 44 |
| oneday/ios/Runner | 7 | 216 | 2 | 9 | 227 |
| oneday/ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| oneday/ios/Runner/Assets.xcassets | 3 | 142 | 0 | 4 | 146 |
| oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 116 | 0 | 1 | 117 |
| oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| oneday/ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| oneday/ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| oneday/lib | 107 | 53,483 | 4,617 | 6,119 | 64,219 |
| oneday/lib (Files) | 1 | 252 | 23 | 11 | 286 |
| oneday/lib/core | 2 | 352 | 33 | 28 | 413 |
| oneday/lib/core/constants | 1 | 35 | 5 | 3 | 43 |
| oneday/lib/core/data | 1 | 317 | 28 | 25 | 370 |
| oneday/lib/debug | 1 | 268 | 10 | 15 | 293 |
| oneday/lib/features | 87 | 49,821 | 4,058 | 5,491 | 59,370 |
| oneday/lib/features/ability_radar | 7 | 2,022 | 201 | 307 | 2,530 |
| oneday/lib/features/ability_radar/models | 2 | 368 | 71 | 74 | 513 |
| oneday/lib/features/ability_radar/pages | 1 | 448 | 41 | 41 | 530 |
| oneday/lib/features/ability_radar/providers | 2 | 455 | 26 | 63 | 544 |
| oneday/lib/features/ability_radar/services | 1 | 467 | 45 | 106 | 618 |
| oneday/lib/features/ability_radar/widgets | 1 | 284 | 18 | 23 | 325 |
| oneday/lib/features/achievement | 17 | 4,448 | 455 | 626 | 5,529 |
| oneday/lib/features/achievement (Files) | 1 | 132 | 0 | 41 | 173 |
| oneday/lib/features/achievement/data | 1 | 444 | 18 | 12 | 474 |
| oneday/lib/features/achievement/models | 6 | 1,063 | 205 | 195 | 1,463 |
| oneday/lib/features/achievement/pages | 2 | 821 | 34 | 59 | 914 |
| oneday/lib/features/achievement/providers | 1 | 224 | 38 | 44 | 306 |
| oneday/lib/features/achievement/services | 2 | 435 | 99 | 127 | 661 |
| oneday/lib/features/achievement/widgets | 4 | 1,329 | 61 | 148 | 1,538 |
| oneday/lib/features/auth | 1 | 666 | 38 | 41 | 745 |
| oneday/lib/features/calendar | 1 | 1,164 | 92 | 80 | 1,336 |
| oneday/lib/features/community | 3 | 1,651 | 88 | 157 | 1,896 |
| oneday/lib/features/daily_plan | 4 | 620 | 106 | 133 | 859 |
| oneday/lib/features/daily_plan/models | 2 | 200 | 42 | 41 | 283 |
| oneday/lib/features/daily_plan/notifiers | 1 | 204 | 31 | 44 | 279 |
| oneday/lib/features/daily_plan/services | 1 | 216 | 33 | 48 | 297 |
| oneday/lib/features/exercise | 11 | 6,452 | 381 | 540 | 7,373 |
| oneday/lib/features/home | 1 | 1,093 | 74 | 65 | 1,232 |
| oneday/lib/features/learning_report | 5 | 2,499 | 229 | 286 | 3,014 |
| oneday/lib/features/learning_report (Files) | 1 | 757 | 42 | 54 | 853 |
| oneday/lib/features/learning_report/models | 2 | 504 | 112 | 102 | 718 |
| oneday/lib/features/learning_report/services | 1 | 474 | 61 | 100 | 635 |
| oneday/lib/features/learning_report/widgets | 1 | 764 | 14 | 30 | 808 |
| oneday/lib/features/main | 1 | 104 | 4 | 6 | 114 |
| oneday/lib/features/memory_palace | 3 | 10,000 | 1,066 | 1,274 | 12,340 |
| oneday/lib/features/onboarding | 2 | 1,026 | 77 | 104 | 1,207 |
| oneday/lib/features/photo_album | 1 | 808 | 49 | 81 | 938 |
| oneday/lib/features/profile | 1 | 701 | 30 | 58 | 789 |
| oneday/lib/features/reflection | 1 | 1,324 | 66 | 135 | 1,525 |
| oneday/lib/features/settings | 1 | 772 | 49 | 62 | 883 |
| oneday/lib/features/splash | 1 | 186 | 12 | 22 | 220 |
| oneday/lib/features/study_time | 5 | 1,250 | 189 | 222 | 1,661 |
| oneday/lib/features/study_time (Files) | 1 | 251 | 11 | 17 | 279 |
| oneday/lib/features/study_time/models | 2 | 439 | 92 | 80 | 611 |
| oneday/lib/features/study_time/providers | 1 | 241 | 26 | 46 | 313 |
| oneday/lib/features/study_time/services | 1 | 319 | 60 | 79 | 458 |
| oneday/lib/features/time_box | 5 | 4,070 | 275 | 342 | 4,687 |
| oneday/lib/features/time_box (Files) | 1 | 3,104 | 192 | 232 | 3,528 |
| oneday/lib/features/time_box/models | 2 | 344 | 58 | 64 | 466 |
| oneday/lib/features/time_box/providers | 1 | 217 | 19 | 25 | 261 |
| oneday/lib/features/time_box/widgets | 1 | 405 | 6 | 21 | 432 |
| oneday/lib/features/vocabulary | 14 | 6,507 | 467 | 796 | 7,770 |
| oneday/lib/features/wage_system | 2 | 2,458 | 110 | 154 | 2,722 |
| oneday/lib/router | 1 | 333 | 32 | 40 | 405 |
| oneday/lib/services | 7 | 1,352 | 259 | 280 | 1,891 |
| oneday/lib/services (Files) | 6 | 1,333 | 257 | 276 | 1,866 |
| oneday/lib/services/providers | 1 | 19 | 2 | 4 | 25 |
| oneday/lib/shared | 8 | 1,105 | 202 | 254 | 1,561 |
| oneday/lib/shared/config | 1 | 69 | 20 | 19 | 108 |
| oneday/lib/shared/services | 1 | 160 | 27 | 37 | 224 |
| oneday/lib/shared/utils | 4 | 610 | 130 | 141 | 881 |
| oneday/lib/shared/widgets | 2 | 266 | 25 | 57 | 348 |
| oneday/lib/shared/widgets (Files) | 1 | 204 | 17 | 43 | 264 |
| oneday/lib/shared/widgets/icons | 1 | 62 | 8 | 14 | 84 |
| oneday/linux | 9 | 335 | 37 | 92 | 464 |
| oneday/linux (Files) | 1 | 104 | 0 | 25 | 129 |
| oneday/linux/flutter | 4 | 115 | 9 | 27 | 151 |
| oneday/linux/runner | 4 | 116 | 28 | 40 | 184 |
| oneday/macos | 7 | 487 | 6 | 26 | 519 |
| oneday/macos (Files) | 1 | 32 | 1 | 10 | 43 |
| oneday/macos/Flutter | 1 | 14 | 3 | 4 | 21 |
| oneday/macos/Runner | 4 | 434 | 0 | 8 | 442 |
| oneday/macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| oneday/macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| oneday/macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| oneday/macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| oneday/scripts | 2 | 327 | 46 | 77 | 450 |
| oneday/test | 59 | 8,305 | 976 | 1,728 | 11,009 |
| oneday/test (Files) | 46 | 5,647 | 699 | 1,350 | 7,696 |
| oneday/test/features | 11 | 2,327 | 194 | 285 | 2,806 |
| oneday/test/features/exercise | 2 | 205 | 32 | 45 | 282 |
| oneday/test/features/study_time | 1 | 264 | 13 | 38 | 315 |
| oneday/test/features/time_box | 1 | 125 | 11 | 18 | 154 |
| oneday/test/features/ui | 7 | 1,733 | 138 | 184 | 2,055 |
| oneday/test/integration | 1 | 108 | 51 | 54 | 213 |
| oneday/test/services | 1 | 223 | 32 | 39 | 294 |
| oneday/web | 2 | 54 | 15 | 5 | 74 |
| oneday/windows | 14 | 691 | 94 | 188 | 973 |
| oneday/windows (Files) | 1 | 89 | 0 | 20 | 109 |
| oneday/windows/flutter | 4 | 140 | 9 | 29 | 178 |
| oneday/windows/runner | 9 | 462 | 85 | 139 | 686 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)