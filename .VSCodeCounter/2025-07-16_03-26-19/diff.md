# Diff Summary

Date : 2025-07-16 03:26:19

Directory /Users/<USER>/development/flutter_apps

Total : 344 files,  2099 codes, 3012 comments, 6507 blanks, all 11618 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 150 | 29,549 | 2,716 | 3,579 | 35,844 |
| Markdown | 137 | 9,375 | 0 | 2,562 | 11,937 |
| C++ | 16 | 540 | 131 | 189 | 860 |
| XML | 10 | 470 | 53 | 12 | 535 |
| CMake | 8 | 461 | 0 | 92 | 553 |
| Ruby | 2 | 63 | 4 | 20 | 87 |
| Swift | 6 | 55 | 7 | 21 | 83 |
| YAML | 3 | 25 | 86 | 22 | 133 |
| HTML | 5 | 19 | 15 | 5 | 39 |
| Properties | 2 | 8 | 0 | 2 | 10 |
| JSON | 5 | -38,466 | 0 | 3 | -38,463 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 344 | 2,099 | 3,012 | 6,507 | 11,618 |
| accompanion | 67 | 8,412 | 729 | 1,108 | 10,249 |
| accompanion/accompanion | 67 | 8,412 | 729 | 1,108 | 10,249 |
| accompanion/accompanion (Files) | 3 | 307 | 85 | 114 | 506 |
| accompanion/accompanion/.augment | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/.augment/rules | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/android | 9 | 74 | 51 | 11 | 136 |
| accompanion/accompanion/android (Files) | 1 | 3 | 0 | 1 | 4 |
| accompanion/accompanion/android/app | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/app/src/main | 5 | 60 | 43 | 7 | 110 |
| accompanion/accompanion/android/app/src/main (Files) | 1 | 34 | 11 | 1 | 46 |
| accompanion/accompanion/android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| accompanion/accompanion/android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/gradle | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/ios | 9 | 260 | 7 | 23 | 290 |
| accompanion/accompanion/ios (Files) | 1 | 31 | 3 | 10 | 44 |
| accompanion/accompanion/ios/Runner | 7 | 222 | 2 | 9 | 233 |
| accompanion/accompanion/ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| accompanion/accompanion/ios/Runner/Assets.xcassets | 3 | 148 | 0 | 4 | 152 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| accompanion/accompanion/ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| accompanion/accompanion/ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/lib | 11 | 5,408 | 424 | 395 | 6,227 |
| accompanion/accompanion/lib (Files) | 1 | 79 | 42 | 16 | 137 |
| accompanion/accompanion/lib/screens | 10 | 5,329 | 382 | 379 | 6,090 |
| accompanion/accompanion/linux | 9 | 325 | 37 | 92 | 454 |
| accompanion/accompanion/linux (Files) | 1 | 104 | 0 | 25 | 129 |
| accompanion/accompanion/linux/flutter | 4 | 105 | 9 | 27 | 141 |
| accompanion/accompanion/linux/runner | 4 | 116 | 28 | 40 | 184 |
| accompanion/accompanion/macos | 7 | 479 | 6 | 27 | 512 |
| accompanion/accompanion/macos (Files) | 1 | 32 | 1 | 10 | 43 |
| accompanion/accompanion/macos/Flutter | 1 | 6 | 3 | 4 | 13 |
| accompanion/accompanion/macos/Runner | 4 | 434 | 0 | 9 | 443 |
| accompanion/accompanion/macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| accompanion/accompanion/macos/Runner/Assets.xcassets | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| accompanion/accompanion/macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/test | 1 | 14 | 10 | 7 | 31 |
| accompanion/accompanion/web | 2 | 54 | 15 | 6 | 75 |
| accompanion/accompanion/windows | 14 | 675 | 94 | 188 | 957 |
| accompanion/accompanion/windows (Files) | 1 | 89 | 0 | 20 | 109 |
| accompanion/accompanion/windows/flutter | 4 | 124 | 9 | 29 | 162 |
| accompanion/accompanion/windows/runner | 9 | 462 | 85 | 139 | 686 |
| oneday | 277 | -6,313 | 2,283 | 5,399 | 1,369 |
| oneday (Files) | 67 | -6,220 | -136 | -1,513 | -7,869 |
| oneday/assets | 3 | -38,021 | 23 | 53 | -37,945 |
| oneday/assets/data | 3 | -38,021 | 23 | 53 | -37,945 |
| oneday/docs | 81 | 12,214 | 2 | 3,297 | 15,513 |
| oneday/docs (Files) | 6 | 660 | 0 | 209 | 869 |
| oneday/docs/core | 4 | 1,352 | 0 | 308 | 1,660 |
| oneday/docs/development | 15 | 1,885 | 2 | 506 | 2,393 |
| oneday/docs/features | 9 | 1,522 | 0 | 420 | 1,942 |
| oneday/docs/fixes | 1 | 91 | 0 | 43 | 134 |
| oneday/docs/guides | 6 | 766 | 0 | 207 | 973 |
| oneday/docs/releases | 1 | 107 | 0 | 30 | 137 |
| oneday/docs/testing | 8 | 1,011 | 0 | 300 | 1,311 |
| oneday/docs/troubleshooting | 27 | 4,284 | 0 | 1,139 | 5,423 |
| oneday/docs/ui_improvements | 4 | 536 | 0 | 135 | 671 |
| oneday/example | 7 | 2,929 | 104 | 185 | 3,218 |
| oneday/lib | 76 | 16,364 | 1,616 | 2,111 | 20,091 |
| oneday/lib (Files) | 1 | 10 | 0 | -1 | 9 |
| oneday/lib/core | 1 | 45 | 10 | 8 | 63 |
| oneday/lib/core/data | 1 | 45 | 10 | 8 | 63 |
| oneday/lib/debug | 1 | 268 | 10 | 15 | 293 |
| oneday/lib/features | 67 | 15,481 | 1,474 | 1,954 | 18,909 |
| oneday/lib/features/ability_radar | 7 | 2,022 | 201 | 307 | 2,530 |
| oneday/lib/features/ability_radar/models | 2 | 368 | 71 | 74 | 513 |
| oneday/lib/features/ability_radar/pages | 1 | 448 | 41 | 41 | 530 |
| oneday/lib/features/ability_radar/providers | 2 | 455 | 26 | 63 | 544 |
| oneday/lib/features/ability_radar/services | 1 | 467 | 45 | 106 | 618 |
| oneday/lib/features/ability_radar/widgets | 1 | 284 | 18 | 23 | 325 |
| oneday/lib/features/achievement | 17 | 4,448 | 455 | 626 | 5,529 |
| oneday/lib/features/achievement (Files) | 1 | 132 | 0 | 41 | 173 |
| oneday/lib/features/achievement/data | 1 | 444 | 18 | 12 | 474 |
| oneday/lib/features/achievement/models | 6 | 1,063 | 205 | 195 | 1,463 |
| oneday/lib/features/achievement/pages | 2 | 821 | 34 | 59 | 914 |
| oneday/lib/features/achievement/providers | 1 | 224 | 38 | 44 | 306 |
| oneday/lib/features/achievement/services | 2 | 435 | 99 | 127 | 661 |
| oneday/lib/features/achievement/widgets | 4 | 1,329 | 61 | 148 | 1,538 |
| oneday/lib/features/calendar | 1 | 192 | 23 | 12 | 227 |
| oneday/lib/features/community | 3 | 871 | 64 | 99 | 1,034 |
| oneday/lib/features/exercise | 10 | 3,233 | 196 | 306 | 3,735 |
| oneday/lib/features/home | 1 | 299 | 42 | 24 | 365 |
| oneday/lib/features/learning_report | 5 | 2,499 | 229 | 286 | 3,014 |
| oneday/lib/features/learning_report (Files) | 1 | 757 | 42 | 54 | 853 |
| oneday/lib/features/learning_report/models | 2 | 504 | 112 | 102 | 718 |
| oneday/lib/features/learning_report/services | 1 | 474 | 61 | 100 | 635 |
| oneday/lib/features/learning_report/widgets | 1 | 764 | 14 | 30 | 808 |
| oneday/lib/features/memory_palace | 3 | -2,071 | -162 | -213 | -2,446 |
| oneday/lib/features/profile | 1 | 204 | 4 | -1 | 207 |
| oneday/lib/features/reflection | 1 | 72 | 5 | 5 | 82 |
| oneday/lib/features/settings | 1 | 74 | 7 | 9 | 90 |
| oneday/lib/features/study_time | 5 | 1,250 | 189 | 222 | 1,661 |
| oneday/lib/features/study_time (Files) | 1 | 251 | 11 | 17 | 279 |
| oneday/lib/features/study_time/models | 2 | 439 | 92 | 80 | 611 |
| oneday/lib/features/study_time/providers | 1 | 241 | 26 | 46 | 313 |
| oneday/lib/features/study_time/services | 1 | 319 | 60 | 79 | 458 |
| oneday/lib/features/time_box | 3 | 875 | 68 | 76 | 1,019 |
| oneday/lib/features/time_box (Files) | 1 | 441 | 61 | 50 | 552 |
| oneday/lib/features/time_box/models | 1 | 29 | 1 | 5 | 35 |
| oneday/lib/features/time_box/widgets | 1 | 405 | 6 | 21 | 432 |
| oneday/lib/features/vocabulary | 8 | 1,293 | 133 | 177 | 1,603 |
| oneday/lib/features/wage_system | 1 | 220 | 20 | 19 | 259 |
| oneday/lib/router | 1 | 95 | 12 | 13 | 120 |
| oneday/lib/services | 5 | 465 | 110 | 122 | 697 |
| oneday/lib/services (Files) | 4 | 446 | 108 | 118 | 672 |
| oneday/lib/services/providers | 1 | 19 | 2 | 4 | 25 |
| oneday/scripts | 1 | 175 | 20 | 45 | 240 |
| oneday/test | 42 | 6,246 | 654 | 1,221 | 8,121 |
| oneday/test (Files) | 29 | 3,588 | 377 | 843 | 4,808 |
| oneday/test/features | 11 | 2,327 | 194 | 285 | 2,806 |
| oneday/test/features/exercise | 2 | 205 | 32 | 45 | 282 |
| oneday/test/features/study_time | 1 | 264 | 13 | 38 | 315 |
| oneday/test/features/time_box | 1 | 125 | 11 | 18 | 154 |
| oneday/test/features/ui | 7 | 1,733 | 138 | 184 | 2,055 |
| oneday/test/integration | 1 | 108 | 51 | 54 | 213 |
| oneday/test/services | 1 | 223 | 32 | 39 | 294 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)