{"file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_OPTIMIZATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 122, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/COVER_MODE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md": {"language": "<PERSON><PERSON>", "code": 128, "comment": 0, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/blueprint.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/ICON_SETUP_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 231, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake": {"language": "CMake", "code": 22, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 15, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md": {"language": "<PERSON><PERSON>", "code": 33, "comment": 0, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt": {"language": "CMake", "code": 89, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt": {"language": "CMake", "code": 98, "comment": 0, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/test_share_function.md": {"language": "<PERSON><PERSON>", "code": 94, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/FINAL_GESTURE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml": {"language": "YAML", "code": 3, "comment": 22, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_MODE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 93, "comment": 0, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/pubspec.yaml": {"language": "YAML", "code": 47, "comment": 69, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/BLUR_TRANSITION_OPTIMIZATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 133, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/Action.html": {"language": "HTML", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/debug_share_issue.md": {"language": "<PERSON><PERSON>", "code": 73, "comment": 0, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_CONFLICT_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 151, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/SHARE_FUNCTION_SIMPLIFICATION.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/README.md": {"language": "<PERSON><PERSON>", "code": 1148, "comment": 0, "blank": 254}, "file:///Users/<USER>/development/flutter_apps/oneday/DEVELOPER_ENTRANCE_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/OPTIMIZING_CURSOR_AI.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/POSITION_FOLLOWING_SYSTEM.md": {"language": "<PERSON><PERSON>", "code": 130, "comment": 0, "blank": 30}, "file:///Users/<USER>/development/flutter_apps/oneday/IMAGE_MARKING_FUNCTION_RESTORE.md": {"language": "<PERSON><PERSON>", "code": 285, "comment": 0, "blank": 85}, "file:///Users/<USER>/development/flutter_apps/oneday/HORIZONTAL_SWIPE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 36}, "file:///Users/<USER>/development/flutter_apps/oneday/COVER_MODE_VERTICAL_BOUNDARY_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 98, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_IMPLEMENTATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake": {"language": "CMake", "code": 20, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_DEBUG_REPORT.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Podfile": {"language": "<PERSON>", "code": 31, "comment": 3, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/BUG_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 72, "comment": 0, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc": {"language": "C++", "code": 83, "comment": 21, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart": {"language": "Dart", "code": 216, "comment": 16, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt": {"language": "CMake", "code": 79, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/web/index.html": {"language": "HTML", "code": 19, "comment": 15, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 11, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle.properties": {"language": "Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt": {"language": "CMake", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt": {"language": "CMake", "code": 104, "comment": 0, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt": {"language": "CMake", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 38, "comment": 12, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 116, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart": {"language": "Dart", "code": 158, "comment": 27, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/main.dart": {"language": "Dart", "code": 237, "comment": 20, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart": {"language": "Dart", "code": 272, "comment": 18, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart": {"language": "Dart", "code": 204, "comment": 17, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart": {"language": "Dart", "code": 91, "comment": 4, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart": {"language": "Dart", "code": 756, "comment": 55, "blank": 70}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart": {"language": "Dart", "code": 212, "comment": 9, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart": {"language": "Dart", "code": 35, "comment": 5, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart": {"language": "Dart", "code": 789, "comment": 44, "blank": 75}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart": {"language": "Dart", "code": 826, "comment": 36, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart": {"language": "Dart", "code": 721, "comment": 58, "blank": 56}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart": {"language": "Dart", "code": 167, "comment": 9, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart": {"language": "Dart", "code": 1251, "comment": 61, "blank": 130}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart": {"language": "Dart", "code": 1390, "comment": 54, "blank": 78}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart": {"language": "Dart", "code": 195, "comment": 35, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart": {"language": "Dart", "code": 83, "comment": 14, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart": {"language": "Dart", "code": 443, "comment": 21, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart": {"language": "Dart", "code": 172, "comment": 20, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart": {"language": "Dart", "code": 1629, "comment": 65, "blank": 131}, "file:///Users/<USER>/development/flutter_apps/oneday/FAST_SWIPE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 140, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart": {"language": "Dart", "code": 101, "comment": 25, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart": {"language": "Dart", "code": 2766, "comment": 376, "blank": 453}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart": {"language": "Dart", "code": 166, "comment": 14, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_backup.dart": {"language": "Dart", "code": 2392, "comment": 224, "blank": 292}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart": {"language": "Dart", "code": 87, "comment": 1, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart": {"language": "Dart", "code": 201, "comment": 13, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart": {"language": "Dart", "code": 96, "comment": 21, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart": {"language": "Dart", "code": 11, "comment": 9, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart": {"language": "Dart", "code": 728, "comment": 41, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/GESTURE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart": {"language": "Dart", "code": 62, "comment": 8, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/KEYBOARD_AVOIDANCE_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 155, "comment": 0, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart": {"language": "Dart", "code": 1966, "comment": 71, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 250, "comment": 0, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/PRD.md": {"language": "<PERSON><PERSON>", "code": 339, "comment": 0, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md": {"language": "<PERSON><PERSON>", "code": 388, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Podfile": {"language": "<PERSON>", "code": 32, "comment": 1, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift": {"language": "Swift", "code": 14, "comment": 3, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart": {"language": "Dart", "code": 985, "comment": 72, "blank": 87}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart": {"language": "Dart", "code": 697, "comment": 42, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart": {"language": "Dart", "code": 1554, "comment": 78, "blank": 89}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart": {"language": "Dart", "code": 794, "comment": 24, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>": {"language": "Dart", "code": 836, "comment": 38, "blank": 45}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart": {"language": "Dart", "code": 1670, "comment": 186, "blank": 224}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules.md": {"language": "<PERSON><PERSON>", "code": 384, "comment": 0, "blank": 119}}