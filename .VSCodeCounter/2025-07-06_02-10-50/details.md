# Details

Date : 2025-07-06 02:10:50

Directory /Users/<USER>/development/flutter_apps

Total : 122 files,  32641 codes, 2133 comments, 4570 blanks, all 39344 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/Augrules.md](/.augment/rules/Augrules.md) | Markdown | 388 | 0 | 120 | 508 |
| [NOTION\_PROJECT\_TEMPLATE.md](/NOTION_PROJECT_TEMPLATE.md) | Markdown | 137 | 0 | 55 | 192 |
| [ONEDAY\_PROJECT\_ANALYSIS.md](/ONEDAY_PROJECT_ANALYSIS.md) | Markdown | 105 | 0 | 47 | 152 |
| [USER\_COMMUNITY\_TEMPLATE.md](/USER_COMMUNITY_TEMPLATE.md) | Markdown | 250 | 0 | 119 | 369 |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/Action.html](/oneday/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [oneday/BLUR\_TRANSITION\_OPTIMIZATION\_SUMMARY.md](/oneday/BLUR_TRANSITION_OPTIMIZATION_SUMMARY.md) | Markdown | 133 | 0 | 34 | 167 |
| [oneday/BUG\_FIX\_SUMMARY.md](/oneday/BUG_FIX_SUMMARY.md) | Markdown | 72 | 0 | 22 | 94 |
| [oneday/COVER\_MODE\_TEST\_GUIDE.md](/oneday/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [oneday/COVER\_MODE\_VERTICAL\_BOUNDARY\_TEST\_GUIDE.md](/oneday/COVER_MODE_VERTICAL_BOUNDARY_TEST_GUIDE.md) | Markdown | 98 | 0 | 35 | 133 |
| [oneday/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [oneday/FAST\_SWIPE\_FIX\_SUMMARY.md](/oneday/FAST_SWIPE_FIX_SUMMARY.md) | Markdown | 140 | 0 | 33 | 173 |
| [oneday/FINAL\_GESTURE\_FIX\_SUMMARY.md](/oneday/FINAL_GESTURE_FIX_SUMMARY.md) | Markdown | 115 | 0 | 33 | 148 |
| [oneday/GESTURE\_CONFLICT\_FIX\_SUMMARY.md](/oneday/GESTURE_CONFLICT_FIX_SUMMARY.md) | Markdown | 151 | 0 | 38 | 189 |
| [oneday/GESTURE\_DEBUG\_REPORT.md](/oneday/GESTURE_DEBUG_REPORT.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/GESTURE\_IMPLEMENTATION\_SUMMARY.md](/oneday/GESTURE_IMPLEMENTATION_SUMMARY.md) | Markdown | 99 | 0 | 32 | 131 |
| [oneday/GESTURE\_MODE\_TEST\_GUIDE.md](/oneday/GESTURE_MODE_TEST_GUIDE.md) | Markdown | 93 | 0 | 26 | 119 |
| [oneday/GESTURE\_OPTIMIZATION\_SUMMARY.md](/oneday/GESTURE_OPTIMIZATION_SUMMARY.md) | Markdown | 122 | 0 | 36 | 158 |
| [oneday/GESTURE\_TEST\_GUIDE.md](/oneday/GESTURE_TEST_GUIDE.md) | Markdown | 108 | 0 | 38 | 146 |
| [oneday/HORIZONTAL\_SWIPE\_FIX\_SUMMARY.md](/oneday/HORIZONTAL_SWIPE_FIX_SUMMARY.md) | Markdown | 137 | 0 | 36 | 173 |
| [oneday/ICON\_SETUP\_GUIDE.md](/oneday/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [oneday/IMAGE\_MARKING\_FUNCTION\_RESTORE.md](/oneday/IMAGE_MARKING_FUNCTION_RESTORE.md) | Markdown | 285 | 0 | 85 | 370 |
| [oneday/KEYBOARD\_AVOIDANCE\_IMPLEMENTATION.md](/oneday/KEYBOARD_AVOIDANCE_IMPLEMENTATION.md) | Markdown | 155 | 0 | 33 | 188 |
| [oneday/OPTIMIZING\_CURSOR\_AI.md](/oneday/OPTIMIZING_CURSOR_AI.md) | Markdown | 144 | 0 | 52 | 196 |
| [oneday/POSITION\_FOLLOWING\_SYSTEM.md](/oneday/POSITION_FOLLOWING_SYSTEM.md) | Markdown | 130 | 0 | 30 | 160 |
| [oneday/PRD.md](/oneday/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | 128 | 0 | 51 | 179 |
| [oneday/README.md](/oneday/README.md) | Markdown | 1,148 | 0 | 254 | 1,402 |
| [oneday/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/analysis\_options.yaml](/oneday/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [oneday/android/app/src/debug/AndroidManifest.xml](/oneday/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/app/src/main/AndroidManifest.xml](/oneday/android/app/src/main/AndroidManifest.xml) | XML | 38 | 12 | 2 | 52 |
| [oneday/android/app/src/main/res/drawable-v21/launch\_background.xml](/oneday/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/drawable/launch\_background.xml](/oneday/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/values-night/styles.xml](/oneday/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/main/res/values/styles.xml](/oneday/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/profile/AndroidManifest.xml](/oneday/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/gradle.properties](/oneday/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [oneday/android/gradle/wrapper/gradle-wrapper.properties](/oneday/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [oneday/assets/icons/README.md](/oneday/assets/icons/README.md) | Markdown | 33 | 0 | 13 | 46 |
| [oneday/blueprint.md](/oneday/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [oneday/debug\_share\_issue.md](/oneday/debug_share_issue.md) | Markdown | 73 | 0 | 24 | 97 |
| [oneday/ios/Podfile](/oneday/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [oneday/ios/Runner/AppDelegate.swift](/oneday/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 116 | 0 | 1 | 117 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard](/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [oneday/ios/Runner/Base.lproj/Main.storyboard](/oneday/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [oneday/ios/Runner/Runner-Bridging-Header.h](/oneday/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [oneday/ios/RunnerTests/RunnerTests.swift](/oneday/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/lib/core/constants/app\_icons.dart](/oneday/lib/core/constants/app_icons.dart) | Dart | 35 | 5 | 3 | 43 |
| [oneday/lib/core/data/pao\_exercises\_data.dart](/oneday/lib/core/data/pao_exercises_data.dart) | Dart | 272 | 18 | 17 | 307 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | 728 | 41 | 42 | 811 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 721 | 58 | 56 | 835 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 794 | 24 | 58 | 876 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 1,554 | 78 | 89 | 1,721 |
| [oneday/lib/features/exercise/exercise\_session\_page.dart](/oneday/lib/features/exercise/exercise_session_page.dart) | Dart | 985 | 72 | 87 | 1,144 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | 91 | 4 | 6 | 101 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 1,629 | 65 | 131 | 1,825 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 2,766 | 376 | 453 | 3,595 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_backup.dart](/oneday/lib/features/memory_palace/scene_detail_page_backup.dart) | Dart | 2,392 | 224 | 292 | 2,908 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | 1,670 | 186 | 224 | 2,080 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 212 | 9 | 17 | 238 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 756 | 55 | 70 | 881 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 789 | 44 | 75 | 908 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 443 | 21 | 50 | 514 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 1,251 | 61 | 130 | 1,442 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 697 | 42 | 53 | 792 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | 167 | 9 | 21 | 197 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 1,966 | 71 | 120 | 2,157 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 1,390 | 54 | 78 | 1,522 |
| [oneday/lib/features/wage\_system/wage\_wallet\_page.dart](/oneday/lib/features/wage_system/wage_wallet_page.dart) | Dart | 826 | 36 | 57 | 919 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 237 | 20 | 10 | 267 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 216 | 16 | 23 | 255 |
| [oneday/lib/shared/services/enhanced\_share\_service.dart](/oneday/lib/shared/services/enhanced_share_service.dart) | Dart | 158 | 27 | 37 | 222 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 195 | 35 | 45 | 275 |
| [oneday/lib/shared/utils/simple\_image\_test.dart](/oneday/lib/shared/utils/simple_image_test.dart) | Dart | 83 | 14 | 26 | 123 |
| [oneday/lib/shared/widgets/icons/wechat\_icon.dart](/oneday/lib/shared/widgets/icons/wechat_icon.dart) | Dart | 62 | 8 | 14 | 84 |
| [oneday/lib/shared/widgets/oneday\_logo.dart](/oneday/lib/shared/widgets/oneday_logo.dart) | Dart | 204 | 17 | 43 | 264 |
| [oneday/linux/CMakeLists.txt](/oneday/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [oneday/linux/flutter/CMakeLists.txt](/oneday/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [oneday/linux/flutter/generated\_plugin\_registrant.cc](/oneday/linux/flutter/generated_plugin_registrant.cc) | C++ | 11 | 4 | 5 | 20 |
| [oneday/linux/flutter/generated\_plugin\_registrant.h](/oneday/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/linux/flutter/generated\_plugins.cmake](/oneday/linux/flutter/generated_plugins.cmake) | CMake | 20 | 0 | 6 | 26 |
| [oneday/linux/runner/CMakeLists.txt](/oneday/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [oneday/linux/runner/main.cc](/oneday/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [oneday/linux/runner/my\_application.cc](/oneday/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [oneday/linux/runner/my\_application.h](/oneday/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [oneday/macos/Flutter/GeneratedPluginRegistrant.swift](/oneday/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 14 | 3 | 4 | 21 |
| [oneday/macos/Podfile](/oneday/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [oneday/macos/Runner/AppDelegate.swift](/oneday/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [oneday/macos/Runner/Base.lproj/MainMenu.xib](/oneday/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [oneday/macos/Runner/MainFlutterWindow.swift](/oneday/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [oneday/macos/RunnerTests/RunnerTests.swift](/oneday/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 47 | 69 | 26 | 142 |
| [oneday/test/create\_album\_success\_message\_test.dart](/oneday/test/create_album_success_message_test.dart) | Dart | 172 | 20 | 27 | 219 |
| [oneday/test/memory\_palace\_card\_test.dart](/oneday/test/memory_palace_card_test.dart) | Dart | 201 | 13 | 29 | 243 |
| [oneday/test/memory\_palace\_persistence\_test.dart](/oneday/test/memory_palace_persistence_test.dart) | Dart | 166 | 14 | 25 | 205 |
| [oneday/test/photo\_album\_creator\_test.dart](/oneday/test/photo_album_creator_test.dart) | Dart | 87 | 1 | 16 | 104 |
| [oneday/test/photo\_album\_dialog\_layout\_test.dart](/oneday/test/photo_album_dialog_layout_test.dart) | Dart | 101 | 25 | 32 | 158 |
| [oneday/test/photo\_album\_dialog\_test.dart](/oneday/test/photo_album_dialog_test.dart) | Dart | 96 | 21 | 28 | 145 |
| [oneday/test/widget\_test.dart](/oneday/test/widget_test.dart) | Dart | 11 | 9 | 6 | 26 |
| [oneday/test\_share\_function.md](/oneday/test_share_function.md) | Markdown | 94 | 0 | 32 | 126 |
| [oneday/web/index.html](/oneday/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [oneday/web/manifest.json](/oneday/web/manifest.json) | JSON | 35 | 0 | 0 | 35 |
| [oneday/windows/CMakeLists.txt](/oneday/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [oneday/windows/flutter/CMakeLists.txt](/oneday/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [oneday/windows/flutter/generated\_plugin\_registrant.cc](/oneday/windows/flutter/generated_plugin_registrant.cc) | C++ | 15 | 4 | 5 | 24 |
| [oneday/windows/flutter/generated\_plugin\_registrant.h](/oneday/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/windows/flutter/generated\_plugins.cmake](/oneday/windows/flutter/generated_plugins.cmake) | CMake | 22 | 0 | 6 | 28 |
| [oneday/windows/runner/CMakeLists.txt](/oneday/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [oneday/windows/runner/flutter\_window.cpp](/oneday/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [oneday/windows/runner/flutter\_window.h](/oneday/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [oneday/windows/runner/main.cpp](/oneday/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [oneday/windows/runner/resource.h](/oneday/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [oneday/windows/runner/utils.cpp](/oneday/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [oneday/windows/runner/utils.h](/oneday/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [oneday/windows/runner/win32\_window.cpp](/oneday/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [oneday/windows/runner/win32\_window.h](/oneday/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)