"filename", "language", "<PERSON>down", "<PERSON><PERSON>", "JSON", "C++", "C<PERSON>ake", "YAM<PERSON>", "HTML", "Properties", "Python", "Ruby", "XML", "Swift", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md", "Markdown", 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 508
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md", "Markdown", 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 125, 553
"/Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 192
"/Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 152
"/Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md", "Markdown", 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 369
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.backup.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 112, 496
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules_副本.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/ANNOTATION_SCALE_FIX_SUMMARY.md", "Markdown", 154, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 197
"/Users/<USER>/development/flutter_apps/oneday/ANNOTATION_SCALE_FIX_TEST_GUIDE.md", "Markdown", 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 187
"/Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md", "Markdown", 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 461
"/Users/<USER>/development/flutter_apps/oneday/Action.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/development/flutter_apps/oneday/COMPREHENSIVE_TEST_GUIDE.md", "Markdown", 276, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 96, 372
"/Users/<USER>/development/flutter_apps/oneday/COVER_MODE_TEST_GUIDE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 150
"/Users/<USER>/development/flutter_apps/oneday/DEVELOPER_ENTRANCE_GUIDE.md", "Markdown", 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 83
"/Users/<USER>/development/flutter_apps/oneday/EDIT_ALBUM_ENHANCEMENT.md", "Markdown", 167, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 210
"/Users/<USER>/development/flutter_apps/oneday/FEATURES.md", "Markdown", 601, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 721
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_DEBUG_GUIDE.md", "Markdown", 199, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 259
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_FEATURE.md", "Markdown", 74, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 94
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_FIXES_VERIFICATION.md", "Markdown", 190, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 50, 240
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md", "Markdown", 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 40, 202
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_FINAL_OPTIMIZATION.md", "Markdown", 142, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 177
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 164
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_TEST_CHECKLIST.md", "Markdown", 114, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 141
"/Users/<USER>/development/flutter_apps/oneday/ICON_SETUP_GUIDE.md", "Markdown", 231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 259
"/Users/<USER>/development/flutter_apps/oneday/KNOWLEDGE_POINT_SYNC_FIX.md", "Markdown", 191, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 233
"/Users/<USER>/development/flutter_apps/oneday/NOTION_STYLE_UPDATES.md", "Markdown", 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 169
"/Users/<USER>/development/flutter_apps/oneday/PRD.md", "Markdown", 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 436
"/Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md", "Markdown", 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 345
"/Users/<USER>/development/flutter_apps/oneday/QUICK_TEST_STEPS.md", "Markdown", 85, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 111
"/Users/<USER>/development/flutter_apps/oneday/README.md", "Markdown", 1163, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 259, 1422
"/Users/<USER>/development/flutter_apps/oneday/REMOVE_CAMERA_FEATURE.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 140
"/Users/<USER>/development/flutter_apps/oneday/SHARE_FUNCTION_SIMPLIFICATION.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 128
"/Users/<USER>/development/flutter_apps/oneday/SHARE_UI_RESTORE_FEATURE.md", "Markdown", 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 155
"/Users/<USER>/development/flutter_apps/oneday/SWIPE_GESTURE_DEMO.md", "Markdown", 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 264
"/Users/<USER>/development/flutter_apps/oneday/SYSTEM_FLOATING_TIMER_USER_GUIDE.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 158
"/Users/<USER>/development/flutter_apps/oneday/TESTING_GUIDE.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 137
"/Users/<USER>/development/flutter_apps/oneday/TIMEBOX_TIMER_FIXES.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/TIMER_FIXES_PROGRESS.md", "Markdown", 131, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 166
"/Users/<USER>/development/flutter_apps/oneday/TIMER_FLOATING_WINDOW_GUIDE.md", "Markdown", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 96
"/Users/<USER>/development/flutter_apps/oneday/TIMER_TEST_GUIDE.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 153
"/Users/<USER>/development/flutter_apps/oneday/WORD_TRAINING_OPTIMIZATION.md", "Markdown", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 39, 147
"/Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml", "YAML", 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 22, 4, 29
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 41, 0, 13, 3, 57
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 0, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 0, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 0, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/gradle.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties", "Properties", 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json", "JSON", 0, 0, 88, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 89
"/Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json", "JSON", 0, 0, 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 100
"/Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json", "JSON", 0, 0, 84516, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 84516
"/Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary_original.json", "JSON", 0, 0, 38714, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 38715
"/Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json", "JSON", 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 159
"/Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md", "Markdown", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 46
"/Users/<USER>/development/flutter_apps/oneday/blueprint.md", "Markdown", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 88
"/Users/<USER>/development/flutter_apps/oneday/build.yaml", "YAML", 0, 0, 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 6, 1, 18
"/Users/<USER>/development/flutter_apps/oneday/ios/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 3, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 2, 14
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 116, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 117
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 24
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 0, 1, 1, 38
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 0, 1, 1, 27
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart", "Dart", 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 3, 43
"/Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart", "Dart", 0, 272, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 17, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart", "Dart", 0, 666, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 41, 745
"/Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart", "Dart", 0, 972, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 69, 68, 1109
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 780, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 58, 862
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart", "Dart", 0, 151, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 34, 214
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart", "Dart", 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 7, 69
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart", "Dart", 0, 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 44, 279
"/Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart", "Dart", 0, 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 48, 297
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 1837, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 98, 112, 2047
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart", "Dart", 0, 985, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 72, 87, 1144
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart", "Dart", 0, 397, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 35, 447
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 794, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 41, 867
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 6, 114
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 5075, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 383, 470, 5928
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 2930, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 435, 501, 3866
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_backup.dart", "Dart", 0, 2394, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 224, 292, 2910
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_副本.dart", "Dart", 0, 1672, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 186, 224, 2082
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 241, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 18, 29, 288
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 785, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 59, 75, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 808, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 49, 81, 938
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 497, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 59, 582
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 1252, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 61, 130, 1443
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 698, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 53, 793
"/Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart", "Dart", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 22, 220
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart", "Dart", 0, 244, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 51, 337
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart", "Dart", 0, 71, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 8, 94
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart", "Dart", 0, 217, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 25, 261
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart", "Dart", 0, 2663, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 131, 182, 2976
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart", "Dart", 0, 452, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 39, 502
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart", "Dart", 0, 233, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 64, 330
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart", "Dart", 0, 894, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 71, 1013
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_data_converter.dart", "Dart", 0, 194, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 31, 40, 265
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart", "Dart", 0, 232, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 47, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart", "Dart", 0, 991, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 72, 1103
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart", "Dart", 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 12, 176
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart", "Dart", 0, 407, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 60, 88, 555
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart", "Dart", 0, 845, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 83, 957
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart", "Dart", 0, 162, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 32, 211
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart", "Dart", 0, 453, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 34, 494
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart", "Dart", 0, 189, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 37, 254
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 1412, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 78, 1544
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart", "Dart", 0, 826, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 57, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 242, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 23, 12, 277
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 238, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 27, 285
"/Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart", "Dart", 0, 44, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 13, 73
"/Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart", "Dart", 0, 434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 65, 69, 568
"/Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart", "Dart", 0, 161, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 42, 52, 255
"/Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart", "Dart", 0, 248, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 24, 298
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart", "Dart", 0, 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 19, 108
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart", "Dart", 0, 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 27, 37, 224
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart", "Dart", 0, 187, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 44, 266
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart", "Dart", 0, 246, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 62, 62, 370
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart", "Dart", 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 26, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart", "Dart", 0, 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 9, 122
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart", "Dart", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 14, 84
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart", "Dart", 0, 204, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 17, 43, 264
"/Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt", "CMake", 0, 0, 0, 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 25, 129
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 10, 89
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 20
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc", "C++", 0, 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 131
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h", "C++", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 3, 4, 21
"/Users/<USER>/development/flutter_apps/oneday/macos/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 1, 10, 43
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 3, 14
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 68
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 0, 1, 344
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 4, 16
"/Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 71, 28, 153
"/Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py", "Python", 0, 0, 0, 0, 0, 0, 0, 0, 152, 0, 0, 0, 26, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart", "Dart", 0, 146, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 48, 224
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart", "Dart", 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 15, 21, 137
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 40, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 152, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 52, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 171, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 61, 277
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 20, 124
"/Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart", "Dart", 0, 172, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 27, 219
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart", "Dart", 0, 201, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 29, 243
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart", "Dart", 0, 166, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 25, 205
"/Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart", "Dart", 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart", "Dart", 0, 81, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 17, 100
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart", "Dart", 0, 101, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 32, 158
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart", "Dart", 0, 96, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 21, 28, 145
"/Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart", "Dart", 0, 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 32, 164
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart", "Dart", 0, 216, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 29, 254
"/Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart", "Dart", 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart", "Dart", 0, 62, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 14, 78
"/Users/<USER>/development/flutter_apps/oneday/test_bubble_alignment_adjustment.dart", "Dart", 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 23, 135
"/Users/<USER>/development/flutter_apps/oneday/test_bubble_alignment_verification.dart", "Dart", 0, 103, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 26, 139
"/Users/<USER>/development/flutter_apps/oneday/test_bubble_center_alignment.dart", "Dart", 0, 92, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 20, 120
"/Users/<USER>/development/flutter_apps/oneday/test_bubble_positioning_fix.dart", "Dart", 0, 109, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/test_compilation_fix.dart", "Dart", 0, 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 29, 180
"/Users/<USER>/development/flutter_apps/oneday/test_complete_image_compression.dart", "Dart", 0, 186, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 38, 233
"/Users/<USER>/development/flutter_apps/oneday/test_coordinate_analysis.dart", "Dart", 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 27, 140
"/Users/<USER>/development/flutter_apps/oneday/test_coordinate_debug.dart", "Dart", 0, 84, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 5, 18, 107
"/Users/<USER>/development/flutter_apps/oneday/test_coordinate_transformation_fix.dart", "Dart", 0, 110, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 16, 24, 150
"/Users/<USER>/development/flutter_apps/oneday/test_image_compression_implementation.dart", "Dart", 0, 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 33, 187
"/Users/<USER>/development/flutter_apps/oneday/test_matrix_analysis.dart", "Dart", 0, 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8, 37, 175
"/Users/<USER>/development/flutter_apps/oneday/test_matrix_fix_verification.dart", "Dart", 0, 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 31, 150
"/Users/<USER>/development/flutter_apps/oneday/test_position_fix_verification.dart", "Dart", 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 11, 53
"/Users/<USER>/development/flutter_apps/oneday/web/index.html", "HTML", 0, 0, 0, 0, 0, 0, 19, 0, 0, 0, 0, 0, 15, 5, 39
"/Users/<USER>/development/flutter_apps/oneday/web/manifest.json", "JSON", 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35
"/Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt", "CMake", 0, 0, 0, 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt", "CMake", 0, 0, 0, 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 12, 110
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 24
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h", "C++", 0, 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake", "CMake", 0, 0, 0, 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt", "CMake", 0, 0, 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 7, 41
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp", "C++", 0, 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h", "C++", 0, 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp", "C++", 0, 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h", "C++", 0, 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp", "C++", 0, 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h", "C++", 0, 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp", "C++", 0, 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h", "C++", 0, 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 31, 24, 103
"/Users/<USER>/development/flutter_apps/oneday/动作.html", "HTML", 0, 0, 0, 0, 0, 0, 693, 0, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/tempPrompts.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"Total", "-", 9157, 40633, 123817, 560, 467, 68, 712, 8, 152, 63, 477, 63, 3797, 7903, 187877