# Details

Date : 2025-07-11 06:51:39

Directory /Users/<USER>/development/flutter_apps

Total : 192 files,  176177 codes, 3797 comments, 7903 blanks, all 187877 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/Augrules.backup.md](/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [.augment/rules/Augrules.md](/.augment/rules/Augrules.md) | Markdown | 428 | 0 | 125 | 553 |
| [NOTION\_PROJECT\_TEMPLATE.md](/NOTION_PROJECT_TEMPLATE.md) | Markdown | 137 | 0 | 55 | 192 |
| [ONEDAY\_PROJECT\_ANALYSIS.md](/ONEDAY_PROJECT_ANALYSIS.md) | Markdown | 105 | 0 | 47 | 152 |
| [USER\_COMMUNITY\_TEMPLATE.md](/USER_COMMUNITY_TEMPLATE.md) | Markdown | 250 | 0 | 119 | 369 |
| [oneday/.cursorrules.backup.md](/oneday/.cursorrules.backup.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 384 | 0 | 112 | 496 |
| [oneday/.cursorrules\_副本.md](/oneday/.cursorrules_%E5%89%AF%E6%9C%AC.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/oneday/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | 154 | 0 | 43 | 197 |
| [oneday/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/oneday/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | 144 | 0 | 43 | 187 |
| [oneday/ARCHITECTURE.md](/oneday/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [oneday/Action.html](/oneday/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [oneday/COMPREHENSIVE\_TEST\_GUIDE.md](/oneday/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | 276 | 0 | 96 | 372 |
| [oneday/COVER\_MODE\_TEST\_GUIDE.md](/oneday/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [oneday/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [oneday/EDIT\_ALBUM\_ENHANCEMENT.md](/oneday/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | 167 | 0 | 43 | 210 |
| [oneday/FEATURES.md](/oneday/FEATURES.md) | Markdown | 601 | 0 | 120 | 721 |
| [oneday/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/oneday/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | 199 | 0 | 60 | 259 |
| [oneday/FLOATING\_TIMER\_FEATURE.md](/oneday/FLOATING_TIMER_FEATURE.md) | Markdown | 74 | 0 | 20 | 94 |
| [oneday/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/oneday/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | 190 | 0 | 50 | 240 |
| [oneday/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/oneday/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | 160 | 2 | 40 | 202 |
| [oneday/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/oneday/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | 142 | 0 | 35 | 177 |
| [oneday/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/oneday/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | 129 | 0 | 35 | 164 |
| [oneday/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/oneday/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | 114 | 0 | 27 | 141 |
| [oneday/ICON\_SETUP\_GUIDE.md](/oneday/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [oneday/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/oneday/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | 191 | 0 | 42 | 233 |
| [oneday/NOTION\_STYLE\_UPDATES.md](/oneday/NOTION_STYLE_UPDATES.md) | Markdown | 125 | 0 | 44 | 169 |
| [oneday/PRD.md](/oneday/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | 250 | 0 | 95 | 345 |
| [oneday/QUICK\_TEST\_STEPS.md](/oneday/QUICK_TEST_STEPS.md) | Markdown | 85 | 0 | 26 | 111 |
| [oneday/README.md](/oneday/README.md) | Markdown | 1,163 | 0 | 259 | 1,422 |
| [oneday/REMOVE\_CAMERA\_FEATURE.md](/oneday/REMOVE_CAMERA_FEATURE.md) | Markdown | 105 | 0 | 35 | 140 |
| [oneday/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/SHARE\_UI\_RESTORE\_FEATURE.md](/oneday/SHARE_UI_RESTORE_FEATURE.md) | Markdown | 120 | 0 | 35 | 155 |
| [oneday/SWIPE\_GESTURE\_DEMO.md](/oneday/SWIPE_GESTURE_DEMO.md) | Markdown | 216 | 0 | 48 | 264 |
| [oneday/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/oneday/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | 117 | 0 | 41 | 158 |
| [oneday/TESTING\_GUIDE.md](/oneday/TESTING_GUIDE.md) | Markdown | 103 | 0 | 34 | 137 |
| [oneday/TIMEBOX\_TIMER\_FIXES.md](/oneday/TIMEBOX_TIMER_FIXES.md) | Markdown | 115 | 0 | 27 | 142 |
| [oneday/TIMER\_FIXES\_PROGRESS.md](/oneday/TIMER_FIXES_PROGRESS.md) | Markdown | 131 | 0 | 35 | 166 |
| [oneday/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/oneday/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | 69 | 0 | 27 | 96 |
| [oneday/TIMER\_TEST\_GUIDE.md](/oneday/TIMER_TEST_GUIDE.md) | Markdown | 121 | 0 | 32 | 153 |
| [oneday/WORD\_TRAINING\_OPTIMIZATION.md](/oneday/WORD_TRAINING_OPTIMIZATION.md) | Markdown | 108 | 0 | 39 | 147 |
| [oneday/analysis\_options.yaml](/oneday/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [oneday/android/app/src/debug/AndroidManifest.xml](/oneday/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/app/src/main/AndroidManifest.xml](/oneday/android/app/src/main/AndroidManifest.xml) | XML | 41 | 13 | 3 | 57 |
| [oneday/android/app/src/main/res/drawable-v21/launch\_background.xml](/oneday/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/drawable/launch\_background.xml](/oneday/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/values-night/styles.xml](/oneday/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/main/res/values/styles.xml](/oneday/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/profile/AndroidManifest.xml](/oneday/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/gradle.properties](/oneday/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [oneday/android/gradle/wrapper/gradle-wrapper.properties](/oneday/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [oneday/assets/data/prefixes.json](/oneday/assets/data/prefixes.json) | JSON | 88 | 0 | 1 | 89 |
| [oneday/assets/data/suffixes.json](/oneday/assets/data/suffixes.json) | JSON | 99 | 0 | 1 | 100 |
| [oneday/assets/data/vocabulary.json](/oneday/assets/data/vocabulary.json) | JSON | 84,516 | 0 | 0 | 84,516 |
| [oneday/assets/data/vocabulary\_original.json](/oneday/assets/data/vocabulary_original.json) | JSON | 38,714 | 0 | 1 | 38,715 |
| [oneday/assets/data/word\_roots.json](/oneday/assets/data/word_roots.json) | JSON | 158 | 0 | 1 | 159 |
| [oneday/assets/icons/README.md](/oneday/assets/icons/README.md) | Markdown | 33 | 0 | 13 | 46 |
| [oneday/blueprint.md](/oneday/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [oneday/build.yaml](/oneday/build.yaml) | YAML | 11 | 6 | 1 | 18 |
| [oneday/ios/Podfile](/oneday/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [oneday/ios/Runner/AppDelegate.swift](/oneday/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 116 | 0 | 1 | 117 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard](/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [oneday/ios/Runner/Base.lproj/Main.storyboard](/oneday/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [oneday/ios/Runner/Runner-Bridging-Header.h](/oneday/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [oneday/ios/RunnerTests/RunnerTests.swift](/oneday/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/lib/core/constants/app\_icons.dart](/oneday/lib/core/constants/app_icons.dart) | Dart | 35 | 5 | 3 | 43 |
| [oneday/lib/core/data/pao\_exercises\_data.dart](/oneday/lib/core/data/pao_exercises_data.dart) | Dart | 272 | 18 | 17 | 307 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | 666 | 38 | 41 | 745 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 972 | 69 | 68 | 1,109 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 780 | 24 | 58 | 862 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.dart](/oneday/lib/features/daily_plan/models/daily_plan.dart) | Dart | 151 | 29 | 34 | 214 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.g.dart](/oneday/lib/features/daily_plan/models/daily_plan.g.dart) | Dart | 49 | 13 | 7 | 69 |
| [oneday/lib/features/daily\_plan/notifiers/daily\_plan\_notifier.dart](/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart) | Dart | 204 | 31 | 44 | 279 |
| [oneday/lib/features/daily\_plan/services/daily\_plan\_storage\_service.dart](/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart) | Dart | 216 | 33 | 48 | 297 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 1,837 | 98 | 112 | 2,047 |
| [oneday/lib/features/exercise/exercise\_session\_page.dart](/oneday/lib/features/exercise/exercise_session_page.dart) | Dart | 985 | 72 | 87 | 1,144 |
| [oneday/lib/features/exercise/focused\_training\_page.dart](/oneday/lib/features/exercise/focused_training_page.dart) | Dart | 397 | 15 | 35 | 447 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | 104 | 4 | 6 | 114 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 5,075 | 383 | 470 | 5,928 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 2,930 | 435 | 501 | 3,866 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_backup.dart](/oneday/lib/features/memory_palace/scene_detail_page_backup.dart) | Dart | 2,394 | 224 | 292 | 2,910 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | 1,672 | 186 | 224 | 2,082 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 241 | 18 | 29 | 288 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 785 | 59 | 75 | 919 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 808 | 49 | 81 | 938 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 497 | 26 | 59 | 582 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 1,252 | 61 | 130 | 1,443 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 698 | 42 | 53 | 793 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | 186 | 12 | 22 | 220 |
| [oneday/lib/features/time\_box/models/timebox\_models.dart](/oneday/lib/features/time_box/models/timebox_models.dart) | Dart | 244 | 42 | 51 | 337 |
| [oneday/lib/features/time\_box/models/timebox\_models.g.dart](/oneday/lib/features/time_box/models/timebox_models.g.dart) | Dart | 71 | 15 | 8 | 94 |
| [oneday/lib/features/time\_box/providers/timebox\_provider.dart](/oneday/lib/features/time_box/providers/timebox_provider.dart) | Dart | 217 | 19 | 25 | 261 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 2,663 | 131 | 182 | 2,976 |
| [oneday/lib/features/vocabulary/create\_vocabulary\_page.dart](/oneday/lib/features/vocabulary/create_vocabulary_page.dart) | Dart | 452 | 11 | 39 | 502 |
| [oneday/lib/features/vocabulary/vocabulary\_cache\_manager.dart](/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart) | Dart | 233 | 33 | 64 | 330 |
| [oneday/lib/features/vocabulary/vocabulary\_category\_page.dart](/oneday/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 894 | 48 | 71 | 1,013 |
| [oneday/lib/features/vocabulary/vocabulary\_data\_converter.dart](/oneday/lib/features/vocabulary/vocabulary_data_converter.dart) | Dart | 194 | 31 | 40 | 265 |
| [oneday/lib/features/vocabulary/vocabulary\_learning\_service.dart](/oneday/lib/features/vocabulary/vocabulary_learning_service.dart) | Dart | 232 | 28 | 47 | 307 |
| [oneday/lib/features/vocabulary/vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | 991 | 40 | 72 | 1,103 |
| [oneday/lib/features/vocabulary/vocabulary\_page.dart](/oneday/lib/features/vocabulary/vocabulary_page.dart) | Dart | 162 | 2 | 12 | 176 |
| [oneday/lib/features/vocabulary/vocabulary\_service.dart](/oneday/lib/features/vocabulary/vocabulary_service.dart) | Dart | 407 | 60 | 88 | 555 |
| [oneday/lib/features/vocabulary/vocabulary\_statistics\_page.dart](/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart) | Dart | 845 | 29 | 83 | 957 |
| [oneday/lib/features/vocabulary/word\_meaning\_service.dart](/oneday/lib/features/vocabulary/word_meaning_service.dart) | Dart | 162 | 17 | 32 | 211 |
| [oneday/lib/features/vocabulary/word\_model.dart](/oneday/lib/features/vocabulary/word_model.dart) | Dart | 453 | 7 | 34 | 494 |
| [oneday/lib/features/vocabulary/word\_root\_service.dart](/oneday/lib/features/vocabulary/word_root_service.dart) | Dart | 189 | 28 | 37 | 254 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 1,412 | 54 | 78 | 1,544 |
| [oneday/lib/features/wage\_system/wage\_wallet\_page.dart](/oneday/lib/features/wage_system/wage_wallet_page.dart) | Dart | 826 | 36 | 57 | 919 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 242 | 23 | 12 | 277 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 238 | 20 | 27 | 285 |
| [oneday/lib/services/first\_time\_service.dart](/oneday/lib/services/first_time_service.dart) | Dart | 44 | 16 | 13 | 73 |
| [oneday/lib/services/floating\_timer\_service.dart](/oneday/lib/services/floating_timer_service.dart) | Dart | 434 | 65 | 69 | 568 |
| [oneday/lib/services/global\_timer\_service.dart](/oneday/lib/services/global_timer_service.dart) | Dart | 161 | 42 | 52 | 255 |
| [oneday/lib/services/system\_overlay\_permission.dart](/oneday/lib/services/system_overlay_permission.dart) | Dart | 248 | 26 | 24 | 298 |
| [oneday/lib/shared/config/image\_compression\_config.dart](/oneday/lib/shared/config/image_compression_config.dart) | Dart | 69 | 20 | 19 | 108 |
| [oneday/lib/shared/services/enhanced\_share\_service.dart](/oneday/lib/shared/services/enhanced_share_service.dart) | Dart | 160 | 27 | 37 | 224 |
| [oneday/lib/shared/utils/image\_compression\_utils.dart](/oneday/lib/shared/utils/image_compression_utils.dart) | Dart | 187 | 35 | 44 | 266 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 246 | 62 | 62 | 370 |
| [oneday/lib/shared/utils/simple\_image\_test.dart](/oneday/lib/shared/utils/simple_image_test.dart) | Dart | 83 | 14 | 26 | 123 |
| [oneday/lib/shared/utils/ui\_utils.dart](/oneday/lib/shared/utils/ui_utils.dart) | Dart | 94 | 19 | 9 | 122 |
| [oneday/lib/shared/widgets/icons/wechat\_icon.dart](/oneday/lib/shared/widgets/icons/wechat_icon.dart) | Dart | 62 | 8 | 14 | 84 |
| [oneday/lib/shared/widgets/oneday\_logo.dart](/oneday/lib/shared/widgets/oneday_logo.dart) | Dart | 204 | 17 | 43 | 264 |
| [oneday/linux/CMakeLists.txt](/oneday/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [oneday/linux/flutter/CMakeLists.txt](/oneday/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [oneday/linux/flutter/generated\_plugin\_registrant.cc](/oneday/linux/flutter/generated_plugin_registrant.cc) | C++ | 11 | 4 | 5 | 20 |
| [oneday/linux/flutter/generated\_plugin\_registrant.h](/oneday/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/linux/flutter/generated\_plugins.cmake](/oneday/linux/flutter/generated_plugins.cmake) | CMake | 20 | 0 | 6 | 26 |
| [oneday/linux/runner/CMakeLists.txt](/oneday/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [oneday/linux/runner/main.cc](/oneday/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [oneday/linux/runner/my\_application.cc](/oneday/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [oneday/linux/runner/my\_application.h](/oneday/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [oneday/macos/Flutter/GeneratedPluginRegistrant.swift](/oneday/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 14 | 3 | 4 | 21 |
| [oneday/macos/Podfile](/oneday/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [oneday/macos/Runner/AppDelegate.swift](/oneday/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [oneday/macos/Runner/Base.lproj/MainMenu.xib](/oneday/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [oneday/macos/Runner/MainFlutterWindow.swift](/oneday/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [oneday/macos/RunnerTests/RunnerTests.swift](/oneday/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 54 | 71 | 28 | 153 |
| [oneday/scripts/transform\_vocabulary.py](/oneday/scripts/transform_vocabulary.py) | Python | 152 | 26 | 32 | 210 |
| [oneday/test/add\_child\_category\_test.dart](/oneday/test/add_child_category_test.dart) | Dart | 146 | 30 | 48 | 224 |
| [oneday/test/category\_edit\_fix\_test.dart](/oneday/test/category_edit_fix_test.dart) | Dart | 101 | 15 | 21 | 137 |
| [oneday/test/category\_edit\_state\_test.dart](/oneday/test/category_edit_state_test.dart) | Dart | 147 | 40 | 55 | 242 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 152 | 38 | 52 | 242 |
| [oneday/test/category\_sidebar\_edit\_test.dart](/oneday/test/category_sidebar_edit_test.dart) | Dart | 171 | 45 | 61 | 277 |
| [oneday/test/context\_aware\_album\_creation\_test.dart](/oneday/test/context_aware_album_creation_test.dart) | Dart | 98 | 6 | 20 | 124 |
| [oneday/test/create\_album\_success\_message\_test.dart](/oneday/test/create_album_success_message_test.dart) | Dart | 172 | 20 | 27 | 219 |
| [oneday/test/memory\_palace\_card\_test.dart](/oneday/test/memory_palace_card_test.dart) | Dart | 201 | 13 | 29 | 243 |
| [oneday/test/memory\_palace\_persistence\_test.dart](/oneday/test/memory_palace_persistence_test.dart) | Dart | 166 | 14 | 25 | 205 |
| [oneday/test/onboarding\_web\_navigation\_test.dart](/oneday/test/onboarding_web_navigation_test.dart) | Dart | 30 | 9 | 11 | 50 |
| [oneday/test/photo\_album\_creator\_test.dart](/oneday/test/photo_album_creator_test.dart) | Dart | 81 | 2 | 17 | 100 |
| [oneday/test/photo\_album\_dialog\_layout\_test.dart](/oneday/test/photo_album_dialog_layout_test.dart) | Dart | 101 | 25 | 32 | 158 |
| [oneday/test/photo\_album\_dialog\_test.dart](/oneday/test/photo_album_dialog_test.dart) | Dart | 96 | 21 | 28 | 145 |
| [oneday/test/three\_dot\_menu\_test.dart](/oneday/test/three_dot_menu_test.dart) | Dart | 108 | 24 | 32 | 164 |
| [oneday/test/vocabulary\_test.dart](/oneday/test/vocabulary_test.dart) | Dart | 216 | 9 | 29 | 254 |
| [oneday/test/widget\_test.dart](/oneday/test/widget_test.dart) | Dart | 11 | 9 | 6 | 26 |
| [oneday/test/word\_meaning\_service\_test.dart](/oneday/test/word_meaning_service_test.dart) | Dart | 62 | 2 | 14 | 78 |
| [oneday/test\_bubble\_alignment\_adjustment.dart](/oneday/test_bubble_alignment_adjustment.dart) | Dart | 103 | 9 | 23 | 135 |
| [oneday/test\_bubble\_alignment\_verification.dart](/oneday/test_bubble_alignment_verification.dart) | Dart | 103 | 10 | 26 | 139 |
| [oneday/test\_bubble\_center\_alignment.dart](/oneday/test_bubble_center_alignment.dart) | Dart | 92 | 8 | 20 | 120 |
| [oneday/test\_bubble\_positioning\_fix.dart](/oneday/test_bubble_positioning_fix.dart) | Dart | 109 | 6 | 27 | 142 |
| [oneday/test\_compilation\_fix.dart](/oneday/test_compilation_fix.dart) | Dart | 144 | 7 | 29 | 180 |
| [oneday/test\_complete\_image\_compression.dart](/oneday/test_complete_image_compression.dart) | Dart | 186 | 9 | 38 | 233 |
| [oneday/test\_coordinate\_analysis.dart](/oneday/test_coordinate_analysis.dart) | Dart | 104 | 9 | 27 | 140 |
| [oneday/test\_coordinate\_debug.dart](/oneday/test_coordinate_debug.dart) | Dart | 84 | 5 | 18 | 107 |
| [oneday/test\_coordinate\_transformation\_fix.dart](/oneday/test_coordinate_transformation_fix.dart) | Dart | 110 | 16 | 24 | 150 |
| [oneday/test\_image\_compression\_implementation.dart](/oneday/test_image_compression_implementation.dart) | Dart | 147 | 7 | 33 | 187 |
| [oneday/test\_matrix\_analysis.dart](/oneday/test_matrix_analysis.dart) | Dart | 130 | 8 | 37 | 175 |
| [oneday/test\_matrix\_fix\_verification.dart](/oneday/test_matrix_fix_verification.dart) | Dart | 105 | 14 | 31 | 150 |
| [oneday/test\_position\_fix\_verification.dart](/oneday/test_position_fix_verification.dart) | Dart | 38 | 4 | 11 | 53 |
| [oneday/web/index.html](/oneday/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [oneday/web/manifest.json](/oneday/web/manifest.json) | JSON | 35 | 0 | 0 | 35 |
| [oneday/windows/CMakeLists.txt](/oneday/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [oneday/windows/flutter/CMakeLists.txt](/oneday/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [oneday/windows/flutter/generated\_plugin\_registrant.cc](/oneday/windows/flutter/generated_plugin_registrant.cc) | C++ | 15 | 4 | 5 | 24 |
| [oneday/windows/flutter/generated\_plugin\_registrant.h](/oneday/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/windows/flutter/generated\_plugins.cmake](/oneday/windows/flutter/generated_plugins.cmake) | CMake | 22 | 0 | 6 | 28 |
| [oneday/windows/runner/CMakeLists.txt](/oneday/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [oneday/windows/runner/flutter\_window.cpp](/oneday/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [oneday/windows/runner/flutter\_window.h](/oneday/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [oneday/windows/runner/main.cpp](/oneday/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [oneday/windows/runner/resource.h](/oneday/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [oneday/windows/runner/utils.cpp](/oneday/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [oneday/windows/runner/utils.h](/oneday/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [oneday/windows/runner/win32\_window.cpp](/oneday/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [oneday/windows/runner/win32\_window.h](/oneday/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |
| [oneday/动作.html](/oneday/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 0 | 0 | 1 | 1 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)