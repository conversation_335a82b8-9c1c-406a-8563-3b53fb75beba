{"file:///Users/<USER>/development/flutter_apps/oneday/ICON_SETUP_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 231, "comment": 0, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/blueprint.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/ANNOTATION_SCALE_FIX_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 144, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/COVER_MODE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/tempPrompts.md": {"language": "<PERSON><PERSON>", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/test_bubble_alignment_adjustment.dart": {"language": "Dart", "code": 103, "comment": 9, "blank": 23}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/suffixes.json": {"language": "JSON", "code": 99, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_DEBUG_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 199, "comment": 0, "blank": 60}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/word_roots.json": {"language": "JSON", "code": 158, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/prefixes.json": {"language": "JSON", "code": 88, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md": {"language": "<PERSON><PERSON>", "code": 250, "comment": 0, "blank": 95}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake": {"language": "CMake", "code": 22, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 15, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/build.yaml": {"language": "YAML", "code": 11, "comment": 6, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt": {"language": "CMake", "code": 98, "comment": 0, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/WORD_TRAINING_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 108, "comment": 0, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/Action.html": {"language": "HTML", "code": 0, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md": {"language": "<PERSON><PERSON>", "code": 33, "comment": 0, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 137, "comment": 0, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/test_compilation_fix.dart": {"language": "Dart", "code": 144, "comment": 7, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml": {"language": "YAML", "code": 3, "comment": 22, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/FEATURES.md": {"language": "<PERSON><PERSON>", "code": 601, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt": {"language": "CMake", "code": 89, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/%E5%8A%A8%E4%BD%9C.html": {"language": "HTML", "code": 693, "comment": 23, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md": {"language": "<PERSON><PERSON>", "code": 160, "comment": 2, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle.properties": {"language": "Properties", "code": 3, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 74, "comment": 0, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart": {"language": "Dart", "code": 204, "comment": 17, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/scripts/transform_vocabulary.py": {"language": "Python", "code": 152, "comment": 26, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_TEST_CHECKLIST.md": {"language": "<PERSON><PERSON>", "code": 114, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart": {"language": "Dart", "code": 62, "comment": 8, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/oneday/test_coordinate_debug.dart": {"language": "Dart", "code": 84, "comment": 5, "blank": 18}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h": {"language": "C++", "code": 20, "comment": 5, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h": {"language": "C++", "code": 9, "comment": 6, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp": {"language": "C++", "code": 30, "comment": 4, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h": {"language": "C++", "code": 48, "comment": 31, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt": {"language": "CMake", "code": 34, "comment": 0, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart": {"language": "Dart", "code": 246, "comment": 62, "blank": 62}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp": {"language": "C++", "code": 210, "comment": 24, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart": {"language": "Dart", "code": 187, "comment": 35, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart": {"language": "Dart", "code": 69, "comment": 20, "blank": 19}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart": {"language": "Dart", "code": 160, "comment": 27, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart": {"language": "Dart", "code": 83, "comment": 14, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt": {"language": "CMake", "code": 21, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h": {"language": "C++", "code": 5, "comment": 5, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake": {"language": "CMake", "code": 20, "comment": 0, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc": {"language": "C++", "code": 11, "comment": 4, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc": {"language": "C++", "code": 5, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp": {"language": "C++", "code": 54, "comment": 2, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart": {"language": "Dart", "code": 94, "comment": 19, "blank": 9}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h": {"language": "C++", "code": 7, "comment": 7, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties": {"language": "Properties", "code": 5, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h": {"language": "C++", "code": 8, "comment": 6, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt": {"language": "CMake", "code": 79, "comment": 0, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc": {"language": "C++", "code": 83, "comment": 21, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt": {"language": "CMake", "code": 104, "comment": 0, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart": {"language": "Dart", "code": 434, "comment": 65, "blank": 69}, "file:///Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp": {"language": "C++", "code": 49, "comment": 7, "blank": 16}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/main.dart": {"language": "Dart", "code": 242, "comment": 23, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart": {"language": "Dart", "code": 238, "comment": 20, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart": {"language": "Dart", "code": 248, "comment": 26, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test_matrix_analysis.dart": {"language": "Dart", "code": 130, "comment": 8, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/first_time_service.dart": {"language": "Dart", "code": 44, "comment": 16, "blank": 13}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart": {"language": "Dart", "code": 161, "comment": 42, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/test_bubble_alignment_verification.dart": {"language": "Dart", "code": 103, "comment": 10, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/pubspec.yaml": {"language": "YAML", "code": 54, "comment": 71, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/KNOWLEDGE_POINT_SYNC_FIX.md": {"language": "<PERSON><PERSON>", "code": 191, "comment": 0, "blank": 42}, "file:///Users/<USER>/development/flutter_apps/oneday/SHARE_UI_RESTORE_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 120, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/test_matrix_fix_verification.dart": {"language": "Dart", "code": 105, "comment": 14, "blank": 31}, "file:///Users/<USER>/development/flutter_apps/oneday/ANNOTATION_SCALE_FIX_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 154, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/SWIPE_GESTURE_DEMO.md": {"language": "<PERSON><PERSON>", "code": 216, "comment": 0, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/README.md": {"language": "<PERSON><PERSON>", "code": 1163, "comment": 0, "blank": 259}, "file:///Users/<USER>/development/flutter_apps/oneday/REMOVE_CAMERA_FEATURE.md": {"language": "<PERSON><PERSON>", "code": 105, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/QUICK_TEST_STEPS.md": {"language": "<PERSON><PERSON>", "code": 85, "comment": 0, "blank": 26}, "file:///Users/<USER>/development/flutter_apps/oneday/TESTING_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 103, "comment": 0, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMER_FIXES_PROGRESS.md": {"language": "<PERSON><PERSON>", "code": 131, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/SHARE_FUNCTION_SIMPLIFICATION.md": {"language": "<PERSON><PERSON>", "code": 99, "comment": 0, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/EDIT_ALBUM_ENHANCEMENT.md": {"language": "<PERSON><PERSON>", "code": 167, "comment": 0, "blank": 43}, "file:///Users/<USER>/development/flutter_apps/oneday/DEVELOPER_ENTRANCE_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 59, "comment": 0, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMEBOX_TIMER_FIXES.md": {"language": "<PERSON><PERSON>", "code": 115, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMER_FLOATING_WINDOW_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 69, "comment": 0, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_TIMER_FIXES_VERIFICATION.md": {"language": "<PERSON><PERSON>", "code": 190, "comment": 0, "blank": 50}, "file:///Users/<USER>/development/flutter_apps/oneday/test_coordinate_analysis.dart": {"language": "Dart", "code": 104, "comment": 9, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Podfile": {"language": "<PERSON>", "code": 31, "comment": 3, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/web/index.html": {"language": "HTML", "code": 19, "comment": 15, "blank": 5}, "file:///Users/<USER>/development/flutter_apps/oneday/TIMER_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 121, "comment": 0, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/COMPREHENSIVE_TEST_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 276, "comment": 0, "blank": 96}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_FINAL_OPTIMIZATION.md": {"language": "<PERSON><PERSON>", "code": 142, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/web/manifest.json": {"language": "JSON", "code": 35, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart": {"language": "Dart", "code": 35, "comment": 5, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart": {"language": "Dart", "code": 1252, "comment": 61, "blank": 130}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart": {"language": "Dart", "code": 272, "comment": 18, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart": {"language": "Dart", "code": 991, "comment": 40, "blank": 72}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart": {"language": "Dart", "code": 894, "comment": 48, "blank": 71}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_data_converter.dart": {"language": "Dart", "code": 194, "comment": 31, "blank": 40}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart": {"language": "Dart", "code": 189, "comment": 28, "blank": 37}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart": {"language": "Dart", "code": 162, "comment": 17, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart": {"language": "Dart", "code": 845, "comment": 29, "blank": 83}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart": {"language": "Dart", "code": 162, "comment": 2, "blank": 12}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart": {"language": "Dart", "code": 407, "comment": 60, "blank": 88}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h": {"language": "C++", "code": 1, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/create_vocabulary_page.dart": {"language": "Dart", "code": 452, "comment": 11, "blank": 39}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml": {"language": "XML", "code": 3, "comment": 4, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"language": "XML", "code": 36, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart": {"language": "Dart", "code": 233, "comment": 33, "blank": 64}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 116, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_learning_service.dart": {"language": "Dart", "code": 232, "comment": 28, "blank": 47}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart": {"language": "Dart", "code": 785, "comment": 59, "blank": 75}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_model.dart": {"language": "Dart", "code": 453, "comment": 7, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json": {"language": "JSON", "code": 23, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml": {"language": "XML", "code": 41, "comment": 13, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md": {"language": "<PERSON><PERSON>", "code": 3, "comment": 0, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard": {"language": "XML", "code": 25, "comment": 1, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart": {"language": "Dart", "code": 241, "comment": 18, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml": {"language": "XML", "code": 9, "comment": 9, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart": {"language": "Dart", "code": 104, "comment": 4, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml": {"language": "XML", "code": 4, "comment": 7, "blank": 2}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart": {"language": "Dart", "code": 780, "comment": 24, "blank": 58}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart": {"language": "Dart", "code": 985, "comment": 72, "blank": 87}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart": {"language": "Dart", "code": 1837, "comment": 98, "blank": 112}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart": {"language": "Dart", "code": 5075, "comment": 383, "blank": 470}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart": {"language": "Dart", "code": 397, "comment": 15, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary_original.json": {"language": "JSON", "code": 38714, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart": {"language": "Dart", "code": 2930, "comment": 435, "blank": 501}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart": {"language": "Dart", "code": 1672, "comment": 186, "blank": 224}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart": {"language": "Dart", "code": 2663, "comment": 131, "blank": 182}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_backup.dart": {"language": "Dart", "code": 2394, "comment": 224, "blank": 292}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart": {"language": "Dart", "code": 497, "comment": 26, "blank": 59}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart": {"language": "Dart", "code": 972, "comment": 69, "blank": 68}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart": {"language": "Dart", "code": 1412, "comment": 54, "blank": 78}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart": {"language": "Dart", "code": 808, "comment": 49, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart": {"language": "Dart", "code": 826, "comment": 36, "blank": 57}, "file:///Users/<USER>/development/flutter_apps/oneday/test_image_compression_implementation.dart": {"language": "Dart", "code": 147, "comment": 7, "blank": 33}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart": {"language": "Dart", "code": 666, "comment": 38, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>": {"language": "Dart", "code": 794, "comment": 32, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/SYSTEM_FLOATING_TIMER_USER_GUIDE.md": {"language": "<PERSON><PERSON>", "code": 117, "comment": 0, "blank": 41}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules_%E5%89%AF%E6%9C%AC.md": {"language": "<PERSON><PERSON>", "code": 384, "comment": 0, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/NOTION_STYLE_UPDATES.md": {"language": "<PERSON><PERSON>", "code": 125, "comment": 0, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules.backup.md": {"language": "<PERSON><PERSON>", "code": 384, "comment": 0, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.g.dart": {"language": "Dart", "code": 71, "comment": 15, "blank": 8}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart": {"language": "Dart", "code": 244, "comment": 42, "blank": 51}, "file:///Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md": {"language": "<PERSON><PERSON>", "code": 380, "comment": 0, "blank": 81}, "file:///Users/<USER>/development/flutter_apps/oneday/.cursorrules.md": {"language": "<PERSON><PERSON>", "code": 384, "comment": 0, "blank": 112}, "file:///Users/<USER>/development/flutter_apps/oneday/test_complete_image_compression.dart": {"language": "Dart", "code": 186, "comment": 9, "blank": 38}, "file:///Users/<USER>/development/flutter_apps/oneday/PRD.md": {"language": "<PERSON><PERSON>", "code": 339, "comment": 0, "blank": 97}, "file:///Users/<USER>/development/flutter_apps/oneday/test_coordinate_transformation_fix.dart": {"language": "Dart", "code": 110, "comment": 16, "blank": 24}, "file:///Users/<USER>/development/flutter_apps/oneday/test_bubble_center_alignment.dart": {"language": "Dart", "code": 92, "comment": 8, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart": {"language": "Dart", "code": 186, "comment": 12, "blank": 22}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart": {"language": "Dart", "code": 216, "comment": 33, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/test_position_fix_verification.dart": {"language": "Dart", "code": 38, "comment": 4, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/test_bubble_positioning_fix.dart": {"language": "Dart", "code": 109, "comment": 6, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Podfile": {"language": "<PERSON>", "code": 32, "comment": 1, "blank": 10}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart": {"language": "Dart", "code": 698, "comment": 42, "blank": 53}, "file:///Users/<USER>/development/flutter_apps/oneday/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md": {"language": "<PERSON><PERSON>", "code": 129, "comment": 0, "blank": 35}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart": {"language": "Dart", "code": 217, "comment": 19, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.g.dart": {"language": "Dart", "code": 49, "comment": 13, "blank": 7}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart": {"language": "Dart", "code": 171, "comment": 45, "blank": 61}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/models/daily_plan.dart": {"language": "Dart", "code": 151, "comment": 29, "blank": 34}, "file:///Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart": {"language": "Dart", "code": 30, "comment": 9, "blank": 11}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart": {"language": "Dart", "code": 166, "comment": 14, "blank": 25}, "file:///Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart": {"language": "Dart", "code": 201, "comment": 13, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart": {"language": "Dart", "code": 146, "comment": 30, "blank": 48}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart": {"language": "Dart", "code": 147, "comment": 40, "blank": 55}, "file:///Users/<USER>/development/flutter_apps/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart": {"language": "Dart", "code": 204, "comment": 31, "blank": 44}, "file:///Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart": {"language": "Dart", "code": 98, "comment": 6, "blank": 20}, "file:///Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart": {"language": "Dart", "code": 108, "comment": 24, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart": {"language": "Dart", "code": 96, "comment": 21, "blank": 28}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart": {"language": "Dart", "code": 152, "comment": 38, "blank": 52}, "file:///Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart": {"language": "Dart", "code": 172, "comment": 20, "blank": 27}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart": {"language": "Dart", "code": 81, "comment": 2, "blank": 17}, "file:///Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart": {"language": "Dart", "code": 101, "comment": 15, "blank": 21}, "file:///Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart": {"language": "Dart", "code": 101, "comment": 25, "blank": 32}, "file:///Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart": {"language": "Dart", "code": 11, "comment": 9, "blank": 6}, "file:///Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart": {"language": "Dart", "code": 216, "comment": 9, "blank": 29}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift": {"language": "Swift", "code": 7, "comment": 2, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift": {"language": "Swift", "code": 14, "comment": 3, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift": {"language": "Swift", "code": 12, "comment": 0, "blank": 4}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift": {"language": "Swift", "code": 11, "comment": 0, "blank": 3}, "file:///Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart": {"language": "Dart", "code": 62, "comment": 2, "blank": 14}, "file:///Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md": {"language": "<PERSON><PERSON>", "code": 428, "comment": 0, "blank": 125}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json": {"language": "JSON", "code": 68, "comment": 0, "blank": 0}, "file:///Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib": {"language": "XML", "code": 343, "comment": 0, "blank": 1}, "file:///Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md": {"language": "<PERSON><PERSON>", "code": 388, "comment": 0, "blank": 120}, "file:///Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md": {"language": "<PERSON><PERSON>", "code": 250, "comment": 0, "blank": 119}, "file:///Users/<USER>/development/flutter_apps/oneday/assets/data/vocabulary.json": {"language": "JSON", "code": 84516, "comment": 0, "blank": 0}}