# Diff Details

Date : 2025-07-11 06:51:39

Directory /Users/<USER>/development/flutter_apps

Total : 133 files,  95926 codes, 1201 comments, 1458 blanks, all 98585 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 0 | 0 | -7 | -7 |
| [oneday/ADD\_CHILD\_CATEGORY\_FEATURE.md](/oneday/ADD_CHILD_CATEGORY_FEATURE.md) | Markdown | -170 | 0 | -43 | -213 |
| [oneday/ANNOTATION\_SCALE\_FIX\_SUMMARY.md](/oneday/ANNOTATION_SCALE_FIX_SUMMARY.md) | Markdown | 154 | 0 | 43 | 197 |
| [oneday/ANNOTATION\_SCALE\_FIX\_TEST\_GUIDE.md](/oneday/ANNOTATION_SCALE_FIX_TEST_GUIDE.md) | Markdown | 144 | 0 | 43 | 187 |
| [oneday/BLUR\_TRANSITION\_OPTIMIZATION\_SUMMARY.md](/oneday/BLUR_TRANSITION_OPTIMIZATION_SUMMARY.md) | Markdown | -133 | 0 | -34 | -167 |
| [oneday/BUG\_FIX\_SUMMARY.md](/oneday/BUG_FIX_SUMMARY.md) | Markdown | -72 | 0 | -22 | -94 |
| [oneday/CATEGORY\_TREE\_UPGRADE\_TEST\_GUIDE.md](/oneday/CATEGORY_TREE_UPGRADE_TEST_GUIDE.md) | Markdown | -169 | 0 | -54 | -223 |
| [oneday/COMPREHENSIVE\_TEST\_GUIDE.md](/oneday/COMPREHENSIVE_TEST_GUIDE.md) | Markdown | 276 | 0 | 96 | 372 |
| [oneday/CONTEXT\_AWARE\_ALBUM\_CREATION.md](/oneday/CONTEXT_AWARE_ALBUM_CREATION.md) | Markdown | -119 | 0 | -36 | -155 |
| [oneday/COVER\_MODE\_VERTICAL\_BOUNDARY\_TEST\_GUIDE.md](/oneday/COVER_MODE_VERTICAL_BOUNDARY_TEST_GUIDE.md) | Markdown | -98 | 0 | -35 | -133 |
| [oneday/DEMO\_ADD\_CHILD\_CATEGORY.md](/oneday/DEMO_ADD_CHILD_CATEGORY.md) | Markdown | -156 | 0 | -29 | -185 |
| [oneday/DRAG\_DROP\_CATEGORY\_TEST\_GUIDE.md](/oneday/DRAG_DROP_CATEGORY_TEST_GUIDE.md) | Markdown | -182 | 0 | -48 | -230 |
| [oneday/DRAG\_HANDLE\_REMOVAL\_TEST\_GUIDE.md](/oneday/DRAG_HANDLE_REMOVAL_TEST_GUIDE.md) | Markdown | -181 | 0 | -54 | -235 |
| [oneday/DRAG\_POSITION\_OPTIMIZATION\_TEST\_GUIDE.md](/oneday/DRAG_POSITION_OPTIMIZATION_TEST_GUIDE.md) | Markdown | -181 | 0 | -48 | -229 |
| [oneday/EDIT\_ALBUM\_ENHANCEMENT.md](/oneday/EDIT_ALBUM_ENHANCEMENT.md) | Markdown | 167 | 0 | 43 | 210 |
| [oneday/FAST\_SWIPE\_FIX\_SUMMARY.md](/oneday/FAST_SWIPE_FIX_SUMMARY.md) | Markdown | -140 | 0 | -33 | -173 |
| [oneday/FEATURES.md](/oneday/FEATURES.md) | Markdown | 191 | 0 | 34 | 225 |
| [oneday/FINAL\_GESTURE\_FIX\_SUMMARY.md](/oneday/FINAL_GESTURE_FIX_SUMMARY.md) | Markdown | -115 | 0 | -33 | -148 |
| [oneday/FINAL\_UI\_FIXES\_SUMMARY.md](/oneday/FINAL_UI_FIXES_SUMMARY.md) | Markdown | -169 | 0 | -47 | -216 |
| [oneday/FLOATING\_BUTTON\_AUTO\_HIDE\_TEST\_GUIDE.md](/oneday/FLOATING_BUTTON_AUTO_HIDE_TEST_GUIDE.md) | Markdown | -147 | 0 | -45 | -192 |
| [oneday/FLOATING\_TIMER\_DEBUG\_GUIDE.md](/oneday/FLOATING_TIMER_DEBUG_GUIDE.md) | Markdown | 199 | 0 | 60 | 259 |
| [oneday/FLOATING\_TIMER\_FEATURE.md](/oneday/FLOATING_TIMER_FEATURE.md) | Markdown | 74 | 0 | 20 | 94 |
| [oneday/FLOATING\_TIMER\_FIXES\_VERIFICATION.md](/oneday/FLOATING_TIMER_FIXES_VERIFICATION.md) | Markdown | 190 | 0 | 50 | 240 |
| [oneday/FLOATING\_TIMER\_SYSTEM\_LEVEL\_IMPLEMENTATION.md](/oneday/FLOATING_TIMER_SYSTEM_LEVEL_IMPLEMENTATION.md) | Markdown | 160 | 2 | 40 | 202 |
| [oneday/FLOATING\_WINDOW\_FINAL\_OPTIMIZATION.md](/oneday/FLOATING_WINDOW_FINAL_OPTIMIZATION.md) | Markdown | 142 | 0 | 35 | 177 |
| [oneday/FLOATING\_WINDOW\_OPTIMIZATION\_SUMMARY.md](/oneday/FLOATING_WINDOW_OPTIMIZATION_SUMMARY.md) | Markdown | 129 | 0 | 35 | 164 |
| [oneday/FLOATING\_WINDOW\_TEST\_CHECKLIST.md](/oneday/FLOATING_WINDOW_TEST_CHECKLIST.md) | Markdown | 114 | 0 | 27 | 141 |
| [oneday/GESTURE\_CONFLICT\_FIX\_SUMMARY.md](/oneday/GESTURE_CONFLICT_FIX_SUMMARY.md) | Markdown | -151 | 0 | -38 | -189 |
| [oneday/GESTURE\_DEBUG\_REPORT.md](/oneday/GESTURE_DEBUG_REPORT.md) | Markdown | -99 | 0 | -29 | -128 |
| [oneday/GESTURE\_IMPLEMENTATION\_SUMMARY.md](/oneday/GESTURE_IMPLEMENTATION_SUMMARY.md) | Markdown | -99 | 0 | -32 | -131 |
| [oneday/GESTURE\_MODE\_TEST\_GUIDE.md](/oneday/GESTURE_MODE_TEST_GUIDE.md) | Markdown | -93 | 0 | -26 | -119 |
| [oneday/GESTURE\_OPTIMIZATION\_SUMMARY.md](/oneday/GESTURE_OPTIMIZATION_SUMMARY.md) | Markdown | -122 | 0 | -36 | -158 |
| [oneday/GESTURE\_TEST\_GUIDE.md](/oneday/GESTURE_TEST_GUIDE.md) | Markdown | -108 | 0 | -38 | -146 |
| [oneday/GIT\_COMMIT\_SUMMARY.md](/oneday/GIT_COMMIT_SUMMARY.md) | Markdown | -127 | 0 | -36 | -163 |
| [oneday/HORIZONTAL\_SWIPE\_FIX\_SUMMARY.md](/oneday/HORIZONTAL_SWIPE_FIX_SUMMARY.md) | Markdown | -137 | 0 | -36 | -173 |
| [oneday/IMAGE\_MARKING\_FUNCTION\_RESTORE.md](/oneday/IMAGE_MARKING_FUNCTION_RESTORE.md) | Markdown | -285 | 0 | -85 | -370 |
| [oneday/KEYBOARD\_AVOIDANCE\_IMPLEMENTATION.md](/oneday/KEYBOARD_AVOIDANCE_IMPLEMENTATION.md) | Markdown | -155 | 0 | -33 | -188 |
| [oneday/KNOWLEDGE\_POINT\_SYNC\_FIX.md](/oneday/KNOWLEDGE_POINT_SYNC_FIX.md) | Markdown | 191 | 0 | 42 | 233 |
| [oneday/NOTION\_STYLE\_UPDATES.md](/oneday/NOTION_STYLE_UPDATES.md) | Markdown | 125 | 0 | 44 | 169 |
| [oneday/OPTIMIZING\_CURSOR\_AI.md](/oneday/OPTIMIZING_CURSOR_AI.md) | Markdown | -144 | 0 | -52 | -196 |
| [oneday/PHOTO\_EXPORT\_GUIDE.md](/oneday/PHOTO_EXPORT_GUIDE.md) | Markdown | -117 | -2 | -45 | -164 |
| [oneday/POPUP\_MENU\_FIX\_TEST\_GUIDE.md](/oneday/POPUP_MENU_FIX_TEST_GUIDE.md) | Markdown | -160 | 0 | -44 | -204 |
| [oneday/POSITION\_FOLLOWING\_SYSTEM.md](/oneday/POSITION_FOLLOWING_SYSTEM.md) | Markdown | -130 | 0 | -30 | -160 |
| [oneday/QUICK\_TEST\_STEPS.md](/oneday/QUICK_TEST_STEPS.md) | Markdown | 85 | 0 | 26 | 111 |
| [oneday/README.md](/oneday/README.md) | Markdown | 15 | 0 | 5 | 20 |
| [oneday/REMOVE\_CAMERA\_FEATURE.md](/oneday/REMOVE_CAMERA_FEATURE.md) | Markdown | 105 | 0 | 35 | 140 |
| [oneday/SHARE\_UI\_RESTORE\_FEATURE.md](/oneday/SHARE_UI_RESTORE_FEATURE.md) | Markdown | 120 | 0 | 35 | 155 |
| [oneday/SWIPE\_GESTURE\_DEMO.md](/oneday/SWIPE_GESTURE_DEMO.md) | Markdown | 216 | 0 | 48 | 264 |
| [oneday/SYSTEM\_FLOATING\_TIMER\_USER\_GUIDE.md](/oneday/SYSTEM_FLOATING_TIMER_USER_GUIDE.md) | Markdown | 117 | 0 | 41 | 158 |
| [oneday/TESTING\_GUIDE.md](/oneday/TESTING_GUIDE.md) | Markdown | 103 | 0 | 34 | 137 |
| [oneday/THREE\_DOT\_MENU\_FEATURE.md](/oneday/THREE_DOT_MENU_FEATURE.md) | Markdown | -107 | 0 | -32 | -139 |
| [oneday/TIMEBOX\_TIMER\_FIXES.md](/oneday/TIMEBOX_TIMER_FIXES.md) | Markdown | 115 | 0 | 27 | 142 |
| [oneday/TIMER\_FIXES\_PROGRESS.md](/oneday/TIMER_FIXES_PROGRESS.md) | Markdown | 131 | 0 | 35 | 166 |
| [oneday/TIMER\_FLOATING\_WINDOW\_GUIDE.md](/oneday/TIMER_FLOATING_WINDOW_GUIDE.md) | Markdown | 69 | 0 | 27 | 96 |
| [oneday/TIMER\_TEST\_GUIDE.md](/oneday/TIMER_TEST_GUIDE.md) | Markdown | 121 | 0 | 32 | 153 |
| [oneday/UI\_FIXES\_SUMMARY.md](/oneday/UI_FIXES_SUMMARY.md) | Markdown | -120 | 0 | -33 | -153 |
| [oneday/UNMOUNTED\_WIDGET\_COMPREHENSIVE\_FIX.md](/oneday/UNMOUNTED_WIDGET_COMPREHENSIVE_FIX.md) | Markdown | -169 | 0 | -35 | -204 |
| [oneday/UNMOUNTED\_WIDGET\_FIX\_GUIDE.md](/oneday/UNMOUNTED_WIDGET_FIX_GUIDE.md) | Markdown | -125 | 0 | -30 | -155 |
| [oneday/WEB\_ONBOARDING\_NAVIGATION.md](/oneday/WEB_ONBOARDING_NAVIGATION.md) | Markdown | -129 | 0 | -36 | -165 |
| [oneday/WORD\_TRAINING\_OPTIMIZATION.md](/oneday/WORD_TRAINING_OPTIMIZATION.md) | Markdown | 108 | 0 | 39 | 147 |
| [oneday/android/app/src/main/AndroidManifest.xml](/oneday/android/app/src/main/AndroidManifest.xml) | XML | 3 | 1 | 1 | 5 |
| [oneday/assets/data/prefixes.json](/oneday/assets/data/prefixes.json) | JSON | 88 | 0 | 1 | 89 |
| [oneday/assets/data/suffixes.json](/oneday/assets/data/suffixes.json) | JSON | 99 | 0 | 1 | 100 |
| [oneday/assets/data/vocabulary.json](/oneday/assets/data/vocabulary.json) | JSON | 84,516 | 0 | 0 | 84,516 |
| [oneday/assets/data/vocabulary\_original.json](/oneday/assets/data/vocabulary_original.json) | JSON | 38,714 | 0 | 1 | 38,715 |
| [oneday/assets/data/word\_roots.json](/oneday/assets/data/word_roots.json) | JSON | 158 | 0 | 1 | 159 |
| [oneday/build.yaml](/oneday/build.yaml) | YAML | 11 | 6 | 1 | 18 |
| [oneday/debug\_share\_issue.md](/oneday/debug_share_issue.md) | Markdown | -73 | 0 | -24 | -97 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | -64 | -3 | -1 | -68 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 251 | 11 | 12 | 274 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 5 | 0 | 0 | 5 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.dart](/oneday/lib/features/daily_plan/models/daily_plan.dart) | Dart | 151 | 29 | 34 | 214 |
| [oneday/lib/features/daily\_plan/models/daily\_plan.g.dart](/oneday/lib/features/daily_plan/models/daily_plan.g.dart) | Dart | 49 | 13 | 7 | 69 |
| [oneday/lib/features/daily\_plan/notifiers/daily\_plan\_notifier.dart](/oneday/lib/features/daily_plan/notifiers/daily_plan_notifier.dart) | Dart | 204 | 31 | 44 | 279 |
| [oneday/lib/features/daily\_plan/services/daily\_plan\_storage\_service.dart](/oneday/lib/features/daily_plan/services/daily_plan_storage_service.dart) | Dart | 216 | 33 | 48 | 297 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 273 | 20 | 23 | 316 |
| [oneday/lib/features/exercise/focused\_training\_page.dart](/oneday/lib/features/exercise/focused_training_page.dart) | Dart | 397 | 15 | 35 | 447 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 1,011 | 100 | 115 | 1,226 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 162 | 59 | 48 | 269 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 20 | 6 | 10 | 36 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 15 | 5 | 6 | 26 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 53 | 5 | 9 | 67 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | 19 | 3 | 1 | 23 |
| [oneday/lib/features/time\_box/models/timebox\_models.dart](/oneday/lib/features/time_box/models/timebox_models.dart) | Dart | 244 | 42 | 51 | 337 |
| [oneday/lib/features/time\_box/models/timebox\_models.g.dart](/oneday/lib/features/time_box/models/timebox_models.g.dart) | Dart | 71 | 15 | 8 | 94 |
| [oneday/lib/features/time\_box/providers/timebox\_provider.dart](/oneday/lib/features/time_box/providers/timebox_provider.dart) | Dart | 217 | 19 | 25 | 261 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 697 | 60 | 62 | 819 |
| [oneday/lib/features/vocabulary/create\_vocabulary\_page.dart](/oneday/lib/features/vocabulary/create_vocabulary_page.dart) | Dart | 452 | 11 | 39 | 502 |
| [oneday/lib/features/vocabulary/vocabulary\_cache\_manager.dart](/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart) | Dart | 233 | 33 | 64 | 330 |
| [oneday/lib/features/vocabulary/vocabulary\_category\_page.dart](/oneday/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 894 | 48 | 71 | 1,013 |
| [oneday/lib/features/vocabulary/vocabulary\_data\_converter.dart](/oneday/lib/features/vocabulary/vocabulary_data_converter.dart) | Dart | 194 | 31 | 40 | 265 |
| [oneday/lib/features/vocabulary/vocabulary\_learning\_service.dart](/oneday/lib/features/vocabulary/vocabulary_learning_service.dart) | Dart | 232 | 28 | 47 | 307 |
| [oneday/lib/features/vocabulary/vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | 991 | 40 | 72 | 1,103 |
| [oneday/lib/features/vocabulary/vocabulary\_page.dart](/oneday/lib/features/vocabulary/vocabulary_page.dart) | Dart | 162 | 2 | 12 | 176 |
| [oneday/lib/features/vocabulary/vocabulary\_service.dart](/oneday/lib/features/vocabulary/vocabulary_service.dart) | Dart | 407 | 60 | 88 | 555 |
| [oneday/lib/features/vocabulary/vocabulary\_statistics\_page.dart](/oneday/lib/features/vocabulary/vocabulary_statistics_page.dart) | Dart | 845 | 29 | 83 | 957 |
| [oneday/lib/features/vocabulary/word\_meaning\_service.dart](/oneday/lib/features/vocabulary/word_meaning_service.dart) | Dart | 162 | 17 | 32 | 211 |
| [oneday/lib/features/vocabulary/word\_model.dart](/oneday/lib/features/vocabulary/word_model.dart) | Dart | 453 | 7 | 34 | 494 |
| [oneday/lib/features/vocabulary/word\_root\_service.dart](/oneday/lib/features/vocabulary/word_root_service.dart) | Dart | 189 | 28 | 37 | 254 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 4 | 2 | 2 | 8 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 23 | 4 | 4 | 31 |
| [oneday/lib/services/first\_time\_service.dart](/oneday/lib/services/first_time_service.dart) | Dart | 44 | 16 | 13 | 73 |
| [oneday/lib/services/floating\_timer\_service.dart](/oneday/lib/services/floating_timer_service.dart) | Dart | 434 | 65 | 69 | 568 |
| [oneday/lib/services/global\_timer\_service.dart](/oneday/lib/services/global_timer_service.dart) | Dart | 161 | 42 | 52 | 255 |
| [oneday/lib/services/system\_overlay\_permission.dart](/oneday/lib/services/system_overlay_permission.dart) | Dart | 248 | 26 | 24 | 298 |
| [oneday/lib/shared/config/image\_compression\_config.dart](/oneday/lib/shared/config/image_compression_config.dart) | Dart | 69 | 20 | 19 | 108 |
| [oneday/lib/shared/services/enhanced\_share\_service.dart](/oneday/lib/shared/services/enhanced_share_service.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/shared/utils/image\_compression\_utils.dart](/oneday/lib/shared/utils/image_compression_utils.dart) | Dart | 187 | 35 | 44 | 266 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 51 | 27 | 17 | 95 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 7 | 2 | 2 | 11 |
| [oneday/scripts/transform\_vocabulary.py](/oneday/scripts/transform_vocabulary.py) | Python | 152 | 26 | 32 | 210 |
| [oneday/test/category\_edit\_fix\_test.dart](/oneday/test/category_edit_fix_test.dart) | Dart | 101 | 15 | 21 | 137 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/test/vocabulary\_test.dart](/oneday/test/vocabulary_test.dart) | Dart | 216 | 9 | 29 | 254 |
| [oneday/test/word\_meaning\_service\_test.dart](/oneday/test/word_meaning_service_test.dart) | Dart | 62 | 2 | 14 | 78 |
| [oneday/test\_bubble\_alignment\_adjustment.dart](/oneday/test_bubble_alignment_adjustment.dart) | Dart | 103 | 9 | 23 | 135 |
| [oneday/test\_bubble\_alignment\_verification.dart](/oneday/test_bubble_alignment_verification.dart) | Dart | 103 | 10 | 26 | 139 |
| [oneday/test\_bubble\_center\_alignment.dart](/oneday/test_bubble_center_alignment.dart) | Dart | 92 | 8 | 20 | 120 |
| [oneday/test\_bubble\_positioning\_fix.dart](/oneday/test_bubble_positioning_fix.dart) | Dart | 109 | 6 | 27 | 142 |
| [oneday/test\_compilation\_fix.dart](/oneday/test_compilation_fix.dart) | Dart | 144 | 7 | 29 | 180 |
| [oneday/test\_complete\_image\_compression.dart](/oneday/test_complete_image_compression.dart) | Dart | 186 | 9 | 38 | 233 |
| [oneday/test\_coordinate\_analysis.dart](/oneday/test_coordinate_analysis.dart) | Dart | 104 | 9 | 27 | 140 |
| [oneday/test\_coordinate\_debug.dart](/oneday/test_coordinate_debug.dart) | Dart | 84 | 5 | 18 | 107 |
| [oneday/test\_coordinate\_transformation\_fix.dart](/oneday/test_coordinate_transformation_fix.dart) | Dart | 110 | 16 | 24 | 150 |
| [oneday/test\_image\_compression\_implementation.dart](/oneday/test_image_compression_implementation.dart) | Dart | 147 | 7 | 33 | 187 |
| [oneday/test\_matrix\_analysis.dart](/oneday/test_matrix_analysis.dart) | Dart | 130 | 8 | 37 | 175 |
| [oneday/test\_matrix\_fix\_verification.dart](/oneday/test_matrix_fix_verification.dart) | Dart | 105 | 14 | 31 | 150 |
| [oneday/test\_popup\_menu\_fix.md](/oneday/test_popup_menu_fix.md) | Markdown | -117 | 0 | -32 | -149 |
| [oneday/test\_position\_fix\_verification.dart](/oneday/test_position_fix_verification.dart) | Dart | 38 | 4 | 11 | 53 |
| [oneday/test\_share\_function.md](/oneday/test_share_function.md) | Markdown | -94 | 0 | -32 | -126 |
| [oneday/vocabulary.json](/oneday/vocabulary.json) | JSON | -38,714 | 0 | 0 | -38,714 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details