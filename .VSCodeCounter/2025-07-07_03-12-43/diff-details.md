# Diff Details

Date : 2025-07-07 03:12:43

Directory /Users/<USER>/development/flutter_apps

Total : 54 files,  47610 codes, 463 comments, 1875 blanks, all 49948 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/Augrules.backup.md](/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [.augment/rules/Augrules.md](/.augment/rules/Augrules.md) | Markdown | 40 | 0 | 5 | 45 |
| [oneday/.cursorrules.backup.md](/oneday/.cursorrules.backup.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/.cursorrules\_副本.md](/oneday/.cursorrules_%E5%89%AF%E6%9C%AC.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/ADD\_CHILD\_CATEGORY\_FEATURE.md](/oneday/ADD_CHILD_CATEGORY_FEATURE.md) | Markdown | 170 | 0 | 43 | 213 |
| [oneday/ARCHITECTURE.md](/oneday/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [oneday/CATEGORY\_TREE\_UPGRADE\_TEST\_GUIDE.md](/oneday/CATEGORY_TREE_UPGRADE_TEST_GUIDE.md) | Markdown | 169 | 0 | 54 | 223 |
| [oneday/CONTEXT\_AWARE\_ALBUM\_CREATION.md](/oneday/CONTEXT_AWARE_ALBUM_CREATION.md) | Markdown | 119 | 0 | 36 | 155 |
| [oneday/DEMO\_ADD\_CHILD\_CATEGORY.md](/oneday/DEMO_ADD_CHILD_CATEGORY.md) | Markdown | 156 | 0 | 29 | 185 |
| [oneday/DRAG\_DROP\_CATEGORY\_TEST\_GUIDE.md](/oneday/DRAG_DROP_CATEGORY_TEST_GUIDE.md) | Markdown | 182 | 0 | 48 | 230 |
| [oneday/DRAG\_HANDLE\_REMOVAL\_TEST\_GUIDE.md](/oneday/DRAG_HANDLE_REMOVAL_TEST_GUIDE.md) | Markdown | 181 | 0 | 54 | 235 |
| [oneday/DRAG\_POSITION\_OPTIMIZATION\_TEST\_GUIDE.md](/oneday/DRAG_POSITION_OPTIMIZATION_TEST_GUIDE.md) | Markdown | 181 | 0 | 48 | 229 |
| [oneday/FEATURES.md](/oneday/FEATURES.md) | Markdown | 410 | 0 | 86 | 496 |
| [oneday/FINAL\_UI\_FIXES\_SUMMARY.md](/oneday/FINAL_UI_FIXES_SUMMARY.md) | Markdown | 169 | 0 | 47 | 216 |
| [oneday/FLOATING\_BUTTON\_AUTO\_HIDE\_TEST\_GUIDE.md](/oneday/FLOATING_BUTTON_AUTO_HIDE_TEST_GUIDE.md) | Markdown | 147 | 0 | 45 | 192 |
| [oneday/GIT\_COMMIT\_SUMMARY.md](/oneday/GIT_COMMIT_SUMMARY.md) | Markdown | 127 | 0 | 36 | 163 |
| [oneday/PHOTO\_EXPORT\_GUIDE.md](/oneday/PHOTO_EXPORT_GUIDE.md) | Markdown | 117 | 2 | 45 | 164 |
| [oneday/POPUP\_MENU\_FIX\_TEST\_GUIDE.md](/oneday/POPUP_MENU_FIX_TEST_GUIDE.md) | Markdown | 160 | 0 | 44 | 204 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | 122 | 0 | 44 | 166 |
| [oneday/THREE\_DOT\_MENU\_FEATURE.md](/oneday/THREE_DOT_MENU_FEATURE.md) | Markdown | 107 | 0 | 32 | 139 |
| [oneday/UI\_FIXES\_SUMMARY.md](/oneday/UI_FIXES_SUMMARY.md) | Markdown | 120 | 0 | 33 | 153 |
| [oneday/UNMOUNTED\_WIDGET\_COMPREHENSIVE\_FIX.md](/oneday/UNMOUNTED_WIDGET_COMPREHENSIVE_FIX.md) | Markdown | 169 | 0 | 35 | 204 |
| [oneday/UNMOUNTED\_WIDGET\_FIX\_GUIDE.md](/oneday/UNMOUNTED_WIDGET_FIX_GUIDE.md) | Markdown | 125 | 0 | 30 | 155 |
| [oneday/WEB\_ONBOARDING\_NAVIGATION.md](/oneday/WEB_ONBOARDING_NAVIGATION.md) | Markdown | 129 | 0 | 36 | 165 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | -19 | 0 | 0 | -19 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 10 | 0 | 0 | 10 |
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | 13 | 0 | 0 | 13 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 2,435 | 218 | 224 | 2,877 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_backup.dart](/oneday/lib/features/memory_palace/scene_detail_page_backup.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 9 | 3 | 2 | 14 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 27 | 4 | 5 | 36 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 4 | 0 | 0 | 4 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 22 | 0 | 0 | 22 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 1 | 1 | 0 | 2 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/lib/shared/utils/ui\_utils.dart](/oneday/lib/shared/utils/ui_utils.dart) | Dart | 94 | 19 | 9 | 122 |
| [oneday/test/add\_child\_category\_test.dart](/oneday/test/add_child_category_test.dart) | Dart | 146 | 30 | 48 | 224 |
| [oneday/test/category\_edit\_state\_test.dart](/oneday/test/category_edit_state_test.dart) | Dart | 147 | 40 | 55 | 242 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 150 | 38 | 52 | 240 |
| [oneday/test/category\_sidebar\_edit\_test.dart](/oneday/test/category_sidebar_edit_test.dart) | Dart | 171 | 45 | 61 | 277 |
| [oneday/test/context\_aware\_album\_creation\_test.dart](/oneday/test/context_aware_album_creation_test.dart) | Dart | 98 | 6 | 20 | 124 |
| [oneday/test/onboarding\_web\_navigation\_test.dart](/oneday/test/onboarding_web_navigation_test.dart) | Dart | 30 | 9 | 11 | 50 |
| [oneday/test/photo\_album\_creator\_test.dart](/oneday/test/photo_album_creator_test.dart) | Dart | -6 | 1 | 1 | -4 |
| [oneday/test/three\_dot\_menu\_test.dart](/oneday/test/three_dot_menu_test.dart) | Dart | 108 | 24 | 32 | 164 |
| [oneday/test\_popup\_menu\_fix.md](/oneday/test_popup_menu_fix.md) | Markdown | 117 | 0 | 32 | 149 |
| [oneday/vocabulary.json](/oneday/vocabulary.json) | JSON | 38,714 | 0 | 0 | 38,714 |
| [oneday/动作.html](/oneday/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 0 | 0 | 1 | 1 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details