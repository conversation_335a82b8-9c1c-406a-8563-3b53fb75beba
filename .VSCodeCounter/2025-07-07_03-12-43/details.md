# Details

Date : 2025-07-07 03:12:43

Directory /Users/<USER>/development/flutter_apps

Total : 156 files,  80251 codes, 2596 comments, 6445 blanks, all 89292 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.augment/rules/Augrules.backup.md](/.augment/rules/Augrules.backup.md) | Markdown | 388 | 0 | 120 | 508 |
| [.augment/rules/Augrules.md](/.augment/rules/Augrules.md) | Markdown | 428 | 0 | 125 | 553 |
| [NOTION\_PROJECT\_TEMPLATE.md](/NOTION_PROJECT_TEMPLATE.md) | Markdown | 137 | 0 | 55 | 192 |
| [ONEDAY\_PROJECT\_ANALYSIS.md](/ONEDAY_PROJECT_ANALYSIS.md) | Markdown | 105 | 0 | 47 | 152 |
| [USER\_COMMUNITY\_TEMPLATE.md](/USER_COMMUNITY_TEMPLATE.md) | Markdown | 250 | 0 | 119 | 369 |
| [oneday/.cursorrules.backup.md](/oneday/.cursorrules.backup.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/.cursorrules\_副本.md](/oneday/.cursorrules_%E5%89%AF%E6%9C%AC.md) | Markdown | 384 | 0 | 119 | 503 |
| [oneday/ADD\_CHILD\_CATEGORY\_FEATURE.md](/oneday/ADD_CHILD_CATEGORY_FEATURE.md) | Markdown | 170 | 0 | 43 | 213 |
| [oneday/ARCHITECTURE.md](/oneday/ARCHITECTURE.md) | Markdown | 380 | 0 | 81 | 461 |
| [oneday/Action.html](/oneday/Action.html) | HTML | 0 | 0 | 1 | 1 |
| [oneday/BLUR\_TRANSITION\_OPTIMIZATION\_SUMMARY.md](/oneday/BLUR_TRANSITION_OPTIMIZATION_SUMMARY.md) | Markdown | 133 | 0 | 34 | 167 |
| [oneday/BUG\_FIX\_SUMMARY.md](/oneday/BUG_FIX_SUMMARY.md) | Markdown | 72 | 0 | 22 | 94 |
| [oneday/CATEGORY\_TREE\_UPGRADE\_TEST\_GUIDE.md](/oneday/CATEGORY_TREE_UPGRADE_TEST_GUIDE.md) | Markdown | 169 | 0 | 54 | 223 |
| [oneday/CONTEXT\_AWARE\_ALBUM\_CREATION.md](/oneday/CONTEXT_AWARE_ALBUM_CREATION.md) | Markdown | 119 | 0 | 36 | 155 |
| [oneday/COVER\_MODE\_TEST\_GUIDE.md](/oneday/COVER_MODE_TEST_GUIDE.md) | Markdown | 115 | 0 | 35 | 150 |
| [oneday/COVER\_MODE\_VERTICAL\_BOUNDARY\_TEST\_GUIDE.md](/oneday/COVER_MODE_VERTICAL_BOUNDARY_TEST_GUIDE.md) | Markdown | 98 | 0 | 35 | 133 |
| [oneday/DEMO\_ADD\_CHILD\_CATEGORY.md](/oneday/DEMO_ADD_CHILD_CATEGORY.md) | Markdown | 156 | 0 | 29 | 185 |
| [oneday/DEVELOPER\_ENTRANCE\_GUIDE.md](/oneday/DEVELOPER_ENTRANCE_GUIDE.md) | Markdown | 59 | 0 | 24 | 83 |
| [oneday/DRAG\_DROP\_CATEGORY\_TEST\_GUIDE.md](/oneday/DRAG_DROP_CATEGORY_TEST_GUIDE.md) | Markdown | 182 | 0 | 48 | 230 |
| [oneday/DRAG\_HANDLE\_REMOVAL\_TEST\_GUIDE.md](/oneday/DRAG_HANDLE_REMOVAL_TEST_GUIDE.md) | Markdown | 181 | 0 | 54 | 235 |
| [oneday/DRAG\_POSITION\_OPTIMIZATION\_TEST\_GUIDE.md](/oneday/DRAG_POSITION_OPTIMIZATION_TEST_GUIDE.md) | Markdown | 181 | 0 | 48 | 229 |
| [oneday/FAST\_SWIPE\_FIX\_SUMMARY.md](/oneday/FAST_SWIPE_FIX_SUMMARY.md) | Markdown | 140 | 0 | 33 | 173 |
| [oneday/FEATURES.md](/oneday/FEATURES.md) | Markdown | 410 | 0 | 86 | 496 |
| [oneday/FINAL\_GESTURE\_FIX\_SUMMARY.md](/oneday/FINAL_GESTURE_FIX_SUMMARY.md) | Markdown | 115 | 0 | 33 | 148 |
| [oneday/FINAL\_UI\_FIXES\_SUMMARY.md](/oneday/FINAL_UI_FIXES_SUMMARY.md) | Markdown | 169 | 0 | 47 | 216 |
| [oneday/FLOATING\_BUTTON\_AUTO\_HIDE\_TEST\_GUIDE.md](/oneday/FLOATING_BUTTON_AUTO_HIDE_TEST_GUIDE.md) | Markdown | 147 | 0 | 45 | 192 |
| [oneday/GESTURE\_CONFLICT\_FIX\_SUMMARY.md](/oneday/GESTURE_CONFLICT_FIX_SUMMARY.md) | Markdown | 151 | 0 | 38 | 189 |
| [oneday/GESTURE\_DEBUG\_REPORT.md](/oneday/GESTURE_DEBUG_REPORT.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/GESTURE\_IMPLEMENTATION\_SUMMARY.md](/oneday/GESTURE_IMPLEMENTATION_SUMMARY.md) | Markdown | 99 | 0 | 32 | 131 |
| [oneday/GESTURE\_MODE\_TEST\_GUIDE.md](/oneday/GESTURE_MODE_TEST_GUIDE.md) | Markdown | 93 | 0 | 26 | 119 |
| [oneday/GESTURE\_OPTIMIZATION\_SUMMARY.md](/oneday/GESTURE_OPTIMIZATION_SUMMARY.md) | Markdown | 122 | 0 | 36 | 158 |
| [oneday/GESTURE\_TEST\_GUIDE.md](/oneday/GESTURE_TEST_GUIDE.md) | Markdown | 108 | 0 | 38 | 146 |
| [oneday/GIT\_COMMIT\_SUMMARY.md](/oneday/GIT_COMMIT_SUMMARY.md) | Markdown | 127 | 0 | 36 | 163 |
| [oneday/HORIZONTAL\_SWIPE\_FIX\_SUMMARY.md](/oneday/HORIZONTAL_SWIPE_FIX_SUMMARY.md) | Markdown | 137 | 0 | 36 | 173 |
| [oneday/ICON\_SETUP\_GUIDE.md](/oneday/ICON_SETUP_GUIDE.md) | Markdown | 231 | 0 | 28 | 259 |
| [oneday/IMAGE\_MARKING\_FUNCTION\_RESTORE.md](/oneday/IMAGE_MARKING_FUNCTION_RESTORE.md) | Markdown | 285 | 0 | 85 | 370 |
| [oneday/KEYBOARD\_AVOIDANCE\_IMPLEMENTATION.md](/oneday/KEYBOARD_AVOIDANCE_IMPLEMENTATION.md) | Markdown | 155 | 0 | 33 | 188 |
| [oneday/OPTIMIZING\_CURSOR\_AI.md](/oneday/OPTIMIZING_CURSOR_AI.md) | Markdown | 144 | 0 | 52 | 196 |
| [oneday/PHOTO\_EXPORT\_GUIDE.md](/oneday/PHOTO_EXPORT_GUIDE.md) | Markdown | 117 | 2 | 45 | 164 |
| [oneday/POPUP\_MENU\_FIX\_TEST\_GUIDE.md](/oneday/POPUP_MENU_FIX_TEST_GUIDE.md) | Markdown | 160 | 0 | 44 | 204 |
| [oneday/POSITION\_FOLLOWING\_SYSTEM.md](/oneday/POSITION_FOLLOWING_SYSTEM.md) | Markdown | 130 | 0 | 30 | 160 |
| [oneday/PRD.md](/oneday/PRD.md) | Markdown | 339 | 0 | 97 | 436 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | 250 | 0 | 95 | 345 |
| [oneday/README.md](/oneday/README.md) | Markdown | 1,148 | 0 | 254 | 1,402 |
| [oneday/SHARE\_FUNCTION\_SIMPLIFICATION.md](/oneday/SHARE_FUNCTION_SIMPLIFICATION.md) | Markdown | 99 | 0 | 29 | 128 |
| [oneday/THREE\_DOT\_MENU\_FEATURE.md](/oneday/THREE_DOT_MENU_FEATURE.md) | Markdown | 107 | 0 | 32 | 139 |
| [oneday/UI\_FIXES\_SUMMARY.md](/oneday/UI_FIXES_SUMMARY.md) | Markdown | 120 | 0 | 33 | 153 |
| [oneday/UNMOUNTED\_WIDGET\_COMPREHENSIVE\_FIX.md](/oneday/UNMOUNTED_WIDGET_COMPREHENSIVE_FIX.md) | Markdown | 169 | 0 | 35 | 204 |
| [oneday/UNMOUNTED\_WIDGET\_FIX\_GUIDE.md](/oneday/UNMOUNTED_WIDGET_FIX_GUIDE.md) | Markdown | 125 | 0 | 30 | 155 |
| [oneday/WEB\_ONBOARDING\_NAVIGATION.md](/oneday/WEB_ONBOARDING_NAVIGATION.md) | Markdown | 129 | 0 | 36 | 165 |
| [oneday/analysis\_options.yaml](/oneday/analysis_options.yaml) | YAML | 3 | 22 | 4 | 29 |
| [oneday/android/app/src/debug/AndroidManifest.xml](/oneday/android/app/src/debug/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/app/src/main/AndroidManifest.xml](/oneday/android/app/src/main/AndroidManifest.xml) | XML | 38 | 12 | 2 | 52 |
| [oneday/android/app/src/main/res/drawable-v21/launch\_background.xml](/oneday/android/app/src/main/res/drawable-v21/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/drawable/launch\_background.xml](/oneday/android/app/src/main/res/drawable/launch_background.xml) | XML | 4 | 7 | 2 | 13 |
| [oneday/android/app/src/main/res/values-night/styles.xml](/oneday/android/app/src/main/res/values-night/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/main/res/values/styles.xml](/oneday/android/app/src/main/res/values/styles.xml) | XML | 9 | 9 | 1 | 19 |
| [oneday/android/app/src/profile/AndroidManifest.xml](/oneday/android/app/src/profile/AndroidManifest.xml) | XML | 3 | 4 | 1 | 8 |
| [oneday/android/gradle.properties](/oneday/android/gradle.properties) | Properties | 3 | 0 | 1 | 4 |
| [oneday/android/gradle/wrapper/gradle-wrapper.properties](/oneday/android/gradle/wrapper/gradle-wrapper.properties) | Properties | 5 | 0 | 1 | 6 |
| [oneday/assets/icons/README.md](/oneday/assets/icons/README.md) | Markdown | 33 | 0 | 13 | 46 |
| [oneday/blueprint.md](/oneday/blueprint.md) | Markdown | 69 | 0 | 19 | 88 |
| [oneday/debug\_share\_issue.md](/oneday/debug_share_issue.md) | Markdown | 73 | 0 | 24 | 97 |
| [oneday/ios/Podfile](/oneday/ios/Podfile) | Ruby | 31 | 3 | 10 | 44 |
| [oneday/ios/Runner/AppDelegate.swift](/oneday/ios/Runner/AppDelegate.swift) | Swift | 12 | 0 | 2 | 14 |
| [oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 116 | 0 | 1 | 117 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json) | JSON | 23 | 0 | 1 | 24 |
| [oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md](/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md) | Markdown | 3 | 0 | 2 | 5 |
| [oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard](/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard) | XML | 36 | 1 | 1 | 38 |
| [oneday/ios/Runner/Base.lproj/Main.storyboard](/oneday/ios/Runner/Base.lproj/Main.storyboard) | XML | 25 | 1 | 1 | 27 |
| [oneday/ios/Runner/Runner-Bridging-Header.h](/oneday/ios/Runner/Runner-Bridging-Header.h) | C++ | 1 | 0 | 1 | 2 |
| [oneday/ios/RunnerTests/RunnerTests.swift](/oneday/ios/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/lib/core/constants/app\_icons.dart](/oneday/lib/core/constants/app_icons.dart) | Dart | 35 | 5 | 3 | 43 |
| [oneday/lib/core/data/pao\_exercises\_data.dart](/oneday/lib/core/data/pao_exercises_data.dart) | Dart | 272 | 18 | 17 | 307 |
| [oneday/lib/features/auth/login\_page.dart](/oneday/lib/features/auth/login_page.dart) | Dart | 730 | 41 | 42 | 813 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | 721 | 58 | 56 | 835 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 775 | 24 | 58 | 857 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 1,564 | 78 | 89 | 1,731 |
| [oneday/lib/features/exercise/exercise\_session\_page.dart](/oneday/lib/features/exercise/exercise_session_page.dart) | Dart | 985 | 72 | 87 | 1,144 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | 104 | 4 | 6 | 114 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | 4,064 | 283 | 355 | 4,702 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | 2,768 | 376 | 453 | 3,597 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_backup.dart](/oneday/lib/features/memory_palace/scene_detail_page_backup.dart) | Dart | 2,394 | 224 | 292 | 2,910 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | 1,672 | 186 | 224 | 2,082 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 221 | 12 | 19 | 252 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | 783 | 59 | 75 | 917 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | 793 | 44 | 75 | 912 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 444 | 21 | 50 | 515 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 1,252 | 61 | 130 | 1,443 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 698 | 42 | 53 | 793 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | 167 | 9 | 21 | 197 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 1,966 | 71 | 120 | 2,157 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | 1,412 | 54 | 78 | 1,544 |
| [oneday/lib/features/wage\_system/wage\_wallet\_page.dart](/oneday/lib/features/wage_system/wage_wallet_page.dart) | Dart | 826 | 36 | 57 | 919 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 238 | 21 | 10 | 269 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 215 | 16 | 23 | 254 |
| [oneday/lib/shared/services/enhanced\_share\_service.dart](/oneday/lib/shared/services/enhanced_share_service.dart) | Dart | 158 | 27 | 37 | 222 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 195 | 35 | 45 | 275 |
| [oneday/lib/shared/utils/simple\_image\_test.dart](/oneday/lib/shared/utils/simple_image_test.dart) | Dart | 83 | 14 | 26 | 123 |
| [oneday/lib/shared/utils/ui\_utils.dart](/oneday/lib/shared/utils/ui_utils.dart) | Dart | 94 | 19 | 9 | 122 |
| [oneday/lib/shared/widgets/icons/wechat\_icon.dart](/oneday/lib/shared/widgets/icons/wechat_icon.dart) | Dart | 62 | 8 | 14 | 84 |
| [oneday/lib/shared/widgets/oneday\_logo.dart](/oneday/lib/shared/widgets/oneday_logo.dart) | Dart | 204 | 17 | 43 | 264 |
| [oneday/linux/CMakeLists.txt](/oneday/linux/CMakeLists.txt) | CMake | 104 | 0 | 25 | 129 |
| [oneday/linux/flutter/CMakeLists.txt](/oneday/linux/flutter/CMakeLists.txt) | CMake | 79 | 0 | 10 | 89 |
| [oneday/linux/flutter/generated\_plugin\_registrant.cc](/oneday/linux/flutter/generated_plugin_registrant.cc) | C++ | 11 | 4 | 5 | 20 |
| [oneday/linux/flutter/generated\_plugin\_registrant.h](/oneday/linux/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/linux/flutter/generated\_plugins.cmake](/oneday/linux/flutter/generated_plugins.cmake) | CMake | 20 | 0 | 6 | 26 |
| [oneday/linux/runner/CMakeLists.txt](/oneday/linux/runner/CMakeLists.txt) | CMake | 21 | 0 | 6 | 27 |
| [oneday/linux/runner/main.cc](/oneday/linux/runner/main.cc) | C++ | 5 | 0 | 2 | 7 |
| [oneday/linux/runner/my\_application.cc](/oneday/linux/runner/my_application.cc) | C++ | 83 | 21 | 27 | 131 |
| [oneday/linux/runner/my\_application.h](/oneday/linux/runner/my_application.h) | C++ | 7 | 7 | 5 | 19 |
| [oneday/macos/Flutter/GeneratedPluginRegistrant.swift](/oneday/macos/Flutter/GeneratedPluginRegistrant.swift) | Swift | 14 | 3 | 4 | 21 |
| [oneday/macos/Podfile](/oneday/macos/Podfile) | Ruby | 32 | 1 | 10 | 43 |
| [oneday/macos/Runner/AppDelegate.swift](/oneday/macos/Runner/AppDelegate.swift) | Swift | 11 | 0 | 3 | 14 |
| [oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json](/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json) | JSON | 68 | 0 | 0 | 68 |
| [oneday/macos/Runner/Base.lproj/MainMenu.xib](/oneday/macos/Runner/Base.lproj/MainMenu.xib) | XML | 343 | 0 | 1 | 344 |
| [oneday/macos/Runner/MainFlutterWindow.swift](/oneday/macos/Runner/MainFlutterWindow.swift) | Swift | 12 | 0 | 4 | 16 |
| [oneday/macos/RunnerTests/RunnerTests.swift](/oneday/macos/RunnerTests/RunnerTests.swift) | Swift | 7 | 2 | 4 | 13 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 47 | 69 | 26 | 142 |
| [oneday/test/add\_child\_category\_test.dart](/oneday/test/add_child_category_test.dart) | Dart | 146 | 30 | 48 | 224 |
| [oneday/test/category\_edit\_state\_test.dart](/oneday/test/category_edit_state_test.dart) | Dart | 147 | 40 | 55 | 242 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 150 | 38 | 52 | 240 |
| [oneday/test/category\_sidebar\_edit\_test.dart](/oneday/test/category_sidebar_edit_test.dart) | Dart | 171 | 45 | 61 | 277 |
| [oneday/test/context\_aware\_album\_creation\_test.dart](/oneday/test/context_aware_album_creation_test.dart) | Dart | 98 | 6 | 20 | 124 |
| [oneday/test/create\_album\_success\_message\_test.dart](/oneday/test/create_album_success_message_test.dart) | Dart | 172 | 20 | 27 | 219 |
| [oneday/test/memory\_palace\_card\_test.dart](/oneday/test/memory_palace_card_test.dart) | Dart | 201 | 13 | 29 | 243 |
| [oneday/test/memory\_palace\_persistence\_test.dart](/oneday/test/memory_palace_persistence_test.dart) | Dart | 166 | 14 | 25 | 205 |
| [oneday/test/onboarding\_web\_navigation\_test.dart](/oneday/test/onboarding_web_navigation_test.dart) | Dart | 30 | 9 | 11 | 50 |
| [oneday/test/photo\_album\_creator\_test.dart](/oneday/test/photo_album_creator_test.dart) | Dart | 81 | 2 | 17 | 100 |
| [oneday/test/photo\_album\_dialog\_layout\_test.dart](/oneday/test/photo_album_dialog_layout_test.dart) | Dart | 101 | 25 | 32 | 158 |
| [oneday/test/photo\_album\_dialog\_test.dart](/oneday/test/photo_album_dialog_test.dart) | Dart | 96 | 21 | 28 | 145 |
| [oneday/test/three\_dot\_menu\_test.dart](/oneday/test/three_dot_menu_test.dart) | Dart | 108 | 24 | 32 | 164 |
| [oneday/test/widget\_test.dart](/oneday/test/widget_test.dart) | Dart | 11 | 9 | 6 | 26 |
| [oneday/test\_popup\_menu\_fix.md](/oneday/test_popup_menu_fix.md) | Markdown | 117 | 0 | 32 | 149 |
| [oneday/test\_share\_function.md](/oneday/test_share_function.md) | Markdown | 94 | 0 | 32 | 126 |
| [oneday/vocabulary.json](/oneday/vocabulary.json) | JSON | 38,714 | 0 | 0 | 38,714 |
| [oneday/web/index.html](/oneday/web/index.html) | HTML | 19 | 15 | 5 | 39 |
| [oneday/web/manifest.json](/oneday/web/manifest.json) | JSON | 35 | 0 | 0 | 35 |
| [oneday/windows/CMakeLists.txt](/oneday/windows/CMakeLists.txt) | CMake | 89 | 0 | 20 | 109 |
| [oneday/windows/flutter/CMakeLists.txt](/oneday/windows/flutter/CMakeLists.txt) | CMake | 98 | 0 | 12 | 110 |
| [oneday/windows/flutter/generated\_plugin\_registrant.cc](/oneday/windows/flutter/generated_plugin_registrant.cc) | C++ | 15 | 4 | 5 | 24 |
| [oneday/windows/flutter/generated\_plugin\_registrant.h](/oneday/windows/flutter/generated_plugin_registrant.h) | C++ | 5 | 5 | 6 | 16 |
| [oneday/windows/flutter/generated\_plugins.cmake](/oneday/windows/flutter/generated_plugins.cmake) | CMake | 22 | 0 | 6 | 28 |
| [oneday/windows/runner/CMakeLists.txt](/oneday/windows/runner/CMakeLists.txt) | CMake | 34 | 0 | 7 | 41 |
| [oneday/windows/runner/flutter\_window.cpp](/oneday/windows/runner/flutter_window.cpp) | C++ | 49 | 7 | 16 | 72 |
| [oneday/windows/runner/flutter\_window.h](/oneday/windows/runner/flutter_window.h) | C++ | 20 | 5 | 9 | 34 |
| [oneday/windows/runner/main.cpp](/oneday/windows/runner/main.cpp) | C++ | 30 | 4 | 10 | 44 |
| [oneday/windows/runner/resource.h](/oneday/windows/runner/resource.h) | C++ | 9 | 6 | 2 | 17 |
| [oneday/windows/runner/utils.cpp](/oneday/windows/runner/utils.cpp) | C++ | 54 | 2 | 10 | 66 |
| [oneday/windows/runner/utils.h](/oneday/windows/runner/utils.h) | C++ | 8 | 6 | 6 | 20 |
| [oneday/windows/runner/win32\_window.cpp](/oneday/windows/runner/win32_window.cpp) | C++ | 210 | 24 | 55 | 289 |
| [oneday/windows/runner/win32\_window.h](/oneday/windows/runner/win32_window.h) | C++ | 48 | 31 | 24 | 103 |
| [oneday/动作.html](/oneday/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 0 | 0 | 1 | 1 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)