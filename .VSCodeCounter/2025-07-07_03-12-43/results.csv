"filename", "language", "Markdown", "<PERSON><PERSON>ake", "C++", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "JSO<PERSON>", "Properties", "Dar<PERSON>", "<PERSON>", "Swift", "XML", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md", "Markdown", 388, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 120, 508
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md", "Markdown", 428, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 125, 553
"/Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 55, 192
"/Users/<USER>/development/flutter_apps/ONEDAY_PROJECT_ANALYSIS.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 152
"/Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md", "Markdown", 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 369
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.backup.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules_副本.md", "Markdown", 384, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/ADD_CHILD_CATEGORY_FEATURE.md", "Markdown", 170, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 43, 213
"/Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md", "Markdown", 380, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 81, 461
"/Users/<USER>/development/flutter_apps/oneday/Action.html", "HTML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"/Users/<USER>/development/flutter_apps/oneday/BLUR_TRANSITION_OPTIMIZATION_SUMMARY.md", "Markdown", 133, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 34, 167
"/Users/<USER>/development/flutter_apps/oneday/BUG_FIX_SUMMARY.md", "Markdown", 72, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 22, 94
"/Users/<USER>/development/flutter_apps/oneday/CATEGORY_TREE_UPGRADE_TEST_GUIDE.md", "Markdown", 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 223
"/Users/<USER>/development/flutter_apps/oneday/CONTEXT_AWARE_ALBUM_CREATION.md", "Markdown", 119, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 155
"/Users/<USER>/development/flutter_apps/oneday/COVER_MODE_TEST_GUIDE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 150
"/Users/<USER>/development/flutter_apps/oneday/COVER_MODE_VERTICAL_BOUNDARY_TEST_GUIDE.md", "Markdown", 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 133
"/Users/<USER>/development/flutter_apps/oneday/DEMO_ADD_CHILD_CATEGORY.md", "Markdown", 156, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 185
"/Users/<USER>/development/flutter_apps/oneday/DEVELOPER_ENTRANCE_GUIDE.md", "Markdown", 59, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 83
"/Users/<USER>/development/flutter_apps/oneday/DRAG_DROP_CATEGORY_TEST_GUIDE.md", "Markdown", 182, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 230
"/Users/<USER>/development/flutter_apps/oneday/DRAG_HANDLE_REMOVAL_TEST_GUIDE.md", "Markdown", 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 54, 235
"/Users/<USER>/development/flutter_apps/oneday/DRAG_POSITION_OPTIMIZATION_TEST_GUIDE.md", "Markdown", 181, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 229
"/Users/<USER>/development/flutter_apps/oneday/FAST_SWIPE_FIX_SUMMARY.md", "Markdown", 140, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 173
"/Users/<USER>/development/flutter_apps/oneday/FEATURES.md", "Markdown", 410, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 86, 496
"/Users/<USER>/development/flutter_apps/oneday/FINAL_GESTURE_FIX_SUMMARY.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 148
"/Users/<USER>/development/flutter_apps/oneday/FINAL_UI_FIXES_SUMMARY.md", "Markdown", 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 47, 216
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_BUTTON_AUTO_HIDE_TEST_GUIDE.md", "Markdown", 147, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 45, 192
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_CONFLICT_FIX_SUMMARY.md", "Markdown", 151, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 189
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_DEBUG_REPORT.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 128
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_IMPLEMENTATION_SUMMARY.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 131
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_MODE_TEST_GUIDE.md", "Markdown", 93, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 26, 119
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_OPTIMIZATION_SUMMARY.md", "Markdown", 122, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 158
"/Users/<USER>/development/flutter_apps/oneday/GESTURE_TEST_GUIDE.md", "Markdown", 108, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 146
"/Users/<USER>/development/flutter_apps/oneday/GIT_COMMIT_SUMMARY.md", "Markdown", 127, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 163
"/Users/<USER>/development/flutter_apps/oneday/HORIZONTAL_SWIPE_FIX_SUMMARY.md", "Markdown", 137, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 173
"/Users/<USER>/development/flutter_apps/oneday/ICON_SETUP_GUIDE.md", "Markdown", 231, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 28, 259
"/Users/<USER>/development/flutter_apps/oneday/IMAGE_MARKING_FUNCTION_RESTORE.md", "Markdown", 285, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 85, 370
"/Users/<USER>/development/flutter_apps/oneday/KEYBOARD_AVOIDANCE_IMPLEMENTATION.md", "Markdown", 155, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 188
"/Users/<USER>/development/flutter_apps/oneday/OPTIMIZING_CURSOR_AI.md", "Markdown", 144, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 52, 196
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_EXPORT_GUIDE.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 45, 164
"/Users/<USER>/development/flutter_apps/oneday/POPUP_MENU_FIX_TEST_GUIDE.md", "Markdown", 160, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 44, 204
"/Users/<USER>/development/flutter_apps/oneday/POSITION_FOLLOWING_SYSTEM.md", "Markdown", 130, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 160
"/Users/<USER>/development/flutter_apps/oneday/PRD.md", "Markdown", 339, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 436
"/Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md", "Markdown", 250, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 95, 345
"/Users/<USER>/development/flutter_apps/oneday/README.md", "Markdown", 1148, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 254, 1402
"/Users/<USER>/development/flutter_apps/oneday/SHARE_FUNCTION_SIMPLIFICATION.md", "Markdown", 99, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 29, 128
"/Users/<USER>/development/flutter_apps/oneday/THREE_DOT_MENU_FEATURE.md", "Markdown", 107, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 139
"/Users/<USER>/development/flutter_apps/oneday/UI_FIXES_SUMMARY.md", "Markdown", 120, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 33, 153
"/Users/<USER>/development/flutter_apps/oneday/UNMOUNTED_WIDGET_COMPREHENSIVE_FIX.md", "Markdown", 169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 35, 204
"/Users/<USER>/development/flutter_apps/oneday/UNMOUNTED_WIDGET_FIX_GUIDE.md", "Markdown", 125, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 30, 155
"/Users/<USER>/development/flutter_apps/oneday/WEB_ONBOARDING_NAVIGATION.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 165
"/Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml", "YAML", 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 0, 22, 4, 29
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/debug/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 38, 12, 2, 52
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable-v21/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/drawable/launch_background.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4, 7, 2, 13
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values-night/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/main/res/values/styles.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 9, 9, 1, 19
"/Users/<USER>/development/flutter_apps/oneday/android/app/src/profile/AndroidManifest.xml", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3, 4, 1, 8
"/Users/<USER>/development/flutter_apps/oneday/android/gradle.properties", "Properties", 0, 0, 0, 0, 0, 0, 3, 0, 0, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/android/gradle/wrapper/gradle-wrapper.properties", "Properties", 0, 0, 0, 0, 0, 0, 5, 0, 0, 0, 0, 0, 1, 6
"/Users/<USER>/development/flutter_apps/oneday/assets/icons/README.md", "Markdown", 33, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 13, 46
"/Users/<USER>/development/flutter_apps/oneday/blueprint.md", "Markdown", 69, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 19, 88
"/Users/<USER>/development/flutter_apps/oneday/debug_share_issue.md", "Markdown", 73, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 24, 97
"/Users/<USER>/development/flutter_apps/oneday/ios/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 31, 0, 0, 3, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 2, 14
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 0, 0, 0, 116, 0, 0, 0, 0, 0, 0, 1, 117
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/Contents.json", "JSON", 0, 0, 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 1, 24
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset/README.md", "Markdown", 3, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 5
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/LaunchScreen.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 36, 1, 1, 38
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Base.lproj/Main.storyboard", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 1, 1, 27
"/Users/<USER>/development/flutter_apps/oneday/ios/Runner/Runner-Bridging-Header.h", "C++", 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2
"/Users/<USER>/development/flutter_apps/oneday/ios/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/lib/core/constants/app_icons.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 35, 0, 0, 0, 5, 3, 43
"/Users/<USER>/development/flutter_apps/oneday/lib/core/data/pao_exercises_data.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 272, 0, 0, 0, 18, 17, 307
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 730, 0, 0, 0, 41, 42, 813
"/Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 721, 0, 0, 0, 58, 56, 835
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 775, 0, 0, 0, 24, 58, 857
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 1564, 0, 0, 0, 78, 89, 1731
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 985, 0, 0, 0, 72, 87, 1144
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 0, 0, 0, 0, 0, 0, 836, 0, 0, 0, 38, 45, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 104, 0, 0, 0, 4, 6, 114
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 4064, 0, 0, 0, 283, 355, 4702
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 2768, 0, 0, 0, 376, 453, 3597
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_backup.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 2394, 0, 0, 0, 224, 292, 2910
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_副本.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 1672, 0, 0, 0, 186, 224, 2082
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 221, 0, 0, 0, 12, 19, 252
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 783, 0, 0, 0, 59, 75, 917
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 793, 0, 0, 0, 44, 75, 912
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 444, 0, 0, 0, 21, 50, 515
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 1252, 0, 0, 0, 61, 130, 1443
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 698, 0, 0, 0, 42, 53, 793
"/Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 167, 0, 0, 0, 9, 21, 197
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 1966, 0, 0, 0, 71, 120, 2157
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 1412, 0, 0, 0, 54, 78, 1544
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 826, 0, 0, 0, 36, 57, 919
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 238, 0, 0, 0, 21, 10, 269
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 215, 0, 0, 0, 16, 23, 254
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/services/enhanced_share_service.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 158, 0, 0, 0, 27, 37, 222
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 195, 0, 0, 0, 35, 45, 275
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 83, 0, 0, 0, 14, 26, 123
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 94, 0, 0, 0, 19, 9, 122
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/icons/wechat_icon.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 62, 0, 0, 0, 8, 14, 84
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/widgets/oneday_logo.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 204, 0, 0, 0, 17, 43, 264
"/Users/<USER>/development/flutter_apps/oneday/linux/CMakeLists.txt", "CMake", 0, 104, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 25, 129
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/CMakeLists.txt", "CMake", 0, 79, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 10, 89
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 11, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 20
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugin_registrant.h", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/linux/flutter/generated_plugins.cmake", "CMake", 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/CMakeLists.txt", "CMake", 0, 21, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 27
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/main.cc", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 7
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.cc", "C++", 0, 0, 83, 0, 0, 0, 0, 0, 0, 0, 0, 21, 27, 131
"/Users/<USER>/development/flutter_apps/oneday/linux/runner/my_application.h", "C++", 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7, 5, 19
"/Users/<USER>/development/flutter_apps/oneday/macos/Flutter/GeneratedPluginRegistrant.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 14, 0, 3, 4, 21
"/Users/<USER>/development/flutter_apps/oneday/macos/Podfile", "Ruby", 0, 0, 0, 0, 0, 0, 0, 0, 32, 0, 0, 1, 10, 43
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/AppDelegate.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 3, 14
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset/Contents.json", "JSON", 0, 0, 0, 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 68
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/Base.lproj/MainMenu.xib", "XML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 343, 0, 1, 344
"/Users/<USER>/development/flutter_apps/oneday/macos/Runner/MainFlutterWindow.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 0, 0, 4, 16
"/Users/<USER>/development/flutter_apps/oneday/macos/RunnerTests/RunnerTests.swift", "Swift", 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 0, 2, 4, 13
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 0, 47, 0, 0, 0, 0, 0, 0, 69, 26, 142
"/Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 146, 0, 0, 0, 30, 48, 224
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 147, 0, 0, 0, 40, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 150, 0, 0, 0, 38, 52, 240
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 171, 0, 0, 0, 45, 61, 277
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 98, 0, 0, 0, 6, 20, 124
"/Users/<USER>/development/flutter_apps/oneday/test/create_album_success_message_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 172, 0, 0, 0, 20, 27, 219
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 201, 0, 0, 0, 13, 29, 243
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_persistence_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 166, 0, 0, 0, 14, 25, 205
"/Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 30, 0, 0, 0, 9, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 81, 0, 0, 0, 2, 17, 100
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 101, 0, 0, 0, 25, 32, 158
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 96, 0, 0, 0, 21, 28, 145
"/Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 108, 0, 0, 0, 24, 32, 164
"/Users/<USER>/development/flutter_apps/oneday/test/widget_test.dart", "Dart", 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 0, 9, 6, 26
"/Users/<USER>/development/flutter_apps/oneday/test_popup_menu_fix.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 149
"/Users/<USER>/development/flutter_apps/oneday/test_share_function.md", "Markdown", 94, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 32, 126
"/Users/<USER>/development/flutter_apps/oneday/vocabulary.json", "JSON", 0, 0, 0, 0, 0, 38714, 0, 0, 0, 0, 0, 0, 0, 38714
"/Users/<USER>/development/flutter_apps/oneday/web/index.html", "HTML", 0, 0, 0, 19, 0, 0, 0, 0, 0, 0, 0, 15, 5, 39
"/Users/<USER>/development/flutter_apps/oneday/web/manifest.json", "JSON", 0, 0, 0, 0, 0, 35, 0, 0, 0, 0, 0, 0, 0, 35
"/Users/<USER>/development/flutter_apps/oneday/windows/CMakeLists.txt", "CMake", 0, 89, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 20, 109
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/CMakeLists.txt", "CMake", 0, 98, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 110
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.cc", "C++", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 0, 4, 5, 24
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugin_registrant.h", "C++", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 0, 5, 6, 16
"/Users/<USER>/development/flutter_apps/oneday/windows/flutter/generated_plugins.cmake", "CMake", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 6, 28
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/CMakeLists.txt", "CMake", 0, 34, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 7, 41
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.cpp", "C++", 0, 0, 49, 0, 0, 0, 0, 0, 0, 0, 0, 7, 16, 72
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/flutter_window.h", "C++", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 0, 5, 9, 34
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/main.cpp", "C++", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 0, 4, 10, 44
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/resource.h", "C++", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 0, 6, 2, 17
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.cpp", "C++", 0, 0, 54, 0, 0, 0, 0, 0, 0, 0, 0, 2, 10, 66
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/utils.h", "C++", 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 0, 6, 6, 20
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.cpp", "C++", 0, 0, 210, 0, 0, 0, 0, 0, 0, 0, 0, 24, 55, 289
"/Users/<USER>/development/flutter_apps/oneday/windows/runner/win32_window.h", "C++", 0, 0, 48, 0, 0, 0, 0, 0, 0, 0, 0, 31, 24, 103
"/Users/<USER>/development/flutter_apps/oneday/动作.html", "HTML", 0, 0, 0, 693, 0, 0, 0, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/tempPrompts.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1
"Total", "-", 10489, 467, 560, 712, 50, 38956, 8, 28409, 63, 63, 474, 2596, 6445, 89292