"filename", "language", "Markdown", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.backup.md", "Markdown", 388, 0, 0, 0, 0, 120, 508
"/Users/<USER>/development/flutter_apps/.augment/rules/Augrules.md", "Markdown", 40, 0, 0, 0, 0, 5, 45
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.backup.md", "Markdown", 384, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules_副本.md", "Markdown", 384, 0, 0, 0, 0, 119, 503
"/Users/<USER>/development/flutter_apps/oneday/ADD_CHILD_CATEGORY_FEATURE.md", "Markdown", 170, 0, 0, 0, 0, 43, 213
"/Users/<USER>/development/flutter_apps/oneday/ARCHITECTURE.md", "Markdown", 380, 0, 0, 0, 0, 81, 461
"/Users/<USER>/development/flutter_apps/oneday/CATEGORY_TREE_UPGRADE_TEST_GUIDE.md", "Markdown", 169, 0, 0, 0, 0, 54, 223
"/Users/<USER>/development/flutter_apps/oneday/CONTEXT_AWARE_ALBUM_CREATION.md", "Markdown", 119, 0, 0, 0, 0, 36, 155
"/Users/<USER>/development/flutter_apps/oneday/DEMO_ADD_CHILD_CATEGORY.md", "Markdown", 156, 0, 0, 0, 0, 29, 185
"/Users/<USER>/development/flutter_apps/oneday/DRAG_DROP_CATEGORY_TEST_GUIDE.md", "Markdown", 182, 0, 0, 0, 0, 48, 230
"/Users/<USER>/development/flutter_apps/oneday/DRAG_HANDLE_REMOVAL_TEST_GUIDE.md", "Markdown", 181, 0, 0, 0, 0, 54, 235
"/Users/<USER>/development/flutter_apps/oneday/DRAG_POSITION_OPTIMIZATION_TEST_GUIDE.md", "Markdown", 181, 0, 0, 0, 0, 48, 229
"/Users/<USER>/development/flutter_apps/oneday/FEATURES.md", "Markdown", 410, 0, 0, 0, 0, 86, 496
"/Users/<USER>/development/flutter_apps/oneday/FINAL_UI_FIXES_SUMMARY.md", "Markdown", 169, 0, 0, 0, 0, 47, 216
"/Users/<USER>/development/flutter_apps/oneday/FLOATING_BUTTON_AUTO_HIDE_TEST_GUIDE.md", "Markdown", 147, 0, 0, 0, 0, 45, 192
"/Users/<USER>/development/flutter_apps/oneday/GIT_COMMIT_SUMMARY.md", "Markdown", 127, 0, 0, 0, 0, 36, 163
"/Users/<USER>/development/flutter_apps/oneday/PHOTO_EXPORT_GUIDE.md", "Markdown", 117, 0, 0, 0, 2, 45, 164
"/Users/<USER>/development/flutter_apps/oneday/POPUP_MENU_FIX_TEST_GUIDE.md", "Markdown", 160, 0, 0, 0, 0, 44, 204
"/Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md", "Markdown", 122, 0, 0, 0, 0, 44, 166
"/Users/<USER>/development/flutter_apps/oneday/THREE_DOT_MENU_FEATURE.md", "Markdown", 107, 0, 0, 0, 0, 32, 139
"/Users/<USER>/development/flutter_apps/oneday/UI_FIXES_SUMMARY.md", "Markdown", 120, 0, 0, 0, 0, 33, 153
"/Users/<USER>/development/flutter_apps/oneday/UNMOUNTED_WIDGET_COMPREHENSIVE_FIX.md", "Markdown", 169, 0, 0, 0, 0, 35, 204
"/Users/<USER>/development/flutter_apps/oneday/UNMOUNTED_WIDGET_FIX_GUIDE.md", "Markdown", 125, 0, 0, 0, 0, 30, 155
"/Users/<USER>/development/flutter_apps/oneday/WEB_ONBOARDING_NAVIGATION.md", "Markdown", 129, 0, 0, 0, 0, 36, 165
"/Users/<USER>/development/flutter_apps/oneday/lib/features/auth/login_page.dart", "Dart", 0, 0, 2, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 0, -19, 0, 0, 0, -19
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 0, 10, 0, 0, 0, 10
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 13, 0, 0, 0, 13
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 0, 2435, 0, 218, 224, 2877
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 0, 2, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_backup.dart", "Dart", 0, 0, 2, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_副本.dart", "Dart", 0, 0, 2, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 0, 9, 0, 3, 2, 14
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 0, 27, 0, 4, 5, 36
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 0, 4, 0, 0, 0, 4
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 0, 1, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 0, 1, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 0, 1, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 0, 22, 0, 0, 0, 22
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 0, 1, 0, 1, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 0, -1, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/ui_utils.dart", "Dart", 0, 0, 94, 0, 19, 9, 122
"/Users/<USER>/development/flutter_apps/oneday/test/add_child_category_test.dart", "Dart", 0, 0, 146, 0, 30, 48, 224
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 0, 147, 0, 40, 55, 242
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 0, 150, 0, 38, 52, 240
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 0, 171, 0, 45, 61, 277
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 0, 98, 0, 6, 20, 124
"/Users/<USER>/development/flutter_apps/oneday/test/onboarding_web_navigation_test.dart", "Dart", 0, 0, 30, 0, 9, 11, 50
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_creator_test.dart", "Dart", 0, 0, -6, 0, 1, 1, -4
"/Users/<USER>/development/flutter_apps/oneday/test/three_dot_menu_test.dart", "Dart", 0, 0, 108, 0, 24, 32, 164
"/Users/<USER>/development/flutter_apps/oneday/test_popup_menu_fix.md", "Markdown", 117, 0, 0, 0, 0, 32, 149
"/Users/<USER>/development/flutter_apps/oneday/vocabulary.json", "JSON", 0, 0, 0, 38714, 0, 0, 38714
"/Users/<USER>/development/flutter_apps/oneday/动作.html", "HTML", 0, 693, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/tempPrompts.md", "Markdown", 0, 0, 0, 0, 0, 1, 1
"Total", "-", 4753, 693, 3450, 38714, 463, 1875, 49948