# Diff Details

Date : 2025-07-22 04:21:03

Directory /Users/<USER>/development/flutter_apps

Total : 232 files,  26297 codes, 2034 comments, 3950 blanks, all 32281 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [NOTION\_PROJECT\_TEMPLATE.md](/NOTION_PROJECT_TEMPLATE.md) | Markdown | 0 | 0 | 3 | 3 |
| [USER\_COMMUNITY\_TEMPLATE.md](/USER_COMMUNITY_TEMPLATE.md) | Markdown | 11 | 0 | 3 | 14 |
| [accompanion/.idea/accompanion.iml](/accompanion/.idea/accompanion.iml) | XML | 15 | 0 | 0 | 15 |
| [accompanion/.idea/caches/deviceStreaming.xml](/accompanion/.idea/caches/deviceStreaming.xml) | XML | 835 | 0 | 0 | 835 |
| [accompanion/.idea/deviceManager.xml](/accompanion/.idea/deviceManager.xml) | XML | 13 | 0 | 0 | 13 |
| [accompanion/.idea/libraries/Dart\_Packages.xml](/accompanion/.idea/libraries/Dart_Packages.xml) | XML | 748 | 0 | 0 | 748 |
| [accompanion/.idea/libraries/Dart\_SDK.xml](/accompanion/.idea/libraries/Dart_SDK.xml) | XML | 31 | 0 | 0 | 31 |
| [accompanion/.idea/misc.xml](/accompanion/.idea/misc.xml) | XML | 6 | 0 | 0 | 6 |
| [accompanion/.idea/modules.xml](/accompanion/.idea/modules.xml) | XML | 8 | 0 | 0 | 8 |
| [accompanion/accompanion/lib/main.dart](/accompanion/accompanion/lib/main.dart) | Dart | 27 | -36 | -6 | -15 |
| [accompanion/accompanion/lib/screens/calculation\_screen.dart](/accompanion/accompanion/lib/screens/calculation_screen.dart) | Dart | -33 | 0 | 0 | -33 |
| [accompanion/accompanion/lib/screens/history\_screen.dart](/accompanion/accompanion/lib/screens/history_screen.dart) | Dart | 50 | 0 | 0 | 50 |
| [accompanion/accompanion/lib/screens/home\_screen.dart](/accompanion/accompanion/lib/screens/home_screen.dart) | Dart | 29 | 6 | 9 | 44 |
| [accompanion/accompanion/lib/screens/mode\_selection\_screen.dart](/accompanion/accompanion/lib/screens/mode_selection_screen.dart) | Dart | 46 | 5 | 6 | 57 |
| [accompanion/accompanion/lib/screens/onboarding\_screen.dart](/accompanion/accompanion/lib/screens/onboarding_screen.dart) | Dart | 2 | 1 | 1 | 4 |
| [accompanion/accompanion/lib/screens/optimization\_tips\_screen.dart](/accompanion/accompanion/lib/screens/optimization_tips_screen.dart) | Dart | 12 | 0 | 0 | 12 |
| [accompanion/accompanion/lib/screens/room\_setup\_screen.dart](/accompanion/accompanion/lib/screens/room_setup_screen.dart) | Dart | 38 | 0 | 2 | 40 |
| [accompanion/accompanion/lib/screens/settings\_screen.dart](/accompanion/accompanion/lib/screens/settings_screen.dart) | Dart | 74 | 1 | 5 | 80 |
| [accompanion/accompanion/lib/screens/splash\_screen.dart](/accompanion/accompanion/lib/screens/splash_screen.dart) | Dart | 1 | 3 | 4 | 8 |
| [accompanion/accompanion/pubspec.yaml](/accompanion/accompanion/pubspec.yaml) | YAML | 1 | 1 | 1 | 3 |
| [accompanion/accompanion/test/widget\_test.dart](/accompanion/accompanion/test/widget_test.dart) | Dart | -4 | 1 | 0 | -3 |
| [oneday/.augment/rules/rules.md](/oneday/.augment/rules/rules.md) | Markdown | 387 | 0 | 114 | 501 |
| [oneday/.cursorrules.md](/oneday/.cursorrules.md) | Markdown | 52 | 0 | 11 | 63 |
| [oneday/Action.html](/oneday/Action.html) | HTML | 693 | 23 | 53 | 769 |
| [oneday/CHECKPOINT\_BACKUP\_STATUS.md](/oneday/CHECKPOINT_BACKUP_STATUS.md) | Markdown | 116 | 0 | 32 | 148 |
| [oneday/CUSTOM\_LIBRARY\_EDITOR\_IMPROVEMENTS.md](/oneday/CUSTOM_LIBRARY_EDITOR_IMPROVEMENTS.md) | Markdown | 101 | 0 | 27 | 128 |
| [oneday/EXERCISE\_LIBRARY\_FIXES.md](/oneday/EXERCISE_LIBRARY_FIXES.md) | Markdown | 112 | 0 | 31 | 143 |
| [oneday/INTERNATIONALIZATION\_IMPLEMENTATION.md](/oneday/INTERNATIONALIZATION_IMPLEMENTATION.md) | Markdown | 126 | 0 | 37 | 163 |
| [oneday/OPTIMIZING\_CURSOR\_AI.md](/oneday/OPTIMIZING_CURSOR_AI.md) | Markdown | 182 | 0 | 58 | 240 |
| [oneday/PROBLEM\_SOLUTIONS.md](/oneday/PROBLEM_SOLUTIONS.md) | Markdown | 120 | 0 | 31 | 151 |
| [oneday/RADAR\_CHART\_FIX\_SUMMARY.md](/oneday/RADAR_CHART_FIX_SUMMARY.md) | Markdown | 83 | 0 | 23 | 106 |
| [oneday/RADAR\_CHART\_GRID\_FIX.md](/oneday/RADAR_CHART_GRID_FIX.md) | Markdown | 109 | 0 | 43 | 152 |
| [oneday/RADAR\_CHART\_PENTAGON\_FIX\_SUMMARY.md](/oneday/RADAR_CHART_PENTAGON_FIX_SUMMARY.md) | Markdown | 145 | 0 | 36 | 181 |
| [oneday/RADAR\_CHART\_TECHNICAL\_ANALYSIS.md](/oneday/RADAR_CHART_TECHNICAL_ANALYSIS.md) | Markdown | 103 | 0 | 29 | 132 |
| [oneday/ROI\_CHART\_Y\_AXIS\_FIX\_SUMMARY.md](/oneday/ROI_CHART_Y_AXIS_FIX_SUMMARY.md) | Markdown | 126 | 0 | 31 | 157 |
| [oneday/STUDY\_STATISTICS\_DEBUG\_REPORT.md](/oneday/STUDY_STATISTICS_DEBUG_REPORT.md) | Markdown | 109 | 0 | 32 | 141 |
| [oneday/STUDY\_STATISTICS\_UI\_REFRESH\_FIX.md](/oneday/STUDY_STATISTICS_UI_REFRESH_FIX.md) | Markdown | 127 | 0 | 42 | 169 |
| [oneday/VERSION\_DIFF\_REPORT.md](/oneday/VERSION_DIFF_REPORT.md) | Markdown | 136 | 0 | 41 | 177 |
| [oneday/analysis\_options.yaml](/oneday/analysis_options.yaml) | YAML | 1 | -1 | 0 | 0 |
| [oneday/assets/data/profanity\_words.json](/oneday/assets/data/profanity_words.json) | JSON | 259 | 0 | 1 | 260 |
| [oneday/devtools\_options.yaml](/oneday/devtools_options.yaml) | YAML | 3 | 0 | 1 | 4 |
| [oneday/docs/ACHIEVEMENT\_UNLOCK\_NAVIGATION\_FIX.md](/oneday/docs/ACHIEVEMENT_UNLOCK_NAVIGATION_FIX.md) | Markdown | 161 | 0 | 40 | 201 |
| [oneday/docs/BUBBLE\_OFFSET\_DEBUGGING.md](/oneday/docs/BUBBLE_OFFSET_DEBUGGING.md) | Markdown | 121 | 0 | 30 | 151 |
| [oneday/docs/BUBBLE\_POSITIONING\_FINAL\_FIX.md](/oneday/docs/BUBBLE_POSITIONING_FINAL_FIX.md) | Markdown | 117 | 0 | 32 | 149 |
| [oneday/docs/BUBBLE\_POSITIONING\_PRECISE\_FIX.md](/oneday/docs/BUBBLE_POSITIONING_PRECISE_FIX.md) | Markdown | 130 | 0 | 43 | 173 |
| [oneday/docs/BUBBLE\_POSITION\_ADJUSTMENT.md](/oneday/docs/BUBBLE_POSITION_ADJUSTMENT.md) | Markdown | 100 | 0 | 29 | 129 |
| [oneday/docs/CLICK\_POSITION\_FIX.md](/oneday/docs/CLICK_POSITION_FIX.md) | Markdown | 126 | 0 | 37 | 163 |
| [oneday/docs/COORDINATE\_SYSTEM\_FINAL\_FIX.md](/oneday/docs/COORDINATE_SYSTEM_FINAL_FIX.md) | Markdown | 111 | 0 | 38 | 149 |
| [oneday/docs/DEBUGGING\_STEP\_1\_TEST\_GUIDE.md](/oneday/docs/DEBUGGING_STEP_1_TEST_GUIDE.md) | Markdown | 115 | 0 | 27 | 142 |
| [oneday/docs/IMAGE\_COMPRESSION\_IMPROVEMENTS.md](/oneday/docs/IMAGE_COMPRESSION_IMPROVEMENTS.md) | Markdown | 103 | 0 | 38 | 141 |
| [oneday/docs/STANDARDIZED\_COORDINATE\_SYSTEM\_IMPLEMENTATION.md](/oneday/docs/STANDARDIZED_COORDINATE_SYSTEM_IMPLEMENTATION.md) | Markdown | 146 | 0 | 46 | 192 |
| [oneday/docs/USER\_IMPORTED\_PHOTOS\_FIX.md](/oneday/docs/USER_IMPORTED_PHOTOS_FIX.md) | Markdown | 146 | 0 | 44 | 190 |
| [oneday/docs/development/PROFANITY\_FILTER\_DEBUG\_GUIDE.md](/oneday/docs/development/PROFANITY_FILTER_DEBUG_GUIDE.md) | Markdown | 113 | 0 | 33 | 146 |
| [oneday/docs/development/PROFANITY\_FILTER\_SYSTEM\_DESIGN.md](/oneday/docs/development/PROFANITY_FILTER_SYSTEM_DESIGN.md) | Markdown | 206 | 0 | 53 | 259 |
| [oneday/docs/development/STUDY\_STATISTICS\_REALTIME\_UPDATE\_IMPLEMENTATION.md](/oneday/docs/development/STUDY_STATISTICS_REALTIME_UPDATE_IMPLEMENTATION.md) | Markdown | 145 | 0 | 43 | 188 |
| [oneday/docs/features/HELP\_FEEDBACK.md](/oneday/docs/features/HELP_FEEDBACK.md) | Markdown | 105 | 0 | 32 | 137 |
| [oneday/docs/features/PROFILE\_DATA\_SYNC.md](/oneday/docs/features/PROFILE_DATA_SYNC.md) | Markdown | 129 | 0 | 39 | 168 |
| [oneday/docs/fixes/ACTION\_LIBRARY\_SELECTOR\_OVERFLOW\_FIX.md](/oneday/docs/fixes/ACTION_LIBRARY_SELECTOR_OVERFLOW_FIX.md) | Markdown | 168 | 0 | 35 | 203 |
| [oneday/docs/fixes/CUSTOM\_ACTION\_EDIT\_SAVE\_FIX.md](/oneday/docs/fixes/CUSTOM_ACTION_EDIT_SAVE_FIX.md) | Markdown | 131 | 0 | 35 | 166 |
| [oneday/docs/troubleshooting/PROBLEM\_SOLUTIONS.md](/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md) | Markdown | 29 | 0 | 6 | 35 |
| [oneday/example/calendar\_task\_display\_demo.dart](/oneday/example/calendar_task_display_demo.dart) | Dart | 23 | 1 | 0 | 24 |
| [oneday/example/color\_scheme\_comparison\_demo.dart](/oneday/example/color_scheme_comparison_demo.dart) | Dart | 22 | 0 | 0 | 22 |
| [oneday/example/study\_session\_completion\_demo.dart](/oneday/example/study_session_completion_demo.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/example/timebox\_rest\_skip\_demo.dart](/oneday/example/timebox_rest_skip_demo.dart) | Dart | 9 | 0 | 0 | 9 |
| [oneday/ios/OneDayWidget/AppIntent.swift](/oneday/ios/OneDayWidget/AppIntent.swift) | Swift | 8 | 7 | 4 | 19 |
| [oneday/ios/OneDayWidget/OneDayWidgetBundle.swift](/oneday/ios/OneDayWidget/OneDayWidgetBundle.swift) | Swift | 8 | 6 | 3 | 17 |
| [oneday/ios/OneDayWidget/OneDayWidgetControl.swift](/oneday/ios/OneDayWidget/OneDayWidgetControl.swift) | Swift | 56 | 7 | 15 | 78 |
| [oneday/l10n.yaml](/oneday/l10n.yaml) | YAML | 4 | 0 | 1 | 5 |
| [oneday/lib/debug/profanity\_filter\_manual\_test\_page.dart](/oneday/lib/debug/profanity_filter_manual_test_page.dart) | Dart | 281 | 10 | 31 | 322 |
| [oneday/lib/debug/profanity\_filter\_test\_page.dart](/oneday/lib/debug/profanity_filter_test_page.dart) | Dart | 189 | 2 | 28 | 219 |
| [oneday/lib/debug/radar\_test\_page.dart](/oneday/lib/debug/radar_test_page.dart) | Dart | 69 | 2 | 7 | 78 |
| [oneday/lib/features/ability\_radar/models/ability\_radar\_models.dart](/oneday/lib/features/ability_radar/models/ability_radar_models.dart) | Dart | 53 | 5 | 9 | 67 |
| [oneday/lib/features/ability\_radar/pages/ability\_radar\_page.dart](/oneday/lib/features/ability_radar/pages/ability_radar_page.dart) | Dart | 187 | 11 | 17 | 215 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_mock\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart) | Dart | -33 | 0 | 0 | -33 |
| [oneday/lib/features/ability\_radar/providers/ability\_radar\_providers.dart](/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart) | Dart | 18 | 10 | 0 | 28 |
| [oneday/lib/features/ability\_radar/services/ability\_radar\_service.dart](/oneday/lib/features/ability_radar/services/ability_radar_service.dart) | Dart | -6 | 0 | 0 | -6 |
| [oneday/lib/features/ability\_radar/services/radar\_share\_service.dart](/oneday/lib/features/ability_radar/services/radar_share_service.dart) | Dart | 274 | 54 | 57 | 385 |
| [oneday/lib/features/ability\_radar/widgets/ability\_radar\_chart.dart](/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart) | Dart | 53 | 13 | 4 | 70 |
| [oneday/lib/features/ability\_radar/widgets/game\_style\_rank\_widget.dart](/oneday/lib/features/ability_radar/widgets/game_style_rank_widget.dart) | Dart | 242 | 20 | 31 | 293 |
| [oneday/lib/features/ability\_radar/widgets/radar\_share\_panel.dart](/oneday/lib/features/ability_radar/widgets/radar_share_panel.dart) | Dart | 257 | 24 | 25 | 306 |
| [oneday/lib/features/achievement/models/user\_level.dart](/oneday/lib/features/achievement/models/user_level.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/achievement/widgets/achievement\_unlock\_notification.dart](/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart) | Dart | 30 | 1 | 2 | 33 |
| [oneday/lib/features/calendar/calendar\_page.dart](/oneday/lib/features/calendar/calendar_page.dart) | Dart | -70 | -3 | -3 | -76 |
| [oneday/lib/features/community/article\_import\_service.dart](/oneday/lib/features/community/article_import_service.dart) | Dart | 53 | 3 | 3 | 59 |
| [oneday/lib/features/community/community\_feed\_page.dart](/oneday/lib/features/community/community_feed_page.dart) | Dart | 116 | 15 | 7 | 138 |
| [oneday/lib/features/community/community\_post\_editor\_page.dart](/oneday/lib/features/community/community_post_editor_page.dart) | Dart | 226 | 14 | 20 | 260 |
| [oneday/lib/features/community/community\_storage\_service.dart](/oneday/lib/features/community/community_storage_service.dart) | Dart | 121 | 11 | 20 | 152 |
| [oneday/lib/features/community/content\_report\_dialog.dart](/oneday/lib/features/community/content_report_dialog.dart) | Dart | 357 | 7 | 18 | 382 |
| [oneday/lib/features/community/content\_report\_service.dart](/oneday/lib/features/community/content_report_service.dart) | Dart | 347 | 33 | 57 | 437 |
| [oneday/lib/features/community/profanity\_filter\_service.dart](/oneday/lib/features/community/profanity_filter_service.dart) | Dart | 368 | 34 | 60 | 462 |
| [oneday/lib/features/community/profanity\_filter\_settings\_page.dart](/oneday/lib/features/community/profanity_filter_settings_page.dart) | Dart | 722 | 16 | 30 | 768 |
| [oneday/lib/features/exercise/action\_library\_category\_manager.dart](/oneday/lib/features/exercise/action_library_category_manager.dart) | Dart | 304 | 24 | 33 | 361 |
| [oneday/lib/features/exercise/create\_action\_library\_dialog.dart](/oneday/lib/features/exercise/create_action_library_dialog.dart) | Dart | 345 | 7 | 20 | 372 |
| [oneday/lib/features/exercise/create\_custom\_library\_dialog.dart](/oneday/lib/features/exercise/create_custom_library_dialog.dart) | Dart | 21 | 0 | 0 | 21 |
| [oneday/lib/features/exercise/custom\_action\_editor\_dialog.dart](/oneday/lib/features/exercise/custom_action_editor_dialog.dart) | Dart | 35 | 2 | 2 | 39 |
| [oneday/lib/features/exercise/custom\_action\_library.dart](/oneday/lib/features/exercise/custom_action_library.dart) | Dart | 4 | 0 | 0 | 4 |
| [oneday/lib/features/exercise/custom\_action\_library\_service.dart](/oneday/lib/features/exercise/custom_action_library_service.dart) | Dart | 31 | 4 | 3 | 38 |
| [oneday/lib/features/exercise/custom\_library\_editor\_page.dart](/oneday/lib/features/exercise/custom_library_editor_page.dart) | Dart | 282 | 24 | 24 | 330 |
| [oneday/lib/features/exercise/exercise\_library\_page.dart](/oneday/lib/features/exercise/exercise_library_page.dart) | Dart | 1,135 | 59 | 46 | 1,240 |
| [oneday/lib/features/exercise/exercise\_session\_page.dart](/oneday/lib/features/exercise/exercise_session_page.dart) | Dart | 13 | -1 | -4 | 8 |
| [oneday/lib/features/exercise/focused\_training\_page.dart](/oneday/lib/features/exercise/focused_training_page.dart) | Dart | 26 | 0 | 0 | 26 |
| [oneday/lib/features/exercise/manage\_custom\_libraries\_dialog.dart](/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart) | Dart | 947 | 45 | 45 | 1,037 |
| [oneday/lib/features/exercise/pao\_integration\_service.dart](/oneday/lib/features/exercise/pao_integration_service.dart) | Dart | 18 | 0 | -1 | 17 |
| [oneday/lib/features/help\_feedback/help\_feedback\_page.dart](/oneday/lib/features/help_feedback/help_feedback_page.dart) | Dart | 666 | 39 | 50 | 755 |
| [oneday/lib/features/home/<USER>/oneday/lib/features/home/<USER>
| [oneday/lib/features/learning\_report/learning\_report\_page.dart](/oneday/lib/features/learning_report/learning_report_page.dart) | Dart | 8 | 0 | 0 | 8 |
| [oneday/lib/features/learning\_report/widgets/chart\_widgets.dart](/oneday/lib/features/learning_report/widgets/chart_widgets.dart) | Dart | 94 | 22 | 18 | 134 |
| [oneday/lib/features/main/main\_container\_page.dart](/oneday/lib/features/main/main_container_page.dart) | Dart | -6 | 0 | 2 | -4 |
| [oneday/lib/features/memory\_palace/palace\_manager\_page.dart](/oneday/lib/features/memory_palace/palace_manager_page.dart) | Dart | -47 | -6 | -23 | -76 |
| [oneday/lib/features/memory\_palace/providers/memory\_palace\_provider.dart](/oneday/lib/features/memory_palace/providers/memory_palace_provider.dart) | Dart | 191 | 18 | 33 | 242 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page.dart](/oneday/lib/features/memory_palace/scene_detail_page.dart) | Dart | -120 | -49 | -87 | -256 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_copy.dart](/oneday/lib/features/memory_palace/scene_detail_page_copy.dart) | Dart | 1,596 | 167 | 199 | 1,962 |
| [oneday/lib/features/memory\_palace/scene\_detail\_page\_副本.dart](/oneday/lib/features/memory_palace/scene_detail_page_%E5%89%AF%E6%9C%AC.dart) | Dart | -1,672 | -186 | -224 | -2,082 |
| [oneday/lib/features/memory\_palace/utils/anchor\_data\_migration.dart](/oneday/lib/features/memory_palace/utils/anchor_data_migration.dart) | Dart | 215 | 30 | 51 | 296 |
| [oneday/lib/features/memory\_palace/utils/image\_coordinate\_system.dart](/oneday/lib/features/memory_palace/utils/image_coordinate_system.dart) | Dart | 187 | 29 | 39 | 255 |
| [oneday/lib/features/onboarding/notion\_style\_onboarding\_page.dart](/oneday/lib/features/onboarding/notion_style_onboarding_page.dart) | Dart | 5 | 0 | -4 | 1 |
| [oneday/lib/features/onboarding/onboarding\_page.dart](/oneday/lib/features/onboarding/onboarding_page.dart) | Dart | -15 | 0 | 1 | -14 |
| [oneday/lib/features/photo\_album/photo\_album\_creator\_page.dart](/oneday/lib/features/photo_album/photo_album_creator_page.dart) | Dart | -16 | -1 | -3 | -20 |
| [oneday/lib/features/profile/profile\_page.dart](/oneday/lib/features/profile/profile_page.dart) | Dart | 84 | 10 | 8 | 102 |
| [oneday/lib/features/reflection/reflection\_log\_page.dart](/oneday/lib/features/reflection/reflection_log_page.dart) | Dart | 3 | 0 | 1 | 4 |
| [oneday/lib/features/settings/settings\_page.dart](/oneday/lib/features/settings/settings_page.dart) | Dart | 4 | 7 | 6 | 17 |
| [oneday/lib/features/splash/splash\_page.dart](/oneday/lib/features/splash/splash_page.dart) | Dart | -9 | 0 | 1 | -8 |
| [oneday/lib/features/study\_time/providers/study\_time\_providers.dart](/oneday/lib/features/study_time/providers/study_time_providers.dart) | Dart | 69 | 0 | 5 | 74 |
| [oneday/lib/features/study\_time/services/study\_time\_statistics\_service.dart](/oneday/lib/features/study_time/services/study_time_statistics_service.dart) | Dart | 13 | 1 | 1 | 15 |
| [oneday/lib/features/study\_time/test\_data\_sync\_page.dart](/oneday/lib/features/study_time/test_data_sync_page.dart) | Dart | 2 | 0 | 0 | 2 |
| [oneday/lib/features/time\_box/managers/task\_category\_manager.dart](/oneday/lib/features/time_box/managers/task_category_manager.dart) | Dart | 311 | 47 | 54 | 412 |
| [oneday/lib/features/time\_box/models/timebox\_models.dart](/oneday/lib/features/time_box/models/timebox_models.dart) | Dart | 82 | 15 | 16 | 113 |
| [oneday/lib/features/time\_box/pages/task\_category\_management\_page.dart](/oneday/lib/features/time_box/pages/task_category_management_page.dart) | Dart | 487 | 14 | 35 | 536 |
| [oneday/lib/features/time\_box/providers/timebox\_provider.dart](/oneday/lib/features/time_box/providers/timebox_provider.dart) | Dart | 6 | 0 | 0 | 6 |
| [oneday/lib/features/time\_box/timebox\_list\_page.dart](/oneday/lib/features/time_box/timebox_list_page.dart) | Dart | 151 | 28 | -16 | 163 |
| [oneday/lib/features/vocabulary/fsrs\_algorithm.dart](/oneday/lib/features/vocabulary/fsrs_algorithm.dart) | Dart | 2 | 3 | 0 | 5 |
| [oneday/lib/features/vocabulary/fsrs\_service.dart](/oneday/lib/features/vocabulary/fsrs_service.dart) | Dart | -43 | 54 | -8 | 3 |
| [oneday/lib/features/vocabulary/graduate\_vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart) | Dart | 1 | 7 | -1 | 7 |
| [oneday/lib/features/vocabulary/vocabulary\_cache\_manager.dart](/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart) | Dart | 15 | 0 | 0 | 15 |
| [oneday/lib/features/vocabulary/vocabulary\_category\_page.dart](/oneday/lib/features/vocabulary/vocabulary_category_page.dart) | Dart | 42 | 0 | -1 | 41 |
| [oneday/lib/features/vocabulary/vocabulary\_manager\_page.dart](/oneday/lib/features/vocabulary/vocabulary_manager_page.dart) | Dart | -4 | 0 | 0 | -4 |
| [oneday/lib/features/vocabulary/vocabulary\_page.dart](/oneday/lib/features/vocabulary/vocabulary_page.dart) | Dart | 26 | 0 | 1 | 27 |
| [oneday/lib/features/vocabulary/vocabulary\_service.dart](/oneday/lib/features/vocabulary/vocabulary_service.dart) | Dart | 60 | 5 | -2 | 63 |
| [oneday/lib/features/vocabulary/word\_meaning\_service.dart](/oneday/lib/features/vocabulary/word_meaning_service.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/lib/features/vocabulary/word\_root\_service.dart](/oneday/lib/features/vocabulary/word_root_service.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/lib/features/wage\_system/store\_page.dart](/oneday/lib/features/wage_system/store_page.dart) | Dart | -22 | 0 | 0 | -22 |
| [oneday/lib/features/wage\_system/wage\_wallet\_page.dart](/oneday/lib/features/wage_system/wage_wallet_page.dart) | Dart | 11 | 0 | 1 | 12 |
| [oneday/lib/features/widget/models/widget\_models.dart](/oneday/lib/features/widget/models/widget_models.dart) | Dart | 152 | 15 | 22 | 189 |
| [oneday/lib/features/widget/services/memory\_palace\_integration\_service.dart](/oneday/lib/features/widget/services/memory_palace_integration_service.dart) | Dart | 350 | 38 | 47 | 435 |
| [oneday/lib/features/widget/services/widget\_service.dart](/oneday/lib/features/widget/services/widget_service.dart) | Dart | 126 | 88 | 32 | 246 |
| [oneday/lib/features/widget/utils/error\_handler.dart](/oneday/lib/features/widget/utils/error_handler.dart) | Dart | 360 | 15 | 24 | 399 |
| [oneday/lib/l10n/app\_localizations.dart](/oneday/lib/l10n/app_localizations.dart) | Dart | 129 | 360 | 90 | 579 |
| [oneday/lib/l10n/app\_localizations\_en.dart](/oneday/lib/l10n/app_localizations_en.dart) | Dart | 175 | 3 | 77 | 255 |
| [oneday/lib/l10n/app\_localizations\_zh.dart](/oneday/lib/l10n/app_localizations_zh.dart) | Dart | 165 | 3 | 77 | 245 |
| [oneday/lib/main.dart](/oneday/lib/main.dart) | Dart | 2 | 2 | 2 | 6 |
| [oneday/lib/router/app\_router.dart](/oneday/lib/router/app_router.dart) | Dart | 14 | 2 | 2 | 18 |
| [oneday/lib/services/app\_initialization\_service.dart](/oneday/lib/services/app_initialization_service.dart) | Dart | -2 | 1 | 0 | -1 |
| [oneday/lib/services/floating\_timer\_service.dart](/oneday/lib/services/floating_timer_service.dart) | Dart | 15 | 0 | 0 | 15 |
| [oneday/lib/services/global\_timer\_service.dart](/oneday/lib/services/global_timer_service.dart) | Dart | 2 | 1 | 0 | 3 |
| [oneday/lib/services/locale\_service.dart](/oneday/lib/services/locale_service.dart) | Dart | 84 | 23 | 17 | 124 |
| [oneday/lib/services/system\_overlay\_permission.dart](/oneday/lib/services/system_overlay_permission.dart) | Dart | 8 | 0 | 0 | 8 |
| [oneday/lib/shared/config/image\_compression\_config.dart](/oneday/lib/shared/config/image_compression_config.dart) | Dart | -2 | 0 | -1 | -3 |
| [oneday/lib/shared/utils/image\_compression\_utils.dart](/oneday/lib/shared/utils/image_compression_utils.dart) | Dart | 26 | 6 | 3 | 35 |
| [oneday/lib/shared/utils/image\_watermark\_utils.dart](/oneday/lib/shared/utils/image_watermark_utils.dart) | Dart | 39 | 0 | -3 | 36 |
| [oneday/lib/shared/utils/simple\_image\_test.dart](/oneday/lib/shared/utils/simple_image_test.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/lib/utils/category\_cleanup\_helper.dart](/oneday/lib/utils/category_cleanup_helper.dart) | Dart | 96 | 10 | 20 | 126 |
| [oneday/pubspec.yaml](/oneday/pubspec.yaml) | YAML | 7 | 3 | 3 | 13 |
| [oneday/scripts/clean\_category\_data.dart](/oneday/scripts/clean_category_data.dart) | Dart | 35 | 8 | 6 | 49 |
| [oneday/scripts/verify\_navigation\_fix.dart](/oneday/scripts/verify_navigation_fix.dart) | Dart | -1 | 0 | -1 | -2 |
| [oneday/scripts/verify\_radar\_fix.dart](/oneday/scripts/verify_radar_fix.dart) | Dart | 111 | 8 | 24 | 143 |
| [oneday/test/achievement\_unlock\_navigation\_test.dart](/oneday/test/achievement_unlock_navigation_test.dart) | Dart | 79 | 12 | 13 | 104 |
| [oneday/test/action\_library\_category\_test.dart](/oneday/test/action_library_category_test.dart) | Dart | 36 | 2 | 9 | 47 |
| [oneday/test/bubble\_positioning\_analysis.dart](/oneday/test/bubble_positioning_analysis.dart) | Dart | 114 | 9 | 26 | 149 |
| [oneday/test/calendar\_task\_display\_test.dart](/oneday/test/calendar_task_display_test.dart) | Dart | 9 | 6 | 0 | 15 |
| [oneday/test/category\_edit\_fix\_test.dart](/oneday/test/category_edit_fix_test.dart) | Dart | 3 | 0 | 0 | 3 |
| [oneday/test/category\_edit\_integration\_test.dart](/oneday/test/category_edit_integration_test.dart) | Dart | 108 | 29 | 41 | 178 |
| [oneday/test/category\_edit\_state\_test.dart](/oneday/test/category_edit_state_test.dart) | Dart | 4 | 0 | 0 | 4 |
| [oneday/test/category\_popup\_menu\_test.dart](/oneday/test/category_popup_menu_test.dart) | Dart | 5 | 0 | 0 | 5 |
| [oneday/test/category\_removal\_test.dart](/oneday/test/category_removal_test.dart) | Dart | 151 | 27 | 32 | 210 |
| [oneday/test/category\_sidebar\_edit\_test.dart](/oneday/test/category_sidebar_edit_test.dart) | Dart | 6 | 0 | -1 | 5 |
| [oneday/test/category\_sidebar\_test.dart](/oneday/test/category_sidebar_test.dart) | Dart | 118 | 20 | 34 | 172 |
| [oneday/test/community\_storage\_test.dart](/oneday/test/community_storage_test.dart) | Dart | 104 | 13 | 17 | 134 |
| [oneday/test/context\_aware\_album\_creation\_test.dart](/oneday/test/context_aware_album_creation_test.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/test/coordinate\_system\_logic\_test.dart](/oneday/test/coordinate_system_logic_test.dart) | Dart | 174 | 32 | 46 | 252 |
| [oneday/test/custom\_action\_display\_test.dart](/oneday/test/custom_action_display_test.dart) | Dart | 78 | 9 | 10 | 97 |
| [oneday/test/custom\_action\_editor\_test.dart](/oneday/test/custom_action_editor_test.dart) | Dart | 66 | 14 | 14 | 94 |
| [oneday/test/custom\_library\_navigation\_test.dart](/oneday/test/custom_library_navigation_test.dart) | Dart | 119 | 22 | 27 | 168 |
| [oneday/test/custom\_library\_navigation\_unit\_test.dart](/oneday/test/custom_library_navigation_unit_test.dart) | Dart | 50 | 15 | 16 | 81 |
| [oneday/test/default\_category\_edit\_test.dart](/oneday/test/default_category_edit_test.dart) | Dart | 132 | 31 | 43 | 206 |
| [oneday/test/exercise\_library\_sidebar\_test.dart](/oneday/test/exercise_library_sidebar_test.dart) | Dart | 76 | 15 | 23 | 114 |
| [oneday/test/features/exercise/category\_integration\_test.dart](/oneday/test/features/exercise/category_integration_test.dart) | Dart | 128 | 22 | 27 | 177 |
| [oneday/test/features/exercise/custom\_action\_library\_test.dart](/oneday/test/features/exercise/custom_action_library_test.dart) | Dart | 155 | 32 | 43 | 230 |
| [oneday/test/features/exercise/custom\_action\_library\_ui\_test.dart](/oneday/test/features/exercise/custom_action_library_ui_test.dart) | Dart | -6 | 0 | 0 | -6 |
| [oneday/test/features/exercise/custom\_category\_test.dart](/oneday/test/features/exercise/custom_category_test.dart) | Dart | 113 | 21 | 24 | 158 |
| [oneday/test/features/help\_feedback/help\_feedback\_page\_test.dart](/oneday/test/features/help_feedback/help_feedback_page_test.dart) | Dart | 88 | 17 | 33 | 138 |
| [oneday/test/features/profile/profile\_data\_sync\_test.dart](/oneday/test/features/profile/profile_data_sync_test.dart) | Dart | 165 | 21 | 38 | 224 |
| [oneday/test/features/time\_box/action\_library\_selector\_overflow\_test.dart](/oneday/test/features/time_box/action_library_selector_overflow_test.dart) | Dart | 301 | 30 | 49 | 380 |
| [oneday/test/features/ui/color\_consistency\_test.dart](/oneday/test/features/ui/color_consistency_test.dart) | Dart | 14 | 0 | 0 | 14 |
| [oneday/test/features/ui/dropdown\_pure\_white\_test.dart](/oneday/test/features/ui/dropdown_pure_white_test.dart) | Dart | -1 | 1 | 0 | 0 |
| [oneday/test/features/ui/material3\_surface\_tinting\_fix\_test.dart](/oneday/test/features/ui/material3_surface_tinting_fix_test.dart) | Dart | 8 | 0 | 0 | 8 |
| [oneday/test/final\_category\_edit\_test.dart](/oneday/test/final_category_edit_test.dart) | Dart | 106 | 36 | 43 | 185 |
| [oneday/test/graduate\_vocabulary\_manager\_test.dart](/oneday/test/graduate_vocabulary_manager_test.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/test/image\_compression\_quality\_test.dart](/oneday/test/image_compression_quality_test.dart) | Dart | 98 | 14 | 25 | 137 |
| [oneday/test/integration/study\_session\_completion\_integration\_test.dart](/oneday/test/integration/study_session_completion_integration_test.dart) | Dart | -5 | 1 | -1 | -5 |
| [oneday/test/integration\_test.dart](/oneday/test/integration_test.dart) | Dart | 89 | 27 | 39 | 155 |
| [oneday/test/manage\_custom\_libraries\_dialog\_test.dart](/oneday/test/manage_custom_libraries_dialog_test.dart) | Dart | 65 | 9 | 18 | 92 |
| [oneday/test/memory\_palace\_card\_test.dart](/oneday/test/memory_palace_card_test.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/test/photo\_album\_dialog\_layout\_test.dart](/oneday/test/photo_album_dialog_layout_test.dart) | Dart | -26 | -1 | -1 | -28 |
| [oneday/test/profanity\_filter\_integration\_test.dart](/oneday/test/profanity_filter_integration_test.dart) | Dart | 94 | 11 | 25 | 130 |
| [oneday/test/services/study\_session\_completion\_service\_test.dart](/oneday/test/services/study_session_completion_service_test.dart) | Dart | 17 | 0 | 0 | 17 |
| [oneday/test/services/study\_session\_completion\_service\_test.mocks.dart](/oneday/test/services/study_session_completion_service_test.mocks.dart) | Dart | 394 | 26 | 58 | 478 |
| [oneday/test/simple\_profanity\_test.dart](/oneday/test/simple_profanity_test.dart) | Dart | 47 | 1 | 16 | 64 |
| [oneday/test/standardized\_coordinate\_system\_test.dart](/oneday/test/standardized_coordinate_system_test.dart) | Dart | 269 | 28 | 50 | 347 |
| [oneday/test/task\_category\_management\_page\_test.dart](/oneday/test/task_category_management_page_test.dart) | Dart | 120 | 25 | 39 | 184 |
| [oneday/test/task\_category\_test.dart](/oneday/test/task_category_test.dart) | Dart | 201 | 17 | 48 | 266 |
| [oneday/test/task\_sync\_test.dart](/oneday/test/task_sync_test.dart) | Dart | 164 | 27 | 42 | 233 |
| [oneday/test/test\_bubble\_alignment\_adjustment.dart](/oneday/test/test_bubble_alignment_adjustment.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/test/test\_bubble\_alignment\_verification.dart](/oneday/test/test_bubble_alignment_verification.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/test/test\_bubble\_center\_alignment.dart](/oneday/test/test_bubble_center_alignment.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/test/test\_coordinate\_transformation\_fix.dart](/oneday/test/test_coordinate_transformation_fix.dart) | Dart | 1 | 0 | 0 | 1 |
| [oneday/test/timebox\_rest\_skip\_test.dart](/oneday/test/timebox_rest_skip_test.dart) | Dart | -9 | 0 | 0 | -9 |
| [oneday/test/vocabulary\_test.dart](/oneday/test/vocabulary_test.dart) | Dart | -5 | 0 | -1 | -6 |
| [oneday/test/word\_meaning\_service\_test.dart](/oneday/test/word_meaning_service_test.dart) | Dart | -1 | 0 | 0 | -1 |
| [oneday/test\_apps/category\_cleanup\_demo.dart](/oneday/test_apps/category_cleanup_demo.dart) | Dart | 253 | 8 | 26 | 287 |
| [oneday/test\_apps/category\_edit\_demo.dart](/oneday/test_apps/category_edit_demo.dart) | Dart | 276 | 7 | 24 | 307 |
| [oneday/test\_apps/category\_management\_demo.dart](/oneday/test_apps/category_management_demo.dart) | Dart | 100 | 1 | 7 | 108 |
| [oneday/test\_apps/category\_removal\_demo.dart](/oneday/test_apps/category_removal_demo.dart) | Dart | 296 | 8 | 26 | 330 |
| [oneday/test\_apps/simple\_cleanup\_test.dart](/oneday/test_apps/simple_cleanup_test.dart) | Dart | 211 | 4 | 23 | 238 |
| [oneday/test\_apps/simple\_dialog\_test.dart](/oneday/test_apps/simple_dialog_test.dart) | Dart | 215 | 3 | 17 | 235 |
| [oneday/test\_apps/task\_count\_verification.dart](/oneday/test_apps/task_count_verification.dart) | Dart | 330 | 13 | 28 | 371 |
| [oneday/test\_apps/task\_sync\_demo.dart](/oneday/test_apps/task_sync_demo.dart) | Dart | 256 | 4 | 27 | 287 |
| [oneday/test\_apps/task\_sync\_verification.dart](/oneday/test_apps/task_sync_verification.dart) | Dart | 320 | 9 | 28 | 357 |
| [oneday/test\_category\_fix.dart](/oneday/test_category_fix.dart) | Dart | 83 | 11 | 22 | 116 |
| [oneday/test\_drag\_fix.dart](/oneday/test_drag_fix.dart) | Dart | 39 | 5 | 10 | 54 |
| [oneday/test\_profanity\_fix.md](/oneday/test_profanity_fix.md) | Markdown | 89 | 0 | 19 | 108 |
| [oneday/动作.html](/oneday/%E5%8A%A8%E4%BD%9C.html) | HTML | 693 | 23 | 53 | 769 |
| [tempPrompts.md](/tempPrompts.md) | Markdown | 186 | 0 | 77 | 263 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details