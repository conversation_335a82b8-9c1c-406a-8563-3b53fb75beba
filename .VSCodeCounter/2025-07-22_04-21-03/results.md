# Summary

Date : 2025-07-22 04:21:03

Directory /Users/<USER>/development/flutter_apps

Total : 527 files,  204573 codes, 8843 comments, 18360 blanks, all 231776 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 257 | 88,268 | 8,116 | 10,853 | 107,237 |
| JSON | 13 | 85,610 | 0 | 10 | 85,620 |
| Markdown | 145 | 23,354 | 2 | 6,538 | 29,894 |
| XML | 27 | 2,603 | 108 | 26 | 2,737 |
| HTML | 6 | 2,117 | 99 | 170 | 2,386 |
| C++ | 32 | 1,100 | 262 | 378 | 1,740 |
| CMake | 16 | 928 | 0 | 184 | 1,112 |
| Swift | 15 | 190 | 34 | 64 | 288 |
| Python | 1 | 152 | 26 | 32 | 210 |
| Ruby | 4 | 126 | 8 | 40 | 174 |
| YAML | 7 | 109 | 188 | 61 | 358 |
| Java Properties | 4 | 16 | 0 | 4 | 20 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 527 | 204,573 | 8,843 | 18,360 | 231,776 |
| . (Files) | 4 | 689 | 0 | 305 | 994 |
| .augment | 2 | 816 | 0 | 245 | 1,061 |
| .augment/rules | 2 | 816 | 0 | 245 | 1,061 |
| accompanion | 74 | 10,311 | 711 | 1,130 | 12,152 |
| accompanion/.idea | 7 | 1,656 | 0 | 0 | 1,656 |
| accompanion/.idea (Files) | 4 | 42 | 0 | 0 | 42 |
| accompanion/.idea/caches | 1 | 835 | 0 | 0 | 835 |
| accompanion/.idea/libraries | 2 | 779 | 0 | 0 | 779 |
| accompanion/accompanion | 67 | 8,655 | 711 | 1,130 | 10,496 |
| accompanion/accompanion (Files) | 3 | 308 | 86 | 115 | 509 |
| accompanion/accompanion/.augment | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/.augment/rules | 2 | 816 | 0 | 245 | 1,061 |
| accompanion/accompanion/android | 9 | 74 | 51 | 11 | 136 |
| accompanion/accompanion/android (Files) | 1 | 3 | 0 | 1 | 4 |
| accompanion/accompanion/android/app | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src | 7 | 66 | 51 | 9 | 126 |
| accompanion/accompanion/android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/app/src/main | 5 | 60 | 43 | 7 | 110 |
| accompanion/accompanion/android/app/src/main (Files) | 1 | 34 | 11 | 1 | 46 |
| accompanion/accompanion/android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| accompanion/accompanion/android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| accompanion/accompanion/android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| accompanion/accompanion/android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| accompanion/accompanion/android/gradle | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| accompanion/accompanion/ios | 9 | 260 | 7 | 23 | 290 |
| accompanion/accompanion/ios (Files) | 1 | 31 | 3 | 10 | 44 |
| accompanion/accompanion/ios/Runner | 7 | 222 | 2 | 9 | 233 |
| accompanion/accompanion/ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| accompanion/accompanion/ios/Runner/Assets.xcassets | 3 | 148 | 0 | 4 | 152 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 122 | 0 | 1 | 123 |
| accompanion/accompanion/ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| accompanion/accompanion/ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| accompanion/accompanion/ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/lib | 11 | 5,654 | 404 | 416 | 6,474 |
| accompanion/accompanion/lib (Files) | 1 | 106 | 6 | 10 | 122 |
| accompanion/accompanion/lib/screens | 10 | 5,548 | 398 | 406 | 6,352 |
| accompanion/accompanion/linux | 9 | 325 | 37 | 92 | 454 |
| accompanion/accompanion/linux (Files) | 1 | 104 | 0 | 25 | 129 |
| accompanion/accompanion/linux/flutter | 4 | 105 | 9 | 27 | 141 |
| accompanion/accompanion/linux/runner | 4 | 116 | 28 | 40 | 184 |
| accompanion/accompanion/macos | 7 | 479 | 6 | 27 | 512 |
| accompanion/accompanion/macos (Files) | 1 | 32 | 1 | 10 | 43 |
| accompanion/accompanion/macos/Flutter | 1 | 6 | 3 | 4 | 13 |
| accompanion/accompanion/macos/Runner | 4 | 434 | 0 | 9 | 443 |
| accompanion/accompanion/macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| accompanion/accompanion/macos/Runner/Assets.xcassets | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 1 | 69 |
| accompanion/accompanion/macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| accompanion/accompanion/macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| accompanion/accompanion/test | 1 | 10 | 11 | 7 | 28 |
| accompanion/accompanion/web | 2 | 54 | 15 | 6 | 75 |
| accompanion/accompanion/windows | 14 | 675 | 94 | 188 | 957 |
| accompanion/accompanion/windows (Files) | 1 | 89 | 0 | 20 | 109 |
| accompanion/accompanion/windows/flutter | 4 | 124 | 9 | 29 | 162 |
| accompanion/accompanion/windows/runner | 9 | 462 | 85 | 139 | 686 |
| oneday | 447 | 192,757 | 8,132 | 16,680 | 217,569 |
| oneday (Files) | 41 | 7,168 | 164 | 1,678 | 9,010 |
| oneday/.augment | 1 | 387 | 0 | 114 | 501 |
| oneday/.augment/rules | 1 | 387 | 0 | 114 | 501 |
| oneday/android | 9 | 81 | 53 | 13 | 147 |
| oneday/android (Files) | 1 | 3 | 0 | 1 | 4 |
| oneday/android/app | 7 | 73 | 53 | 11 | 137 |
| oneday/android/app/src | 7 | 73 | 53 | 11 | 137 |
| oneday/android/app/src/debug | 1 | 3 | 4 | 1 | 8 |
| oneday/android/app/src/main | 5 | 67 | 45 | 9 | 121 |
| oneday/android/app/src/main (Files) | 1 | 41 | 13 | 3 | 57 |
| oneday/android/app/src/main/res | 4 | 26 | 32 | 6 | 64 |
| oneday/android/app/src/main/res/drawable | 1 | 4 | 7 | 2 | 13 |
| oneday/android/app/src/main/res/drawable-v21 | 1 | 4 | 7 | 2 | 13 |
| oneday/android/app/src/main/res/values | 1 | 9 | 9 | 1 | 19 |
| oneday/android/app/src/main/res/values-night | 1 | 9 | 9 | 1 | 19 |
| oneday/android/app/src/profile | 1 | 3 | 4 | 1 | 8 |
| oneday/android/gradle | 1 | 5 | 0 | 1 | 6 |
| oneday/android/gradle/wrapper | 1 | 5 | 0 | 1 | 6 |
| oneday/assets | 8 | 85,846 | 23 | 71 | 85,940 |
| oneday/assets/data | 7 | 85,813 | 23 | 58 | 85,894 |
| oneday/assets/icons | 1 | 33 | 0 | 13 | 46 |
| oneday/docs | 99 | 14,616 | 2 | 3,977 | 18,595 |
| oneday/docs (Files) | 17 | 2,036 | 0 | 613 | 2,649 |
| oneday/docs/core | 4 | 1,352 | 0 | 308 | 1,660 |
| oneday/docs/development | 18 | 2,349 | 2 | 635 | 2,986 |
| oneday/docs/features | 11 | 1,756 | 0 | 491 | 2,247 |
| oneday/docs/fixes | 3 | 390 | 0 | 113 | 503 |
| oneday/docs/guides | 6 | 766 | 0 | 207 | 973 |
| oneday/docs/releases | 1 | 107 | 0 | 30 | 137 |
| oneday/docs/testing | 8 | 1,011 | 0 | 300 | 1,311 |
| oneday/docs/troubleshooting | 27 | 4,313 | 0 | 1,145 | 5,458 |
| oneday/docs/ui_improvements | 4 | 536 | 0 | 135 | 671 |
| oneday/example | 7 | 2,985 | 105 | 185 | 3,275 |
| oneday/ios | 12 | 326 | 27 | 45 | 398 |
| oneday/ios (Files) | 1 | 31 | 3 | 10 | 44 |
| oneday/ios/OneDayWidget | 3 | 72 | 20 | 22 | 114 |
| oneday/ios/Runner | 7 | 216 | 2 | 9 | 227 |
| oneday/ios/Runner (Files) | 2 | 13 | 0 | 3 | 16 |
| oneday/ios/Runner/Assets.xcassets | 3 | 142 | 0 | 4 | 146 |
| oneday/ios/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 116 | 0 | 1 | 117 |
| oneday/ios/Runner/Assets.xcassets/LaunchImage.imageset | 2 | 26 | 0 | 3 | 29 |
| oneday/ios/Runner/Base.lproj | 2 | 61 | 2 | 2 | 65 |
| oneday/ios/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| oneday/lib | 135 | 64,731 | 5,890 | 7,282 | 77,903 |
| oneday/lib (Files) | 1 | 254 | 25 | 13 | 292 |
| oneday/lib/core | 2 | 352 | 33 | 28 | 413 |
| oneday/lib/core/constants | 1 | 35 | 5 | 3 | 43 |
| oneday/lib/core/data | 1 | 317 | 28 | 25 | 370 |
| oneday/lib/debug | 4 | 807 | 24 | 81 | 912 |
| oneday/lib/features | 107 | 59,780 | 4,906 | 6,304 | 70,990 |
| oneday/lib/features/ability_radar | 10 | 3,067 | 338 | 450 | 3,855 |
| oneday/lib/features/ability_radar/models | 2 | 421 | 76 | 83 | 580 |
| oneday/lib/features/ability_radar/pages | 1 | 635 | 52 | 58 | 745 |
| oneday/lib/features/ability_radar/providers | 2 | 440 | 36 | 63 | 539 |
| oneday/lib/features/ability_radar/services | 2 | 735 | 99 | 163 | 997 |
| oneday/lib/features/ability_radar/widgets | 3 | 836 | 75 | 83 | 994 |
| oneday/lib/features/achievement | 17 | 4,480 | 456 | 628 | 5,564 |
| oneday/lib/features/achievement (Files) | 1 | 132 | 0 | 41 | 173 |
| oneday/lib/features/achievement/data | 1 | 444 | 18 | 12 | 474 |
| oneday/lib/features/achievement/models | 6 | 1,065 | 205 | 195 | 1,465 |
| oneday/lib/features/achievement/pages | 2 | 821 | 34 | 59 | 914 |
| oneday/lib/features/achievement/providers | 1 | 224 | 38 | 44 | 306 |
| oneday/lib/features/achievement/services | 2 | 435 | 99 | 127 | 661 |
| oneday/lib/features/achievement/widgets | 4 | 1,359 | 62 | 150 | 1,571 |
| oneday/lib/features/auth | 1 | 666 | 38 | 41 | 745 |
| oneday/lib/features/calendar | 1 | 1,094 | 89 | 77 | 1,260 |
| oneday/lib/features/community | 8 | 3,961 | 221 | 372 | 4,554 |
| oneday/lib/features/daily_plan | 4 | 620 | 106 | 133 | 859 |
| oneday/lib/features/daily_plan/models | 2 | 200 | 42 | 41 | 283 |
| oneday/lib/features/daily_plan/notifiers | 1 | 204 | 31 | 44 | 279 |
| oneday/lib/features/daily_plan/services | 1 | 216 | 33 | 48 | 297 |
| oneday/lib/features/exercise | 13 | 9,613 | 545 | 708 | 10,866 |
| oneday/lib/features/help_feedback | 1 | 666 | 39 | 50 | 755 |
| oneday/lib/features/home | 1 | 1,211 | 80 | 75 | 1,366 |
| oneday/lib/features/learning_report | 5 | 2,601 | 251 | 304 | 3,156 |
| oneday/lib/features/learning_report (Files) | 1 | 765 | 42 | 54 | 861 |
| oneday/lib/features/learning_report/models | 2 | 504 | 112 | 102 | 718 |
| oneday/lib/features/learning_report/services | 1 | 474 | 61 | 100 | 635 |
| oneday/lib/features/learning_report/widgets | 1 | 858 | 36 | 48 | 942 |
| oneday/lib/features/main | 1 | 98 | 4 | 8 | 110 |
| oneday/lib/features/memory_palace | 6 | 10,350 | 1,069 | 1,262 | 12,681 |
| oneday/lib/features/memory_palace (Files) | 3 | 9,757 | 992 | 1,139 | 11,888 |
| oneday/lib/features/memory_palace/providers | 1 | 191 | 18 | 33 | 242 |
| oneday/lib/features/memory_palace/utils | 2 | 402 | 59 | 90 | 551 |
| oneday/lib/features/onboarding | 2 | 1,016 | 77 | 101 | 1,194 |
| oneday/lib/features/photo_album | 1 | 792 | 48 | 78 | 918 |
| oneday/lib/features/profile | 1 | 785 | 40 | 66 | 891 |
| oneday/lib/features/reflection | 1 | 1,327 | 66 | 136 | 1,529 |
| oneday/lib/features/settings | 1 | 776 | 56 | 68 | 900 |
| oneday/lib/features/splash | 1 | 177 | 12 | 23 | 212 |
| oneday/lib/features/study_time | 5 | 1,334 | 190 | 228 | 1,752 |
| oneday/lib/features/study_time (Files) | 1 | 253 | 11 | 17 | 281 |
| oneday/lib/features/study_time/models | 2 | 439 | 92 | 80 | 611 |
| oneday/lib/features/study_time/providers | 1 | 310 | 26 | 51 | 387 |
| oneday/lib/features/study_time/services | 1 | 332 | 61 | 80 | 473 |
| oneday/lib/features/time_box | 7 | 5,107 | 379 | 431 | 5,917 |
| oneday/lib/features/time_box (Files) | 1 | 3,255 | 220 | 216 | 3,691 |
| oneday/lib/features/time_box/managers | 1 | 311 | 47 | 54 | 412 |
| oneday/lib/features/time_box/models | 2 | 426 | 73 | 80 | 579 |
| oneday/lib/features/time_box/pages | 1 | 487 | 14 | 35 | 536 |
| oneday/lib/features/time_box/providers | 1 | 223 | 19 | 25 | 267 |
| oneday/lib/features/time_box/widgets | 1 | 405 | 6 | 21 | 432 |
| oneday/lib/features/vocabulary | 14 | 6,604 | 536 | 785 | 7,925 |
| oneday/lib/features/wage_system | 2 | 2,447 | 110 | 155 | 2,712 |
| oneday/lib/features/widget | 4 | 988 | 156 | 125 | 1,269 |
| oneday/lib/features/widget/models | 1 | 152 | 15 | 22 | 189 |
| oneday/lib/features/widget/services | 2 | 476 | 126 | 79 | 681 |
| oneday/lib/features/widget/utils | 1 | 360 | 15 | 24 | 399 |
| oneday/lib/l10n | 3 | 469 | 366 | 244 | 1,079 |
| oneday/lib/router | 1 | 347 | 34 | 42 | 423 |
| oneday/lib/services | 8 | 1,459 | 284 | 297 | 2,040 |
| oneday/lib/services (Files) | 7 | 1,440 | 282 | 293 | 2,015 |
| oneday/lib/services/providers | 1 | 19 | 2 | 4 | 25 |
| oneday/lib/shared | 8 | 1,167 | 208 | 253 | 1,628 |
| oneday/lib/shared/config | 1 | 67 | 20 | 18 | 105 |
| oneday/lib/shared/services | 1 | 160 | 27 | 37 | 224 |
| oneday/lib/shared/utils | 4 | 674 | 136 | 141 | 951 |
| oneday/lib/shared/widgets | 2 | 266 | 25 | 57 | 348 |
| oneday/lib/shared/widgets (Files) | 1 | 204 | 17 | 43 | 264 |
| oneday/lib/shared/widgets/icons | 1 | 62 | 8 | 14 | 84 |
| oneday/lib/utils | 1 | 96 | 10 | 20 | 126 |
| oneday/linux | 9 | 335 | 37 | 92 | 464 |
| oneday/linux (Files) | 1 | 104 | 0 | 25 | 129 |
| oneday/linux/flutter | 4 | 115 | 9 | 27 | 151 |
| oneday/linux/runner | 4 | 116 | 28 | 40 | 184 |
| oneday/macos | 7 | 487 | 6 | 26 | 519 |
| oneday/macos (Files) | 1 | 32 | 1 | 10 | 43 |
| oneday/macos/Flutter | 1 | 14 | 3 | 4 | 21 |
| oneday/macos/Runner | 4 | 434 | 0 | 8 | 442 |
| oneday/macos/Runner (Files) | 2 | 23 | 0 | 7 | 30 |
| oneday/macos/Runner/Assets.xcassets | 1 | 68 | 0 | 0 | 68 |
| oneday/macos/Runner/Assets.xcassets/AppIcon.appiconset | 1 | 68 | 0 | 0 | 68 |
| oneday/macos/Runner/Base.lproj | 1 | 343 | 0 | 1 | 344 |
| oneday/macos/RunnerTests | 1 | 7 | 2 | 4 | 13 |
| oneday/scripts | 4 | 472 | 62 | 106 | 640 |
| oneday/test | 90 | 12,321 | 1,597 | 2,692 | 16,610 |
| oneday/test (Files) | 70 | 8,292 | 1,149 | 2,043 | 11,484 |
| oneday/test/features | 17 | 3,292 | 338 | 499 | 4,129 |
| oneday/test/features/exercise | 5 | 595 | 107 | 139 | 841 |
| oneday/test/features/help_feedback | 1 | 88 | 17 | 33 | 138 |
| oneday/test/features/profile | 1 | 165 | 21 | 38 | 224 |
| oneday/test/features/study_time | 1 | 264 | 13 | 38 | 315 |
| oneday/test/features/time_box | 2 | 426 | 41 | 67 | 534 |
| oneday/test/features/ui | 7 | 1,754 | 139 | 184 | 2,077 |
| oneday/test/integration | 1 | 103 | 52 | 53 | 208 |
| oneday/test/services | 2 | 634 | 58 | 97 | 789 |
| oneday/test_apps | 9 | 2,257 | 57 | 206 | 2,520 |
| oneday/web | 2 | 54 | 15 | 5 | 74 |
| oneday/windows | 14 | 691 | 94 | 188 | 973 |
| oneday/windows (Files) | 1 | 89 | 0 | 20 | 109 |
| oneday/windows/flutter | 4 | 140 | 9 | 29 | 178 |
| oneday/windows/runner | 9 | 462 | 85 | 139 | 686 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)