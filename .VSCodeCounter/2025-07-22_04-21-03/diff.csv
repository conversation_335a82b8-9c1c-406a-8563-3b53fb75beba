"filename", "language", "Markdown", "<PERSON><PERSON><PERSON>", "<PERSON>TM<PERSON>", "<PERSON>AM<PERSON>", "Dar<PERSON>", "<PERSON>", "XML", "comment", "blank", "total"
"/Users/<USER>/development/flutter_apps/NOTION_PROJECT_TEMPLATE.md", "Markdown", 0, 0, 0, 0, 0, 0, 0, 0, 3, 3
"/Users/<USER>/development/flutter_apps/USER_COMMUNITY_TEMPLATE.md", "Markdown", 11, 0, 0, 0, 0, 0, 0, 0, 3, 14
"/Users/<USER>/development/flutter_apps/accompanion/.idea/accompanion.iml", "XML", 0, 0, 0, 0, 0, 0, 15, 0, 0, 15
"/Users/<USER>/development/flutter_apps/accompanion/.idea/caches/deviceStreaming.xml", "XML", 0, 0, 0, 0, 0, 0, 835, 0, 0, 835
"/Users/<USER>/development/flutter_apps/accompanion/.idea/deviceManager.xml", "XML", 0, 0, 0, 0, 0, 0, 13, 0, 0, 13
"/Users/<USER>/development/flutter_apps/accompanion/.idea/libraries/Dart_Packages.xml", "XML", 0, 0, 0, 0, 0, 0, 748, 0, 0, 748
"/Users/<USER>/development/flutter_apps/accompanion/.idea/libraries/Dart_SDK.xml", "XML", 0, 0, 0, 0, 0, 0, 31, 0, 0, 31
"/Users/<USER>/development/flutter_apps/accompanion/.idea/misc.xml", "XML", 0, 0, 0, 0, 0, 0, 6, 0, 0, 6
"/Users/<USER>/development/flutter_apps/accompanion/.idea/modules.xml", "XML", 0, 0, 0, 0, 0, 0, 8, 0, 0, 8
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/main.dart", "Dart", 0, 0, 0, 0, 27, 0, 0, -36, -6, -15
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/calculation_screen.dart", "Dart", 0, 0, 0, 0, -33, 0, 0, 0, 0, -33
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/history_screen.dart", "Dart", 0, 0, 0, 0, 50, 0, 0, 0, 0, 50
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/home_screen.dart", "Dart", 0, 0, 0, 0, 29, 0, 0, 6, 9, 44
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/mode_selection_screen.dart", "Dart", 0, 0, 0, 0, 46, 0, 0, 5, 6, 57
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/onboarding_screen.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 1, 1, 4
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/optimization_tips_screen.dart", "Dart", 0, 0, 0, 0, 12, 0, 0, 0, 0, 12
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/room_setup_screen.dart", "Dart", 0, 0, 0, 0, 38, 0, 0, 0, 2, 40
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/settings_screen.dart", "Dart", 0, 0, 0, 0, 74, 0, 0, 1, 5, 80
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/lib/screens/splash_screen.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 3, 4, 8
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/pubspec.yaml", "YAML", 0, 0, 0, 1, 0, 0, 0, 1, 1, 3
"/Users/<USER>/development/flutter_apps/accompanion/accompanion/test/widget_test.dart", "Dart", 0, 0, 0, 0, -4, 0, 0, 1, 0, -3
"/Users/<USER>/development/flutter_apps/oneday/.augment/rules/rules.md", "Markdown", 387, 0, 0, 0, 0, 0, 0, 0, 114, 501
"/Users/<USER>/development/flutter_apps/oneday/.cursorrules.md", "Markdown", 52, 0, 0, 0, 0, 0, 0, 0, 11, 63
"/Users/<USER>/development/flutter_apps/oneday/Action.html", "HTML", 0, 0, 693, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/oneday/CHECKPOINT_BACKUP_STATUS.md", "Markdown", 116, 0, 0, 0, 0, 0, 0, 0, 32, 148
"/Users/<USER>/development/flutter_apps/oneday/CUSTOM_LIBRARY_EDITOR_IMPROVEMENTS.md", "Markdown", 101, 0, 0, 0, 0, 0, 0, 0, 27, 128
"/Users/<USER>/development/flutter_apps/oneday/EXERCISE_LIBRARY_FIXES.md", "Markdown", 112, 0, 0, 0, 0, 0, 0, 0, 31, 143
"/Users/<USER>/development/flutter_apps/oneday/INTERNATIONALIZATION_IMPLEMENTATION.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 37, 163
"/Users/<USER>/development/flutter_apps/oneday/OPTIMIZING_CURSOR_AI.md", "Markdown", 182, 0, 0, 0, 0, 0, 0, 0, 58, 240
"/Users/<USER>/development/flutter_apps/oneday/PROBLEM_SOLUTIONS.md", "Markdown", 120, 0, 0, 0, 0, 0, 0, 0, 31, 151
"/Users/<USER>/development/flutter_apps/oneday/RADAR_CHART_FIX_SUMMARY.md", "Markdown", 83, 0, 0, 0, 0, 0, 0, 0, 23, 106
"/Users/<USER>/development/flutter_apps/oneday/RADAR_CHART_GRID_FIX.md", "Markdown", 109, 0, 0, 0, 0, 0, 0, 0, 43, 152
"/Users/<USER>/development/flutter_apps/oneday/RADAR_CHART_PENTAGON_FIX_SUMMARY.md", "Markdown", 145, 0, 0, 0, 0, 0, 0, 0, 36, 181
"/Users/<USER>/development/flutter_apps/oneday/RADAR_CHART_TECHNICAL_ANALYSIS.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 29, 132
"/Users/<USER>/development/flutter_apps/oneday/ROI_CHART_Y_AXIS_FIX_SUMMARY.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 31, 157
"/Users/<USER>/development/flutter_apps/oneday/STUDY_STATISTICS_DEBUG_REPORT.md", "Markdown", 109, 0, 0, 0, 0, 0, 0, 0, 32, 141
"/Users/<USER>/development/flutter_apps/oneday/STUDY_STATISTICS_UI_REFRESH_FIX.md", "Markdown", 127, 0, 0, 0, 0, 0, 0, 0, 42, 169
"/Users/<USER>/development/flutter_apps/oneday/VERSION_DIFF_REPORT.md", "Markdown", 136, 0, 0, 0, 0, 0, 0, 0, 41, 177
"/Users/<USER>/development/flutter_apps/oneday/analysis_options.yaml", "YAML", 0, 0, 0, 1, 0, 0, 0, -1, 0, 0
"/Users/<USER>/development/flutter_apps/oneday/assets/data/profanity_words.json", "JSON", 0, 259, 0, 0, 0, 0, 0, 0, 1, 260
"/Users/<USER>/development/flutter_apps/oneday/devtools_options.yaml", "YAML", 0, 0, 0, 3, 0, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/docs/ACHIEVEMENT_UNLOCK_NAVIGATION_FIX.md", "Markdown", 161, 0, 0, 0, 0, 0, 0, 0, 40, 201
"/Users/<USER>/development/flutter_apps/oneday/docs/BUBBLE_OFFSET_DEBUGGING.md", "Markdown", 121, 0, 0, 0, 0, 0, 0, 0, 30, 151
"/Users/<USER>/development/flutter_apps/oneday/docs/BUBBLE_POSITIONING_FINAL_FIX.md", "Markdown", 117, 0, 0, 0, 0, 0, 0, 0, 32, 149
"/Users/<USER>/development/flutter_apps/oneday/docs/BUBBLE_POSITIONING_PRECISE_FIX.md", "Markdown", 130, 0, 0, 0, 0, 0, 0, 0, 43, 173
"/Users/<USER>/development/flutter_apps/oneday/docs/BUBBLE_POSITION_ADJUSTMENT.md", "Markdown", 100, 0, 0, 0, 0, 0, 0, 0, 29, 129
"/Users/<USER>/development/flutter_apps/oneday/docs/CLICK_POSITION_FIX.md", "Markdown", 126, 0, 0, 0, 0, 0, 0, 0, 37, 163
"/Users/<USER>/development/flutter_apps/oneday/docs/COORDINATE_SYSTEM_FINAL_FIX.md", "Markdown", 111, 0, 0, 0, 0, 0, 0, 0, 38, 149
"/Users/<USER>/development/flutter_apps/oneday/docs/DEBUGGING_STEP_1_TEST_GUIDE.md", "Markdown", 115, 0, 0, 0, 0, 0, 0, 0, 27, 142
"/Users/<USER>/development/flutter_apps/oneday/docs/IMAGE_COMPRESSION_IMPROVEMENTS.md", "Markdown", 103, 0, 0, 0, 0, 0, 0, 0, 38, 141
"/Users/<USER>/development/flutter_apps/oneday/docs/STANDARDIZED_COORDINATE_SYSTEM_IMPLEMENTATION.md", "Markdown", 146, 0, 0, 0, 0, 0, 0, 0, 46, 192
"/Users/<USER>/development/flutter_apps/oneday/docs/USER_IMPORTED_PHOTOS_FIX.md", "Markdown", 146, 0, 0, 0, 0, 0, 0, 0, 44, 190
"/Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_DEBUG_GUIDE.md", "Markdown", 113, 0, 0, 0, 0, 0, 0, 0, 33, 146
"/Users/<USER>/development/flutter_apps/oneday/docs/development/PROFANITY_FILTER_SYSTEM_DESIGN.md", "Markdown", 206, 0, 0, 0, 0, 0, 0, 0, 53, 259
"/Users/<USER>/development/flutter_apps/oneday/docs/development/STUDY_STATISTICS_REALTIME_UPDATE_IMPLEMENTATION.md", "Markdown", 145, 0, 0, 0, 0, 0, 0, 0, 43, 188
"/Users/<USER>/development/flutter_apps/oneday/docs/features/HELP_FEEDBACK.md", "Markdown", 105, 0, 0, 0, 0, 0, 0, 0, 32, 137
"/Users/<USER>/development/flutter_apps/oneday/docs/features/PROFILE_DATA_SYNC.md", "Markdown", 129, 0, 0, 0, 0, 0, 0, 0, 39, 168
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/ACTION_LIBRARY_SELECTOR_OVERFLOW_FIX.md", "Markdown", 168, 0, 0, 0, 0, 0, 0, 0, 35, 203
"/Users/<USER>/development/flutter_apps/oneday/docs/fixes/CUSTOM_ACTION_EDIT_SAVE_FIX.md", "Markdown", 131, 0, 0, 0, 0, 0, 0, 0, 35, 166
"/Users/<USER>/development/flutter_apps/oneday/docs/troubleshooting/PROBLEM_SOLUTIONS.md", "Markdown", 29, 0, 0, 0, 0, 0, 0, 0, 6, 35
"/Users/<USER>/development/flutter_apps/oneday/example/calendar_task_display_demo.dart", "Dart", 0, 0, 0, 0, 23, 0, 0, 1, 0, 24
"/Users/<USER>/development/flutter_apps/oneday/example/color_scheme_comparison_demo.dart", "Dart", 0, 0, 0, 0, 22, 0, 0, 0, 0, 22
"/Users/<USER>/development/flutter_apps/oneday/example/study_session_completion_demo.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/example/timebox_rest_skip_demo.dart", "Dart", 0, 0, 0, 0, 9, 0, 0, 0, 0, 9
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/AppIntent.swift", "Swift", 0, 0, 0, 0, 0, 8, 0, 7, 4, 19
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetBundle.swift", "Swift", 0, 0, 0, 0, 0, 8, 0, 6, 3, 17
"/Users/<USER>/development/flutter_apps/oneday/ios/OneDayWidget/OneDayWidgetControl.swift", "Swift", 0, 0, 0, 0, 0, 56, 0, 7, 15, 78
"/Users/<USER>/development/flutter_apps/oneday/l10n.yaml", "YAML", 0, 0, 0, 4, 0, 0, 0, 0, 1, 5
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_manual_test_page.dart", "Dart", 0, 0, 0, 0, 281, 0, 0, 10, 31, 322
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/profanity_filter_test_page.dart", "Dart", 0, 0, 0, 0, 189, 0, 0, 2, 28, 219
"/Users/<USER>/development/flutter_apps/oneday/lib/debug/radar_test_page.dart", "Dart", 0, 0, 0, 0, 69, 0, 0, 2, 7, 78
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/models/ability_radar_models.dart", "Dart", 0, 0, 0, 0, 53, 0, 0, 5, 9, 67
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/pages/ability_radar_page.dart", "Dart", 0, 0, 0, 0, 187, 0, 0, 11, 17, 215
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_mock_providers.dart", "Dart", 0, 0, 0, 0, -33, 0, 0, 0, 0, -33
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/providers/ability_radar_providers.dart", "Dart", 0, 0, 0, 0, 18, 0, 0, 10, 0, 28
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/ability_radar_service.dart", "Dart", 0, 0, 0, 0, -6, 0, 0, 0, 0, -6
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/services/radar_share_service.dart", "Dart", 0, 0, 0, 0, 274, 0, 0, 54, 57, 385
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/ability_radar_chart.dart", "Dart", 0, 0, 0, 0, 53, 0, 0, 13, 4, 70
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/game_style_rank_widget.dart", "Dart", 0, 0, 0, 0, 242, 0, 0, 20, 31, 293
"/Users/<USER>/development/flutter_apps/oneday/lib/features/ability_radar/widgets/radar_share_panel.dart", "Dart", 0, 0, 0, 0, 257, 0, 0, 24, 25, 306
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/models/user_level.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/achievement/widgets/achievement_unlock_notification.dart", "Dart", 0, 0, 0, 0, 30, 0, 0, 1, 2, 33
"/Users/<USER>/development/flutter_apps/oneday/lib/features/calendar/calendar_page.dart", "Dart", 0, 0, 0, 0, -70, 0, 0, -3, -3, -76
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/article_import_service.dart", "Dart", 0, 0, 0, 0, 53, 0, 0, 3, 3, 59
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_feed_page.dart", "Dart", 0, 0, 0, 0, 116, 0, 0, 15, 7, 138
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_post_editor_page.dart", "Dart", 0, 0, 0, 0, 226, 0, 0, 14, 20, 260
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/community_storage_service.dart", "Dart", 0, 0, 0, 0, 121, 0, 0, 11, 20, 152
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_dialog.dart", "Dart", 0, 0, 0, 0, 357, 0, 0, 7, 18, 382
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/content_report_service.dart", "Dart", 0, 0, 0, 0, 347, 0, 0, 33, 57, 437
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_service.dart", "Dart", 0, 0, 0, 0, 368, 0, 0, 34, 60, 462
"/Users/<USER>/development/flutter_apps/oneday/lib/features/community/profanity_filter_settings_page.dart", "Dart", 0, 0, 0, 0, 722, 0, 0, 16, 30, 768
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/action_library_category_manager.dart", "Dart", 0, 0, 0, 0, 304, 0, 0, 24, 33, 361
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_action_library_dialog.dart", "Dart", 0, 0, 0, 0, 345, 0, 0, 7, 20, 372
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/create_custom_library_dialog.dart", "Dart", 0, 0, 0, 0, 21, 0, 0, 0, 0, 21
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_editor_dialog.dart", "Dart", 0, 0, 0, 0, 35, 0, 0, 2, 2, 39
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library.dart", "Dart", 0, 0, 0, 0, 4, 0, 0, 0, 0, 4
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_action_library_service.dart", "Dart", 0, 0, 0, 0, 31, 0, 0, 4, 3, 38
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/custom_library_editor_page.dart", "Dart", 0, 0, 0, 0, 282, 0, 0, 24, 24, 330
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_library_page.dart", "Dart", 0, 0, 0, 0, 1135, 0, 0, 59, 46, 1240
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/exercise_session_page.dart", "Dart", 0, 0, 0, 0, 13, 0, 0, -1, -4, 8
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/focused_training_page.dart", "Dart", 0, 0, 0, 0, 26, 0, 0, 0, 0, 26
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/manage_custom_libraries_dialog.dart", "Dart", 0, 0, 0, 0, 947, 0, 0, 45, 45, 1037
"/Users/<USER>/development/flutter_apps/oneday/lib/features/exercise/pao_integration_service.dart", "Dart", 0, 0, 0, 0, 18, 0, 0, 0, -1, 17
"/Users/<USER>/development/flutter_apps/oneday/lib/features/help_feedback/help_feedback_page.dart", "Dart", 0, 0, 0, 0, 666, 0, 0, 39, 50, 755
"/Users/<USER>/development/flutter_apps/oneday/lib/features/home/<USER>", "Dart", 0, 0, 0, 0, 118, 0, 0, 6, 10, 134
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/learning_report_page.dart", "Dart", 0, 0, 0, 0, 8, 0, 0, 0, 0, 8
"/Users/<USER>/development/flutter_apps/oneday/lib/features/learning_report/widgets/chart_widgets.dart", "Dart", 0, 0, 0, 0, 94, 0, 0, 22, 18, 134
"/Users/<USER>/development/flutter_apps/oneday/lib/features/main/main_container_page.dart", "Dart", 0, 0, 0, 0, -6, 0, 0, 0, 2, -4
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/palace_manager_page.dart", "Dart", 0, 0, 0, 0, -47, 0, 0, -6, -23, -76
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/providers/memory_palace_provider.dart", "Dart", 0, 0, 0, 0, 191, 0, 0, 18, 33, 242
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page.dart", "Dart", 0, 0, 0, 0, -120, 0, 0, -49, -87, -256
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_copy.dart", "Dart", 0, 0, 0, 0, 1596, 0, 0, 167, 199, 1962
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/scene_detail_page_副本.dart", "Dart", 0, 0, 0, 0, -1672, 0, 0, -186, -224, -2082
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/anchor_data_migration.dart", "Dart", 0, 0, 0, 0, 215, 0, 0, 30, 51, 296
"/Users/<USER>/development/flutter_apps/oneday/lib/features/memory_palace/utils/image_coordinate_system.dart", "Dart", 0, 0, 0, 0, 187, 0, 0, 29, 39, 255
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/notion_style_onboarding_page.dart", "Dart", 0, 0, 0, 0, 5, 0, 0, 0, -4, 1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/onboarding/onboarding_page.dart", "Dart", 0, 0, 0, 0, -15, 0, 0, 0, 1, -14
"/Users/<USER>/development/flutter_apps/oneday/lib/features/photo_album/photo_album_creator_page.dart", "Dart", 0, 0, 0, 0, -16, 0, 0, -1, -3, -20
"/Users/<USER>/development/flutter_apps/oneday/lib/features/profile/profile_page.dart", "Dart", 0, 0, 0, 0, 84, 0, 0, 10, 8, 102
"/Users/<USER>/development/flutter_apps/oneday/lib/features/reflection/reflection_log_page.dart", "Dart", 0, 0, 0, 0, 3, 0, 0, 0, 1, 4
"/Users/<USER>/development/flutter_apps/oneday/lib/features/settings/settings_page.dart", "Dart", 0, 0, 0, 0, 4, 0, 0, 7, 6, 17
"/Users/<USER>/development/flutter_apps/oneday/lib/features/splash/splash_page.dart", "Dart", 0, 0, 0, 0, -9, 0, 0, 0, 1, -8
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/providers/study_time_providers.dart", "Dart", 0, 0, 0, 0, 69, 0, 0, 0, 5, 74
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/services/study_time_statistics_service.dart", "Dart", 0, 0, 0, 0, 13, 0, 0, 1, 1, 15
"/Users/<USER>/development/flutter_apps/oneday/lib/features/study_time/test_data_sync_page.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 0, 0, 2
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/managers/task_category_manager.dart", "Dart", 0, 0, 0, 0, 311, 0, 0, 47, 54, 412
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/models/timebox_models.dart", "Dart", 0, 0, 0, 0, 82, 0, 0, 15, 16, 113
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/pages/task_category_management_page.dart", "Dart", 0, 0, 0, 0, 487, 0, 0, 14, 35, 536
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/providers/timebox_provider.dart", "Dart", 0, 0, 0, 0, 6, 0, 0, 0, 0, 6
"/Users/<USER>/development/flutter_apps/oneday/lib/features/time_box/timebox_list_page.dart", "Dart", 0, 0, 0, 0, 151, 0, 0, 28, -16, 163
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_algorithm.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 3, 0, 5
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/fsrs_service.dart", "Dart", 0, 0, 0, 0, -43, 0, 0, 54, -8, 3
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/graduate_vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 7, -1, 7
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_cache_manager.dart", "Dart", 0, 0, 0, 0, 15, 0, 0, 0, 0, 15
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_category_page.dart", "Dart", 0, 0, 0, 0, 42, 0, 0, 0, -1, 41
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_manager_page.dart", "Dart", 0, 0, 0, 0, -4, 0, 0, 0, 0, -4
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_page.dart", "Dart", 0, 0, 0, 0, 26, 0, 0, 0, 1, 27
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/vocabulary_service.dart", "Dart", 0, 0, 0, 0, 60, 0, 0, 5, -2, 63
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_meaning_service.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/vocabulary/word_root_service.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/store_page.dart", "Dart", 0, 0, 0, 0, -22, 0, 0, 0, 0, -22
"/Users/<USER>/development/flutter_apps/oneday/lib/features/wage_system/wage_wallet_page.dart", "Dart", 0, 0, 0, 0, 11, 0, 0, 0, 1, 12
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/models/widget_models.dart", "Dart", 0, 0, 0, 0, 152, 0, 0, 15, 22, 189
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/memory_palace_integration_service.dart", "Dart", 0, 0, 0, 0, 350, 0, 0, 38, 47, 435
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/services/widget_service.dart", "Dart", 0, 0, 0, 0, 126, 0, 0, 88, 32, 246
"/Users/<USER>/development/flutter_apps/oneday/lib/features/widget/utils/error_handler.dart", "Dart", 0, 0, 0, 0, 360, 0, 0, 15, 24, 399
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations.dart", "Dart", 0, 0, 0, 0, 129, 0, 0, 360, 90, 579
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_en.dart", "Dart", 0, 0, 0, 0, 175, 0, 0, 3, 77, 255
"/Users/<USER>/development/flutter_apps/oneday/lib/l10n/app_localizations_zh.dart", "Dart", 0, 0, 0, 0, 165, 0, 0, 3, 77, 245
"/Users/<USER>/development/flutter_apps/oneday/lib/main.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 2, 2, 6
"/Users/<USER>/development/flutter_apps/oneday/lib/router/app_router.dart", "Dart", 0, 0, 0, 0, 14, 0, 0, 2, 2, 18
"/Users/<USER>/development/flutter_apps/oneday/lib/services/app_initialization_service.dart", "Dart", 0, 0, 0, 0, -2, 0, 0, 1, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/lib/services/floating_timer_service.dart", "Dart", 0, 0, 0, 0, 15, 0, 0, 0, 0, 15
"/Users/<USER>/development/flutter_apps/oneday/lib/services/global_timer_service.dart", "Dart", 0, 0, 0, 0, 2, 0, 0, 1, 0, 3
"/Users/<USER>/development/flutter_apps/oneday/lib/services/locale_service.dart", "Dart", 0, 0, 0, 0, 84, 0, 0, 23, 17, 124
"/Users/<USER>/development/flutter_apps/oneday/lib/services/system_overlay_permission.dart", "Dart", 0, 0, 0, 0, 8, 0, 0, 0, 0, 8
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/config/image_compression_config.dart", "Dart", 0, 0, 0, 0, -2, 0, 0, 0, -1, -3
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_compression_utils.dart", "Dart", 0, 0, 0, 0, 26, 0, 0, 6, 3, 35
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/image_watermark_utils.dart", "Dart", 0, 0, 0, 0, 39, 0, 0, 0, -3, 36
"/Users/<USER>/development/flutter_apps/oneday/lib/shared/utils/simple_image_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/lib/utils/category_cleanup_helper.dart", "Dart", 0, 0, 0, 0, 96, 0, 0, 10, 20, 126
"/Users/<USER>/development/flutter_apps/oneday/pubspec.yaml", "YAML", 0, 0, 0, 7, 0, 0, 0, 3, 3, 13
"/Users/<USER>/development/flutter_apps/oneday/scripts/clean_category_data.dart", "Dart", 0, 0, 0, 0, 35, 0, 0, 8, 6, 49
"/Users/<USER>/development/flutter_apps/oneday/scripts/verify_navigation_fix.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, -1, -2
"/Users/<USER>/development/flutter_apps/oneday/scripts/verify_radar_fix.dart", "Dart", 0, 0, 0, 0, 111, 0, 0, 8, 24, 143
"/Users/<USER>/development/flutter_apps/oneday/test/achievement_unlock_navigation_test.dart", "Dart", 0, 0, 0, 0, 79, 0, 0, 12, 13, 104
"/Users/<USER>/development/flutter_apps/oneday/test/action_library_category_test.dart", "Dart", 0, 0, 0, 0, 36, 0, 0, 2, 9, 47
"/Users/<USER>/development/flutter_apps/oneday/test/bubble_positioning_analysis.dart", "Dart", 0, 0, 0, 0, 114, 0, 0, 9, 26, 149
"/Users/<USER>/development/flutter_apps/oneday/test/calendar_task_display_test.dart", "Dart", 0, 0, 0, 0, 9, 0, 0, 6, 0, 15
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_fix_test.dart", "Dart", 0, 0, 0, 0, 3, 0, 0, 0, 0, 3
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_integration_test.dart", "Dart", 0, 0, 0, 0, 108, 0, 0, 29, 41, 178
"/Users/<USER>/development/flutter_apps/oneday/test/category_edit_state_test.dart", "Dart", 0, 0, 0, 0, 4, 0, 0, 0, 0, 4
"/Users/<USER>/development/flutter_apps/oneday/test/category_popup_menu_test.dart", "Dart", 0, 0, 0, 0, 5, 0, 0, 0, 0, 5
"/Users/<USER>/development/flutter_apps/oneday/test/category_removal_test.dart", "Dart", 0, 0, 0, 0, 151, 0, 0, 27, 32, 210
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_edit_test.dart", "Dart", 0, 0, 0, 0, 6, 0, 0, 0, -1, 5
"/Users/<USER>/development/flutter_apps/oneday/test/category_sidebar_test.dart", "Dart", 0, 0, 0, 0, 118, 0, 0, 20, 34, 172
"/Users/<USER>/development/flutter_apps/oneday/test/community_storage_test.dart", "Dart", 0, 0, 0, 0, 104, 0, 0, 13, 17, 134
"/Users/<USER>/development/flutter_apps/oneday/test/context_aware_album_creation_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/test/coordinate_system_logic_test.dart", "Dart", 0, 0, 0, 0, 174, 0, 0, 32, 46, 252
"/Users/<USER>/development/flutter_apps/oneday/test/custom_action_display_test.dart", "Dart", 0, 0, 0, 0, 78, 0, 0, 9, 10, 97
"/Users/<USER>/development/flutter_apps/oneday/test/custom_action_editor_test.dart", "Dart", 0, 0, 0, 0, 66, 0, 0, 14, 14, 94
"/Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_test.dart", "Dart", 0, 0, 0, 0, 119, 0, 0, 22, 27, 168
"/Users/<USER>/development/flutter_apps/oneday/test/custom_library_navigation_unit_test.dart", "Dart", 0, 0, 0, 0, 50, 0, 0, 15, 16, 81
"/Users/<USER>/development/flutter_apps/oneday/test/default_category_edit_test.dart", "Dart", 0, 0, 0, 0, 132, 0, 0, 31, 43, 206
"/Users/<USER>/development/flutter_apps/oneday/test/exercise_library_sidebar_test.dart", "Dart", 0, 0, 0, 0, 76, 0, 0, 15, 23, 114
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/category_integration_test.dart", "Dart", 0, 0, 0, 0, 128, 0, 0, 22, 27, 177
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_test.dart", "Dart", 0, 0, 0, 0, 155, 0, 0, 32, 43, 230
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_action_library_ui_test.dart", "Dart", 0, 0, 0, 0, -6, 0, 0, 0, 0, -6
"/Users/<USER>/development/flutter_apps/oneday/test/features/exercise/custom_category_test.dart", "Dart", 0, 0, 0, 0, 113, 0, 0, 21, 24, 158
"/Users/<USER>/development/flutter_apps/oneday/test/features/help_feedback/help_feedback_page_test.dart", "Dart", 0, 0, 0, 0, 88, 0, 0, 17, 33, 138
"/Users/<USER>/development/flutter_apps/oneday/test/features/profile/profile_data_sync_test.dart", "Dart", 0, 0, 0, 0, 165, 0, 0, 21, 38, 224
"/Users/<USER>/development/flutter_apps/oneday/test/features/time_box/action_library_selector_overflow_test.dart", "Dart", 0, 0, 0, 0, 301, 0, 0, 30, 49, 380
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/color_consistency_test.dart", "Dart", 0, 0, 0, 0, 14, 0, 0, 0, 0, 14
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/dropdown_pure_white_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 1, 0, 0
"/Users/<USER>/development/flutter_apps/oneday/test/features/ui/material3_surface_tinting_fix_test.dart", "Dart", 0, 0, 0, 0, 8, 0, 0, 0, 0, 8
"/Users/<USER>/development/flutter_apps/oneday/test/final_category_edit_test.dart", "Dart", 0, 0, 0, 0, 106, 0, 0, 36, 43, 185
"/Users/<USER>/development/flutter_apps/oneday/test/graduate_vocabulary_manager_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/test/image_compression_quality_test.dart", "Dart", 0, 0, 0, 0, 98, 0, 0, 14, 25, 137
"/Users/<USER>/development/flutter_apps/oneday/test/integration/study_session_completion_integration_test.dart", "Dart", 0, 0, 0, 0, -5, 0, 0, 1, -1, -5
"/Users/<USER>/development/flutter_apps/oneday/test/integration_test.dart", "Dart", 0, 0, 0, 0, 89, 0, 0, 27, 39, 155
"/Users/<USER>/development/flutter_apps/oneday/test/manage_custom_libraries_dialog_test.dart", "Dart", 0, 0, 0, 0, 65, 0, 0, 9, 18, 92
"/Users/<USER>/development/flutter_apps/oneday/test/memory_palace_card_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/test/photo_album_dialog_layout_test.dart", "Dart", 0, 0, 0, 0, -26, 0, 0, -1, -1, -28
"/Users/<USER>/development/flutter_apps/oneday/test/profanity_filter_integration_test.dart", "Dart", 0, 0, 0, 0, 94, 0, 0, 11, 25, 130
"/Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.dart", "Dart", 0, 0, 0, 0, 17, 0, 0, 0, 0, 17
"/Users/<USER>/development/flutter_apps/oneday/test/services/study_session_completion_service_test.mocks.dart", "Dart", 0, 0, 0, 0, 394, 0, 0, 26, 58, 478
"/Users/<USER>/development/flutter_apps/oneday/test/simple_profanity_test.dart", "Dart", 0, 0, 0, 0, 47, 0, 0, 1, 16, 64
"/Users/<USER>/development/flutter_apps/oneday/test/standardized_coordinate_system_test.dart", "Dart", 0, 0, 0, 0, 269, 0, 0, 28, 50, 347
"/Users/<USER>/development/flutter_apps/oneday/test/task_category_management_page_test.dart", "Dart", 0, 0, 0, 0, 120, 0, 0, 25, 39, 184
"/Users/<USER>/development/flutter_apps/oneday/test/task_category_test.dart", "Dart", 0, 0, 0, 0, 201, 0, 0, 17, 48, 266
"/Users/<USER>/development/flutter_apps/oneday/test/task_sync_test.dart", "Dart", 0, 0, 0, 0, 164, 0, 0, 27, 42, 233
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_adjustment.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_alignment_verification.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/test/test_bubble_center_alignment.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/test/test_coordinate_transformation_fix.dart", "Dart", 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"/Users/<USER>/development/flutter_apps/oneday/test/timebox_rest_skip_test.dart", "Dart", 0, 0, 0, 0, -9, 0, 0, 0, 0, -9
"/Users/<USER>/development/flutter_apps/oneday/test/vocabulary_test.dart", "Dart", 0, 0, 0, 0, -5, 0, 0, 0, -1, -6
"/Users/<USER>/development/flutter_apps/oneday/test/word_meaning_service_test.dart", "Dart", 0, 0, 0, 0, -1, 0, 0, 0, 0, -1
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_cleanup_demo.dart", "Dart", 0, 0, 0, 0, 253, 0, 0, 8, 26, 287
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_edit_demo.dart", "Dart", 0, 0, 0, 0, 276, 0, 0, 7, 24, 307
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_management_demo.dart", "Dart", 0, 0, 0, 0, 100, 0, 0, 1, 7, 108
"/Users/<USER>/development/flutter_apps/oneday/test_apps/category_removal_demo.dart", "Dart", 0, 0, 0, 0, 296, 0, 0, 8, 26, 330
"/Users/<USER>/development/flutter_apps/oneday/test_apps/simple_cleanup_test.dart", "Dart", 0, 0, 0, 0, 211, 0, 0, 4, 23, 238
"/Users/<USER>/development/flutter_apps/oneday/test_apps/simple_dialog_test.dart", "Dart", 0, 0, 0, 0, 215, 0, 0, 3, 17, 235
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_count_verification.dart", "Dart", 0, 0, 0, 0, 330, 0, 0, 13, 28, 371
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_demo.dart", "Dart", 0, 0, 0, 0, 256, 0, 0, 4, 27, 287
"/Users/<USER>/development/flutter_apps/oneday/test_apps/task_sync_verification.dart", "Dart", 0, 0, 0, 0, 320, 0, 0, 9, 28, 357
"/Users/<USER>/development/flutter_apps/oneday/test_category_fix.dart", "Dart", 0, 0, 0, 0, 83, 0, 0, 11, 22, 116
"/Users/<USER>/development/flutter_apps/oneday/test_drag_fix.dart", "Dart", 0, 0, 0, 0, 39, 0, 0, 5, 10, 54
"/Users/<USER>/development/flutter_apps/oneday/test_profanity_fix.md", "Markdown", 89, 0, 0, 0, 0, 0, 0, 0, 19, 108
"/Users/<USER>/development/flutter_apps/oneday/动作.html", "HTML", 0, 0, 693, 0, 0, 0, 0, 23, 53, 769
"/Users/<USER>/development/flutter_apps/tempPrompts.md", "Markdown", 186, 0, 0, 0, 0, 0, 0, 0, 77, 263
"Total", "-", 4822, 259, 1386, 16, 18086, 72, 1656, 2034, 3950, 32281