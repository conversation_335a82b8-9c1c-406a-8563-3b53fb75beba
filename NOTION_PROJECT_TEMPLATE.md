# 🚀 OneDay 项目主页 (Project Hub)

> **项目愿景**: 融合认知科学与技术，打造高效、健康、可持续的量化学习平台。

欢迎来到 `OneDay` 项目的协作空间！这里是我们的中央指挥室，包含了项目所有关键信息的入口。

---

### 快速导航

| 链接类型 | URL | 描述 |
| :--- | :--- | :--- |
| **💻代码仓库** | `[GitHub Repo URL]` | 项目的Git仓库，所有代码的家。 |
| **🎨设计稿** | `[Figma/Sketch URL]` | 所有的UI/UX设计源文件。 |
| **🚀部署环境** | `[TestFlight/Firebase URL]` | 测试版本的部署地址。 |
| **📊数据分析** | `[Analytics Dashboard URL]` | 用户行为数据分析面板。 |

---

### 🗺️ 产品路线图 (Roadmap)

> 这是我们前进的方向，展示了未来的主要功能和版本规划。

| 版本 | 核心主题 | 关键特性 | 预计发布 | 状态 |
| :--- | :--- | :--- | :--- | :--- |
| **v1.0** | MVP核心功能上线 | • 时间盒子<br>• 记忆宫殿<br>• 基础工资系统 | `Q3 2024` | `✅ 已完成` |
| **v1.1** | 社区与互动 | • 社区动态<br>• 知识点分享<br>• 学习小组 | `Q4 2024` | `in Progress` |
| **v1.2** | AI赋能 | • 智能复习提醒<br>• 个性化运动推荐<br>• 知识点自动归类 | `Q1 2025` | `Planning` |
| **v2.0** | 跨平台与AR | • Web/桌面端支持<br>• 真实AR记忆宫殿 | `Q3 2025` | `Backlog` |

---

<br>

# 🎨 产品与设计 (Product & Design)

> 这里定义了产品的灵魂：我们为谁服务，以及产品应该是什么样子。

## 1. 需求文档 (PRD)

*   **目标用户**: 追求高效学习的学生、需要持续充电的职场人士、认知科学爱好者。
*   **核心痛点**: 学习效率低、缺乏反馈、难以坚持。
*   **功能需求**:
    *   **用户故事**: 作为一个学生，我希望能用记忆宫殿法来背诵专业课知识点，并将它们与我的真实校园场景关联起来。
    *   **功能列表**: [链接到详细的功能需求数据库]
*   **非功能需求**:
    *   **性能**: 应用启动时间 < 3s，页面切换流畅。
    *   **兼容性**: 兼容主流的 iOS 和 Android 版本。
    *   **设计**: 严格遵循 Notion 风格极简设计。

## 2. 设计系统 (UI/UX)

> **核心原则**: "白纸黑字"，追求极简、优雅、高效、专注。[[memory:5086034744852321039]]

| 设计元素 | 规范 | 示例 (Hex/Font Name) |
| :--- | :--- | :--- |
| **主色调 (品牌色)** | 柔和蓝，用于按钮、图标、选中状态 | `#2E7EED` |
| **辅助色** | 优雅紫，用于次要强调 | `#7C3AED` |
| **背景色** | 纯白或极浅灰，营造"白纸"感 | `#FFFFFF` / `#F7F6F3` |
| **主要文本色** | 深灰，保证可读性 | `#37352F` |
| **次要文本色** | 中灰，用于辅助信息 | `#787774` |
| **英文字体** | `Inter` 或系统默认 `SF Pro` | - |
| **中文字体** | `PingFang SC` (苹方) | - |
| **核心交互** | 扁平化、克制阴影、细边框 | - |
| **组件库** | [链接到Figma组件库] | - |

---

<br>

# 🛠️ 工程与技术 (Engineering)

> 这里是工程师的乐园，包含了所有的技术决策和实现细节。

## 1. 系统架构

项目采用**面向功能 (Feature-Oriented)** 的分层架构，确保代码的高内聚、低耦合。

```mermaid
graph TD
    subgraph "🚀 Presentation Layer (UI)"
        A[features/../*_page.dart] -- "Manages UI & State" --> B(shared/widgets)
        A -- "Consumes State" --> C{Riverpod Providers}
        A -- "Navigates" --> D[router/app_router.dart]
    end

    subgraph "💼 Domain Layer (Business Logic)"
        C -- "Exposes Logic" --> E[features/../notifiers]
        E -- "Uses" --> F[features/../models]
    end

    subgraph "💾 Data Layer"
        G[Repository] -- "Abstracts Data Source" --> H[API Service]
        G -- "Abstracts Data Source" --> I[Local Storage (SharedPreferences)]
        E -- "Depends on" --> G
    end

    style A fill:#D1E7DD
    style C fill:#F8D7DA
    style E fill:#F8D7DA
    style G fill:#CFF4FC
```

*   **技术栈**: Flutter, Dart, Riverpod, GoRouter.
*   **代码规范**:
    *   遵循 `effective_dart` 风格。
    *   所有功能模块必须有独立的 `README.md`。
    *   UI组件优先提取到 `shared/widgets`。
*   **环境搭建**:
    1.  安装 Flutter 3.x SDK。
    2.  运行 `flutter pub get`。
    3.  运行 `flutter run`。
    > 详见 [SETUP_GUIDE.md]

## 2. 核心功能深潜

*   **[深潜] 记忆宫殿**: [链接到记忆宫殿功能详细技术文档]
*   **[深潜] 工资系统**: [链接到工资系统经济模型与实现文档]

---

<br>

# 🎯 任务与迭代 (Tasks & Sprints)

> 我们在这里管理日常的开发工作，将伟大的蓝图分解为可执行的步骤。
> **强烈建议**: 在Notion中将以下表格转换为真正的Database。

## 1. 任务看板 (Kanban Board)

| 任务名称 | 负责人 | 优先级 | 状态 | 关联模块 | Sprint |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **BUG-123**: 游客模式下无法进入商城 | `[Assignee]` | `High` | `In Progress` | `wage_system` | `Sprint 3` |
| **FEAT-45**: 开发社区动态Feed流 | `[Assignee]` | `Medium` | `Not Started` | `community` | `Sprint 4` |
| **CHORE-78**: 升级Riverpod到最新版 | `[Assignee]` | `Low` | `Done` | `core` | `Sprint 3` |

## 2. Bug上报模板

```markdown
### Bug描述
清晰、简洁地描述问题。

### 复现步骤
1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 出现错误

### 期望结果
描述期望发生什么。

### 实际结果
描述实际发生了什么。

### 截图/录屏
(如果适用, 在此添加截图或录屏)

### 环境信息
- **设备**: [例如: iPhone 14 Pro]
- **系统版本**: [例如: iOS 16.5]
- **应用版本**: [例如: v1.0.2]
```

---

<br>

# 🤝 会议与知识库 (Docs & Meetings)

> 沉淀团队智慧，确保信息畅通。

## 1. 会议纪要

| 会议名称 | 日期 | 参与者 | [链接到会议纪要] |
| :--- | :--- | :--- | :--- |
| **Sprint 3 规划会** | `2024-07-15` | `[All]` | `[Link]` |
| **技术架构评审会** | `2024-07-10` | `[Eng Team]` | `[Link]` |

## 2. 知识库/Wiki

*   **[指南] 如何进行Code Review**: [链接到文档]
*   **[指南] 版本发布流程**: [链接到文档]
*   **[分享] Riverpod最佳实践**: [链接到文档]

---

### 如何在Notion中使用此模板

1.  在Notion中创建一个新页面。
2.  将此文件的全部内容**复制**并**粘贴**到Notion页面中。
3.  Notion会自动识别Markdown格式。
4.  对于表格（如任务看板），点击表格左上角的`•••`菜单，选择 **"Turn into database"**，即可将其转换为功能强大的Notion数据库，然后您可以自由添加筛选、排序和不同的视图（如日历视图、看板视图）。 


